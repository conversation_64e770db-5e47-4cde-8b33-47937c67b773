import React, { useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { useSelector } from 'react-redux';
import { isTabDevice } from '../../config/appConfig';
import colors from '../../config/colors';
import { generateTermAndConditionData } from '../../constants/TermAndCondition';

import useStyles from '../../hooks/useStyles';
import ModalWrapper from '../modal/ModalWrapper/ModalWrapper';
import customTermAndCondition from './TermAndConditionStyles';
const TermAndCondition = ({
  setTermAndConditionEnabled,
  isTermAndConditionAccepted,
  logOut,
  parentDetails,
}) => {
  const TermAndConditionStyles = useStyles(customTermAndCondition);
  const rightColumnRef = useRef(null);

  const [selectedId, setSelectedId] = useState(1);
  const [useScrollPosition, setUseScrollPosition] = useState(true);

  const { userData } = useSelector(state => state?.auth);

  const { children } = useSelector(state => state?.common);

  const TermAndConditionData = generateTermAndConditionData(
    userData,
    parentDetails,
    children
  );
  const handleTitleClick = id => {
    setUseScrollPosition(false);

    setSelectedId(id);
    const index = TermAndConditionData.findIndex(item => item.id === id);
    if (rightColumnRef.current) {
      rightColumnRef.current.scrollToIndex({ index });
    }
  };

  const handleScroll = event => {
    if (useScrollPosition) {
      const contentOffsetY = event.nativeEvent.contentOffset.y;
      const itemHeight = 150;
      const visibleItemIndex = Math.ceil(contentOffsetY / itemHeight);
      if (
        visibleItemIndex >= 0 &&
        visibleItemIndex < TermAndConditionData.length
      ) {
        setSelectedId(TermAndConditionData[visibleItemIndex].id);
      }
    }
  };

  const renderItem = ({ item }) => {
    return (
      <View style={{ paddingLeft: wp('3%'), paddingRight: wp('3%') }}>
        <Text style={TermAndConditionStyles.contentTitle}>{item.title}</Text>
        {item.content.map(value => (
          <Text style={TermAndConditionStyles.contentText}>{value}</Text>
        ))}
      </View>
    );
  };
  return (
    <>
      <View>
        <View style={TermAndConditionStyles.container}>
          {/* TDOD Need to fix mobile wrapper */}
          <ModalWrapper transparent>
            <View style={TermAndConditionStyles.centeredView}>
              <View style={TermAndConditionStyles.overlay}></View>
              <View style={TermAndConditionStyles.modalView}>
                <View style={TermAndConditionStyles.modalTitleContainer}>
                  <Text style={TermAndConditionStyles.modalTitle}>
                    Terms & Conditions
                  </Text>
                </View>
                <View style={TermAndConditionStyles.modalBody}>
                  <View style={TermAndConditionStyles.titleList}>
                    {isTabDevice() &&
                      TermAndConditionData.map(item => (
                        <TouchableOpacity
                          key={item.id}
                          onPress={() => handleTitleClick(item.id)}
                          style={TermAndConditionStyles.titles}
                        >
                          <Text
                            style={[
                              TermAndConditionStyles.titleListText,
                              selectedId === item.id && {
                                color: colors.green,
                              },
                            ]}
                          >
                            {item.title}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    <View
                      style={
                        TermAndConditionStyles.termAndConditionAcceptSection
                      }
                    >
                      <>
                        <TouchableOpacity
                          style={TermAndConditionStyles.agreeButton}
                          onPress={() =>
                            isTermAndConditionAccepted
                              ? setTermAndConditionEnabled(false)
                              : logOut()
                          }
                        >
                          <Text style={TermAndConditionStyles.agreeButtonText}>
                            Cancel
                          </Text>
                        </TouchableOpacity>
                        {/* {!isExpired && (
                            <TouchableOpacity
                              style={TermAndConditionStyles.remindMeLaterButton}
                              onPress={async () => {
                                await submitTermAndCondition(
                                  { termActionType: 'LATER' },
                                  userId
                                ),
                                  getTermAndConditionDetails(userId),
                                  dispatch({
                                    type: SET_TERM_AND_CONDITION,
                                    payload: false,
                                  });
                              }}
                            >
                              <Text
                                style={TermAndConditionStyles.remindMeLaterText}
                              >
                                Remind me Later
                              </Text>
                            </TouchableOpacity>
                          )} */}
                      </>
                    </View>
                  </View>
                  <View style={TermAndConditionStyles.flatList}>
                    <FlatList
                      ref={rightColumnRef}
                      data={TermAndConditionData}
                      renderItem={renderItem}
                      keyExtractor={item => {
                        item.id;
                      }}
                      style={TermAndConditionStyles.content}
                      onScroll={handleScroll}
                      scrollEventThrottle={16}
                    />
                  </View>
                </View>
              </View>
            </View>
          </ModalWrapper>
        </View>
      </View>
    </>
  );
};

export default TermAndCondition;
