import AsyncStorage from '@react-native-async-storage/async-storage';
import { applyMiddleware, compose, createStore } from 'redux'; // Import Redux
import { persistReducer, persistStore } from 'redux-persist';
import reduxThunk from 'redux-thunk';
import rootReducer from './reducers/index';

declare global {
  interface Window {
    __REDUX_DEVTOOLS_EXTENSION_COMPOSE__?: typeof compose;
  }
}
const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;

let middleware = [reduxThunk];

if (process.env.NODE_ENV === 'development') {
  middleware = [...middleware];
}

const persistConfig = {
  key: 'auth',
  storage: AsyncStorage,
  whitelist: ['auth'],
};
const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = createStore(
  persistedReducer,
  composeEnhancers(applyMiddleware(...middleware))
);
const persistor = persistStore(store);

export type RootStore = ReturnType<typeof rootReducer>;

export { store, persistor };
