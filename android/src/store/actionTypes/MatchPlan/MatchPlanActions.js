export const MATCH_TYPES_REQUEST = 'MATCH_TYPES_REQUEST';
export const MATCH_TYPES_SUCCESS = 'MATCH_TYPES_SUCCESS';
export const MATCH_TYPES_FAILED = 'MATCH_TYPES_FAILED';

export const MATCH_TYPE_FORMATIONS_REQUEST = 'MATCH_TYPE_FORMATIONS_REQUEST';
export const MATCH_TYPE_FORMATIONS_SUCCESS = 'MATCH_TYPE_FORMATIONS_SUCCESS';
export const MATCH_TYPE_FORMATIONS_FAILED = 'MATCH_TYPE_FORMATIONS_FAILED';

export const MATCH_PLAN_REQUEST = 'MATCH_PLAN_REQUEST';
export const MATCH_PLAN_SUCCESS = 'MATCH_PLAN_SUCCESS';
export const MATCH_PLAN_FAILED = 'MATCH_PLAN_FAILED';

export const MATCH_CANDIDATE_PLAYERS_REQUEST =
  'MATCH_CANDIDATE_PLAYERS_REQUEST';
export const MATCH_CANDIDATE_PLAYERS_SUCCESS =
  'MATCH_CANDIDATE_PLAYERS_SUCCESS';
export const MATCH_CANDIDATE_PLAYERS_FAILED = 'MATCH_CANDIDATE_PLAYERS_FAILED';

export const MATCH_CANDIDATE_PLAYERS_SEARCH_REQUEST =
  'MATCH_CANDIDATE_PLAYERS_SEARCH_REQUEST';
export const MATCH_CANDIDATE_PLAYERS_SEARCH_SUCCESS =
  'MATCH_CANDIDATE_PLAYERS_SEARCH_SUCCESS';
export const MATCH_CANDIDATE_PLAYERS_SEARCH_FAILED =
  'MATCH_CANDIDATE_PLAYERS_SEARCH_FAILED';

export const MATCH_PLAN_SAVE_REQUEST = 'MATCH_PLAN_SAVE_REQUEST';
export const MATCH_PLAN_SAVE_SUCCESS = 'MATCH_PLAN_SAVE_SUCCESS';
export const MATCH_PLAN_SAVE_FAILED = 'MATCH_PLAN_SAVE_FAILED';
export const MATCH_PLAN_CLEAR_SAVE_STATUS = 'MATCH_PLAN_CLEAR_SAVE_STATUS';

export const MATCH_PLAN_RESET = 'MATCH_PLAN_RESET ';

export const MATCH_PLAYER_RATINGS_REQUEST = 'MATCH_PLAYER_RATINGS_REQUEST';
export const MATCH_PLAYER_RATINGS_SUCCESS = 'MATCH_PLAYER_RATINGS_SUCCESS';
export const MATCH_PLAYER_RATINGS_FAILED = 'MATCH_PLAYER_RATINGS_FAILED';
export const MATCH_PLAYER_RATINGS_RESET = 'MATCH_PLAYER_RATINGS_RESET';

export const SAVE_PLAYER_RATINGS_REQUEST = 'SAVE_PLAYER_RATINGS_REQUEST';
export const SAVE_PLAYER_RATINGS_SUCCESS = 'SAVE_PLAYER_RATINGS_SUCCESS';
export const SAVE_PLAYER_RATINGS_FAILED = 'SAVE_PLAYER_RATINGS_FAILED';
export const SAVE_PLAYER_RATINGS_RESET = 'SAVE_PLAYER_RATINGS_RESET';

export const GET_INITIAL_RATINGS_REQUEST = 'GET_INITIAL_RATINGS_REQUEST';
export const GET_INITIAL_RATINGS_SUCCESS = 'GET_INITIAL_RATINGS_SUCCESS';
export const GET_INITIAL_RATINGS_FAILED = 'GET_INITIAL_RATINGS_FAILED';

export const GET_INITIAL_MATCH_PLAN_REQUEST = 'GET_INITIAL_MATCH_PLAN_REQUEST';
export const GET_INITIAL_MATCH_PLAN_SUCCESS = 'GET_INITIAL_MATCH_PLAN_SUCCESS';
export const GET_INITIAL_MATCH_PLAN_FAILED = 'GET_INITIAL_MATCH_PLAN_FAILED';

export const GET_LATEST_MATCH_PLAN_REQUEST = 'GET_LATEST_MATCH_PLAN_REQUEST';
export const GET_LATEST_MATCH_PLAN_SUCCESS = 'GET_LATEST_MATCH_PLAN_SUCCESS';
export const GET_LATEST_MATCH_PLAN_FAILED = 'GET_LATEST_MATCH_PLAN_FAILED';

export const SET_MATCH_LOG_VIEW = 'SET_MATCH_LOG_VIEW';

export const REMOVE_SELECTED_MATCH_PLAN = 'REMOVE_SELECTED_MATCH_PLAN';
export const SET_SELECTED_MATCH_DETAILS = 'SET_SELECTED_MATCH_DETAILS';
