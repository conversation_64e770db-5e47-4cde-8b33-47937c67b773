export const PLAYER_MATCH_LOG_EVENTS_REQUEST =
  'PLAYER_MATCH_LOG_EVENTS_REQUEST';
export const PLAYER_MATCH_LOG_EVENTS_SUCCESS =
  'PLAYER_MATCH_LOG_EVENTS_SUCCESS';
export const PLAYER_MATCH_LOG_EVENTS_FAILED = 'PLAYER_MATCH_LOG_EVENTS_FAILED';

export const PLAYER_MATCH_LOG_ACTIVITIES_REQUEST =
  'PLAYER_MATCH_LOG_ACTIVITIES_REQUEST';
export const PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS =
  'PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS';
export const PLAYER_MATCH_LOG_ACTIVITIES_FAILED =
  'PLAYER_MATCH_LOG_ACTIVITIES_FAILED';
export const PLAYER_MATCH_LOG_ACTIVITIES_RESET =
  'PLAYER_MATCH_LOG_ACTIVITIES_RESET';

export const PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST =
  'PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST';
export const PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS =
  'PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS';
export const PLAYER_MATCH_LOG_MATCH_PLAN_FAILED =
  'PLAYER_MATCH_LOG_MATCH_PLAN_FAILED';
export const PLAYER_MATCH_LOG_MATCH_PLAN_RESET =
  'PLAYER_MATCH_LOG_MATCH_PLAN_RESET';

export const PLAYER_MATCH_LOG_FORMATIONS_REQUEST =
  'PLAYER_MATCH_LOG_FORMATIONS_REQUEST';
export const PLAYER_MATCH_LOG_FORMATIONS_SUCCESS =
  'PLAYER_MATCH_LOG_FORMATIONS_SUCCESS';
export const PLAYER_MATCH_LOG_FORMATIONS_FAILED =
  'PLAYER_MATCH_LOG_FORMATIONS_FAILED';

export const FETCH_PLAYER_MATCH_LOG_TEAMS_REQUEST =
  'FETCH_PLAYER_MATCH_LOG_TEAMS_REQUEST';
export const FETCH_PLAYER_MATCH_LOG_TEAMS_SUCCESS =
  'FETCH_PLAYER_MATCH_LOG_TEAMS_SUCCESS';
export const FETCH_PLAYER_MATCH_LOG_TEAMS_FAILED =
  'FETCH_PLAYER_MATCH_LOG_TEAMS_FAILED';
export const FETCH_PLAYER_MATCH_LOG_TEAMS_RESET =
  'FETCH_PLAYER_MATCH_LOG_TEAMS_RESET';

export const PLAYER_MATCH_LOG_SCORE_REQUEST = 'PLAYER_MATCH_LOG_SCORE_REQUEST';
export const PLAYER_MATCH_LOG_SCORE_SUCCESS = 'PLAYER_MATCH_LOG_SCORE_SUCCESS';
export const PLAYER_MATCH_LOG_SCORE_FAILED = 'PLAYER_MATCH_LOG_SCORE_FAILED';

export const SET_CURRENT_PLAYER_LOG_MATCH = 'SET_CURRENT_PLAYER_LOG_MATCH';
export const PLAYER_MATCH_LOG_RESET = 'PLAYER_MATCH_LOG_RESET';
export const PLAYER_MATCH_LOG_ALL_RESET = 'PLAYER_MATCH_LOG_ALL_RESET';

export const PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_SUCCESS =
  'PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_SUCCESS';

export const PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_REVERSE =
  'PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_REVERSE';

export const UPDATE_PLAYER_MATCH_SCORE_SOCKET =
  'UPDATE_PLAYER_MATCH_SCORE_SOCKET';
