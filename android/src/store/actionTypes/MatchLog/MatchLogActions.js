export const MATCH_LOG_ACTIVITIES_REQUEST = 'MATCH_LOG_ACTIVITIES_REQUEST';
export const MATCH_LOG_ACTIVITIES_SUCCESS = 'MATCH_LOG_ACTIVITIES_SUCCESS';
export const MATCH_LOG_ACTIVITIES_FAILED = 'MATCH_LOG_ACTIVITIES_FAILED';

export const MATCH_LOG_MORE_ACTIVITIES_REQUEST =
  'MATCH_LOG_MORE_ACTIVITIES_REQUEST';
export const MATCH_LOG_MORE_ACTIVITIES_SUCCESS =
  'MATCH_LOG_MORE_ACTIVITIES_SUCCESS';
export const MATCH_LOG_MORE_ACTIVITIES_FAILED =
  'MATCH_LOG_MORE_ACTIVITIES_FAILED';

export const MATCH_LOG_EVENTS_REQUEST = 'MATCH_LOG_EVENTS_REQUEST';
export const MATCH_LOG_EVENTS_SUCCESS = 'MATCH_LOG_EVENTS_SUCCESS';
export const MATCH_LOG_EVENTS_FAILED = 'MATCH_LOG_EVENTS_FAILED';

export const MATCH_LOG_MORE_EVENTS_REQUEST = 'MATCH_LOG_MORE_EVENTS_REQUEST';
export const MATCH_LOG_MORE_EVENTS_SUCCESS = 'MATCH_LOG_MORE_EVENTS_SUCCESS';
export const MATCH_LOG_MORE_EVENTS_FAILED = 'MATCH_LOG_MORE_EVENTS_FAILED';

export const MATCH_LOG_CONCLUDED_EVENTS_REQUEST =
  'MATCH_LOG_CONCLUDED_EVENTS_REQUEST';
export const MATCH_LOG_CONCLUDED_EVENTS_SUCCESS =
  'MATCH_LOG_CONCLUDED_EVENTS_SUCCESS';
export const MATCH_LOG_CONCLUDED_EVENTS_SUCCESS_CUSTOM_CONTENT =
  'MATCH_LOG_CONCLUDED_EVENTS_SUCCESS_CUSTOM_CONTENT';
export const MATCH_LOG_CONCLUDED_EVENTS_FAILED =
  'MATCH_LOG_CONCLUDED_EVENTS_FAILED';

export const MATCH_LOG_MATCH_FORMATION_REQUEST =
  'MATCH_LOG_MATCH_FORMATION_REQUEST';
export const MATCH_LOG_MATCH_FORMATION_SUCCESS =
  'MATCH_LOG_MATCH_FORMATION_SUCCESS';
export const MATCH_LOG_MATCH_FORMATION_FAILED =
  'MATCH_LOG_MATCH_FORMATION_FAILED';

export const MATCH_LOG_PLAYER_COORDINATES_REQUEST =
  'MATCH_LOG_PLAYER_COORDINATES_REQUEST';
export const MATCH_LOG_PLAYER_COORDINATES_SUCCESS =
  'MATCH_LOG_PLAYER_COORDINATES_SUCCESS';
export const MATCH_LOG_PLAYER_COORDINATES_FAILED =
  'MATCH_LOG_PLAYER_COORDINATES_FAILED';

export const MATCH_LOG_SET_SELECTED_EVENT = 'MATCH_LOG_SET_SELECTED_EVENT';

export const MATCH_LOG_MATCH_PLAN_REQUEST = 'MATCH_LOG_MATCH_PLAN_REQUEST';
export const MATCH_LOG_MATCH_PLAN_SUCCESS = 'MATCH_LOG_MATCH_PLAN_SUCCESS';
export const MATCH_LOG_MATCH_PLAN_FAILED = 'MATCH_LOG_MATCH_PLAN_FAILED';

export const MATCH_LOG_ADD_NEW_ACTION = 'MATCH_LOG_ADD_NEW_ACTION';

export const MATCH_LOG_SET_GAME_STATUS = 'MATCH_LOG_SET_GAME_STATUS';

export const MATCH_LOG_UPDATE_SCORE = 'MATCH_LOG_UPDATE_SCORE';

export const MATCH_LOG_OPPONENT_DETAILS_REQUEST =
  'MATCH_LOG_OPPONENT_DETAILS_REQUEST';
export const MATCH_LOG_OPPONENT_DETAILS_SUCCESS =
  'MATCH_LOG_OPPONENT_DETAILS_SUCCESS';
export const MATCH_LOG_OPPONENT_DETAILS_FAILED =
  'MATCH_LOG_OPPONENT_DETAILS_FAILED';

export const MATCH_INVITEES_REQUEST = 'MATCH_INVITEES_REQUEST';
export const MATCH_INVITEES_SUCCESS = 'MATCH_INVITEES_SUCCESS';
export const MATCH_INVITEES_FAILED = 'MATCH_INVITEES_FAILED';

export const RESET_INVITEES = 'RESET_INVITEES';

export const MATCH_ATTENDANCE_SAVE_REQUEST = 'MATCH_ATTENDANCE_SAVE_REQUEST';
export const MATCH_ATTENDANCE_SAVE_SUCCESS = 'MATCH_ATTENDANCE_SAVE_SUCCESS';
export const MATCH_ATTENDANCE_SAVE_FAILED = 'MATCH_ATTENDANCE_SAVE_FAILED';

export const SET_MATCH_INVITEES_ATTENDANT_STATE =
  'SET_MATCH_INVITEES_ATTENDANT_STATE';
export const MATCH_LOG_SET_CURRENT_STAGE = 'MATCH_LOG_SET_CURRENT_STAGE';

export const MATCH_LOG_REMOVE_LOG_ITEM = 'MATCH_LOG_REMOVE_LOG_ITEM';

export const INVITEES_STATUS_REQUEST = 'INVITEES_STATUS_REQUEST';
export const INVITEES_STATUS_SUCCESS = 'INVITEES_STATUS_SUCCESS';
export const INVITEES_STATUS_FAIL = 'INVITEES_STATUS_FAIL';

export const MATCH_LOG_SET_PLAYER_LIST = 'MATCH_LOG_SET_PLAYER_LIST';

export const MATCH_LOG_SAVE_ACTIVITY_REQUEST =
  'MATCH_LOG_SAVE_ACTIVITY_REQUEST';
export const MATCH_LOG_SAVE_ACTIVITY_SUCCESS =
  'MATCH_LOG_SAVE_ACTIVITY_SUCCESS';
export const MATCH_LOG_SAVE_ACTIVITY_SUCCESS_CUSTOM_CONTENT =
  'MATCH_LOG_SAVE_ACTIVITY_SUCCESS_CUSTOM_CONTENT';
export const MATCH_LOG_SAVE_ACTIVITY_FAILED = 'MATCH_LOG_SAVE_ACTIVITY_FAILED';

export const MATCH_LOG_BULK_SAVE_REQUEST = 'MATCH_LOG_BULK_SAVE_REQUEST';
export const MATCH_LOG_BULK_SAVE_SUCCESS = 'MATCH_LOG_BULK_SAVE_SUCCESS';
export const MATCH_LOG_BULK_SAVE_SUCCESS_CUSTOM_CONTENT =
  'MATCH_LOG_BULK_SAVE_SUCCESS_CUSTOM_CONTENT';
export const MATCH_LOG_BULK_SAVE_FAILED = 'MATCH_LOG_BULK_SAVE_FAILED';

export const MATCH_LOG_BULK_DELETE_REQUEST = 'MATCH_LOG_BULK_DELETE_REQUEST';
export const MATCH_LOG_BULK_DELETE_SUCCESS = 'MATCH_LOG_BULK_DELETE_SUCCESS';
export const MATCH_LOG_BULK_DELETE_SUCCESS_CUSTOM_CONTENT =
  'MATCH_LOG_BULK_DELETE_SUCCESS_CUSTOM_CONTENT';
export const MATCH_LOG_BULK_DELETE_FAILED = 'MATCH_LOG_BULK_DELETE_FAILED';

export const MATCH_LOG_GET_SCORE_REQUEST = 'MATCH_LOG_GET_SCORE_REQUEST';
export const MATCH_LOG_GET_SCORE_SUCCESS = 'MATCH_LOG_GET_SCORE_SUCCESS';
export const MATCH_LOG_GET_SCORE_FAILED = 'MATCH_LOG_GET_SCORE_FAILED';

export const MATCH_LOG_DELETE_ACTIVITY_REQUEST =
  'MATCH_LOG_DELETE_ACTIVITY_REQUEST';
export const MATCH_LOG_DELETE_ACTIVITY_SUCCESS =
  'MATCH_LOG_DELETE_ACTIVITY_SUCCESS';
export const MATCH_LOG_DELETE_ACTIVITY_SUCCESS_CUSTOM_CONTENT =
  'MATCH_LOG_DELETE_ACTIVITY_SUCCESS_CUSTOM_CONTENT';
export const MATCH_LOG_DELETE_ACTIVITY_FAILED =
  'MATCH_LOG_DELETE_ACTIVITY_FAILED';
export const MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES =
  'MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES';

export const MATCH_LOG_SET_SELECTED_DATE = 'MATCH_LOG_SET_SELECTED_DATE';
export const MATCH_SUMMERY_REQUEST = 'MATCH_SUMMERY_REQUEST';
export const MATCH_SUMMERY_SUCCESS = 'MATCH_SUMMERY_SUCCESS';
export const MATCH_SUMMERY_FAILED = 'MATCH_SUMMERY_FAILED';

export const MATCH_MORE_SUMMERY_REQUEST = 'MATCH_MORE_SUMMERY_REQUEST';
export const MATCH_MORE_SUMMERY_SUCCESS = 'MATCH_MORE_SUMMERY_SUCCESS';
export const MATCH_MORE_SUMMERY_FAILED = 'MATCH_MORE_SUMMERY_FAILED';

export const RESET_SUMMERY = 'RESET_SUMMERY';

export const MATCH_LOG_GET_LOG_REQUEST = 'MATCH_LOG_GET_LOG_REQUEST';
export const MATCH_LOG_GET_LOG_SUCCESS = 'MATCH_LOG_GET_LOG_SUCCESS';
export const MATCH_LOG_GET_LOG_SUCCESS_CUSTOM_CONTENT =
  'MATCH_LOG_GET_LOG_SUCCESS_CUSTOM_CONTENT';
export const MATCH_LOG_GET_LOG_FAILED = 'MATCH_LOG_GET_LOG_FAILED';

export const MATCH_LOG_SET_SELECTED_TEAM = 'MATCH_LOG_SET_SELECTED_TEAM';

export const MATCH_LOG_SAVE_GAME_REQUEST = 'MATCH_LOG_SAVE_GAME_REQUEST';
export const MATCH_LOG_SAVE_GAME_SUCCESS = 'MATCH_LOG_SAVE_GAME_SUCCESS';
export const MATCH_LOG_SAVE_GAME_FAILED = 'MATCH_LOG_SAVE_GAME_FAILED';

export const MATCH_LOG_RESET_DATA = 'MATCH_LOG_RESET_DATA';
export const FETCH_ONGOING_MATCH_REQUEST = 'FETCH_ONGOING_MATCH_REQUEST';
export const FETCH_ONGOING_MATCH_SUCCESS = 'FETCH_ONGOING_MATCH_SUCCESS';
export const FETCH_ONGOING_MATCH_FAIL = 'FETCH_ONGOING_MATCH_FAIL';

export const SET_ONGOING_MATCH = 'SET_ONGOING_MATCH';
export const SELECT_ONGOING_MATCH = 'SELECT_ONGOING_MATCH';

export const GO_TO_LATEST_ONGOING_MATCH = 'GO_TO_LATEST_ONGOING_MATCH';
export const RESET_SELECTED_DATE_MATCH_LOG = 'RESET_SELECTED_DATE_MATCH_LOG';
export const CLOSE_INVALID_ACTION = 'CLOSE_INVALID_ACTION';

export const UPDATE_MATCH_SOCKET_MESSAGE = 'UPDATE_MATCH_SOCKET_MESSAGE';
export const UPDATE_MATCH_SOCKET_REVERSE_MESSAGE =
  'UPDATE_MATCH_SOCKET_REVERSE_MESSAGE';

export const CLEAR_MATCH_SOCKET_MESSAGES = 'CLEAR_MATCH_SOCKET_MESSAGES';
export const FETCH_INITIAL_PLAYERS_REQUEST = 'FETCH_INITIAL_PLAYERS_REQUEST';
export const FETCH_INITIAL_PLAYERS_SUCCESS = 'FETCH_INITIAL_PLAYERS_SUCCESS';
export const FETCH_INITIAL_PLAYERS_FAIL = 'FETCH_INITIAL_PLAYERS_FAIL';

export const FETCH_INITIAL_FORMATIONS_REQUEST =
  'FETCH_INITIAL_FORMATIONS_REQUEST';
export const FETCH_INITIAL_FORMATIONS_SUCCESS =
  'FETCH_INITIAL_FORMATIONS_SUCCESS';
export const FETCH_INITIAL_FORMATIONS_FAILED =
  'FETCH_INITIAL_FORMATIONS_FAILED';

export const SET_CURRENT_MATCH_PLAN = 'SET_CURRENT_MATCH_PLAN';
export const SET_MATCH_FORMATION_COORDINATES =
  'SET_MATCH_FORMATION_COORDINATES';

export const SET_MATCH_CONCLUDE = 'SET_MATCH_CONCLUDE';
