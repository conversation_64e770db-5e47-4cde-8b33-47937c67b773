export const FETCH_FILE_URL_REQUEST = 'FETCH_FILE_URL_REQUEST';
export const FETCH_FILE_URL_SUCCESS = 'FETCH_FILE_URL_SUCCESS';
export const FETCH_FILE_URL_FAIL = 'FETCH_FILE_URL_FAIL';
export const INITIAL_DATA_LIST_SETUP = 'INITIAL_DATA_LIST_SETUP';
export const LIST_DATA_FAILED = 'LIST_DATA_FAILED';
export const LIST_DATA_REQUEST = 'LIST_DATA_REQUEST';
export const LIST_DATA_SUCCESS = 'LIST_DATA_SUCCESS';
export const LIST_DATA_RESET = 'LIST_DATA_RESET';

export const LIST_DATA_FAILED_CUSTOM_CONTENT =
  'LIST_DATA_FAILED_CUSTOM_CONTENT';
export const LIST_DATA_REQUEST_CUSTOM_CONTENT =
  'LIST_DATA_REQUEST_CUSTOM_CONTENT';
export const LIST_DATA_SUCCESS_CUSTOM_CONTENT =
  'LIST_DATA_SUCCESS_CUSTOM_CONTENT';

export const HEADER_LOGO_CLICKED = 'HEADER_LOGO_CLICKED';

export const INITIATE_WS = 'INITIATE_WS';
export const GET_CHILD_DATA_REQUEST = 'GET_CHILD_DATA_REQUEST';
export const GET_CHILD_DATA_SUCCESS = 'GET_CHILD_DATA_SUCCESS';
export const GET_CHILD_DATA_FAIL = 'GET_CHILD_DATA_FAIL';

export const FETCH_IMAGE_URL_REQUEST = 'FETCH_IMAGE_URL_REQUEST';
export const FETCH_IMAGE_URL_SUCCESS = 'FETCH_IMAGE_URL_SUCCESS';
export const FETCH_IMAGE_URL_SUCCESS_CUSTOM_CONTENT =
  'FETCH_IMAGE_URL_SUCCESS_CUSTOM_CONTENT';
export const FETCH_IMAGE_URL_FAIL = 'FETCH_IMAGE_URL_FAIL';

export const FETCH_HEADER_PARENT_INFO_REQUEST =
  'FETCH_HEADER_PARENT_INFO_REQUEST';
export const FETCH_HEADER_PARENT_INFO_SUCCESS =
  'FETCH_HEADER_PARENT_INFO_SUCCESS';
export const FETCH_HEADER_PARENT_INFO_FAILED =
  'FETCH_HEADER_PARENT_INFO_FAILED';

export const GET_CHILD_INFORMATION_REQUEST = 'GET_CHILD_INFORMATION_REQUEST';
export const GET_CHILD_INFORMATION_SUCCESS = 'GET_CHILD_INFORMATION_SUCCESS';
export const GET_CHILD_INFORMATION_FAILED = 'GET_CHILD_INFORMATION_FAILED';
export const USER_INFORMATION_REQUEST = 'USER_INFORMATION_REQUEST';
export const USER_INFORMATION_SUCCESS = 'USER_INFORMATION_SUCCESS';
export const USER_INFORMATION_FAILED = 'USER_INFORMATION_FAILED';
export const SET_SELECTED_CHILD = 'SET_SELECTED_CHILD';
export const IS_USER_IN_IAP_CHART = 'IS_USER_IN_IAP_CHART';

export const GET_CHILD_TEAMS_INFORMATION_REQUEST =
  'GET_CHILD_TEAMS_INFORMATION_REQUEST';
export const GET_CHILD_TEAMS_INFORMATION_SUCCESS =
  'GET_CHILD_TEAMS_INFORMATION_SUCCESS';
export const GET_CHILD_TEAMS_INFORMATION_FAILED =
  'GET_CHILD_TEAMS_INFORMATION_FAILED';

export const FETCH_FILE_URL_REQUEST_CUSTOM_CONTENT =
  'FETCH_FILE_URL_REQUEST_CUSTOM_CONTENT';
export const FETCH_FILE_URL_SUCCESS_CUSTOM_CONTENT =
  'FETCH_FILE_URL_SUCCESS_CUSTOM_CONTENT';
export const FETCH_FILE_URL_FAIL_CUSTOM_CONTENT =
  'FETCH_FILE_URL_FAIL_CUSTOM_CONTENT';
export const FILE_UPLOAD_S3_INFO_FAILED = 'FILE_UPLOAD_S3_INFO_FAILED';
export const FILE_UPLOAD_S3_INFO_REQUEST = 'FILE_UPLOAD_S3_INFO_REQUEST';
export const FILE_UPLOAD_S3_INFO_SUCCESS = 'FILE_UPLOAD_S3_INFO_SUCCESS';
export const TRY_WS_RE_CONNECT = 'TRY_WS_RE_CONNECT';

export const GET_UPDATED_CHILD_ID_REQUEST = 'GET_UPDATED_CHILD_ID_REQUEST';
export const GET_UPDATED_CHILD_ID_SUCCESS = 'GET_UPDATED_CHILD_ID_SUCCESS';
export const GET_UPDATED_CHILD_ID_FAIL = 'GET_UPDATED_CHILD_ID_FAIL';

export const WS_IS_CONNECT = 'WS_IS_CONNECT';

export const CREATE_NEW_MESSAGE_CLICK = 'CREATE_NEW_MESSAGE_CLICK';

export const FETCH_S3_BUCKET_LOCATION_REQUEST =
  'FETCH_S3_BUCKET_LOCATION_REQUEST';
export const FETCH_S3_BUCKET_LOCATION_SUCCESS =
  'FETCH_S3_BUCKET_LOCATION_SUCCESS';
export const FETCH_S3_BUCKET_LOCATION_FAIL = 'FETCH_S3_BUCKET_LOCATION_FAIL';

export const SET_REFRESH_COUNT = 'SET_REFRESH_COUNT';

export const SET_CURRENT_ROUTE = 'SET_CURRENT_ROUTE';

export const SET_PLANNER_MODAL_STATE = 'SET_PLANNER_MODAL_STATE';

export const SET_ON_SYNC_START = 'SET_ON_SYNC_START';
export const SET_CONNECTION_ID_MATCH_WS = 'SET_CONNECTION_ID_MATCH_WS';
export const IS_SCREEN_ACTIVE = 'IS_SCREEN_ACTIVE';

export const IS_USER_IN_MATCHES = 'IS_USER_IN_MATCHES';

export const IS_USER_IN_MATCH_LOG = 'IS_USER_IN_MATCH_LOG';

export const GET_CLUB_SETTINGS_REQUEST = 'GET_CLUB_SETTINGS_REQUEST';
export const GET_CLUB_SETTINGS_SUCCESS = 'GET_CLUB_SETTINGS_SUCCESS';
export const GET_CLUB_SETTINGS_FAIL = 'GET_CLUB_SETTINGS_FAIL';
