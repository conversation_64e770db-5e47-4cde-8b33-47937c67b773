export const PLAYER_FAIL = 'PLAYER_FAIL';
export const PLAYER_REQUEST = 'PLAYER_REQUEST';
export const PLAYER_SUCCESS = 'PLAYER_SUCCESS';
export const PLAYER_SUCCESS_CUSTOM_CONTENT = 'PLAYER_SUCCESS_CUSTOM_CONTENT';
export const SET_CURRENT_FILTERING_TEAM = 'SET_CURRENT_FILTERING_TEAM';
export const RESET_PLAYER = 'RESET_PLAYER';

export const PLAYER_INITIAL_UPDATE_FAIL = 'PLAYER_INITIAL_UPDATE_FAIL';
export const PLAYER_INITIAL_UPDATE_REQUEST = 'PLAYER_INITIAL_UPDATE_REQUEST';
export const PLAYER_INITIAL_UPDATE_SUCCESS = 'PLAYER_INITIAL_UPDATE_SUCCESS';

export const PLAYER_MORE_UPDATE_FAIL = 'PLAYER_MORE_UPDATE_FAIL';
export const PLAYER_MORE_UPDATE_REQUEST = 'PLAYER_MORE_UPDATE_REQUEST';
export const PLAYER_MORE_UPDATE_SUCCESS = 'PLAYER_MORE_UPDATE_SUCCESS';

export const PLAYER_EDIT_UPDATE_FAIL = 'PLAYER_EDIT_UPDATE_FAIL';
export const PLAYER_EDIT_UPDATE_REQUEST = 'PLAYER_EDIT_UPDATE_REQUEST';
export const PLAYER_EDIT_UPDATE_SUCCESS = 'PLAYER_EDIT_UPDATE_SUCCESS';
export const PLAYER_EDIT_UPDATE_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_EDIT_UPDATE_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_ADD_UPDATE_FAIL = 'PLAYER_ADD_UPDATE_FAIL';
export const PLAYER_ADD_UPDATE_REQUEST = 'PLAYER_ADD_UPDATE_REQUEST';
export const PLAYER_ADD_UPDATE_SUCCESS = 'PLAYER_ADD_UPDATE_SUCCESS';

export const PLAYER_DELETE_UPDATE_FAIL = 'PLAYER_DELETE_UPDATE_FAIL';
export const PLAYER_DELETE_UPDATE_REQUEST = 'PLAYER_DELETE_UPDATE_REQUEST';
export const PLAYER_DELETE_UPDATE_SUCCESS = 'PLAYER_DELETE_UPDATE_SUCCESS';
export const PLAYER_DELETE_UPDATE_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_DELETE_UPDATE_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_INITIAL_MEDICAL_HISTORY_FAIL =
  'PLAYER_INITIAL_MEDICAL_HISTORY_FAIL';
export const PLAYER_INITIAL_MEDICAL_HISTORY_REQUEST =
  'PLAYER_INITIAL_MEDICAL_HISTORY_REQUEST';
export const PLAYER_INITIAL_MEDICAL_HISTORY_SUCCESS =
  'PLAYER_INITIAL_MEDICAL_HISTORY_SUCCESS';

export const PLAYER_MORE_MEDICAL_HISTORY_FAIL =
  'PLAYER_MORE_MEDICAL_HISTORY_FAIL';
export const PLAYER_MORE_MEDICAL_HISTORY_REQUEST =
  'PLAYER_MORE_MEDICAL_HISTORY_REQUEST';
export const PLAYER_MORE_MEDICAL_HISTORY_SUCCESS =
  'PLAYER_MORE_MEDICAL_HISTORY_SUCCESS';

// FETCH ALL PLAYERS IN SELECTED TEAM AND ADD
export const PLAYER_FETCH_REQUEST = 'PLAYER_FETCH_REQUEST';
export const PLAYER_FETCH_SUCCESS = 'PLAYER_FETCH_SUCCESS';
export const PLAYER_FETCH_FAIL = 'PLAYER_FETCH_FAIL';
export const RESET_PLAYER_FETCH = 'RESET_PLAYER_FETCH';

export const PLAYER_FETCH_MORE_REQUEST = 'PLAYER_FETCH_MORE_REQUEST';
export const PLAYER_FETCH_MORE_SUCCESS = 'PLAYER_FETCH_MORE_SUCCESS';
export const PLAYER_FETCH_MORE_FAIL = 'PLAYER_FETCH_MORE_FAIL';

export const PLAYER_ADD_REQUEST = 'PLAYER_ADD_REQUEST';
export const PLAYER_ADD_SUCCESS = 'PLAYER_ADD_SUCCESS';
export const PLAYER_ADD_FAIL = 'PLAYER_ADD_FAIL';

export const PLAYER_ADD_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_ADD_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_EDIT_MEDICAL_HISTORY_FAIL =
  'PLAYER_EDIT_MEDICAL_HISTORY_FAIL';
export const PLAYER_EDIT_MEDICAL_HISTORY_REQUEST =
  'PLAYER_EDIT_MEDICAL_HISTORY_REQUEST';
export const PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS =
  'PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS';
export const PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_ADD_MEDICAL_HISTORY_FAIL =
  'PLAYER_ADD_MEDICAL_HISTORY_FAIL';
export const PLAYER_ADD_MEDICAL_HISTORY_REQUEST =
  'PLAYER_ADD_MEDICAL_HISTORY_REQUEST';
export const PLAYER_ADD_MEDICAL_HISTORY_SUCCESS =
  'PLAYER_ADD_MEDICAL_HISTORY_SUCCESS';

export const PLAYER_DELETE_MEDICAL_HISTORY_FAIL =
  'PLAYER_DELETE_MEDICAL_HISTORY_FAIL';
export const PLAYER_DELETE_MEDICAL_HISTORY_REQUEST =
  'PLAYER_DELETE_MEDICAL_HISTORY_REQUEST';
export const PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS =
  'PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS';
export const PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_INITIAL_MEDICAL_INJURIES_FAIL =
  'PLAYER_INITIAL_MEDICAL_INJURIES_FAIL';
export const PLAYER_INITIAL_MEDICAL_INJURIES_REQUEST =
  'PLAYER_INITIAL_MEDICAL_INJURIES_REQUEST';
export const PLAYER_INITIAL_MEDICAL_INJURIES_SUCCESS =
  'PLAYER_INITIAL_MEDICAL_INJURIES_SUCCESS';

export const PLAYER_MORE_MEDICAL_INJURIES_FAIL =
  'PLAYER_MORE_MEDICAL_INJURIES_FAIL';
export const PLAYER_MORE_MEDICAL_INJURIES_REQUEST =
  'PLAYER_MORE_MEDICAL_INJURIES_REQUEST';
export const PLAYER_MORE_MEDICAL_INJURIES_SUCCESS =
  'PLAYER_MORE_MEDICAL_INJURIES_SUCCESS';

export const PLAYER_EDIT_MEDICAL_INJURIES_FAIL =
  'PLAYER_EDIT_MEDICAL_INJURIES_FAIL';
export const PLAYER_EDIT_MEDICAL_INJURIES_REQUEST =
  'PLAYER_EDIT_MEDICAL_INJURIES_REQUEST';
export const PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS =
  'PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS';
export const PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_ADD_MEDICAL_INJURIES_FAIL =
  'PLAYER_ADD_MEDICAL_INJURIES_FAIL';
export const PLAYER_ADD_MEDICAL_INJURIES_REQUEST =
  'PLAYER_ADD_MEDICAL_INJURIES_REQUEST';
export const PLAYER_ADD_MEDICAL_INJURIES_SUCCESS =
  'PLAYER_ADD_MEDICAL_INJURIES_SUCCESS';

export const PLAYER_DELETE_MEDICAL_INJURIES_FAIL =
  'PLAYER_DELETE_MEDICAL_INJURIES_FAIL';
export const PLAYER_DELETE_MEDICAL_INJURIES_REQUEST =
  'PLAYER_DELETE_MEDICAL_INJURIES_REQUEST';
export const PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS =
  'PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS';
export const PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT =
  'PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT';

export const PLAYER_INFO_REQUEST = 'PLAYER_INFO_REQUEST';
export const PLAYER_INFO_SUCCESS = 'PLAYER_INFO_SUCCESS';
export const PLAYER_INFO_FAIL = 'PLAYER_INFO_FAIL';

export const PLAYER_INFO_RESET = 'PLAYER_INFO_RESET';

export const PLAYER_UPCOMING_EVENTS_REQUEST = 'PLAYER_UPCOMING_EVENTS_REQUEST';
export const PLAYER_UPCOMING_EVENTS_SUCCESS = 'PLAYER_UPCOMING_EVENTS_SUCCESS';
export const PLAYER_UPCOMING_EVENTS_FAILED = 'PLAYER_UPCOMING_EVENTS_FAILED';

export const PLAYER_UPCOMING_EVENTS_UPDATE_REQUEST =
  'PLAYER_UPCOMING_EVENTS_UPDATE_REQUEST';
export const PLAYER_UPCOMING_EVENTS_UPDATE_SUCCESS =
  'PLAYER_UPCOMING_EVENTS_UPDATE_SUCCESS';
export const PLAYER_UPCOMING_EVENTS_UPDATE_FAILED =
  'PLAYER_UPCOMING_EVENTS_UPDATE_FAILED';

export const PLAYER_PAST_EVENTS_REQUEST = 'PLAYER_PAST_EVENTS_REQUEST';
export const PLAYER_PAST_EVENTS_SUCCESS = 'PLAYER_PAST_EVENTS_SUCCESS';
export const PLAYER_PAST_EVENTS_FAILED = 'PLAYER_PAST_EVENTS_FAILED';

export const PLAYER_PAST_EVENTS_UPDATE_REQUEST =
  'PLAYER_PAST_EVENTS_UPDATE_REQUEST';
export const PLAYER_PAST_EVENTS_UPDATE_SUCCESS =
  'PLAYER_PAST_EVENTS_UPDATE_SUCCESS';
export const PLAYER_PAST_EVENTS_UPDATE_FAILED =
  'PLAYER_PAST_EVENTS_UPDATE_FAILED';

export const PLAYER_PAST_EVENTS_ATTENDANCE_REQUEST =
  'PLAYER_PAST_EVENTS_ATTENDANCE_REQUEST';
export const PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS =
  'PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS';
export const PLAYER_PAST_EVENTS_ATTENDANCE_FAILED =
  'PLAYER_PAST_EVENTS_ATTENDANCE_FAILED';

export const PLAYER_INFO_UPDATE_REQUEST = 'PLAYER_INFO_UPDATE_REQUEST';
export const PLAYER_INFO_UPDATE_SUCCESS = 'PLAYER_INFO_UPDATE_SUCCESS';
export const PLAYER_INFO__UPDATE_FAIL = 'PLAYER_INFO__UPDATE_FAIL';

export const PLAYER_POSITION_REQUEST = 'PLAYER_POSITION_REQUEST';
export const PLAYER_POSITION_SUCCESS = 'PLAYER_POSITION_SUCCESS';
export const PLAYER_POSITION_FAIL = 'PLAYER_POSITION_FAIL';
export const PLAYER_POSITION_RESET = 'PLAYER_POSITION_RESET';

export const PLAYER_EVENTS_TEAM_DATA_REQUEST =
  'PLAYER_EVENTS_TEAM_DATA_REQUEST';
export const PLAYER_EVENTS_TEAM_DATA_SUCCESS =
  'PLAYER_EVENTS_TEAM_DATA_SUCCESS';
export const PLAYER_EVENTS_TEAM_DATA_FAILED = 'PLAYER_EVENTS_TEAM_DATA_FAILED';

export const PLAYER_STATUS_REQUEST = 'PLAYER_STATUS_REQUEST';
export const PLAYER_STATUS_SUCCESS = 'PLAYER_STATUS_SUCCESS';
export const PLAYER_STATUS_FAILED = 'PLAYER_STATUS_FAILED';

export const PLAYER_TEAM_LABEL_REQUEST = 'PLAYER_TEAM_LABEL_REQUEST';
export const PLAYER_TEAM_LABEL_SUCCESS = 'PLAYER_TEAM_LABEL_SUCCESS';
export const PLAYER_TEAM_LABEL_FAILED = 'PLAYER_TEAM_LABEL_FAILED';

export const PLAYER_DOCUMENT_REQUEST = 'PLAYER_DOCUMENT_REQUEST';
export const PLAYER_DOCUMENT_SUCCESS = 'PLAYER_DOCUMENT_SUCCESS';
export const PLAYER_DOCUMENT_FAIL = 'PLAYER_DOCUMENT_FAIL';
export const PLAYER_DOCUMENT_RESET = 'PLAYER_DOCUMENT_RESET';

export const PLAYER_MORE_DOCUMENT_REQUEST = 'PLAYER_MORE_DOCUMENT_REQUEST';
export const PLAYER_MORE_DOCUMENT_SUCCESS = 'PLAYER_MORE_DOCUMENT_SUCCESS';
export const PLAYER_MORE_DOCUMENT_FAIL = 'PLAYER_MORE_DOCUMENT_FAIL';

export const PLAYER_INFO_DOCUMENT_DELETE_REQUEST =
  'PLAYER_INFO_DOCUMENT_DELETE_REQUEST';
export const PLAYER_INFO_DOCUMENT_DELETE_SUCCESS =
  'PLAYER_INFO_DOCUMENT_DELETE_SUCCESS';
export const PLAYER_INFO_DOCUMENT_DELETE_FAIL =
  'PLAYER_INFO_DOCUMENT_DELETE_FAIL';

export const PLAYER_INFO_SHOW_UPLOAD_MODAL = 'PLAYER_INFO_SHOW_UPLOAD_MODAL';
export const PLAYER_INFO_EDIT_UPLOAD_MODAL = 'PLAYER_INFO_EDIT_UPLOAD_MODAL';
export const PLAYER_DOCUMENT_INITIAL_SUCCESS ='PLAYER_DOCUMENT_INITIAL_SUCCESS'

