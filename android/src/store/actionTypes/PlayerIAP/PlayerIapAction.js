export const PLAYER_IAP_CATEGORY_SET = 'PLAYER_IAP_CATEGORY_SET';

export const PLAYER_IAP_CATEGORY_FAILED = 'PLAYER_IAP_CATEGORY_FAILED';
export const PLAYER_IAP_CATEGORY_REQUEST = 'PLAYER_IAP_CATEGORY_REQUEST';
export const PLAYER_IAP_CATEGORY_SUCCESS = 'PLAYER_IAP_CATEGORY_SUCCESS';

export const PLAYER_IAP_COMMENTS_FAILED = 'PLAYER_IAP_COMMENTS_FAILED';
export const PLAYER_IAP_COMMENTS_REQUEST = 'PLAYER_IAP_COMMENTS_REQUEST';
export const PLAYER_IAP_COMMENTS_SUCCESS = 'PLAYER_IAP_COMMENTS_SUCCESS';

export const PLAYER_IAP_COMMENTS_UPDATE_FAILED =
  'PLAYER_IAP_COMMENTS_UPDATE_FAILED';
export const PLAYER_IAP_COMMENTS_UPDATE_REQUEST =
  'PLAYER_IAP_COMMENTS_UPDATE_REQUEST';
export const PLAYER_IAP_COMMENTS_UPDATE_SUCCESS =
  'PLAYER_IAP_COMMENTS_UPDATE_SUCCESS';

export const PLAYER_IAP_COMMENT_SAVE_FAILED = 'PLAYER_IAP_COMMENTS_SAVE_FAILED';
export const PLAYER_IAP_COMMENT_SAVE_REQUEST =
  'PLAYER_IAP_COMMENTS_SAVE_REQUEST';
export const PLAYER_IAP_COMMENT_SAVE_SUCCESS =
  'PLAYER_IAP_COMMENTS_SAVE_SUCCESS';

export const PLAYER_IAP_CRITERIA_FAILED = 'PLAYER_IAP_CRITERIA_FAILED';
export const PLAYER_IAP_CRITERIA_REQUEST = 'PLAYER_IAP_CRITERIA_REQUEST';
export const PLAYER_IAP_CRITERIA_SUCCESS = 'PLAYER_IAP_CRITERIA_SUCCESS';

export const PLAYER_IAP_STAT_FAILED = 'PLAYER_IAP_STAT_FAILED';
export const PLAYER_IAP_STAT_REQUEST = 'PLAYER_IAP_STAT_REQUEST';
export const PLAYER_IAP_STAT_SUCCESS = 'PLAYER_IAP_STAT_SUCCESS';

export const PLAYER_IAP_STAT_UPDATE_FAILED = 'PLAYER_IAP_STAT_UPDATE_FAILED';
export const PLAYER_IAP_STAT_UPDATE_REQUEST = 'PLAYER_IAP_STAT_UPDATE_REQUEST';
export const PLAYER_IAP_STAT_UPDATE_SUCCESS = 'PLAYER_IAP_STAT_UPDATE_SUCCESS';

export const PLAYER_IAP_STAT_SAVE_FAILED = 'PLAYER_IAP_STAT_SAVE_FAILED';
export const PLAYER_IAP_STAT_SAVE_REQUEST = 'PLAYER_IAP_STAT_SAVE_REQUEST';
export const PLAYER_IAP_STAT_SAVE_SUCCESS = 'PLAYER_IAP_STAT_SAVE_SUCCESS';

export const PLAYER_IAP_STAT_GRAPH_DATA_REQUEST =
  'PLAYER_IAP_STAT_GRAPH_DATA_REQUEST';
export const PLAYER_IAP_STAT_GRAPH_DATA_SUCCESS =
  'PLAYER_IAP_STAT_GRAPH_DATA_SUCCESS';
export const PLAYER_IAP_STAT_GRAPH_DATA_FAILED =
  'PLAYER_IAP_STAT_GRAPH_DATA_FAILED';

export const IAP_STAT_GRAPH_DATE_RANGE_CHANGED =
  'IAP_STAT_GRAPH_DATE_RANGE_CHANGED';
export const IAP_STAT_SET_GRAPH_VIEW = 'IAP_STAT_SET_GRAPH_VIEW';
export const IAP_STAT_SET_GRAPH_TITLE = 'IAP_STAT_SET_GRAPH_TITLE';

export const PLAYER_IAP_SET_SHOW_STATS_GRAPH =
  'PLAYER_IAP_SET_SHOW_STATS_GRAPH';
export const PLAYER_STATS_CATEGORY_SET = 'PLAYER_STATS_CATEGORY_SET';

export const PLAYER_IAP_REPORT_FAILED = 'PLAYER_IAP_REPORT_FAILED';
export const PLAYER_IAP_REPORT_REQUEST = 'PLAYER_IAP_REPORT_REQUEST';
export const PLAYER_IAP_REPORT_SUCCESS = 'PLAYER_IAP_REPORT_SUCCESS';
