import { dateTimeConversion } from '../../../helpers';
import {
  EVENT_OPPONENT_DETAILS_REQUEST,
  EVENT_OPPONENT_DETAILS_SUCCESS,
  EVENT_RSVP_ALL_INVITEE_FAILED,
  EVENT_RSVP_ALL_INVITEE_REQUEST,
  EVENT_RSVP_ALL_INVITEE_SUCCESS,
  EVENT_RSVP_ALL_SAVE_FAILED,
  EVENT_RSVP_ALL_SAVE_REQUEST,
  EVENT_RSVP_ALL_SAVE_SUCCESS,
  EVENT_RSVP_FETCH_FAILED,
  EVENT_RSVP_FETCH_REQUEST,
  EVENT_RSVP_FETCH_SUCCESS,
  EVENT_RSVP_LIST_INITIAL_REQUEST,
  EVENT_RSVP_LIST_INITIAL_SUCCESS,
  EVENT_RSVP_LIST_INITIAL_FAILED,
  EVENT_RSVP_LIST_MORE_SUCCESS,
  EVENT_RSVP_SAVE_FAILED,
  EVENT_RSVP_SAVE_REQUEST,
  EVENT_RSVP_SAVE_SUCCESS,
  EVENT_SEASON_DETAILS_REQUEST,
  EVENT_SEASON_DETAILS_SUCCESS,
  EVENT_TOURNAMENT_DETAILS_REQUEST,
  EVENT_TOURNAMENT_DETAILS_SUCCESS,
  FETCH_ALL_EVENTS_FAIL,
  FETCH_ALL_EVENTS_REQUEST,
  FETCH_ALL_EVENTS_SUCCESS,
  RESET_PLANNER,
  SET_DEVICE_CALENDER_EVENT_LIST,
  SET_DEVICE_CALENDER_LIST,
  SET_SELECT_EVENT,
  WEEK_DATA_SUCCESS,
  EVENT_RSVP_LIST_TOTAL_RECORDS_SUCCESS,
  EVENT_RSVP_LIST_TOTAL_RECORDS_REQUEST,
  EVENT_RSVP_LIST_TOTAL_RECORDS_FAILED,
  SET_NOTIFCATION_EVENT_ID,
  EVENT_RSVP_LIST_MORE_FAILED,
} from '../../actionTypes/Planner/PlannerAction';

export interface PlannerReducerType {
  calendar: any[];
  calendarLoading: boolean;
  deviceCalendarList: any[];
  opponentDetails: any;
  seasonDetails: any;
  tournamentDetails: any;
  rsvpSaveLoading: boolean;
  rsvpSaveError: boolean;
  rsvpSaveSuccess: any;
  rsvpDataLoading: boolean;
  rsvpData: any;
  rsvpList: any[];
  rsvpListCurrentPage: number;
  rsvpListLoading: boolean;
  selectedDateEvents: any;
  createEventLoading: boolean;
  createEventSuccess: boolean;
  createEventFail: boolean;
  createTournamentLoading: boolean;
  createTournamentResult: boolean;
  createTournamentFail: boolean;
  RsvpSaveAllLoading: boolean;
  RsvpSaveAllError: boolean;
  RsvpSaveAllSuccess: any;
  RsvpInviteeFetchLoading: boolean;
  RsvpInviteeFetchData_Error: any;
  RsvpInviteeFetchData: any[];
  WeekType: any;
  WeekSchedule: any;
  deviceCalendarEventList: any;
  rsvpTotalRecords: any;
  notificationEventId?: string;
  calendarMap: any;
}

const initialState: PlannerReducerType = {
  calendar: [],
  calendarLoading: false,
  deviceCalendarList: [],
  opponentDetails: null,
  seasonDetails: null,
  tournamentDetails: null,
  rsvpSaveLoading: false,
  rsvpSaveError: false,
  rsvpSaveSuccess: null,
  rsvpDataLoading: false,
  rsvpData: null,
  rsvpList: [],
  rsvpListCurrentPage: 1,
  rsvpListLoading: false,
  selectedDateEvents: {},
  createEventLoading: false,
  createEventSuccess: false,
  createEventFail: false,
  createTournamentLoading: false,
  createTournamentResult: false,
  createTournamentFail: false,
  RsvpSaveAllLoading: false,
  RsvpSaveAllError: false,
  RsvpSaveAllSuccess: null,
  RsvpInviteeFetchLoading: false,
  RsvpInviteeFetchData_Error: false,
  RsvpInviteeFetchData: [],
  WeekType: '',
  WeekSchedule: '',
  deviceCalendarEventList: null,
  rsvpTotalRecords: null,
  calendarMap : {}
};

const PlannerReducer = (
  state = initialState,
  action: any
): PlannerReducerType => {
  const { data, page, size, totalRecords } = action?.payload || {};
  const customPayloadInput = action?.payload?.customInput;

  switch (action.type) {
    case EVENT_RSVP_LIST_TOTAL_RECORDS_REQUEST:
    case EVENT_RSVP_LIST_TOTAL_RECORDS_FAILED:
      return {
        ...state,
        rsvpTotalRecords: null,
      };
    case EVENT_RSVP_LIST_TOTAL_RECORDS_SUCCESS:
      return {
        ...state,
        rsvpTotalRecords: action.payload || null,
      };
    case FETCH_ALL_EVENTS_REQUEST:
      return {
        ...state,
        calendarLoading: true,
      };

    case FETCH_ALL_EVENTS_SUCCESS:
      return {
        ...state,
        calendar: data || [],
        calendarMap : createEventDateMap(data),
        calendarLoading: false,
      };

    case FETCH_ALL_EVENTS_FAIL:
      return {
        ...state,
        calendarLoading: false,
      };
    case EVENT_SEASON_DETAILS_REQUEST:
      return {
        ...state,
        seasonDetails: null,
      };
    case EVENT_SEASON_DETAILS_SUCCESS:
      return {
        ...state,
        seasonDetails: action.payload?.data?.[0],
      };
    case EVENT_OPPONENT_DETAILS_REQUEST:
      return {
        ...state,
        opponentDetails: null,
      };
    case EVENT_OPPONENT_DETAILS_SUCCESS:
      return {
        ...state,
        opponentDetails: action.payload?.data?.[0],
      };
    case EVENT_TOURNAMENT_DETAILS_REQUEST:
      return {
        ...state,
        tournamentDetails: null,
      };
    case EVENT_TOURNAMENT_DETAILS_SUCCESS:
      return {
        ...state,
        tournamentDetails: action.payload?.data?.[0],
      };

    case EVENT_RSVP_SAVE_REQUEST:
      return {
        ...state,
        rsvpSaveLoading: true,
        rsvpSaveError: false,
        rsvpSaveSuccess: null,
      };
    case EVENT_RSVP_SAVE_SUCCESS:
      return {
        ...state,
        rsvpSaveLoading: false,
        rsvpSaveError: false,
        rsvpSaveSuccess: true,
      };
    case EVENT_RSVP_SAVE_FAILED:
      return {
        ...state,
        rsvpSaveLoading: false,
        rsvpSaveError: true,
        rsvpSaveSuccess: false,
      };

    //All invites
    case EVENT_RSVP_ALL_INVITEE_REQUEST:
      return {
        ...state,
        RsvpInviteeFetchLoading: true,
        RsvpInviteeFetchData_Error: null,
        RsvpInviteeFetchData: [],
      };
    case EVENT_RSVP_ALL_INVITEE_SUCCESS:
      return {
        ...state,
        RsvpInviteeFetchLoading: false,
        RsvpInviteeFetchData: action.payload?.data,
      };
    case EVENT_RSVP_ALL_INVITEE_FAILED:
      return {
        ...state,
        RsvpInviteeFetchLoading: false,
        RsvpInviteeFetchData_Error: action.payload,
      };

    //All events
    case EVENT_RSVP_ALL_SAVE_REQUEST:
      return {
        ...state,
        RsvpSaveAllLoading: true,
        RsvpSaveAllError: false,
        RsvpSaveAllSuccess: null,
      };
    case EVENT_RSVP_ALL_SAVE_SUCCESS:
      return {
        ...state,
        RsvpSaveAllLoading: false,
        RsvpSaveAllError: false,
        RsvpSaveAllSuccess: true,
      };
    case EVENT_RSVP_ALL_SAVE_FAILED:
      return {
        ...state,
        RsvpSaveAllLoading: false,
        RsvpSaveAllError: true,
        RsvpSaveAllSuccess: false,
      };

    case WEEK_DATA_SUCCESS:
      return {
        ...state,
        WeekType: action.WeekType,
        WeekSchedule: action.WeekSchedule,
      };

    case EVENT_RSVP_FETCH_REQUEST:
      return {
        ...state,
        rsvpData: null,
        rsvpDataLoading: true,
      };
    case EVENT_RSVP_FETCH_SUCCESS:
      return {
        ...state,
        rsvpData: action.payload?.data?.[0],
        rsvpDataLoading: false,
      };
    case EVENT_RSVP_FETCH_FAILED:
      return {
        ...state,
        rsvpDataLoading: false,
      };

    case EVENT_RSVP_LIST_INITIAL_REQUEST:
      return {
        ...state,
        rsvpList: [],
        rsvpListLoading: true,
      };
    case EVENT_RSVP_LIST_INITIAL_SUCCESS:
      return {
        ...state,
        rsvpList: action.payload?.data,
        rsvpListCurrentPage: action.payload?.page,
        rsvpListLoading: false,
      };
    case EVENT_RSVP_LIST_INITIAL_FAILED:
    case EVENT_RSVP_LIST_MORE_FAILED:
      return {
        ...state,
        rsvpListLoading: false,
      };
    case EVENT_RSVP_LIST_MORE_SUCCESS:
      return {
        ...state,
        rsvpList: [...state.rsvpList, ...action.payload.data],
        rsvpListCurrentPage: action.payload?.page,
        rsvpListLoading: false,
      };

    case SET_SELECT_EVENT:
      return {
        ...state,
        selectedDateEvents: action.payload,
      };
    case SET_DEVICE_CALENDER_LIST:
      return {
        ...state,
        deviceCalendarList: action.payload,
      };
    case SET_DEVICE_CALENDER_EVENT_LIST:
      return {
        ...state,
        deviceCalendarEventList: action.payload,
      };
    case SET_NOTIFCATION_EVENT_ID:
      return {
        ...state,
        notificationEventId: action.payload,
      };
    case RESET_PLANNER:
      return initialState;
    default:
      return state;
  }
};

const createEventDateMap = (events: any[]) => {
  // Initialize result object
  const dateMap : any = {};

  events?.forEach(event => {
    
      // Get date string in YYYY-MM-DD format
      const date = dateTimeConversion(event?.startTime);
      const dateKey = date?.yearMonthDateString;

      // Initialize array for date if it doesn't exist
      if (!dateMap[dateKey]) {
          dateMap[dateKey] = [];
      }

      // Add event to the array for that date
      dateMap[dateKey].push(event);
  });

  return dateMap;
}

export default PlannerReducer;
