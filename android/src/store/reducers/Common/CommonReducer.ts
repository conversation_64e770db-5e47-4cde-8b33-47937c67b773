import { w3cwebsocket } from 'websocket';
import {
  CREATE_NEW_MESSAGE_CLICK,
  FETCH_FILE_URL_FAIL_CUSTOM_CONTENT,
  FETCH_FILE_URL_REQUEST_CUSTOM_CONTENT,
  FETCH_FILE_URL_SUCCESS_CUSTOM_CONTENT,
  FETCH_HEADER_PARENT_INFO_FAILED,
  FETCH_HEADER_PARENT_INFO_REQUEST,
  FETCH_HEADER_PARENT_INFO_SUCCESS,
  FETCH_IMAGE_URL_FAIL,
  FETCH_IMAGE_URL_REQUEST,
  FETCH_IMAGE_URL_SUCCESS_CUSTOM_CONTENT,
  FETCH_S3_BUCKET_LOCATION_SUCCESS,
  FILE_UPLOAD_S3_INFO_SUCCESS,
  GET_CHILD_DATA_FAIL,
  GET_CHILD_DATA_REQUEST,
  GET_CHILD_DATA_SUCCESS,
  GET_CHILD_INFORMATION_FAILED,
  GET_CHILD_INFORMATION_REQUEST,
  GET_CHILD_INFORMATION_SUCCESS,
  GET_CHILD_TEAMS_INFORMATION_FAILED,
  GET_CHILD_TEAMS_INFORMATION_REQUEST,
  GET_CHILD_TEAMS_INFORMATION_SUCCESS,
  GET_CLUB_SETTINGS_FAIL,
  GET_CLUB_SETTINGS_REQUEST,
  GET_CLUB_SETTINGS_SUCCESS,
  GET_UPDATED_CHILD_ID_FAIL,
  GET_UPDATED_CHILD_ID_REQUEST,
  GET_UPDATED_CHILD_ID_SUCCESS,
  HEADER_LOGO_CLICKED,
  INITIAL_DATA_LIST_SETUP,
  INITIATE_WS,
  IS_SCREEN_ACTIVE,
  IS_USER_IN_IAP_CHART,
  IS_USER_IN_MATCHES,
  IS_USER_IN_MATCH_LOG,
  LIST_DATA_FAILED_CUSTOM_CONTENT,
  LIST_DATA_REQUEST_CUSTOM_CONTENT,
  LIST_DATA_RESET,
  LIST_DATA_SUCCESS_CUSTOM_CONTENT,
  SET_CONNECTION_ID_MATCH_WS,
  SET_CURRENT_ROUTE,
  SET_ON_SYNC_START,
  SET_PLANNER_MODAL_STATE,
  SET_REFRESH_COUNT,
  SET_SELECTED_CHILD,
  TRY_WS_RE_CONNECT,
  USER_INFORMATION_SUCCESS,
  WS_IS_CONNECT,
} from '../../actionTypes/common/commonActionTypes';

export interface s3FileObjectType {
  bucketName: string;
  fileKey: string;
}
interface apiRequestListSingleItem {
  index: number;
  data?: any;
  request?: boolean;
  success?: boolean;
  failed?: boolean;
}

interface apiRequestListType {
  [id: string]: apiRequestListSingleItem[];
}
interface wsClientType {
  [type: string]: w3cwebsocket | null;
}
interface isWsReconnectType {
  [type: string]: boolean;
}
interface isWsConnectionLoadingType {
  [type: string]: boolean;
}
interface isWsIsConnectType {
  [type: string]: boolean;
}
interface refreshCountType {
  [type: string]: number;
}

interface CommonReducerType {
  isDownloading: { [id: string]: boolean };
  downloadableFileUrl: any;
  wsClient: wsClientType;
  apiRequestList: apiRequestListType | null;
  parentDetails: any[];
  parentDetailsLoading: boolean;
  imageUrl: any;
  parentImageLoading: boolean;
  children: any[];
  childrenLoading: boolean;
  childInformation: any;
  userInformation: any;
  selectedChild: any;
  isUserInIAPChart: boolean;
  childTeamsInformation: any[];
  loadChildTeamsInformation: boolean;
  s3BucketInfo: {
    [name: string]: s3FileObjectType;
  } | null;
  isWsReconnect: isWsReconnectType;
  wsIsConnect: isWsIsConnectType;
  isCreateNewMessage: boolean;
  updatedChildrenIds: string[] | null;
  updatedChildrenIdsLoading: boolean;
  isChildrenDataUpdated: boolean;
  s3BucketLocation: { bucketName: string; filePath: string } | null;
  refreshCount: refreshCountType;
  refreshCountLimit: Number;
  wsConnectionLoading: isWsConnectionLoadingType;
  currentRoute: string;
  isPlannerModalOpened: boolean;
  isOnSync: boolean;
  connectionIdMatchWs: string | null;
  isAppOnActive: boolean;
  headerClicked: boolean;
  isUserInMatches: boolean;
  isUserInMatchLog: boolean;
  clubSettings: any;
  clubSettingsLoading: boolean;
}

const initialState: CommonReducerType = {
  downloadableFileUrl: {},
  isDownloading: {},
  wsClient: {},
  apiRequestList: null,
  parentDetails: [],
  parentDetailsLoading: false,
  imageUrl: {},
  parentImageLoading: false,
  children: [],
  childrenLoading: false,
  childInformation: null,
  userInformation: null,
  selectedChild: null,
  isUserInIAPChart: false,
  childTeamsInformation: [],
  loadChildTeamsInformation: false,
  s3BucketInfo: null,
  isWsReconnect: {},
  wsIsConnect: {},
  isCreateNewMessage: false,
  updatedChildrenIds: null,
  updatedChildrenIdsLoading: false,
  isChildrenDataUpdated: false,
  s3BucketLocation: null,
  refreshCount: {},
  refreshCountLimit: 3,
  wsConnectionLoading: {},
  currentRoute: '',
  isPlannerModalOpened: false,
  isOnSync: false,
  connectionIdMatchWs: null,
  isAppOnActive: true,
  headerClicked: false,
  isUserInMatches: false,
  isUserInMatchLog: false,
  clubSettings: null,
  clubSettingsLoading: false,
};

const CommonReducer = (
  state: CommonReducerType = initialState,
  action: any
): CommonReducerType => {
  const payload = action?.payload || {};
  const { data, page, size, totalRecords, dataWrapper, customInput } =
    payload || {};
  const customPayloadInput =
    action?.payload?.customInput || action?.customInput;

  const { id } = customInput || {};

  switch (action.type) {
    case IS_SCREEN_ACTIVE:
      return {
        ...state,
        isAppOnActive: action.payload,
      };
    case INITIAL_DATA_LIST_SETUP:
      return {
        ...state,
        apiRequestList: validateUniqueKey(
          state?.apiRequestList || {},
          dataWrapper?.data
        ),
      };
    case SET_REFRESH_COUNT:
      return {
        ...state,
        refreshCount: action?.payload
          ? { ...state.refreshCount, [action.customInput]: action?.payload }
          : { ...state.refreshCount, [action.customInput]: 0 },
      };

    case SET_SELECTED_CHILD: {
      return {
        ...state,
        selectedChild: action?.payload,
      };
    }
    case GET_CHILD_INFORMATION_REQUEST: {
      return { ...state };
    }
    case GET_CHILD_INFORMATION_SUCCESS: {
      return {
        ...state,
        childInformation: data[0] || null,
      };
    }
    case GET_CHILD_INFORMATION_FAILED: {
      return { ...state };
    }
    case FETCH_HEADER_PARENT_INFO_REQUEST: {
      return {
        ...state,
        parentDetailsLoading: true,
      };
    }
    case HEADER_LOGO_CLICKED:
      return {
        ...state,
        headerClicked: action.payload,
      };
    case FETCH_HEADER_PARENT_INFO_SUCCESS: {
      return {
        ...state,
        parentDetails: data || [],
        parentDetailsLoading: false,
      };
    }
    case FETCH_HEADER_PARENT_INFO_FAILED: {
      return {
        ...state,
        parentDetailsLoading: false,
      };
    }

    case LIST_DATA_RESET:
      return {
        ...state,
        apiRequestList: {
          ...state.apiRequestList,
          [action?.payload?.dataWrapper?.data?.uniqueID]: null,
        },
      };

    case LIST_DATA_REQUEST_CUSTOM_CONTENT:
      return {
        ...state,
        apiRequestList: updateApiRequestStatus(
          state.apiRequestList || {},
          true,
          false,
          false,
          customPayloadInput,
          {}
        ),
      };
    case LIST_DATA_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        apiRequestList: updateApiRequestStatus(
          state.apiRequestList || {},
          true,
          true,
          false,
          customPayloadInput,
          action?.payload
        ),
      };
    case LIST_DATA_FAILED_CUSTOM_CONTENT:
      return {
        ...state,
        apiRequestList: updateApiRequestStatus(
          state.apiRequestList || {},
          true,
          false,
          true,
          customPayloadInput,
          {}
        ),
      };

    case INITIATE_WS:
      return {
        ...state,

        wsClient: action?.payload
          ? { ...state.wsClient, [action.customInput]: action?.payload }
          : { ...state.wsClient, [action.customInput]: null },
      };
    case WS_IS_CONNECT:
      return {
        ...state,
        wsIsConnect: action?.payload
          ? {
              ...state.wsIsConnect,
              [action.customInput]: action?.payload?.data,
            }
          : { ...state.wsIsConnect, [action.customInput]: null },
      };
    case FETCH_IMAGE_URL_REQUEST:
      return {
        ...state,
        parentImageLoading: true,
      };
    case FETCH_IMAGE_URL_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        imageUrl: {
          ...state.imageUrl,
          [id]: data?.preSignedUrl,
        },

        parentImageLoading: false,
      };

    case FETCH_IMAGE_URL_FAIL:
      return { ...state, parentImageLoading: false };
    case GET_CHILD_DATA_REQUEST:
      return { ...state, childrenLoading: false };
    case GET_CHILD_DATA_SUCCESS:
      return {
        ...state,
        children: data || [],
        childrenLoading: false,
        selectedChild: data?.length ? data?.[0] : state?.selectedChild,
      };
    case GET_CHILD_DATA_FAIL:
      return { ...state, childrenLoading: false };

    case USER_INFORMATION_SUCCESS:
      return {
        ...state,
        userInformation: data?.[0] || null,
      };
    case IS_USER_IN_IAP_CHART:
      return {
        ...state,
        isUserInIAPChart: action?.payload,
      };

    case GET_CHILD_TEAMS_INFORMATION_REQUEST:
      return {
        ...state,
        loadChildTeamsInformation: false,
      };

    case GET_CHILD_TEAMS_INFORMATION_SUCCESS:
      return {
        ...state,
        childTeamsInformation: data || [],
        loadChildTeamsInformation: true,
      };

    case GET_CHILD_TEAMS_INFORMATION_FAILED:
      return {
        ...state,
        loadChildTeamsInformation: true,
      };

    case FETCH_FILE_URL_REQUEST_CUSTOM_CONTENT:
      return {
        ...state,
        isDownloading: { [customInput.uniqueID]: true },
      };
    case FETCH_FILE_URL_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        downloadableFileUrl: {
          ...state.downloadableFileUrl,
          [customInput.uniqueID]: action?.payload?.data,
        },
        isDownloading: {
          ...state.isDownloading,
          [customInput.uniqueID]: false,
        },
      };

    case FILE_UPLOAD_S3_INFO_SUCCESS:
      return {
        ...state,
        s3BucketInfo: setS3BucketInfo(state.s3BucketInfo, customInput, payload),
      };

    case FETCH_FILE_URL_FAIL_CUSTOM_CONTENT:
      return {
        ...state,
        isDownloading: { [customInput.uniqueID]: false },
      };
    case TRY_WS_RE_CONNECT:
      return {
        ...state,
        isWsReconnect: action?.payload
          ? { ...state.isWsReconnect, [action.customInput]: data }
          : { ...state.isWsReconnect, [action.customInput]: null },
        wsConnectionLoading: action?.payload
          ? { ...state.wsConnectionLoading, [action.customInput]: data }
          : { ...state.wsConnectionLoading, [action.customInput]: null },
      };

    case CREATE_NEW_MESSAGE_CLICK:
      return {
        ...state,
        isCreateNewMessage: data,
      };
    case GET_UPDATED_CHILD_ID_REQUEST:
      return {
        ...state,
        updatedChildrenIdsLoading: true,
      };
    case GET_UPDATED_CHILD_ID_SUCCESS:
      return {
        ...state,
        updatedChildrenIds: data?.[0]?.childrenIds,
        updatedChildrenIdsLoading: false,
        isChildrenDataUpdated: arr_diff(
          state?.updatedChildrenIds || [],
          data?.[0]?.childrenIds
        ).length
          ? true
          : false,
      };
    case GET_UPDATED_CHILD_ID_FAIL:
      return {
        ...state,
        updatedChildrenIdsLoading: false,
      };

    case FETCH_S3_BUCKET_LOCATION_SUCCESS:
      return {
        ...state,
        s3BucketLocation: payload,
      };

    case SET_CURRENT_ROUTE:
      return {
        ...state,
        currentRoute: data.currentRoute,
      };
    case SET_PLANNER_MODAL_STATE:
      return {
        ...state,
        isPlannerModalOpened: data.isPlannerModalOpened,
      };
    case SET_ON_SYNC_START:
      return {
        ...state,
        isOnSync: true,
      };
    case SET_CONNECTION_ID_MATCH_WS:
      return {
        ...state,
        connectionIdMatchWs: data,
      };

    case IS_USER_IN_MATCHES:
      return {
        ...state,
        isUserInMatches: action?.payload,
      };
    case IS_USER_IN_MATCH_LOG:
      return {
        ...state,
        isUserInMatchLog: action?.payload,
      };

    case GET_CLUB_SETTINGS_REQUEST:
      return {
        ...state,
        clubSettingsLoading: true,
      };
    case GET_CLUB_SETTINGS_SUCCESS:
      return {
        ...state,
        clubSettings: action?.payload,
        clubSettingsLoading: false,
      };
    case GET_CLUB_SETTINGS_FAIL:
      return {
        ...state,
        clubSettingsLoading: false,
      };

    default:
      return state;
  }
};

const arr_diff = (oldChildArray: any[], newChildArray: any[]) => {
  let a = [],
    diff = [];

  for (let i = 0; i < oldChildArray.length; i++) {
    a[oldChildArray[i]] = true;
  }

  for (let i = 0; i < newChildArray.length; i++) {
    if (a[newChildArray[i]]) {
      delete a[newChildArray[i]];
    } else {
      a[newChildArray[i]] = true;
    }
  }

  for (let k in a) {
    diff.push(k);
  }

  return diff;
};

const validateUniqueKey = (
  list: apiRequestListType,
  { uniqueID, length }: any
) => {
  list = list || {};

  if (!list[uniqueID]) {
    list[uniqueID] = [];

    let index = 0;
    while (index <= length - 1) {
      list[uniqueID].push({
        index,
        request: false,
        success: false,
        failed: false,
      });
      index++;
    }
  }

  return list;
};

const updateApiRequestStatus = (
  list: apiRequestListType,
  request: boolean,
  success: boolean,
  failed: boolean,
  { uniqueID, index }: { uniqueID: string; index: number },
  data: any
) => {
  list = list || {};

  const apiRequestList = list[uniqueID];
  apiRequestList[index] = { index, request, success, failed, ...data };
  list[uniqueID] = apiRequestList;

  return list;
};

const setS3BucketInfo = (
  state: any,
  customInput: { key: string },
  payload: any
) => {
  state = state || {};
  state[customInput?.key] = payload;
  return state;
};

export const setSelectedChild = (selectedChild : any) => ({
  type: SET_SELECTED_CHILD,
  payload: selectedChild
});

export default CommonReducer;
