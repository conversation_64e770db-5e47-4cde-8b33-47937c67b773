import {
  TERM_AND_CONDITION_DATA_FETCH_SUCCESS,
  TERM_AND_CONDITION_DATA_FETCH_REQUEST,
  TERM_AND_CONDITION_DATA_FETCH_FAIL,
  POST_TERM_AND_CONDITION_DATA_SUCCESS,
  POST_TERM_AND_CONDITION_DATA_REQUEST,
  POST_TERM_AND_CONDITION_DATA_FAIL,
  CHECK_USER_TYPE,
  SET_TERM_AND_CONDITION,
} from '../../actionTypes/TermAndCondition/termAndCondtionAction';
import AsyncStorage from '@react-native-async-storage/async-storage';

const initialState = {
  termAndConditionData: undefined,
  isTermAndConditionDataLoading: false,
  termAndConditionData_error: null,
  submitTermAndConditionLoading: false,
  submitTermAndConditionError: null,
  submitTermAndConditionSuccess: false,
  userType: null,
  setTermAndCondition: false,
  isTermAndConditionPosting: false,
};

const TermAndConditionReducer = (state = initialState, action) => {
  switch (action.type) {
    case TERM_AND_CONDITION_DATA_FETCH_REQUEST:
      return {
        ...state,
        isTermAndConditionDataLoading: true,
        termAndConditionData: undefined,
        termAndConditionData_error: null,
      };
    case TERM_AND_CONDITION_DATA_FETCH_SUCCESS:
      const newValue = JSON.stringify(action.payload);
      AsyncStorage.setItem(
        'TermAndConditionData',
        action.payload ? newValue : JSON.stringify({ userType: 'ExistingUser' })
      );

      return {
        ...state,
        isTermAndConditionDataLoading: false,
        termAndConditionData: action.payload,
      };
    case TERM_AND_CONDITION_DATA_FETCH_FAIL:
      return {
        ...state,
        isTermAndConditionDataLoading: false,
        termAndConditionData_error: action.payload,
      };
    case POST_TERM_AND_CONDITION_DATA_REQUEST:
      return {
        ...state,
        submitTermAndConditionLoading: true,
        submitTermAndConditionSuccess: false,
        isTermAndConditionPosting: true,
      };
    case POST_TERM_AND_CONDITION_DATA_SUCCESS:
      return {
        ...state,
        submitTermAndConditionLoading: false,
        submitTermAndConditionSuccess: true,
        isTermAndConditionPosting: false,
      };
    case POST_TERM_AND_CONDITION_DATA_FAIL: {
      return {
        ...state,
        submitTermAndConditionLoading: false,
        submitTermAndConditionError: action.payload,
        isTermAndConditionPosting: false,
      };
    }
    case CHECK_USER_TYPE: {
      return {
        ...state,
        userType: action.payload,
      };
    }
    case SET_TERM_AND_CONDITION: {
      return {
        ...state,
        setTermAndCondition: action.payload,
      };
    }
    default:
      return state;
  }
};

export default TermAndConditionReducer;
