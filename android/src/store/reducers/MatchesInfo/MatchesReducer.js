import {
  INITIAL_MATCHES_SUCCESS,
  INITIAL_MATCHES_REQUEST,
  INITIAL_MATCHES_FAIL,
  MORE_MATCHES_SUCCESS,
  MATCH_STATUS_SUCCESS,
  MATCHES_RESET,
  MATCH_STATUS_SUCCESS_CUSTOM_CONTENT,
} from '../../actionTypes/MatchesInfo/MatchesAction';

import { playerConstant } from '../../../constants/constants';

// const {} = playerConstant;

const initialState = {
  matches: [],
  isMatchesLoading: false,
  matchesPage: 1,
  matchesPageSize: 3,
  matchesTotalRecords: 3,
  matchesInitialPage: 1,
  matchesInitialPageSize: 3,
};

const MatchesReducer = (state = initialState, action) => {
  const { data, page, size, totalRecords } = action?.payload || {};

  switch (action.type) {
    case INITIAL_MATCHES_REQUEST:
      return {
        ...state,
        isMatchesLoading: true,
      };

    case INITIAL_MATCHES_FAIL:
      return {
        ...state,
        isMatchesLoading: false,
      };

    case INITIAL_MATCHES_SUCCESS:
      return {
        ...state,
        matches: data || [],
        matchesPage: page || state.matchesPageSize,
        matchesPageSize: size || state.matchesPageSize,
        matchesTotalRecords: totalRecords || state.matchesTotalRecords,
        isMatchesLoading: false,
      };

    case MORE_MATCHES_SUCCESS:
      return {
        ...state,
        matches: data ? [...state.matches, ...data] : state.matches,
        matchesPage: page || state.matchesPageSize,
        matchesPageSize: size || state.matchesPageSize,
        matchesTotalRecords: totalRecords || state.matchesTotalRecords,
      };

    case MATCH_STATUS_SUCCESS:
      return {
        ...state,
        matches: [...updateData(state.matches, action?.payload)],
      };

    case MATCH_STATUS_SUCCESS_CUSTOM_CONTENT: {
      return {
        ...state,
        matches: [...updateData(state.matches, action?.payload)],
      };
    }

    case MATCHES_RESET:
      return initialState;

    default:
      return state;
  }
};

/**
 *update content
 */
const updateData = (state, payload) => {
  if (payload?.length) {
    state.forEach((element, index) => {
      const updateIndex = getArrayIndexStatus(payload, element._id);

      const payloadEvent = payload[updateIndex];

      if (updateIndex >= 0) {
        state[index].isReady = payloadEvent.isReady ? 'yes' : 'no';
      } else {
        state[index].isReady = `no`;
      }
    });
    return state;
  } else {
    return state.map(item => {
      item.isReady = `no`;

      return item;
    });
  }
};

/**
 *Get array index from object key value
 */
const getArrayIndexStatus = (state, id) => {
  return state.findIndex(element => element.eventId === id);
};

export default MatchesReducer;
