import { FETCH_ALL_USERS_FAIL } from '../../actionTypes/ManageUsers/ManageUsersAction';
import { avoidedDuplicationData } from '../../../helpers';
import {
  UPLOAD_IMAGE_REQUEST,
  UPLOAD_IMAGE_SUCCESS,
  UPLOAD_IMAGE_FAIL,
  UPLOAD_IMAGE_RESET,
} from '../../actionTypes/UploadImage/uploadImageAction';
import {
  ADD_USER_REQUEST,
  ADD_USER_SUCCESS,
  ADD_USER_FAIL,
  GET_USER_BY_EMAIL_REQUEST,
  GET_USER_BY_EMAIL_SUCCESS,
  GET_USER_BY_EMAIL_FAIL,
  GET_KOACH_USERS_BY_EMAIL_REQUEST,
  GET_KOACH_USERS_BY_EMAIL_SUCCESS,
  GET_KOACH_USERS_BY_EMAIL_FAIL,
  GET_KOACH_USERS_BY_EMAIL_SUCCESS_CUSTOM_CONTENT,
  CHANGE_USER_SEARCH_PARAMS_TYPE,
  GET_ALL_USERS_FAIL,
  GET_ALL_USERS_REQUEST,
  GET_ALL_USERS_SUCCESS,
  GET_ALL_USERS_RESET,
  GET_ALL_SELECTED_CHILDREN_SUCCESS,
  GET_ALL_SELECTED_CHILDREN_RESET,
  GET_PASSWORD_RESET_REQUEST,
  GET_PASSWORD_RESET_SUCCESS,
  GET_PASSWORD_RESET_FAIL,
  RESET_PASSWORD_INITIAL,
  GET_CERTIFICATIONS_REQUEST,
  GET_CERTIFICATIONS_SUCCESS,
  GET_CERTIFICATIONS_FAIL,
  GET_CERTIFICATIONS_RESET,
  POST_CERTIFICATE_REQUEST,
  POST_CERTIFICATE_SUCCESS,
  POST_CERTIFICATE_FAIL,
  GET_CITIES_FAIL,
  GET_CITIES_REQUEST,
  GET_CITIES_SUCCESS,
  GET_CITIES_RESET,
} from '../../actionTypes/User/User';

const initailState = {
  uploadPofileImageData: '',
  uploadPofileImageData_error: null,
  uploadPofileImageLoading: false,
  createUserLoading: false,
  creatUserSuccess: false,
  createUserFail: false,
  userExists: false,
  koachUsersFetching: false,
  koachUsersData: null,
  koachUsersFetchSuccess: false,
  koachUsersError: false,
  searchParams: null,
  childrenLoading: false,
  childernError: null,
  children: [],
  childrenTotalRecords: 0,
  childrenPage: 1,
  selectedChildren: [],
  passwordResetLoading: false,
  passwordResetSuccess: false,
  isPasswordResetError: false,
  cerficatesLoading:false,
  certificates:[],
  cerficatesError:null,
  createCertificateLoading:false,
  createCertificationSuccess:false,
  createCertificationFail:false,
  cities:[],
  citiesRequestLoading:false,
  citiesRequestSuccess: false,
};

const AddUserReducer = (state = initailState, action) => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  switch (action.type) {
    case ADD_USER_REQUEST:
      return {
        ...state,
        createUserLoading: true,
        creatUserSuccess: false,
        createUserFail: false,
        userExists: false,
      };
    case ADD_USER_SUCCESS:
      return {
        ...state,
        createUserLoading: false,
        creatUserSuccess: true,
        createUserFail: false,
      };
    case ADD_USER_FAIL:
      return {
        ...state,
        createUserLoading: false,
        creatUserSuccess: false,
        createUserFail: action?.payload,
      };

    case UPLOAD_IMAGE_REQUEST:
      return {
        ...state,
        uploadPofileImageData: '',
        uploadPofileImageData_error: null,
        uploadPofileImageLoading: true,
      };
    case UPLOAD_IMAGE_SUCCESS:
      return {
        ...state,
        uploadPofileImageData: action.payload,
        uploadPofileImageLoading: false,
      };
    case UPLOAD_IMAGE_FAIL:
      return {
        ...state,
        uploadPofileImageData_error: action.payload,
        uploadPofileImageLoading: false,
      };
    case UPLOAD_IMAGE_RESET:
      return {
        ...state,
        uploadPofileImageData: '',
        uploadPofileImageLoading: false,
      };
    case GET_USER_BY_EMAIL_REQUEST:
      return {
        ...state,
        userExists: false,
      };
    case GET_USER_BY_EMAIL_SUCCESS:
      return {
        ...state,
        userExists: !!action?.payload?.data?.length,
      };
    case GET_USER_BY_EMAIL_FAIL:
      return {
        ...state,
        userExists: false,
      };
    case CHANGE_USER_SEARCH_PARAMS_TYPE:
      return {
        ...state,
        koachUsersData: null,
        searchParams: action.payload,
      };
    case RESET_PASSWORD_INITIAL:
      return {
        ...state,
        passwordResetLoading: false,
        passwordResetSuccess: false,
        isPasswordResetError: false,
      };
    case GET_PASSWORD_RESET_REQUEST:
      return {
        ...state,
        passwordResetLoading: true,
        passwordResetSuccess: false,
        isPasswordResetError: false,
      };
    case GET_PASSWORD_RESET_SUCCESS:
      return {
        ...state,
        passwordResetLoading: false,
        passwordResetSuccess: true,
        isPasswordResetError: false,
      };
    case GET_PASSWORD_RESET_FAIL:
      return {
        ...state,
        passwordResetLoading: false,
        passwordResetSuccess: false,
        isPasswordResetError: true,
      };

    case GET_KOACH_USERS_BY_EMAIL_REQUEST:
      return {
        ...state,
        koachUsersFetching: true,
        koachUsersError: false,
        koachUsersFetchSuccess: false,
        userExists: false,
        createUserFail: false,
      };

    case GET_KOACH_USERS_BY_EMAIL_SUCCESS_CUSTOM_CONTENT:
      const isMatched =
        JSON.stringify(action.payload.customInput?.searchParams) ===
        JSON.stringify(state.searchParams);

      return {
        ...state,
        koachUsersFetching: false,
        koachUsersFetchSuccess: true,
        koachUsersData:
          isMatched && state.koachUsersData && action.payload?.data
            ? {
                ...state.koachUsersData,
                ...action.payload?.data,
                data: [
                  ...state.koachUsersData.data,
                  ...action.payload?.data?.data,
                ],
              }
            : isMatched
            ? action.payload?.data
            : null,
      };

    case GET_KOACH_USERS_BY_EMAIL_FAIL:
      return {
        ...state,
        koachUsersFetching: false,
        koachUsersData: null,
        koachUsersError: true,
        koachUsersFetchSuccess: false,
      };
    case GET_ALL_USERS_REQUEST:
      return {
        ...state,
        childrenLoading: true,
        childernError: null,
      };
    case GET_ALL_USERS_SUCCESS:
      return {
        ...state,
        children:
          state?.children?.length && page > 1
            ? avoidedDuplicationData(state?.children, data || null, 'id')
            : data || null,
        childrenTotalRecords: totalRecords || state?.childrenTotalRecords,
        childrenPage: page || state?.childrenPage,
        childrenLoading: false,
        childernError: null,
      };
    case GET_ALL_USERS_FAIL:
      return {
        ...state,
        childrenLoading: true,
        childernError: true,
      };
    case GET_ALL_USERS_RESET:
      return {
        ...state,
        childrenLoading: false,
        childernError: false,
        children: [],
      };
    case GET_ALL_SELECTED_CHILDREN_SUCCESS:
      return {
        ...state,
        selectedChildren: action.payload?.data,
      };
    case GET_ALL_SELECTED_CHILDREN_RESET:
      return {
        ...state,
        selectedChildren: [],
      };
    case GET_CERTIFICATIONS_REQUEST:{
      return{
        ...state,
        cerficatesLoading:true
      }
    };
    case GET_CERTIFICATIONS_SUCCESS:{
      return{
        ...state,
        certificates:action.payload?.data,
        cerficatesLoading:false
      }
    };
    case GET_CERTIFICATIONS_FAIL:{
      return{
        ...state,
        cerficatesError:action.payload,
        cerficatesLoading:false

      }
    };
    case GET_CERTIFICATIONS_RESET: {
      return {
        ...state,
        certificates: []
      }
    };
    case POST_CERTIFICATE_REQUEST:{
      return{
        ...state,
        createCertificateLoading:true
      }
    };
    case POST_CERTIFICATE_SUCCESS:{
      return{
        ...state,
        createCertificationSuccess:true,
        createCertificateLoading:false
      }
    };
    case POST_CERTIFICATE_FAIL:{
      return{
        ...state,
        createCertificationFail:action.payload,
        createCertificateLoading:false
      }
    }
    case GET_CITIES_SUCCESS:
      return {
        ...state,
        citiesRequestSuccess: true,
        cities: action.payload,
      };
    case GET_CITIES_FAIL:
      return {
        ...state,
        citiesRequestSuccess: false,
        cities: [],
      };
    case GET_CITIES_RESET:
      return {
        ...state,
        citiesRequestSuccess: false,
        citiesRequestLoading: false,
        cities: [],
      };
    
    default:
      return state;
  }
};

export default AddUserReducer;
