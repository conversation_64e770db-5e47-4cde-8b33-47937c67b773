import { playerConstant } from '../../../constants/constants';
import { avoidedDuplicationData } from '../../../helpers';
import {
  PLAYER_ADD_FAIL,
  PLAYER_ADD_MEDICAL_HISTORY_FAIL,
  PLAYER_ADD_MEDICAL_HISTORY_REQUEST,
  PLAYER_ADD_MEDICAL_HISTORY_SUCCESS,
  PLAYER_ADD_MEDICAL_INJURIES_FAIL,
  PLAYER_ADD_MEDICAL_INJURIES_REQUEST,
  PLAYER_ADD_MEDICAL_INJURIES_SUCCESS,
  PLAYER_ADD_REQUEST,
  PLAYER_ADD_SUCCESS,
  PLAYER_ADD_SUCCESS_CUSTOM_CONTENT,
  PLAYER_ADD_UPDATE_SUCCESS,
  PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT,
  PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT,
  PLAY<PERSON>_DELETE_UPDATE_SUCCESS_CUSTOM_CONTENT,
  PLAYER_DOCUMENT_REQUEST,
  PLAYER_DOCUMENT_SUCCESS,
  PLAYER_EDIT_MEDICAL_HISTORY_FAIL,
  PLAYER_EDIT_MEDICAL_HISTORY_REQUEST,
  PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT,
  PLAYER_EDIT_MEDICAL_INJURIES_FAIL,
  PLAYER_EDIT_MEDICAL_INJURIES_REQUEST,
  PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS,
  PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT,
  PLAYER_EDIT_UPDATE_SUCCESS_CUSTOM_CONTENT,
  PLAYER_EVENTS_TEAM_DATA_FAILED,
  PLAYER_EVENTS_TEAM_DATA_REQUEST,
  PLAYER_EVENTS_TEAM_DATA_SUCCESS,
  PLAYER_FAIL,
  PLAYER_FETCH_FAIL,
  PLAYER_FETCH_MORE_SUCCESS,
  PLAYER_FETCH_REQUEST,
  PLAYER_FETCH_SUCCESS,
  PLAYER_INFO_UPDATE_REQUEST,
  PLAYER_INFO_UPDATE_SUCCESS,
  PLAYER_INFO__UPDATE_FAIL,
  PLAYER_INITIAL_MEDICAL_HISTORY_FAIL,
  PLAYER_INITIAL_MEDICAL_HISTORY_REQUEST,
  PLAYER_INITIAL_MEDICAL_HISTORY_SUCCESS,
  PLAYER_INITIAL_MEDICAL_INJURIES_FAIL,
  PLAYER_INITIAL_MEDICAL_INJURIES_REQUEST,
  PLAYER_INITIAL_MEDICAL_INJURIES_SUCCESS,
  PLAYER_INITIAL_UPDATE_REQUEST,
  PLAYER_INITIAL_UPDATE_SUCCESS,
  PLAYER_MORE_MEDICAL_HISTORY_SUCCESS,
  PLAYER_MORE_MEDICAL_INJURIES_SUCCESS,
  PLAYER_MORE_UPDATE_SUCCESS,
  PLAYER_PAST_EVENTS_ATTENDANCE_FAILED,
  PLAYER_PAST_EVENTS_ATTENDANCE_REQUEST,
  PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS,
  PLAYER_PAST_EVENTS_FAILED,
  PLAYER_PAST_EVENTS_REQUEST,
  PLAYER_PAST_EVENTS_SUCCESS,
  PLAYER_PAST_EVENTS_UPDATE_REQUEST,
  PLAYER_PAST_EVENTS_UPDATE_SUCCESS,
  PLAYER_POSITION_FAIL,
  PLAYER_POSITION_REQUEST,
  PLAYER_POSITION_RESET,
  PLAYER_POSITION_SUCCESS,
  PLAYER_REQUEST,
  PLAYER_STATUS_SUCCESS,
  PLAYER_SUCCESS_CUSTOM_CONTENT,
  PLAYER_TEAM_LABEL_FAILED,
  PLAYER_TEAM_LABEL_REQUEST,
  PLAYER_TEAM_LABEL_SUCCESS,
  PLAYER_UPCOMING_EVENTS_FAILED,
  PLAYER_UPCOMING_EVENTS_REQUEST,
  PLAYER_UPCOMING_EVENTS_SUCCESS,
  PLAYER_UPCOMING_EVENTS_UPDATE_SUCCESS,
  RESET_PLAYER,
  RESET_PLAYER_FETCH,
  SET_CURRENT_FILTERING_TEAM,
  PLAYER_INFO_DOCUMENT_DELETE_REQUEST,
  PLAYER_INFO_DOCUMENT_DELETE_SUCCESS,
  PLAYER_INFO_DOCUMENT_DELETE_FAIL,
  PLAYER_INFO_SHOW_UPLOAD_MODAL,
  PLAYER_INFO_EDIT_UPLOAD_MODAL,
  PLAYER_DOCUMENT_INITIAL_SUCCESS,
} from '../../actionTypes/player/playerAction';
import {
  REPORT_DELETE,
  REPORT_RESET,
  UPLOAD_REPORT_NEW,
  UPLOAD_REPORT_SUCCESS,
  UPLOAD_REPORT_UPDATE,
} from '../../actionTypes/UploadReport/uploadreportAction';

const {
  playerMedicalHistoryPageInitial,
  playerMedicalHistoryPageSizeInitial,
  playerUpdatePageInitial,
  playerUpdatePageSizeInitial,
  playerInjuriesPageInitial,
  playerInjuriesPageSizeInitial,
  playerUpcomingEventsPageSize,
  playerPastEventsPageSize,
  playerUpcomingEventsInitialPage,
  playerPastEventsInitialPage,
} = playerConstant;

const initialState = {
  playerPositionData: '',
  playerPositionData_error: null,
  playerInfoUpdateData: {},
  playerInfoUpdateData_error: null,
  playerInfoUpdateLoading: false,
  playerData: null,
  playerDataTotalRecord: 0,
  searchedPlayerTotalRecord: 0,
  playerDataPage: 1,
  playerData_error: null,
  playerAddData: '',
  playerAddDataLoading: false,
  playerAddTeamData: {},
  playerAddData_error: null,
  playerAddTeamData_error: null,
  playerInjuries: [],
  playerInjuriesLoading: false,
  playerInjuriesPage: null,
  playerInjuriesPageInitial,
  playerInjuriesPageSize: null,
  playerInjuriesPageSizeInitial,
  playerInjuriesTotalRecords: null,
  playerMedicalHistory: [],
  playerMedicalHistoryLoading: false,
  playerMedicalHistoryPage: null,
  playerMedicalHistoryPageInitial,
  playerMedicalHistoryPageSize: null,
  playerMedicalHistoryPageSizeInitial,
  playerMedicalHistoryTotalRecords: null,
  playerPastEvents: [],
  playerPastEventsCurrentPage: playerPastEventsInitialPage,
  playerPastEventsError: false,
  playerPastEventsLoading: false,
  playerPastEventsPageSize: playerPastEventsPageSize,
  playerUpcomingEvents: [],
  playerUpcomingEventsCurrentPage: playerUpcomingEventsInitialPage,
  playerUpcomingEventsError: false,
  playerUpcomingEventsLoading: false,
  playerUpcomingEventsPageSize: playerUpcomingEventsPageSize,
  playerUpdate: [],
  playerUpdateLoading: false,
  playerUpdatePage: null,
  playerUpdatePageInitial,
  playerUpdatePageSize: null,
  playerUpdatePageSizeInitial,
  playerUpdateTotalRecords: null,
  playerAttendanceLoading: false,
  playerAttendance: [],
  playerAttendanceFailed: false,
  playerEventTeams: [],
  playerEventTeamsLoading: false,
  playerEventTeamsFailed: false,
  playerDataLoading: false,
  playerUpdateSuccess: null,
  filterByTeamId: null,
  teamLabelsLoading: false,
  teamLabels: null,
  teamLabelsError: null,
  playerPastEventTotalRecords: 0,
  playerUploadedReportLoading: false,
  playerUploadedReport: [],
  playerInjuriesTriggerLoading: true,
  playerMedicalHistoryTriggerLoading: true,
  documentList: [],
  documentListLoading: false,
  documentListPage: 1,
  documentListPageSize: 10,
  documentListTotalRecords: null,
  deleteDocumentLoading: false,
  deleteDocumentSuccess: false,
  deleteDocumentFailed: false,
  isUploadBtnClicked: false,
  isUploadDocumentEditMode: false,
  isPlayerAdded: false,
};

const PlayerReducer = (state = initialState, action) => {
  const { data, page, size, totalRecords } = action?.payload || {};

  switch (action.type) {
    case PLAYER_INFO_SHOW_UPLOAD_MODAL:
      return { ...state, isUploadBtnClicked: action?.payload };
    case PLAYER_INFO_EDIT_UPLOAD_MODAL:
      return { ...state, isUploadDocumentEditMode: action?.payload };
    case PLAYER_DOCUMENT_REQUEST:
      return { ...state, documentListLoading: false };
    case PLAYER_DOCUMENT_INITIAL_SUCCESS:
      const tempDocu = action?.payload ? [...data] : []
      return {
        ...state,
        documentListLoading: false,
        documentList: [...tempDocu],
        documentListPage: page || 1,
        documentListPageSize: size || state.documentListPageSize,
        documentListTotalRecords:
          totalRecords || state.documentListTotalRecords,
      };
    case PLAYER_DOCUMENT_SUCCESS:
      const temp = page === 1 ? [...data] : [...state.documentList, ...data];
      return {
        ...state,
        documentListLoading: false,
        documentList: [...temp],
        documentListPage: page || 1,
        documentListPageSize: size || state.documentListPageSize,
        documentListTotalRecords:
          totalRecords || state.documentListTotalRecords,
      };

    case SET_CURRENT_FILTERING_TEAM:
      return {
        ...state,
        filterByTeamId: action?.payload,
      };
    case PLAYER_REQUEST:
      return {
        ...state,
        playerData_error: null,
        playerDataLoading: true,
      };
    case PLAYER_SUCCESS_CUSTOM_CONTENT:
      const isMatched =
        action?.payload?.customInput?.filterByTeamId === state.filterByTeamId;
      const pageNo = action?.payload?.customInput?.pageNo;
      const hasSearch = action?.payload?.customInput?.hasSearch;

      let playerData = [];
      let playerDataTotalRecord = 0;
      let searchedPlayerTotalRecord = 0;

      if (hasSearch) {
        searchedPlayerTotalRecord = data?.totalRecords || 0;
        playerDataTotalRecord = state.playerDataTotalRecord;
      } else {
        playerDataTotalRecord = data?.totalRecords || 0;
        searchedPlayerTotalRecord = 0;
      }

      if (pageNo === 1) {
        playerData = data?.data || [];
      } else if (isMatched && pageNo > 1) {
        playerData = getPlayerUniqueData(
          state.playerData || [],
          data?.data,
          data?.page
        );
      } else {
        playerData = state.playerData;
        playerDataTotalRecord =
          data?.totalRecords || state.playerDataTotalRecord;
      }

      return {
        ...state,
        playerData: playerData,
        playerDataTotalRecord: playerDataTotalRecord,
        searchedPlayerTotalRecord: searchedPlayerTotalRecord,
        playerDataPage: data?.page || state.playerDataPage,
        playerDataLoading: isMatched ? false : state.playerDataLoading,
        isPlayerAdded: false,
      };
    case PLAYER_FAIL:
      return {
        ...state,
        playerData_error: action.payload,
        playerDataLoading: false,
      };

    case RESET_PLAYER:
      return {
        ...initialState,
      };

    case PLAYER_STATUS_SUCCESS:
      return {
        ...state,
        playerData: [
          ...updatePlayerAvailabilityData(
            state.playerData,
            action?.payload?.data
          ),
        ],
      };

    case PLAYER_TEAM_LABEL_REQUEST:
      return {
        ...state,
        teamLabelsLoading: true,
        teamLabels: null,
        teamLabelsError: null,
      };
    case PLAYER_TEAM_LABEL_SUCCESS:
      return {
        ...state,
        teamLabelsLoading: false,
        teamLabels: action.payload?.data?.map(({ teamName }) => teamName) || [],
      };
    case PLAYER_TEAM_LABEL_FAILED:
      return {
        ...state,
        teamLabelsLoading: false,
        teamLabelsError: action.payload,
      };

    // MEDICAL_INJURIES START

    case PLAYER_INITIAL_MEDICAL_INJURIES_REQUEST:
      return {
        ...state,
        playerInjuries: [],
        playerInjuriesLoading: true,
      };

    case PLAYER_INITIAL_MEDICAL_INJURIES_SUCCESS:
      return {
        ...state,
        playerInjuries: data || [],
        playerInjuriesLoading: false,
        playerInjuriesPage: page || state.playerInjuriesPageSize,
        playerInjuriesPageSize: size || state.playerInjuriesPageSize,
        playerInjuriesTotalRecords:
          totalRecords || state.playerInjuriesTotalRecords,
      };

    case PLAYER_INITIAL_MEDICAL_INJURIES_FAIL:
      return {
        ...state,
        playerInjuriesLoading: false,
      };

    case PLAYER_MORE_MEDICAL_INJURIES_SUCCESS:
      return {
        ...state,
        playerInjuries: data
          ? [...state.playerInjuries, ...data]
          : state.playerInjuries,
        playerInjuriesPage: page || state.playerInjuriesPageSize,
        playerInjuriesPageSize: size || state.playerInjuriesPageSize,
        playerInjuriesTotalRecords:
          totalRecords || state.playerInjuriesTotalRecords,
      };

    case PLAYER_EDIT_MEDICAL_INJURIES_REQUEST:
      return {
        ...state,
        playerInjuriesTriggerLoading: true,
      };

    case PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS:
      return {
        ...state,
        playerInjuriesTriggerLoading: false,
      };

    case PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerInjuries: [...updateData(state.playerInjuries, action?.payload)],
        playerInjuriesTriggerLoading: false,
      };

    case PLAYER_EDIT_MEDICAL_INJURIES_FAIL:
      return { ...state, playerInjuriesTriggerLoading: false };

    case PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerInjuries: [...deleteData(state.playerInjuries, action?.payload)],
      };

    case PLAYER_ADD_MEDICAL_INJURIES_REQUEST:
      return {
        ...state,
        playerInjuriesTriggerLoading: true,
      };

    case PLAYER_ADD_MEDICAL_INJURIES_SUCCESS:
      return {
        ...state,
        playerInjuries: [...addData(state.playerInjuries, action?.payload)],
        playerInjuriesTriggerLoading: false,
      };

    case PLAYER_ADD_MEDICAL_INJURIES_FAIL:
      return { ...state, playerInjuriesTriggerLoading: false };
    // MEDICAL_INJURIES END

    // Medical History START

    case PLAYER_INITIAL_MEDICAL_HISTORY_REQUEST:
      return {
        ...state,
        playerMedicalHistory: [],
        playerMedicalHistoryLoading: true,
      };
    case PLAYER_INITIAL_MEDICAL_HISTORY_FAIL:
      return {
        ...state,
        playerMedicalHistoryLoading: false,
      };

    case PLAYER_INITIAL_MEDICAL_HISTORY_SUCCESS:
      return {
        ...state,
        playerMedicalHistory: data || [],
        playerMedicalHistoryPage: page || state.playerMedicalHistoryPage,
        playerMedicalHistoryPageSize:
          size || state.playerMedicalHistoryPageSize,
        playerMedicalHistoryTotalRecords:
          totalRecords || state.playerMedicalHistoryTotalRecords,
        playerMedicalHistoryLoading: false,
      };

    case PLAYER_MORE_MEDICAL_HISTORY_SUCCESS:
      return {
        ...state,
        playerMedicalHistory: data
          ? [...state.playerMedicalHistory, ...data]
          : state.playerMedicalHistory,
        playerMedicalHistoryPage: page || state.playerMedicalHistoryPage,
        playerMedicalHistoryPageSize:
          size || state.playerMedicalHistoryPageSize,
        playerMedicalHistoryTotalRecords:
          totalRecords || state.playerMedicalHistoryTotalRecords,
      };

    case PLAYER_EDIT_MEDICAL_HISTORY_REQUEST:
      return {
        ...state,
        playerMedicalHistoryTriggerLoading: true,
      };

    case PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerMedicalHistory: [
          ...updateData(state.playerMedicalHistory, action?.payload),
        ],
        playerMedicalHistoryTriggerLoading: false,
      };

    case PLAYER_EDIT_MEDICAL_HISTORY_FAIL:
      return {
        ...state,
        playerMedicalHistoryTriggerLoading: false,
      };

    case PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerMedicalHistory: [
          ...deleteData(state.playerMedicalHistory, action?.payload),
        ],
      };

    case PLAYER_ADD_MEDICAL_HISTORY_REQUEST:
      return {
        ...state,
        playerMedicalHistoryTriggerLoading: true,
      };

    case PLAYER_ADD_MEDICAL_HISTORY_SUCCESS:
      return {
        ...state,
        playerMedicalHistory: [
          ...addData(state.playerMedicalHistory, action?.payload),
        ],
        playerMedicalHistoryTriggerLoading: false,
      };

    case PLAYER_ADD_MEDICAL_HISTORY_FAIL:
      return {
        ...state,
        playerMedicalHistoryTriggerLoading: false,
      };

    // Medical History END

    case PLAYER_INITIAL_UPDATE_REQUEST:
      return {
        ...state,
        playerUpdate: [],
        playerUpdateLoading: true,
      };

    case PLAYER_INITIAL_UPDATE_SUCCESS:
      return {
        ...state,
        playerUpdate: data || [],
        playerUpdateLoading: false,
        playerUpdatePage: page || state.playerUpdatePage,
        playerUpdatePageSize: size || state.playerUpdatePageSize,
        playerUpdateTotalRecords:
          totalRecords || state.playerUpdateTotalRecords,
      };

    case PLAYER_MORE_UPDATE_SUCCESS:
      return {
        ...state,
        playerUpdate: data
          ? [...state.playerUpdate, ...data]
          : state.playerUpdate,
        playerUpdatePage: page || state.playerUpdatePage,
        playerUpdatePageSize: size || state.playerUpdatePageSize,
        playerUpdateTotalRecords:
          totalRecords || state.playerUpdateTotalRecords,
      };

    case PLAYER_EDIT_UPDATE_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerUpdate: [...updateData(state.playerUpdate, action?.payload)],
      };

    case PLAYER_DELETE_UPDATE_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerUpdate: [...deleteData(state.playerUpdate, action?.payload)],
      };

    case PLAYER_ADD_UPDATE_SUCCESS:
      return {
        ...state,
        playerUpdate: [...addData(state.playerUpdate, action?.payload)],
      };

    case PLAYER_UPCOMING_EVENTS_REQUEST:
      return {
        ...state,
        playerUpcomingEvents: [],
        playerUpcomingEventsLoading: true,
        playerUpcomingEventsError: false,
      };
    case PLAYER_UPCOMING_EVENTS_SUCCESS:
      return {
        ...state,
        playerUpcomingEvents: data,
        playerUpcomingEventsLoading: false,
        playerUpcomingEventsError: false,
        playerUpcomingEventsCurrentPage: page,
      };
    case PLAYER_UPCOMING_EVENTS_FAILED:
      return {
        ...state,
        playerUpcomingEvents: [],
        playerUpcomingEventsLoading: false,
        playerUpcomingEventsError: true,
      };
    case PLAYER_UPCOMING_EVENTS_UPDATE_SUCCESS:
      return {
        ...state,
        playerUpcomingEvents: [...state.playerUpcomingEvents, ...data],
        playerUpcomingEventsLoading: false,
        playerUpcomingEventsError: false,
        playerUpcomingEventsCurrentPage: page,
      };

    case PLAYER_PAST_EVENTS_REQUEST:
      return {
        ...state,
        playerPastEvents: [],
        playerPastEventsLoading: true,
        playerPastEventsError: false,
      };
    case PLAYER_PAST_EVENTS_SUCCESS:
      return {
        ...state,
        playerPastEvents: data,
        playerPastEventsLoading: false,
        playerPastEventsError: false,
        playerPastEventsCurrentPage: page,
        playerPastEventTotalRecords:
          totalRecords || state.playerPastEventTotalRecords,
      };
    case PLAYER_PAST_EVENTS_FAILED:
      return {
        ...state,
        playerPastEvents: [],
        playerPastEventsLoading: false,
        playerPastEventsError: true,
      };
    case PLAYER_PAST_EVENTS_UPDATE_REQUEST:
      return {
        ...state,
        playerPastEventsCurrentPage: state.playerPastEventsCurrentPage + 1,
      };
    case PLAYER_PAST_EVENTS_UPDATE_SUCCESS:
      return {
        ...state,
        playerPastEvents: data?.length
          ? [...state.playerPastEvents, ...data]
          : state.playerPastEvents,
        playerPastEventsLoading: false,
        playerPastEventsError: false,
      };
    case PLAYER_FETCH_REQUEST:
      return {
        ...state,
        playerAddData: '',
        playerAddData_error: null,
        playerAddDataLoading: true,
      };
    case PLAYER_FETCH_SUCCESS:
      return {
        ...state,
        playerAddData: action.payload,
        playerAddDataLoading: false,
      };

    case RESET_PLAYER_FETCH:
      return {
        ...initialState,
      };

    case PLAYER_FETCH_FAIL:
      return {
        ...state,
        playerAddData_error: action.payload,
        playerAddDataLoading: false,
      };

    case PLAYER_FETCH_MORE_SUCCESS:
      if (
        !action?.payload?.data?.length ||
        state?.playerAddData?.page === action?.payload?.page
      ) {
        return { ...state };
      }
      return {
        ...state,
        playerAddData: {
          ...action.payload,
          data: [
            ...(state?.playerAddData?.data || []),
            ...(action?.payload?.data || []),
          ],
        },
      };

    case PLAYER_ADD_REQUEST:
      return {
        ...state,
        playerAddTeamData: {},
        playerAddTeamData_error: null,
      };
    case PLAYER_ADD_SUCCESS:
      return {
        ...state,
        // playerAddTeamData: action.payload,
      };
    case PLAYER_ADD_FAIL:
      return {
        ...state,
        playerAddTeamData_error: action.payload,
        isPlayerAdded: false,
      };

    case PLAYER_ADD_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        playerData: addPlayersData(
          state?.playerData,
          action?.payload?.customInput
        ),
        isPlayerAdded: true,
      };

    case PLAYER_PAST_EVENTS_ATTENDANCE_REQUEST:
      return {
        ...state,
        playerAttendance: [],
        playerAttendanceFailed: false,
        playerAttendanceLoading: true,
      };
    case PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS:
      return {
        ...state,
        playerAttendance: data,
        playerAttendanceFailed: false,
        playerAttendanceLoading: false,
      };
    case PLAYER_PAST_EVENTS_ATTENDANCE_FAILED:
      return {
        ...state,
        playerAttendance: [],
        playerAttendanceFailed: true,
        playerAttendanceLoading: false,
      };
    //player position
    case PLAYER_POSITION_REQUEST:
      return {
        ...state,
        playerPositionData: '',
        playerPositionData_error: null,
      };

    case PLAYER_POSITION_SUCCESS:
      return {
        ...state,
        playerPositionData: action.payload,
      };

    case PLAYER_POSITION_FAIL:
      return {
        ...state,
        playerPositionData_error: action.payload,
      };

    case PLAYER_POSITION_RESET:
      return {
        ...initialState,
      };

    //player update
    case PLAYER_INFO_UPDATE_REQUEST:
      return {
        ...state,
        playerInfoUpdateData: {},
        playerInfoUpdateData_error: null,
        playerInfoUpdateLoading: true,
        playerUpdateSuccess: false,
      };

    case PLAYER_INFO_UPDATE_SUCCESS:
      return {
        ...state,
        playerInfoUpdateData: action.payload,
        playerInfoUpdateLoading: false,
        playerUpdateSuccess: true,
      };

    case PLAYER_INFO__UPDATE_FAIL:
      return {
        ...state,
        playerInfoUpdateData_error: action.payload,
        playerUpdateSuccess: null,
        playerInfoUpdateLoading: false,
      };

    case 'PLAYER_INFO_UPDATE_RESET':
      return {
        ...state,
        playerInfoUpdateData: {},
        playerInfoUpdateData_error: null,
        playerInfoUpdateLoading: false,
        playerUpdateSuccess: false,
      };

    case PLAYER_EVENTS_TEAM_DATA_REQUEST:
      return {
        ...state,
        playerEventTeamsLoading: true,
        playerEventTeamsFailed: false,
      };
    case PLAYER_EVENTS_TEAM_DATA_SUCCESS:
      return {
        ...state,
        playerEventTeams: [...state.playerEventTeams, ...data],
        playerEventTeamsLoading: false,
        playerEventTeamsFailed: false,
      };
    case PLAYER_EVENTS_TEAM_DATA_FAILED:
      return {
        ...state,
        playerEventTeamsLoading: false,
        playerEventTeamsFailed: true,
      };

    case UPLOAD_REPORT_NEW:
      return {
        ...state,
        playerUploadedReport: [...action.payload],
      };

    case UPLOAD_REPORT_UPDATE:
      return {
        ...state,
        playerUploadedReport: [
          ...state.playerUploadedReport,
          ...action.payload,
        ],
      };
    case UPLOAD_REPORT_SUCCESS:
      return {
        ...state,
        playerUploadedReport: [
          ...state.playerUploadedReport,
          ...[
            {
              ...action.payload.data,
              fileName: action?.payload?.data?.fileName || '',
            },
          ],
        ],
      };

    case REPORT_DELETE:
      return {
        ...state,
        playerUploadedReport: [
          ...state.playerUploadedReport.filter(
            (report, index) => index !== data
          ),
        ],
      };

    case PLAYER_INFO_DOCUMENT_DELETE_REQUEST:
      return {
        ...state,
        deleteDocumentLoading: true,
        deleteDocumentSuccess: false,
        deleteDocumentFailed: false,
      };
    case PLAYER_INFO_DOCUMENT_DELETE_SUCCESS:
      return {
        ...state,
        documentListPage: 1,
        documentList: [],
        deleteDocumentLoading: false,
        deleteDocumentSuccess: true,
      };
    case PLAYER_INFO_DOCUMENT_DELETE_FAIL:
      return {
        ...state,
        documentListPage: 1,
        documentList: [],
        deleteDocumentLoading: false,
        deleteDocumentFailed: true,
      };

    case REPORT_RESET:
      return {
        ...state,
        playerUploadedReport: [],
      };
    default:
      return state;
  }
};
/**
 *add Player
 */
const addPlayersData = (state, payload) => {
  if (!state) {
    state = [];
  }
  state.push(payload);

  return state;
};

/**
 *add update
 */
const addData = (state, payload) => {
  const { _id, message, createdDate } = payload;

  return [{ _id, message, createdDate }, ...state];
};

/**
 *delete update
 */
const deleteData = (state, payload) => {
  const { _id } = payload?.customInput;
  const deleteIndex = getArrayIndex(state, _id);
  state.splice(deleteIndex, 1);

  return state;
};

/**
 *update content
 */
const updateData = (state, payload) => {
  const { updateId, createdDate, message } = payload?.customInput;
  const updateIndex = getArrayIndex(state, updateId);

  state[updateIndex].message = message;
  state[updateIndex].createdDate = createdDate;

  return state;
};

/**
 *Get array index from object key value
 */
const getArrayIndex = (state, id) => {
  return state.findIndex(element => element._id === id);
};

const updatePlayerAvailabilityData = (state, payload) => {
  payload &&
    state.forEach((element, index) => {
      const updateIndex = getPlayerArrayIndex(payload, element.userId);

      const payloadEvent = payload[updateIndex];

      if (updateIndex >= 0) {
        state[index].isAvailable = `${payloadEvent.isAvailable}`;
      } else {
        state[index].isAvailable = `false`;
      }
    });
  return state;
};

const getPlayerArrayIndex = (state, id) => {
  return state.findIndex(element => element.userId === id);
};

const getPlayerUniqueData = (currentData, newData, page) => {
  return (uniquePlayerDataList = currentData?.length
    ? avoidedDuplicationData(currentData, newData || null, 'userId')
    : newData || null);
};

export default PlayerReducer;
