import {
  PLAYER_INFO_FAIL,
  PLAYER_INFO_REQUEST,
  PLAYER_INFO_RESET,
  PLAYER_INFO_SUCCESS,
} from '../../actionTypes/player/playerAction';
const initailState = {
  PlayerInfoDataLoading: false,
  PlayerInfoData: undefined,
  PlayerInfoData_error: null,
};

const PlayerInfoReducer = (state = initailState, action) => {
  switch (action.type) {
    case PLAYER_INFO_REQUEST:
      return {
        ...state,
        PlayerInfoDataLoading: true,
        PlayerInfoData: undefined,
        playerData_error: null,
      };

    case PLAYER_INFO_SUCCESS:
      return {
        ...state,
        PlayerInfoDataLoading: false,
        PlayerInfoData: action.payload,
      };

    case PLAYER_INFO_FAIL:
      return {
        ...state,
        PlayerInfoDataLoading: false,
        PlayerInfoData_error: action.payload,
      };

    case PLAYER_INFO_RESET:
      return {
        ...initailState,
      };

    default:
      return state;
  }
};

export default PlayerInfoReducer;
