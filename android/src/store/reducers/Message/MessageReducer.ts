import { messageTypes, socketMessageTypes } from '../../../constants/constants';
import {
  avoidedDuplicationData,
  getArrayIndexUsingKey,
  messageDataMapping,
} from '../../../helpers/common';
import {
  CHECK_CHAT_INITIATED_FAIL,
  CHECK_CHAT_INITIATED_REQUEST,
  CHECK_CHAT_INITIATED_SUCCESS_CUSTOM_CONTENT,
  CHECK_IS_APP_ACTIVE,
  CHECK_VALIDITY_OF_PERSONAL_CHAT_FAIL,
  CHECK_VALIDITY_OF_PERSONAL_CHAT_REQUEST,
  CHECK_VALIDITY_OF_PERSONAL_CHAT_SUCCESS,
  CREATE_CHAT_ROOM_ID_FAIL,
  CREATE_CHAT_ROOM_ID_REQUEST,
  CREATE_CHAT_ROOM_ID_SUCCESS_CUSTOM_CONTENT,
  FETCH_FORWARD_MSG_PLAYERS_FAIL,
  FETCH_FORWARD_MSG_PLAYERS_REQUEST,
  FET<PERSON>_FORWARD_MSG_PLAYERS_SUCCESS,
  FET<PERSON>_FORWARD_MSG_TEAM_FAIL,
  FETCH_FORWARD_MSG_TEAM_REQUEST,
  FETCH_FORWARD_MSG_TEAM_SUCCESS,
  FETCH_MESSAGE_FOR_USERS_FAIL,
  FETCH_MESSAGE_FOR_USERS_REQUEST,
  FETCH_MESSAGE_FOR_USERS_SUCCESS_CUSTOM_CONTENT,
  FETCH_MORE_MESSAGE_FOR_USERS_SUCCESS_CUSTOM_CONTENT,
  FETCH_REPLIED_CONTENT_SUCCESS,
  GET_CHAT_INFO_DETAILS_FOR_NEW_USER_SUCCESS_CUSTOM_CONTENT,
  GET_CREATE_NEW_MESSAGE_USERS_FAIL,
  GET_CREATE_NEW_MESSAGE_USERS_REQUEST,
  GET_CREATE_NEW_MESSAGE_USERS_SUCCESS,
  GET_FAST_CHAT_LATEST_MESSAGE_FAIL,
  GET_FAST_CHAT_LATEST_MESSAGE_REQUEST,
  GET_FAST_CHAT_LATEST_MESSAGE_SUCCESS,
  GET_FAST_MESSAGE_CHAT_MEMBER_INFO_FAIL,
  GET_FAST_MESSAGE_CHAT_MEMBER_INFO_REQUEST,
  GET_FAST_MESSAGE_CHAT_MEMBER_INFO_SUCCESS,
  GET_NOTFICATION_REQUEST,
  GET_NOTFICATION_SUCCESS,
  GET_PAST_MESSAGE_CHAT_INFO_FAIL,
  GET_PAST_MESSAGE_CHAT_INFO_REQUEST,
  GET_PAST_MESSAGE_CHAT_INFO_SUCCESS_CUSTOM_CONTENT,
  GET_UNREAD_MESSAGE_COUNT_FAIL,
  GET_UNREAD_MESSAGE_COUNT_REQUEST,
  GET_UNREAD_MESSAGE_COUNT_SUCCESS,
  GET_USER_DETAILS_FOR_NEW_USER_SUCCESS,
  IMPORT_LAST_MESSAGE,
  IMPORT_MESSAGES,
  IMPORT_RELEVANT_DETAILS_TO_INITIALIZE_CHAT,
  MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
  RESET_MESSAGE_TYPES,
  RESET_TO_INITIAL_STATE_MESSAGE,
  SELECTED_USER_FOR_MESSAGE,
  SET_CHAT_ROOM_ID_FOR_SELECTED_MESSAGE,
  SET_CONNECTION_ID,
  SET_LAST_SEEN_MESSAGE_SUCCESS_CUSTOM_CONTENT,
  SET_NOTFICATION_REQUEST,
  SET_NOTFICATION_SUCCESS,
  SET_PAST_MESSAGE_CHAT_INFO,
  SET_PAST_MESSAGE_CHAT_MEMBER_INFO,
  SET_RESET_SELECTED_MESSAGE_CHAT_ID,
  SET_SELECTED_MESSAGE_TYPE,
  SET_TEAM_PROFILE_PICTURE_SUCCESS,
  SET_UNREAD_MESSAGE_COUNT,
  SET_UNREAD_MESSAGE_COUNT_SELECTED_RESET,
  UPDATE_MESSAGE_COUNT_PERSONAL,
  UPDATE_MESSAGE_COUNT_TEAM,
  GET_PERSONAL_TAB_COUNT_SUCCESS,
  GET_TEAMS_TAB_COUNT_SUCCESS,
  SET_PREV_CHAT_STATE,
  SET_CURRENT_CHAT_STATE,
  GET_SELECTED_NOTFICATION_REQUEST,
  GET_SELECTED_NOTFICATION_SUCCESS,
  GET_SELECTED_NOTFICATION_FAIL,
  RESET_SELECTED_NOTIFICATION,
  SET_SELECTED_MESSAGE_NOTIFICATION,
  SET_REAL_TIME_PREVIEW_IMAGE,
  SET_PREV_MESSAGE_NOTIFICATION,
  SET_REAL_TIME_VIDEO_PREVIEW_IMAGE,
  GET_TEAMS_CHAT_MEMBER_INFO_SUCCESS,
  PAST_MESSAGE_CHAT_MEMBER_INFO_RESET,
  REMOVE_MESSAGE,
} from '../../actionTypes/Message/MessageAction';

type Action = {
  type: string;
  payload: {
    _id?: any;
    data?: any;
    totalRecords?: number;
    page?: number;
    size?: number;
    customInput?: any;
    [name: string]: any;
  };
};
export interface createNewMessageUsersType {
  id: string;
  firstName: string | null;
  lastName: string | null;
  nickName?: string | null;
  dateOfBirth?: string | null;
  profileImageUrl?: string | null;
  type?: string | null;
  height?: string | null;
  weight?: string | null;
  contact?: string | null;
  parentContact?: string | null;
  emergencyContact?: string | null;
  emailId?: string | null;
  expoPushNotificationTokens?: [] | null;
  profileImage?: string | null;
  joinedDate?: string | null;
}
export interface selectedMessageChatType {
  [id: string]: {
    _id?: string;
    type?: string;
    memberUserIds?: string[] | null;
  } | null;
}

export interface selectedUsersMessageContentType {
  [id: string]: singleMessageContentTypes[] | null;
}

export interface s3Object {
  bucketName: string;
  fileKey: string;
  fileName: string;
}

export interface singleMessageContentTypes {
  _id: string;
  chatId?: string | null;
  senderUserId?: string;
  type?: string;
  content?: string;
  sentDate?: Date;
  image?: s3Object;
  previewImage?: s3Object;
  video?: s3Object;
  file?: s3Object;
  isDeleted?: boolean;
  isBroadcast?: boolean;
  isForwarded?: boolean;
  forwardedMessageId?: string;
  isUserOwnMessage?: boolean;
  repliedForMessageId: string;
  chatType: string;
  connectionId?: string;
  socketMessageType?: string;
  previewVideoImage?: s3Object;
  deleted? :boolean
}

export interface pastMessageChatInfo {
  _id: string;
  type: string | null;
  memberUserIds: string[] | null;
  creatorUserId: string | null;
  opponentUserIds?: string[] | null;
  name?: string | null;
}

export interface pastMessageChatMemberInfo {
  id: string;
  firstName: string | null;
  lastName: string | null;
  nickName?: string | null;
  dateOfBirth?: string | null;
  profileImageUrl?: string | null;
  type?: string | null;
  height?: string | null;
  weight?: string | null;
  contact?: string | null;
  parentContact?: string | null;
  emergencyContact?: string | null;
  emailId?: string | null;
  expoPushNotificationTokens?: [] | null;
  profileImage?: string | null;
  joinedDate?: string | null;
}
export interface IunreadMessageCountValue {
  chatId: string;
  count: number;
  userId: string;
}
interface IunreadMessageCount {
  [name: string]: IunreadMessageCountValue;
}

interface Is3BucketData {
  bucketName: string;
  fileKey: string;
  fileName: string;
}

export interface IrepliedMessageContentType {
  _id: string;
  chatId: string;
  senderUserId: string;
  type: string;
  content?: string;
  forwardedMessageId?: string;
  sentDate?: string;
  video?: Is3BucketData | null;
  deleted?: string;
  broadcast?: string;
  repliedForMessageId?: string;
  image?: Is3BucketData | null;
  file?: Is3BucketData | null;
}

export interface messageReducerType {
  selectedMessageType: string;
  createNewMessageUsers: createNewMessageUsersType[] | null;
  createNewMessageUsersLoading: boolean;
  createNewMessageUsersTotalRecords: number;
  createNewMessageUsersPage: number;
  createNewMessageFailLoading: boolean;
  selectedUsersForMessages: createNewMessageUsersType[] | null;
  selectedMessageChatId: selectedMessageChatType | null;
  selectedMessageChatIdLoading: boolean;
  selectedMessageChatIdTotalRecords: number;
  messages: singleMessageContentTypes[] | null;
  messagesTotalRecords: number;
  messagesPage: number;
  messagesSize: number;
  selectedUsersMessageContent: selectedUsersMessageContentType | null;
  selectedUsersMessageContentLoading: boolean;
  selectedUsersMessageContentTotalRecords: number;
  selectedUsersMessageContentPage: number;
  pastMessageChatInfo: pastMessageChatInfo[] | null;
  pastMessageChatInfoLoading: boolean;
  pastMessageChatInfoTotalRecords: number;
  pastMessageChatInfoPage: number;
  pastMessageChatInfoFailLoading: boolean;
  pastMessageChatMemberInfo: pastMessageChatMemberInfo[] | null;
  pastMessageChatMemberInfoLoading: boolean;
  pastMessageChatMemberInfoTotalRecords: number;
  pastMessageChatMemberInfoPage: number;
  pastMessageChatMemberInfoFailLoading: boolean;
  pastChatLatestMessage: singleMessageContentTypes[] | null;
  pastChatLatestMessageLoading: boolean;
  pastChatLatestMessageTotalRecords: number;
  pastChatLatestMessagePage: number;
  pastChatLatestMessageFailLoading: boolean;
  forwardMsgUsers: pastMessageChatMemberInfo[] | null;
  forwardMsgUsersPage: number;
  forwardMsgUsersTotalRecords: number;
  forwardMsgUsersLoading: boolean;
  forwardMsgTeams: pastMessageChatMemberInfo[] | null;
  forwardMsgTeamsPage: number;
  forwardMsgTeamsTotalRecords: number;
  forwardMsgTeamsLoading: boolean;
  unreadMessageCount: IunreadMessageCount;
  unreadMessageCountLoading: boolean;
  unreadMessageCountFailLoading: boolean;
  teamMemberInfo: pastMessageChatMemberInfo[] | null;
  repliedContent: IrepliedMessageContentType[] | null;
  uploadedContent: s3Object | null;
  connectionId: string | null;
  isAllowPushNotificationForMsg: boolean;
  isAllowPushNotificationForMsgLoading: boolean;
  isScreenActive: boolean;
  isChatValid: boolean;
  personalMessageCount: string[] | null;
  teamMessageCount: string[] | null;
  tabCountPagePersonal: number;
  tabCountPageTeams: number;
  prevSelectedMessageChatId: selectedMessageChatType | null;
  prevSelectedUsersForMessages: createNewMessageUsersType[] | null;
  prevIsChatValid: boolean;
  notificationSelectedMessageChatInfo: pastMessageChatInfo[];
  notificationSelectedLoading: boolean;
  isNotificationSelected: boolean;
  notificationSelectedFormatedMessages: any;
  notificationPrevMessageChatInfo: any;
  teamMessageChatMemberInfo: pastMessageChatMemberInfo[] | null;
  teamMessageChatMemberInfoPage: number;
  teamMessageChatMemberInfoTotalRecord: number;
}

const INITIAL_STATE: messageReducerType = {
  selectedMessageType: messageTypes.PERSONAL,
  createNewMessageUsers: null,
  createNewMessageUsersLoading: false,
  createNewMessageUsersTotalRecords: 0,
  createNewMessageUsersPage: 1,
  createNewMessageFailLoading: false,
  selectedMessageChatIdLoading: false,
  selectedMessageChatIdTotalRecords: 0,
  selectedUsersMessageContent: null,
  selectedUsersMessageContentLoading: false,
  selectedUsersMessageContentTotalRecords: 0,
  selectedUsersMessageContentPage: 1,
  messages: [],
  messagesTotalRecords: 0,
  messagesPage: 1,
  messagesSize: 100,
  pastMessageChatInfo: null,
  pastMessageChatInfoLoading: false,
  pastMessageChatInfoTotalRecords: 0,
  pastMessageChatInfoPage: 1,
  pastMessageChatInfoFailLoading: false,
  pastMessageChatMemberInfo: null,
  pastMessageChatMemberInfoLoading: false,
  pastMessageChatMemberInfoTotalRecords: 0,
  pastMessageChatMemberInfoPage: 1,
  pastMessageChatMemberInfoFailLoading: false,
  pastChatLatestMessage: null,
  pastChatLatestMessageLoading: false,
  pastChatLatestMessageTotalRecords: 0,
  pastChatLatestMessagePage: 1,
  pastChatLatestMessageFailLoading: false,
  forwardMsgUsers: null,
  forwardMsgUsersPage: 1,
  forwardMsgUsersTotalRecords: 0,
  forwardMsgUsersLoading: false,
  forwardMsgTeams: null,
  forwardMsgTeamsPage: 1,
  forwardMsgTeamsTotalRecords: 0,
  forwardMsgTeamsLoading: false,
  unreadMessageCount: {},
  unreadMessageCountLoading: false,
  unreadMessageCountFailLoading: false,
  teamMemberInfo: null,
  repliedContent: [],
  connectionId: null,
  isAllowPushNotificationForMsg: true,
  isAllowPushNotificationForMsgLoading: false,
  uploadedContent: null,
  isScreenActive: false,
  personalMessageCount: null,
  teamMessageCount: null,
  tabCountPagePersonal: 50,
  tabCountPageTeams: 50,
  selectedUsersForMessages: null,
  selectedMessageChatId: null,
  isChatValid: false,
  prevSelectedMessageChatId: null,
  prevSelectedUsersForMessages: null,
  prevIsChatValid: false,
  notificationSelectedMessageChatInfo: [],
  notificationSelectedLoading: false,
  isNotificationSelected: false,
  notificationSelectedFormatedMessages: null,
  notificationPrevMessageChatInfo: null,
  teamMessageChatMemberInfo: [],
  teamMessageChatMemberInfoPage: 1,
  teamMessageChatMemberInfoTotalRecord: 0,
};

const messageReducer = (
  state = INITIAL_STATE,
  action: Action
): messageReducerType => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  const customInput = action?.payload?.customInput;

  switch (action.type) {
    case SET_PREV_CHAT_STATE:
      return {
        ...state,
        prevSelectedMessageChatId: payload?.selectedMessageChatId,
        prevSelectedUsersForMessages: payload?.selectedUsersForMessages,
        prevIsChatValid: payload?.isChatValid,
      };
    case SET_CURRENT_CHAT_STATE:
      return {
        ...state,
        selectedUsersForMessages: payload?.prevSelectedUsersForMessages,
        selectedMessageChatId: payload?.prevSelectedMessageChatId,
        isChatValid: payload?.prevIsChatValid,
        prevSelectedMessageChatId: null,
        prevSelectedUsersForMessages: null,
        prevIsChatValid: false,
      };
    case SET_PAST_MESSAGE_CHAT_INFO:
      return {
        ...state,
        pastMessageChatInfo: addNewForwardChatToTop(
          state?.pastMessageChatInfo,
          data || {}
        ),
      };
    case SET_PAST_MESSAGE_CHAT_MEMBER_INFO:
      return {
        ...state,
        pastMessageChatMemberInfo: addNewForwardChatToTop(
          state?.pastMessageChatMemberInfo,
          action?.payload || {}
        ),
      };
    case FETCH_REPLIED_CONTENT_SUCCESS:
      return { ...state, repliedContent: data || null };
    case FETCH_FORWARD_MSG_TEAM_REQUEST:
      return { ...state, forwardMsgTeamsLoading: true };
    case FETCH_FORWARD_MSG_TEAM_SUCCESS:
      return {
        ...state,
        forwardMsgTeams:
          state?.forwardMsgTeams && page > 1
            ? avoidedDuplicationData(
                state?.forwardMsgTeams,
                data || null,
                '_id'
              )
            : data || null,
        forwardMsgTeamsLoading: false,
        forwardMsgTeamsTotalRecords:
          totalRecords || state?.forwardMsgTeamsTotalRecords,
        forwardMsgTeamsPage: page || state?.forwardMsgTeamsPage,
      };
    case FETCH_FORWARD_MSG_TEAM_FAIL:
      return { ...state, forwardMsgTeamsLoading: false };
    case FETCH_FORWARD_MSG_PLAYERS_REQUEST:
      return { ...state, forwardMsgUsersLoading: true };
    case FETCH_FORWARD_MSG_PLAYERS_SUCCESS:
      return {
        ...state,
        forwardMsgUsers:
          state?.forwardMsgUsers && page > 1
            ? avoidedDuplicationData(state?.forwardMsgUsers, data || null, 'id')
            : data || null,
        forwardMsgUsersLoading: false,
        forwardMsgUsersTotalRecords:
          totalRecords || state?.forwardMsgUsersTotalRecords,
        forwardMsgUsersPage: page || state?.forwardMsgUsersPage,
      };
    case FETCH_FORWARD_MSG_PLAYERS_FAIL:
      return { ...state, forwardMsgUsersLoading: false };
    case SET_SELECTED_MESSAGE_TYPE:
      return {
        ...state,
        selectedMessageType: customInput || messageTypes.PERSONAL,
      };
    case GET_CREATE_NEW_MESSAGE_USERS_REQUEST:
      return {
        ...state,
        createNewMessageUsersLoading: true,
        createNewMessageFailLoading: false,
      };
    case GET_CREATE_NEW_MESSAGE_USERS_SUCCESS:
      return {
        ...state,
        createNewMessageUsers:
          state?.createNewMessageUsers && page > 1
            ? avoidedDuplicationData(
                state?.createNewMessageUsers,
                data || null,
                'id'
              )
            : data || null,
        createNewMessageUsersLoading: false,
        createNewMessageUsersTotalRecords:
          totalRecords || state.createNewMessageUsersTotalRecords,
        createNewMessageUsersPage: page || state.createNewMessageUsersPage,
      };
    case GET_CREATE_NEW_MESSAGE_USERS_FAIL:
      return {
        ...state,
        createNewMessageUsersLoading: false,
        createNewMessageFailLoading: true,
      };
    case CHECK_CHAT_INITIATED_REQUEST:
    case CREATE_CHAT_ROOM_ID_REQUEST:
      return {
        ...state,
        selectedMessageChatIdLoading: true,
      };
    case CREATE_CHAT_ROOM_ID_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        selectedMessageChatId: { [customInput?.id]: data || null },
        selectedMessageChatIdTotalRecords: data ? 1 : 0,
      };
    case SET_CHAT_ROOM_ID_FOR_SELECTED_MESSAGE:
      return {
        ...state,
        selectedMessageChatId: {
          [customInput?.id]: customInput?.value || null,
        },
        selectedMessageChatIdTotalRecords: 1,
        selectedUsersMessageContent: null,
        selectedUsersMessageContentLoading: false,
        selectedUsersMessageContentTotalRecords: 0,
        selectedUsersMessageContentPage: 1,
        personalMessageCount: state.personalMessageCount?.includes(
          customInput?.value?._id
        )
          ? state.personalMessageCount?.filter(
              data => data !== customInput?.value?._id
            )
          : state.personalMessageCount,
        teamMessageCount: state.teamMessageCount?.includes(customInput?.id)
          ? state.teamMessageCount?.filter(data => data !== customInput?.id)
          : state.teamMessageCount,
      };
    case CHECK_CHAT_INITIATED_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        selectedMessageChatId: { [customInput?.id]: data?.data?.[0] || null },
        selectedMessageChatIdTotalRecords: data?.totalRecords || 0,
      };
    case MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE:
      return {
        ...state,
        pastMessageChatInfo: moveChatOrder(state?.pastMessageChatInfo, data),
      };
    case CHECK_CHAT_INITIATED_FAIL:
    case CREATE_CHAT_ROOM_ID_FAIL:
      return {
        ...state,
        selectedMessageChatIdLoading: false,
      };
    case SELECTED_USER_FOR_MESSAGE:
      return {
        ...state,
        selectedMessageChatId: null,
        selectedUsersForMessages: customInput ? [customInput] : null,
        selectedMessageChatIdTotalRecords: 0,
        selectedUsersMessageContent: null,
        selectedUsersMessageContentLoading: false,
        selectedUsersMessageContentTotalRecords: 0,
        selectedUsersMessageContentPage: 1,
        isChatValid: false,
      };
    case FETCH_MESSAGE_FOR_USERS_REQUEST: {
      return {
        ...state,
        selectedUsersMessageContentLoading: true,
      };
    }
    case FETCH_MESSAGE_FOR_USERS_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,

        selectedUsersMessageContent: {
          [customInput?.id]: messageDataMapping(
            customInput?.logInUser,
            data?.data
          ),
        },
        selectedUsersMessageContentLoading: false,
        selectedUsersMessageContentTotalRecords:
          data?.totalRecords || state.selectedUsersMessageContentTotalRecords,
        selectedUsersMessageContentPage:
          data?.page || state.selectedUsersMessageContentPage,
      };
    case FETCH_MESSAGE_FOR_USERS_FAIL:
      return {
        ...state,
        selectedUsersMessageContentLoading: false,
      };

    case FETCH_MORE_MESSAGE_FOR_USERS_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        // need to call avoidedDuplicationData function when scroll up
        selectedUsersMessageContent: {
          [customInput?.id]: messageDataMapping(
            customInput?.logInUser,
            combineMessages(
              customInput?.id,
              state.selectedUsersMessageContent,
              data?.data
            )
          ),
        },
        selectedUsersMessageContentLoading: false,
        selectedUsersMessageContentTotalRecords:
          data?.totalRecords || state.selectedUsersMessageContentTotalRecords,
        selectedUsersMessageContentPage:
          data?.page || state.selectedUsersMessageContentPage,
      };

    case GET_PAST_MESSAGE_CHAT_INFO_REQUEST:
      return {
        ...state,
        pastMessageChatInfoLoading: true,
        pastMessageChatInfoFailLoading: false,
      };
    case GET_PAST_MESSAGE_CHAT_INFO_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        pastMessageChatInfo: arrangePastMessageInfoData(
          state?.pastMessageChatInfo,
          data?.data,
          data?.page,
          customInput?.userId
        ),
        pastMessageChatInfoLoading: false,
        pastMessageChatInfoTotalRecords:
          data?.totalRecords || state.pastMessageChatInfoTotalRecords,
        pastMessageChatInfoPage: data?.page || state.pastMessageChatInfoPage,
      };
    case GET_PAST_MESSAGE_CHAT_INFO_FAIL:
      return {
        ...state,
        pastMessageChatInfoLoading: false,
        pastMessageChatInfoFailLoading: true,
      };
    case GET_FAST_CHAT_LATEST_MESSAGE_REQUEST:
    case GET_FAST_CHAT_LATEST_MESSAGE_FAIL:
    case GET_FAST_MESSAGE_CHAT_MEMBER_INFO_FAIL:
    case GET_FAST_MESSAGE_CHAT_MEMBER_INFO_REQUEST:
      return {
        ...state,
      };
    case GET_FAST_MESSAGE_CHAT_MEMBER_INFO_SUCCESS:
      return {
        ...state,
        pastMessageChatMemberInfo: data || null,
      };
    case GET_FAST_CHAT_LATEST_MESSAGE_SUCCESS:
      return {
        ...state,
        pastChatLatestMessage: data || null,
      };
    case RESET_MESSAGE_TYPES:
      return {
        ...state,
        createNewMessageUsers: null,
        createNewMessageUsersLoading: false,
        createNewMessageUsersTotalRecords: 0,
        createNewMessageUsersPage: 1,
        createNewMessageFailLoading: false,
        selectedUsersForMessages: null,
        selectedMessageChatId: null,
        selectedMessageChatIdLoading: false,
        selectedMessageChatIdTotalRecords: 0,
        selectedUsersMessageContent: null,
        selectedUsersMessageContentLoading: false,
        selectedUsersMessageContentTotalRecords: 0,
        selectedUsersMessageContentPage: 1,
        pastMessageChatInfo: null,
        pastMessageChatInfoLoading: false,
        pastMessageChatInfoTotalRecords: 0,
        pastMessageChatInfoPage: 1,
        pastMessageChatInfoFailLoading: false,
        pastMessageChatMemberInfo: null,
        pastMessageChatMemberInfoLoading: false,
        pastMessageChatMemberInfoTotalRecords: 0,
        pastMessageChatMemberInfoPage: 1,
        pastMessageChatMemberInfoFailLoading: false,
        pastChatLatestMessage: null,
        pastChatLatestMessageLoading: false,
        pastChatLatestMessageTotalRecords: 0,
        pastChatLatestMessagePage: 1,
        pastChatLatestMessageFailLoading: false,
        isChatValid: false,
      };
    case IMPORT_MESSAGES:
      return {
        ...state,
        selectedUsersMessageContent: dumpDataIntoMessageContent(
          state.selectedUsersMessageContent,
          data,
          customInput
        ),
      };
    case IMPORT_LAST_MESSAGE:
      return {
        ...state,
        pastChatLatestMessage: arrangeLastMessageInfo(
          data,
          state.pastChatLatestMessage
        ),
      };
    case REMOVE_MESSAGE:
      /**
      * Checked Selected Message content exist or not
      */
     
     const { selectedUsersMessageContent, pastChatLatestMessage } = state;
     console.log("pastChatLatestMessage", pastChatLatestMessage);
  
      if (selectedUsersMessageContent == null) {
        return state;
      }
      if (pastChatLatestMessage == null) {
        return state
      }
      /**
       * Instead of calling whole API again Finding a deleted Message index and update it
       * */
      const { chatType, chatId, senderUserId, messageId } = payload;
      const targetObjectId = chatType === messageTypes.TEAMS ? chatId : senderUserId;

      const userMessages = selectedUsersMessageContent[targetObjectId] || [];

      const updatedUserMessages = userMessages.map(chat =>
        chat._id === messageId ? { ...chat, deleted: true } : chat
      );
      const updateChatLatestMessage = pastChatLatestMessage.map(message =>
        message._id === messageId ? { ...message, deleted: true } : message
      )

      console.log("pastChatLatestMessage",updateChatLatestMessage);
      

      return {
        ...state,
        selectedUsersMessageContent: {
          ...selectedUsersMessageContent,
          [targetObjectId]: updatedUserMessages
        },
        pastChatLatestMessage: updateChatLatestMessage
      };
    case IMPORT_RELEVANT_DETAILS_TO_INITIALIZE_CHAT:
      return {
        ...state,
        pastMessageChatInfo: setPastChatInfoForNewChat(
          state.pastMessageChatInfo,
          data,
          customInput
        ),
        pastMessageChatMemberInfo: setPastUserDetailsFoForNewChat(
          state.pastMessageChatMemberInfo,
          data
        ),
      };
    case GET_USER_DETAILS_FOR_NEW_USER_SUCCESS:
      return {
        ...state,
        pastMessageChatMemberInfo: setPastUserDetailsFoForNewChat(
          state.pastMessageChatMemberInfo,
          { userDetails: data?.[0] }
        ),
      };
    case GET_CHAT_INFO_DETAILS_FOR_NEW_USER_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        pastMessageChatInfo: setPastChatInfoForNewChat(
          state.pastMessageChatInfo,
          { chatInfo: data?.data?.[0] },
          customInput?.userId
        ),
      };
    case RESET_TO_INITIAL_STATE_MESSAGE:
      /**
       * Don't to reset isScreenActive & connectionId
       * isScreenActive => golable state for Device,/
       * connectionId can't be empty
       * notificationPrevMessageChatInfo can't be empty
       */
      return {
        ...INITIAL_STATE,
        isScreenActive: state.isScreenActive,
        connectionId: state.connectionId,
        notificationPrevMessageChatInfo: state.notificationPrevMessageChatInfo,
      };

    case GET_UNREAD_MESSAGE_COUNT_REQUEST:
      return {
        ...state,
        unreadMessageCountLoading: true,
        unreadMessageCount: state.unreadMessageCount,
        unreadMessageCountFailLoading: false,
      };
    case GET_UNREAD_MESSAGE_COUNT_SUCCESS:
      return {
        ...state,
        unreadMessageCountLoading: false,
        unreadMessageCount: setUnReadCount(data, state.unreadMessageCount),
        unreadMessageCountFailLoading: false,
      };
    case GET_UNREAD_MESSAGE_COUNT_FAIL:
      return {
        ...state,
        unreadMessageCountLoading: false,
        unreadMessageCount: state.unreadMessageCount,
        unreadMessageCountFailLoading: true,
      };
    case SET_UNREAD_MESSAGE_COUNT:
      return {
        ...state,
        unreadMessageCount: {
          ...state.unreadMessageCount,
          [data?.chatId]: {
            ...state.unreadMessageCount?.[data?.chatId],
            count: (state.unreadMessageCount?.[data?.chatId]?.count || 0) + 1,
          },
        },
      };
    case SET_UNREAD_MESSAGE_COUNT_SELECTED_RESET:
      return {
        ...state,
        unreadMessageCount: {
          ...state.unreadMessageCount,
          [data?.chatId]: {
            ...state.unreadMessageCount?.[data?.chatId],
            count: 0,
          },
        },
      };
    case SET_LAST_SEEN_MESSAGE_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        unreadMessageCount: {
          ...state.unreadMessageCount,
          [customInput.chatId]: {
            ...state.unreadMessageCount?.[customInput.chatId],
            count: 0,
          },
        },
      };
    case SET_CONNECTION_ID:
      return {
        ...state,
        connectionId: data,
      };
    case GET_NOTFICATION_REQUEST:
    case SET_NOTFICATION_REQUEST:
      return {
        ...state,
        isAllowPushNotificationForMsgLoading: true,
      };
    case SET_NOTFICATION_SUCCESS:
      return {
        ...state,
        isAllowPushNotificationForMsgLoading: false,
        isAllowPushNotificationForMsg: !state.isAllowPushNotificationForMsg,
      };
    case GET_NOTFICATION_SUCCESS:
      return {
        ...state,
        isAllowPushNotificationForMsgLoading: false,
        isAllowPushNotificationForMsg: payload?.allowPushNotification,
      };
    case SET_RESET_SELECTED_MESSAGE_CHAT_ID:
      return {
        ...state,
        selectedUsersForMessages: null,
        selectedMessageChatId: null,
        selectedMessageChatIdLoading: false,
        selectedMessageChatIdTotalRecords: 0,
        selectedUsersMessageContent: null,
        selectedUsersMessageContentLoading: false,
        selectedUsersMessageContentTotalRecords: 0,
        selectedUsersMessageContentPage: 1,
        isChatValid: false,
      };
    case SET_TEAM_PROFILE_PICTURE_SUCCESS:
      return {
        ...state,
        selectedMessageChatId: { [payload?._id]: payload || null },
      };
    case CHECK_IS_APP_ACTIVE:
      return {
        ...state,
        isScreenActive: payload.data,
      };

    case CHECK_VALIDITY_OF_PERSONAL_CHAT_REQUEST:
      return {
        ...state,
        isChatValid: false,
      };
    case CHECK_VALIDITY_OF_PERSONAL_CHAT_SUCCESS:
      return {
        ...state,
        isChatValid: !!data?.length,
      };
    case CHECK_VALIDITY_OF_PERSONAL_CHAT_FAIL:
      return {
        ...state,
        isChatValid: false,
      };
    case UPDATE_MESSAGE_COUNT_PERSONAL:
      return {
        ...state,
        personalMessageCount: calculateMessageCount(
          data,
          state.personalMessageCount
        ),
      };
    case UPDATE_MESSAGE_COUNT_TEAM:
      return {
        ...state,
        teamMessageCount: calculateMessageCount(data, state.teamMessageCount),
      };
    case GET_PERSONAL_TAB_COUNT_SUCCESS:
      return {
        ...state,
        tabCountPagePersonal:
          totalRecords > state.tabCountPagePersonal
            ? totalRecords
            : state.tabCountPagePersonal,
        personalMessageCount:
          totalRecords <= state.tabCountPagePersonal
            ? data?.map((data: any) => data?._id) || null
            : state.personalMessageCount,
      };
    case GET_TEAMS_TAB_COUNT_SUCCESS:
      return {
        ...state,
        tabCountPageTeams:
          totalRecords > state.tabCountPageTeams
            ? totalRecords
            : state.tabCountPageTeams,
        teamMessageCount:
          totalRecords <= state.tabCountPageTeams
            ? data?.map((data: any) => data?._id) || null
            : state.teamMessageCount,
      };
    case GET_SELECTED_NOTFICATION_REQUEST:
      return {
        ...state,
        notificationSelectedLoading: true,
        isNotificationSelected: false,
      };
    case RESET_SELECTED_NOTIFICATION:
    case GET_SELECTED_NOTFICATION_FAIL:
      return {
        ...state,
        notificationSelectedLoading: false,
        isNotificationSelected: false,
        notificationSelectedMessageChatInfo: [],
        notificationSelectedFormatedMessages: null,
      };
    case GET_SELECTED_NOTFICATION_SUCCESS:
      return {
        ...state,
        notificationSelectedLoading: false,
        isNotificationSelected: true,
        notificationSelectedMessageChatInfo: data,
        selectedMessageType: data?.[0]?.type || messageTypes.PERSONAL,
      };
    case SET_SELECTED_MESSAGE_NOTIFICATION:
      return {
        ...state,
        notificationSelectedFormatedMessages: customInput,
      };
    case SET_REAL_TIME_PREVIEW_IMAGE:
      return {
        ...state,
        selectedUsersMessageContent: pushCompressedImage(
          state.selectedUsersMessageContent,
          data
        ),
      };
    case SET_PREV_MESSAGE_NOTIFICATION:
      return {
        ...state,
        notificationPrevMessageChatInfo: customInput,
      };
    case SET_REAL_TIME_VIDEO_PREVIEW_IMAGE:
      return {
        ...state,
        selectedUsersMessageContent: pushCompressedImage(
          state.selectedUsersMessageContent,
          data
        ),
      };
    case GET_TEAMS_CHAT_MEMBER_INFO_SUCCESS:
      return {
        ...state,
        pastMessageChatMemberInfo: data || null,
      };
    case PAST_MESSAGE_CHAT_MEMBER_INFO_RESET:
      return {
        ...state,
        pastMessageChatMemberInfo: null,
        pastMessageChatMemberInfoLoading: false,
        pastMessageChatMemberInfoTotalRecords: 0,
        pastMessageChatMemberInfoPage: 1,
        pastMessageChatMemberInfoFailLoading: false,
      };
    default:
      return state;
  }
};
const pushCompressedImage = (
  currentData: selectedUsersMessageContentType | null,
  compressedImage: any
) => {
  let updatedData = currentData;
  const uniqueKey = compressedImage?.uniqueKey;
  const message = compressedImage?.message;

  const currentIndex = getArrayIndexUsingKey(
    updatedData?.[uniqueKey] || [],
    '_id',
    message?.messageId
  );
  if (currentIndex >= 0) {
    updatedData?.[uniqueKey]?.map(
      (singleMessage: singleMessageContentTypes) => {
        if (singleMessage?._id === message?.messageId) {
          if (
            message?.socketMessageType ===
            socketMessageTypes.IMAGE_MESSAGE_COMPRESSED
          ) {
            singleMessage.previewImage = message?.previewImage;
          } else if (
            message?.socketMessageType ===
            socketMessageTypes.VIDEO_MESSAGE_PREVIEW_IMAGE_GENERATED
          ) {
            singleMessage.previewVideoImage = message?.previewImage;
          }
        }
        return singleMessage;
      }
    );
  }

  return updatedData;
};
const calculateMessageCount = (
  msgObject: any,
  unreadMessageArray: string[] | null
) => {
  const updatedArray = unreadMessageArray || [];
  !updatedArray?.includes(msgObject?.chatId) &&
    updatedArray?.push(msgObject?.chatId);
  return updatedArray;
};

const addNewForwardChatToTop = (pastData: any, data: any) => {
  return [data, ...(pastData || [])];
};

const moveChatOrder = (pastData: pastMessageChatInfo[] | null, data: any) => {
  const newArrangeData: pastMessageChatInfo[] | null = pastData;
  const currentIndex = getArrayIndexUsingKey(pastData || [], '_id', data);
  if (currentIndex > 0) {
    const movedObject: pastMessageChatInfo | null =
      newArrangeData?.[currentIndex] || null;

    newArrangeData?.splice(currentIndex, 1) || null;
    movedObject && newArrangeData?.unshift(movedObject);
  }
  return newArrangeData;
};

const setPastUserDetailsFoForNewChat = (
  pastData: pastMessageChatMemberInfo[] | null,
  data: any
) => {
  const newArrangeData: pastMessageChatMemberInfo[] | null = pastData;
  newArrangeData?.unshift(data?.userDetails);
  return newArrangeData;
};
const setPastChatInfoForNewChat = (
  pastData: pastMessageChatInfo[] | null,
  data: any,
  userId: string
) => {
  let newArrangeData: pastMessageChatInfo[] | null = pastData;

  if (data?.chatInfo) {
    const chatID = data?.chatInfo?._id || '';
    if (chatID) {
      newArrangeData =
        newArrangeData?.filter((item: pastMessageChatInfo) => {
          return item?._id !== chatID;
        }) || [];
    }
    const opponentUserIds = data?.chatInfo?.memberUserIds?.filter(
      (data: string) => data !== userId
    );
    const newChatObject = {
      ...data?.chatInfo,
      opponentUserIds: opponentUserIds,
    };
    newArrangeData?.unshift(newChatObject);
  }

  return newArrangeData;
};

const arrangeLastMessageInfo = (
  msg: singleMessageContentTypes | null,
  pastLatestMsg: singleMessageContentTypes[] | null
) => {
  const currentIndex = getArrayIndexUsingKey(
    pastLatestMsg || [],
    'chatId',
    msg?.chatId
  );
  const newArrangeData: singleMessageContentTypes[] = pastLatestMsg || [];
  if (currentIndex >= 0 && msg) {
    newArrangeData?.splice(currentIndex, 1) || null;
    newArrangeData?.unshift(msg);
  } else {
    msg && newArrangeData?.unshift(msg);
  }
  return newArrangeData;
};

const dumpDataIntoMessageContent = (
  currentData: selectedUsersMessageContentType | null,
  newData: singleMessageContentTypes[] | null,
  id: string
) => {
  const chatID = newData?.[0]?._id || '';

  const duplicateMessage =
    currentData?.[id]?.filter(msg => msg?._id === chatID) || [];

  if (!duplicateMessage?.length) {
    const newArrangedData: selectedUsersMessageContentType | null =
      currentData?.[id]?.length
        ? {
            [id]: [...(newData || []), ...(currentData?.[id] || [])] || null,
          } || null
        : { [id]: [...(newData || [])] || null } || null;
    return newArrangedData;
  } else {
    return currentData;
  }
};

const arrangePastMessageInfoData = (
  currentData: pastMessageChatInfo[] | null,
  newData: pastMessageChatInfo[] | null,
  page: number,
  userId: any
) => {
  const arrangeNewData: pastMessageChatInfo[] | null =
    newData?.map((data: pastMessageChatInfo) => {
      const opponentUserIds =
        data.memberUserIds?.filter((data: string) => data !== userId) || null;
      return {
        ...data,
        opponentUserIds: opponentUserIds?.length ? opponentUserIds : null,
      };
    }) || null;

  return (
    avoidedDuplicationData(currentData, arrangeNewData || [], '_id') || null
  );
};

const setUnReadCount = (
  data: IunreadMessageCountValue[],
  unreadMessageCounts: IunreadMessageCount
) => {
  if (!data?.length) {
    return unreadMessageCounts;
  }
  let tmpUnreadMessageCountValue: IunreadMessageCount = {};

  for (const tmpUnreadMessageCount of data) {
    tmpUnreadMessageCountValue[tmpUnreadMessageCount.chatId] =
      tmpUnreadMessageCount;
  }
  return { ...unreadMessageCounts, ...tmpUnreadMessageCountValue };
};

const combineMessages = (id: string, oldMessages: any, newMessages: any) => {
  const oldMag = oldMessages?.[id] || [];

  return [...oldMag, ...newMessages];
};

export default messageReducer;
