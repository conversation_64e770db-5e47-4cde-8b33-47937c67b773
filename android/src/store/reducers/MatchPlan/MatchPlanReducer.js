import { avoidedDuplicationData } from '../../../helpers';

const {
  MATCH_TYPES_REQUEST,
  MATCH_TYPES_SUCCESS,
  MATCH_TYPES_FAILED,
  MATCH_TYPE_FORMATIONS_REQUEST,
  MATCH_TYPE_FORMATIONS_SUCCESS,
  MATCH_TYPE_FORMATIONS_FAILED,
  MATCH_PLAN_REQUEST,
  MATCH_PLAN_SUCCESS,
  MATCH_PLAN_FAILED,
  MATCH_CANDIDATE_PLAYERS_REQUEST,
  MATCH_CANDIDATE_PLAYERS_SUCCESS,
  MATCH_CANDIDATE_PLAYERS_FAILED,
  MATCH_CANDIDATE_PLAYERS_SEARCH_REQUEST,
  MATCH_CANDIDATE_PLAYERS_SEARCH_SUCCESS,
  MATCH_CANDIDATE_PLAYERS_SEARCH_FAILED,
  MATCH_PLAN_SAVE_REQUEST,
  MATCH_PLAN_SAVE_SUCCESS,
  MATCH_PLAN_SAVE_FAILED,
  MATCH_PLAN_CLEAR_SAVE_STATUS,
  <PERSON>TCH_PLAN_RESET,
  MATCH_PLAYER_RATINGS_REQUEST,
  MATCH_PLAYER_RATINGS_SUCCESS,
  MATCH_PLAYER_RATINGS_FAILED,
  SAVE_PLAYER_RATINGS_REQUEST,
  SAVE_PLAYER_RATINGS_SUCCESS,
  SAVE_PLAYER_RATINGS_FAILED,
  SAVE_PLAYER_RATINGS_RESET,
  SET_MATCH_LOG_VIEW,
  MATCH_PLAYER_RATINGS_RESET,
  GET_INITIAL_MATCH_PLAN_REQUEST,
  GET_INITIAL_MATCH_PLAN_SUCCESS,
  GET_INITIAL_MATCH_PLAN_FAILED,
  GET_LATEST_MATCH_PLAN_REQUEST,
  GET_LATEST_MATCH_PLAN_SUCCESS,
  GET_LATEST_MATCH_PLAN_FAILED,
  GET_INITIAL_RATINGS_REQUEST,
  GET_INITIAL_RATINGS_SUCCESS,
  GET_INITIAL_RATINGS_FAILED,
  REMOVE_SELECTED_MATCH_PLAN,
  SET_SELECTED_MATCH_DETAILS,
} = require('../../actionTypes/MatchPlan/MatchPlanActions');
const initialState = {
  isMatchLogView: false,
  matchFormations: [],
  matchFormationsFailed: false,
  matchFormationsLoading: false,
  matchTypes: [],
  matchTypesFailed: false,
  matchTypesLoading: false,
  matchPlan: {},
  matchPlanLoading: false,
  matchPlanFailed: false,
  matchCandidatePlayers: [],
  matchCandidatePlayersTotalRecord: 0,
  matchCandidatePlayersPage: 1,
  matchCandidatePlayersLoading: false,
  matchCandidatePlayersError: false,
  matchPlanSaveLoading: false,
  matchPlanSaveFailed: false,
  matchPlanSaveSuccess: null,
  matchPlayerRatingsFetching: false,
  matchPlayerRatings: [],
  matchPlayerRatingsTotalsRecord: 0,
  matchPlayerRatingsPage: 1,
  matchPlayerRatingsError: false,
  savePlayerRatingsLoading: false,
  savePlayerRatingsSuccess: false,
  savePlayerRatingsError: false,
  initialMatchPlan: null,
  latestMatchPlan: null,
  isMatchPlanLoad: false,
  initialsPlayerRating: [],
  isInitialsPlayerRatingLoad: false,
  selectedMatchDetails: undefined,
  duplicateSelectedMatchDetails: undefined,
};

const matchPlanReducer = (state = initialState, action) => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  switch (action.type) {
    case MATCH_TYPES_REQUEST:
      return {
        ...state,
        matchTypes: [],
        matchTypesLoading: true,
        matchTypesFailed: false,
      };

    case MATCH_TYPES_SUCCESS:
      return {
        ...state,
        matchTypesLoading: false,
        matchTypesFailed: false,
        matchTypes: action.payload,
      };

    case MATCH_TYPES_FAILED:
      return {
        ...state,
        matchTypesLoading: false,
        matchTypesFailed: true,
      };

    case MATCH_TYPE_FORMATIONS_REQUEST:
      return {
        ...state,
        matchFormations: [],
        matchFormationsLoading: true,
        matchFormationsFailed: false,
      };

    case MATCH_TYPE_FORMATIONS_SUCCESS:
      return {
        ...state,
        matchFormationsLoading: false,
        matchFormationsFailed: false,
        matchFormations: action.payload,
      };

    case MATCH_TYPE_FORMATIONS_FAILED:
      return {
        ...state,
        matchFormationsLoading: false,
        matchFormationsFailed: true,
      };

    case MATCH_PLAN_REQUEST:
      return {
        ...state,
        matchPlan: {},
        matchPlanLoading: true,
        matchPlanFailed: false,
      };
    case MATCH_PLAN_SUCCESS:
      return {
        ...state,
        matchPlan: action.payload,
        matchPlanLoading: false,
        matchPlanFailed: false,
      };

    case MATCH_PLAN_FAILED:
      return {
        ...state,
        matchPlanLoading: false,
        matchPlanFailed: true,
      };
    case MATCH_CANDIDATE_PLAYERS_REQUEST:
      return {
        ...state,
        matchCandidatePlayersLoading: true,
        matchCandidatePlayersError: false,
      };
    case MATCH_CANDIDATE_PLAYERS_SUCCESS:
      return {
        ...state,
        matchCandidatePlayers: handleMatchCandidatePlayers(
          state?.matchCandidatePlayers,
          data,
          'load'
        ),
        matchCandidatePlayersTotalRecord:
          totalRecords || state?.matchCandidatePlayersTotalRecord,
        matchCandidatePlayersPage: page || state?.matchCandidatePlayersPage,
        matchCandidatePlayersLoading: false,
        matchCandidatePlayersError: false,
      };
    case MATCH_CANDIDATE_PLAYERS_FAILED:
      return {
        ...state,
        matchCandidatePlayersLoading: false,
        matchCandidatePlayersError: true,
      };
    case MATCH_CANDIDATE_PLAYERS_SEARCH_REQUEST:
      return {
        ...state,
        matchCandidatePlayersLoading: true,
        matchCandidatePlayersError: false,
      };
    case MATCH_CANDIDATE_PLAYERS_SEARCH_SUCCESS:
      return {
        ...state,
        matchCandidatePlayers: handleMatchCandidatePlayers(
          state?.matchCandidatePlayers,
          data,
          'search'
        ),
        matchCandidatePlayersTotalRecord:
          totalRecords || state?.matchCandidatePlayersTotalRecord,
        matchCandidatePlayersPage: page || state?.matchCandidatePlayersPage,
        matchCandidatePlayersLoading: false,
        matchCandidatePlayersError: false,
      };
    case MATCH_CANDIDATE_PLAYERS_SEARCH_FAILED:
      return {
        ...state,
        matchCandidatePlayersLoading: false,
        matchCandidatePlayersError: true,
      };
    case MATCH_PLAN_SAVE_REQUEST:
      return {
        ...state,
        matchPlanSaveLoading: true,
        matchPlanSaveFailed: false,
        matchPlanSaveSuccess: null,
      };
    case MATCH_PLAN_SAVE_SUCCESS:
      return {
        ...state,
        matchPlanSaveLoading: false,
        matchPlanSaveFailed: false,
        matchPlanSaveSuccess: true,
      };
    case MATCH_PLAN_SAVE_FAILED:
      return {
        ...state,
        matchPlanSaveLoading: false,
        matchPlanSaveFailed: true,
        matchPlanSaveSuccess: false,
      };
    case MATCH_PLAN_CLEAR_SAVE_STATUS:
      return {
        ...state,
        matchPlanSaveSuccess: null,
        matchPlanSaveFailed: false,
        matchPlanSaveLoading: false,
      };
    // Player ratings for match
    case MATCH_PLAYER_RATINGS_REQUEST:
      return {
        ...state,
        matchPlayerRatingsFetching: true,
        matchPlayerRatings: [],
        matchPlayerRatingsError: false,
      };
    case MATCH_PLAYER_RATINGS_SUCCESS:
      return {
        ...state,
        matchPlayerRatingsFetching: false,
        matchPlayerRatings: organizePlayerRatings(action?.payload?.data),
        matchPlayerRatingsError: false,
      };
    case MATCH_PLAYER_RATINGS_FAILED:
      return {
        ...state,
        matchPlayerRatingsFetching: false,
        matchPlayerRatings: null,
        matchPlayerRatingsError: true,
      };
    case MATCH_PLAYER_RATINGS_RESET:
      return {
        ...state,
        matchPlayerRatingsFetching: false,
        matchPlayerRatings: [],
        matchPlayerRatingsError: false,
        initialMatchPlan: null,
        isMatchPlanLoad: false,
        initialsPlayerRating: [],
      };
    case SAVE_PLAYER_RATINGS_REQUEST:
      return {
        ...state,
        savePlayerRatingsLoading: true,
        savePlayerRatingsSuccess: false,
        savePlayerRatingsError: false,
      };
    case SAVE_PLAYER_RATINGS_SUCCESS:
      return {
        ...state,
        savePlayerRatingsLoading: false,
        savePlayerRatingsSuccess: true,
        savePlayerRatingsError: false,
      };
    case SAVE_PLAYER_RATINGS_FAILED:
      return {
        ...state,
        savePlayerRatingsLoading: false,
        savePlayerRatingsSuccess: false,
        savePlayerRatingsError: true,
      };
    case SAVE_PLAYER_RATINGS_RESET:
      return {
        ...state,
        savePlayerRatingsLoading: false,
        savePlayerRatingsSuccess: false,
        savePlayerRatingsError: false,
      };

    case MATCH_PLAN_RESET:
      return initialState;

    case SET_MATCH_LOG_VIEW:
      return {
        ...state,
        isMatchLogView: action.payload,
      };

    case GET_INITIAL_MATCH_PLAN_REQUEST:
      return {
        ...state,
        initialMatchPlan: null,
        isMatchPlanLoad: false,
      };
    case GET_INITIAL_MATCH_PLAN_SUCCESS:
      return {
        ...state,
        initialMatchPlan: action.payload,
        isMatchPlanLoad: true,
      };
    case GET_INITIAL_MATCH_PLAN_FAILED:
      return {
        ...state,
        initialMatchPlan: null,
        isMatchPlanLoad: true,
      };
    case GET_LATEST_MATCH_PLAN_SUCCESS:
      return {
        ...state,
        latestMatchPlan: action.payload,
      };

    case GET_INITIAL_RATINGS_REQUEST:
      return {
        ...state,
        matchPlayerRatingsFetching: true,
      };
    case GET_INITIAL_RATINGS_SUCCESS:
      return {
        ...state,
        initialsPlayerRating: data || [],
        matchPlayerRatingsTotalsRecord: action?.payload?.totalRecords || 0,
        isInitialsPlayerRatingLoad: true,
        matchPlayerRatingsFetching: true,
      };
    case GET_INITIAL_RATINGS_FAILED:
      return {
        ...state,
        matchPlayerRatingsFetching: true,
      };
    case SET_SELECTED_MATCH_DETAILS:
      return {
        ...state,
        selectedMatchDetails: action.payload,
        duplicateSelectedMatchDetails: action.payload,
      };
    case REMOVE_SELECTED_MATCH_PLAN:
      return {
        ...state,
        selectedMatchDetails: undefined,
      };
    default:
      return state;
  }
};

const organizePlayerRatings = data => {
  if (!data) {
    return [];
  }

  let isMissingUserKey = data.some(value => !value.hasOwnProperty('user'));

  if (!isMissingUserKey) {
    const res = data?.map(rating => ({
      ...rating,
      ...rating['user'],
    }));
    return res || [];
  }

  return data || [];
};

const handleMatchCandidatePlayers = (matchCandidatePlayers, data, type) => {
  if (type === 'search') {
    const filteredList = avoidedDuplicationData([], data || [], 'userId');
    return [...filteredList];
  } else {
    const filteredList = avoidedDuplicationData(
      matchCandidatePlayers || [],
      data || [],
      'userId'
    );
    return [...filteredList];
  }
};

export default matchPlanReducer;
