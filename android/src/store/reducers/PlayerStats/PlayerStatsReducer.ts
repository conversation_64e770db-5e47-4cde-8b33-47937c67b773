import {
  PLAYER_STATS_FAIL,
  PLAYER_STATS_REQUEST,
  PLAYER_STATS_SUCCESS,
  RESET_PLAYER_STATS,
  PLAYER_STATS_SEASON_REQUEST,
  PLAYER_STATS_SEASON_SUCCESS,
  PLAYER_STATS_SEASON_FAIL,
  P<PERSON>YER_STATS_TOURNAMENT_REQUEST,
  PLAYER_STATS_TOURNAMENT_SUCCESS,
  PLAYER_STATS_TOURNAMENT_FAIL,
  PLAYER_STATS_TOTAL_TABLE_DATA_REQUEST,
  PLAYER_STATS_TOTAL_TABLE_DATA_SUCCESS,
  PLAYER_STATS_TOTAL_TABLE_DATA_FAIL,
  RESET_TABLE_DATA,
  PLAYER_STATS_GET_TEAMS_REQUEST,
  PLAYER_STATS_GET_TEAMS_SUCCESS,
  PLAYER_STATS_GET_TEAMS_FAIL,
  <PERSON>LA<PERSON><PERSON>_STATS_GET_OPPONENT_REQUEST,
  P<PERSON>Y<PERSON>_STATS_GET_OPPONENT_SUCCESS,
  P<PERSON>Y<PERSON>_STATS_GET_OPPONENT_FAIL,
} from '../../actionTypes/PlayerStats/PlayerStatsActionTypes';

type Action = {
  type: string;
  payload: {
    data?: any;
    totalRecords?: number;
    page?: number;
    size?: number;
    customInput: any;
  };
};

export interface playerStatsDataType {
  minutes: Number;
  goals: Number;
  assists: Number;
  yellowCard: Number;
  redCard: Number;
  rating: Number | string;
  appearances: Number;
  season: string;
  tournament: string;
}

export interface seasonsType {
  _id: string;
  name: string;
}

export interface tournamentsType {
  _id: string;
  name: string;
}

export interface opponentType {
  _id: string;
  name: string;
}

export interface teamType {
  _id?: string;
  name?: string;
}

export interface tableDataType {
  durationInMinutes: number;
  goals: number;
  assists: number;
  yellowCard: number;
  redCard: number;
  rating: number | string;
  appearances: number;
  season: string;
  tournament: string;
}
export interface tableTotalDataType {
  durationInMinutes: 50;
  goals: 2;
  assists: 1;
  yellowCard: 0;
  redCard: 1;
  rating: 0.0;
  appearances: 0;
}

export interface playerStatsType {
  playerStatsData: playerStatsDataType[] | null;
  playerStatsLoading: boolean;
  seasonsData?: seasonsType[];
  tournamentsData?: tournamentsType[];
  teamData?: teamType[];
  opponentData?: opponentType[];
  tableDataLoading: boolean;
  tableDataError: boolean;
  tableTotalData?: tableTotalDataType | {};
  aggregateData?: tableDataType[];
  matchWiseData?: tableDataType[];
  averageData?: tableDataType[];
  aggregateTotalRecords: number;
  aggregatePage: number;
  averageTotalRecords: number;
  averagePage: number;
  matchWiseTotalRecords: number;
  matchWisePage: number;
}

const INITIAL_STATE: playerStatsType = {
  playerStatsData: null,
  playerStatsLoading: false,
  seasonsData: [],
  tournamentsData: [],
  teamData: [],
  opponentData: [],
  aggregateData: [],
  aggregateTotalRecords: 0,
  aggregatePage: 1,
  averageData: [],
  averageTotalRecords: 0,
  averagePage: 1,
  matchWiseData: [],
  matchWiseTotalRecords: 0,
  matchWisePage: 1,
  tableDataLoading: false,
  tableDataError: false,
  tableTotalData: {},
};

const playerStatsReducer = (
  state = INITIAL_STATE,
  action: Action
): playerStatsType => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  const customInput = action?.payload?.customInput;

  switch (action.type) {
    case PLAYER_STATS_TOTAL_TABLE_DATA_SUCCESS:
      return { ...state, tableTotalData: action?.payload || {} };
    case PLAYER_STATS_REQUEST:
      return {
        ...state,
        tableDataLoading: true,
        tableDataError: false,
      };
    case 'PLAYER_STATS_SUCCESS_CUSTOM_CONTENT':
      return {
        ...state,
        ...renderTableData(state, data, customInput),
        tableDataLoading: false,
      };
    case PLAYER_STATS_FAIL:
      return {
        ...state,
        tableDataLoading: false,
        tableDataError: true,
        aggregateData: [],
        averageData: [],
        matchWiseData: [],
      };
    case PLAYER_STATS_TOURNAMENT_SUCCESS:
      return { ...state, tournamentsData: data || [] };
    case PLAYER_STATS_SEASON_SUCCESS:
      return { ...state, seasonsData: data || [] };
    case PLAYER_STATS_GET_TEAMS_SUCCESS:
      return { ...state, teamData: data || [] };
    case PLAYER_STATS_GET_OPPONENT_SUCCESS:
      return { ...state, opponentData: data || [] };
    case PLAYER_STATS_REQUEST:
      return {
        ...state,
        playerStatsLoading: true,
      };
    case PLAYER_STATS_SUCCESS:
      return {
        ...state,
        playerStatsData: data || null,
        playerStatsLoading: false,
      };
    case PLAYER_STATS_FAIL:
      return {
        ...state,
        playerStatsLoading: false,
      };
    case RESET_PLAYER_STATS:
      return INITIAL_STATE;
    case RESET_TABLE_DATA:
      return {
        ...state,
        aggregateData: [],
        aggregatePage: 1,
        aggregateTotalRecords: 0,
        averageData: [],
        averagePage: 1,
        averageTotalRecords: 0,
        matchWiseData: [],
        matchWisePage: 1,
        matchWiseTotalRecords: 0,
      };
    default:
      return state;
  }
};

const getDataType = (customInput: string) =>
  customInput === '1'
    ? 'aggregateData'
    : customInput === '2'
    ? 'averageData'
    : 'matchWiseData';

const getTotalRecordsType = (customInput: string) =>
  customInput === '1'
    ? 'aggregateTotalRecords'
    : customInput === '2'
    ? 'averageTotalRecords'
    : 'matchWiseTotalRecords';

const getPageType = (customInput: string) =>
  customInput === '1'
    ? 'aggregatePage'
    : customInput === '2'
    ? 'averagePage'
    : 'matchWisePage';

const renderTableData = (state: any, data: any, customInput: string) => {
  const dataType = getDataType(customInput);
  const totalRecordsType = getTotalRecordsType(customInput);
  const pageType = getPageType(customInput);

  return {
    [dataType]: [...(state?.[dataType] || []), ...(data?.data || [])],
    [totalRecordsType]: data?.totalRecords || state?.[totalRecordsType],
    [pageType]: data?.page || state?.[pageType],
  };
};

export default playerStatsReducer;
