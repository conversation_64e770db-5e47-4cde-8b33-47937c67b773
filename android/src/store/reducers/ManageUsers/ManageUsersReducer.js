import { convertArrayToObject } from '../../../utils/common';
import {
  CREATE_TEAM_FAILED,
  CREATE_TEAM_REQUEST,
  CREATE_TEAM_SUCCESS,
  FETCH_ALL_USERS_FAIL,
  FETCH_ALL_USERS_RESET,
  FETCH_ALL_USERS_REQUEST,
  FETCH_ALL_USERS_SUCCESS,
  RETRIEVE_TEAM_LABELS_FOR_USER_REQUEST,
  RETRIEVE_TEAM_LABELS_FOR_USER_FAILED,
  RETRIEVE_TEAM_LABELS_FOR_USER_CUSTOM_CONTENT,
  FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_REQUEST,
  FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_SUCCESS,
  FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_FAILED,
  UPDATE_TEAM_FAILED,
  UPDATE_TEAM_REQUEST,
  UPDATE_TEAM_SUCCESS,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_FAIL,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_RESET,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_REQUEST,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_SUCCESS,
  MANAGE_USERS_RESET,
  CHANGE_PARAMS_TYPE,
  FETCH_ALL_USERS_SUCCESS_CUSTOM_CONTENT,
  DELETE_TEAM_REQUEST,
  DELETE_TEAM_SUCCESS,
  DELETE_TEAM_FAILED,
  DELETE_USER_REQUEST,
  DELETE_USER_SUCCESS,
  DELETE_USER_FAIL,
  CREATE_TEAM_RESET,
  DELETE_TEAM_RESET,
  DELETE_USER_RESET,
  FETCH_ONGOING_RECURRING_PLAN_SUCCESS,
  ONGOING_RECURRING_PLAN_RESET,
  FETCH_CHILDREN_REQUEST,
  FETCH_CHILDREN_SUCCESS,
  FETCH_CHILDREN_FAIL,
} from '../../actionTypes/ManageUsers/ManageUsersAction';

const initialState = {
  allUsersLoading: false,
  allUsers: null,
  allUsersError: null,
  createTeamLoading: false,
  createTeamError: false,
  createTeamErrorMessage: '',
  createTeamSuccess: null,
  allUsersPageNo: 0,
  allUsersTotalRecords: 0,
  stopFetchingUsers: false,
  teamUpdateLoading: false,
  teamUpdateError: false,
  teamUpdateSuccess: null,
  allTeamsLoading: false,
  allTeams: null,
  allTeamsPageNo: 0,
  allTeamsTotalRecords: 0,
  allTeamsError: null,
  selectedTeams: null,
  stopFetchingTeams: false,
  searchParams: [],
  teamDeleteLoading: false,
  teamDeleteSuccess: null,
  teamDeleteError: false,
  teamDeleteErrorMessage: '',
  deleteUserLoading: false,
  deleteUserError: false,
  deleteUserSuccess: null,
  deleteUserErrorMessage: '',
  isOngoingRecurringPlanAvilable : false,
  isOngoingRecurringPlanAvilableSuccess : false,
  childrenList:null
};

const ManageUsersReducer = (state = initialState, action) => {
  switch (action.type) {
    case CHANGE_PARAMS_TYPE:
      return {
        ...state,
        searchParams: action?.payload,
      };

    case FETCH_ALL_USERS_REQUEST:
      return {
        ...state,
        allUsersLoading: true,
        allUsersError: null,
      };

    case FETCH_ALL_USERS_SUCCESS_CUSTOM_CONTENT:
      const isMatched =
        JSON.stringify(action?.payload?.customInput?.searchParams) ===
        JSON.stringify(state.searchParams);

      return {
        ...state,
        allUsersLoading: isMatched ? false : state.allUsersLoading,
        allUsers: isMatched
          ? action?.payload?.data?.data
            ? [...(state.allUsers || []), ...action?.payload?.data?.data]
            : [...(state.allUsers || [])]
          : [...(state.allUsers || [])],
        allUsersPageNo: isMatched
          ? action?.payload?.data
            ? action?.payload?.data?.page
            : state.allUsersPageNo
          : state.allUsersPageNo,
        allUsersTotalRecords: isMatched
          ? action?.payload?.data
            ? action?.payload?.data?.totalRecords
            : state.allUsersTotalRecords
          : state.allUsersTotalRecords,
        allUsersError: null,
        stopFetchingUsers: action?.payload?.data?.data === undefined,
      };

    case FETCH_ALL_USERS_RESET:
      return {
        ...state,
        allUsersLoading: false,
        allUsers: null,
        allUsersPageNo: 0,
        allUsersTotalRecords: 0,
        allUsersError: null,
        stopFetchingUsers: false,
      };

    case FETCH_ALL_USERS_FAIL:
      return {
        ...state,
        allUsersLoading: false,
        allUsersError: action?.payload,
      };
    case CREATE_TEAM_REQUEST:
      return {
        ...state,
        createTeamLoading: true,
        createTeamError: false,
        createTeamSuccess: null,
        createTeamErrorMessage: '',
      };
    case CREATE_TEAM_SUCCESS:
      return {
        ...state,
        createTeamLoading: false,
        createTeamError: false,
        createTeamSuccess: true,
        createTeamErrorMessage: '',
      };
    case CREATE_TEAM_FAILED:
      return {
        ...state,
        createTeamLoading: false,
        createTeamError: true,
        createTeamSuccess: false,
        createTeamErrorMessage: action?.payload,
      };
    case CREATE_TEAM_RESET:
      return {
        ...state,
        createTeamLoading: false,
        createTeamError: false,
        createTeamSuccess: null,
        createTeamErrorMessage: '',
      };

    case RETRIEVE_TEAM_LABELS_FOR_USER_CUSTOM_CONTENT:
      return {
        ...state,
        allUsers: addTeamLabelsToUser(action?.payload, state.allUsers),
      };

    case RETRIEVE_TEAM_LABELS_FOR_USER_REQUEST:
      return {
        ...state,
      };

    case RETRIEVE_TEAM_LABELS_FOR_USER_FAILED:
      return {
        ...state,
      };

    case FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_REQUEST:
      return {
        ...state,
      };

    case FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_SUCCESS:
      return {
        ...state,
        selectedTeams: addTeamToUsers(action?.payload, state.allUsers),
      };

    case FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_FAILED:
      return {
        ...state,
      };

    case UPDATE_TEAM_REQUEST:
      return {
        ...state,
        teamUpdateLoading: true,
        teamUpdateError: false,
        teamUpdateSuccess: null,
      };
    case UPDATE_TEAM_SUCCESS:
      return {
        ...state,
        teamUpdateLoading: false,
        teamUpdateError: false,
        teamUpdateSuccess: true,
      };
    case UPDATE_TEAM_FAILED:
      return {
        ...state,
        teamUpdateLoading: false,
        teamUpdateError: true,
        teamUpdateSuccess: false,
      };

    case FETCH_TEAMS_FOR_USER_MANAGEMENT_REQUEST:
      return {
        ...state,
        allTeamsLoading: true,
        allTeamsError: null,
      };

    case FETCH_TEAMS_FOR_USER_MANAGEMENT_SUCCESS:
      return {
        ...state,
        allTeamsLoading: false,
        allTeams: action.payload?.data?.length
          ? [...(state.allTeams || []), ...action.payload?.data]
          : [...(state.allTeams || [])],
        allTeamsPageNo: action?.payload?.data
          ? action?.payload?.page
          : state.allTeamsPageNo,
        allTeamsTotalRecords: action?.payload?.data
          ? action?.payload?.totalRecords
          : state.allTeamsTotalRecords,
        allTeamsError: null,
        stopFetchingTeams: action?.payload?.data === undefined,
      };

    case FETCH_TEAMS_FOR_USER_MANAGEMENT_RESET:
      return {
        ...state,
        allTeamsLoading: false,
        allTeams: null,
        allTeamsPageNo: 0,
        allTeamsTotalRecords: 0,
        allTeamsError: null,
        stopFetchingTeams: false,
      };

    case FETCH_TEAMS_FOR_USER_MANAGEMENT_FAIL:
      return {
        ...state,
        allTeamsLoading: false,
        allTeamsError: action?.payload,
      };

    case MANAGE_USERS_RESET:
      return {
        ...initialState,
      };

    case DELETE_TEAM_REQUEST:
      return {
        ...state,
        teamDeleteLoading: true,
        teamDeleteError: false,
        teamDeleteSuccess: null,
        teamDeleteErrorMessage: '',
      };
    case DELETE_TEAM_SUCCESS:
      return {
        ...state,
        teamDeleteLoading: false,
        teamDeleteError: false,
        teamDeleteSuccess: true,
        teamDeleteErrorMessage: '',
      };
    case DELETE_TEAM_FAILED:
      return {
        ...state,
        teamDeleteLoading: false,
        teamDeleteError: true,
        teamDeleteSuccess: false,
        teamDeleteErrorMessage: action.payload,
      };
    case DELETE_TEAM_RESET:
      return {
        ...state,
        teamDeleteLoading: false,
        teamDeleteError: false,
        teamDeleteSuccess: false,
        teamDeleteErrorMessage: '',
      };

    case DELETE_USER_REQUEST:
      return {
        ...state,
        deleteUserLoading: true,
        deleteUserError: false,
        deleteUserSuccess: null,
        deleteUserErrorMessage: '',
      };
    case DELETE_USER_SUCCESS:
      return {
        ...state,
        deleteUserLoading: false,
        deleteUserError: false,
        deleteUserSuccess: true,
        deleteUserErrorMessage: '',
      };
    case DELETE_USER_FAIL:
      return {
        ...state,
        deleteUserLoading: false,
        deleteUserError: true,
        deleteUserSuccess: false,
        deleteUserErrorMessage: action.payload,
      };
    case DELETE_USER_RESET:
      return {
        ...state,
        deleteUserLoading: false,
        deleteUserError: false,
        deleteUserSuccess: false,
        deleteUserErrorMessage: '',
      };
    case FETCH_ONGOING_RECURRING_PLAN_SUCCESS:
      return {
        ...state,
        isOngoingRecurringPlanAvilableSuccess: true,
        isOngoingRecurringPlanAvilable: action.payload?.data?.length
          ? true
          : false,
      };
    case ONGOING_RECURRING_PLAN_RESET:
      return {
        ...state,
        isOngoingRecurringPlanAvilableSuccess: false,
        isOngoingRecurringPlanAvilable: false,
      };
    case FETCH_CHILDREN_SUCCESS:
      return {
        ...state,
       childrenList: action.payload?.data
      };
    default:
      return state;
  }
};

const addTeamLabelsToUser = (teamData, allUsers) => {
  allUsers?.forEach(userData => {
    if (teamData?.customInput === userData.id) {
      const teamDataArray = teamData?.data?.data;

      if (teamDataArray?.length) {
        userData.teamLabels = teamDataArray?.map(
          ({ teamName, _id }, index) => ({
            teamName:
              index !== teamDataArray?.length - 1 ? teamName + ' | ' : teamName,
            _id,
          })
        );
      } else {
        userData.teamLabels = 'Team Not Assigned';
      }
    }
  });
  return allUsers;
};

const addTeamToUsers = (teamData, allUsers) => {
  const teamDataArray = teamData?.data || [];
  const convertedObject = convertArrayToObject(teamDataArray);

  return allUsers.map(userData => {
    if (convertedObject[userData.id]) {
      userData.teamLabels = convertedObject[userData.id];
    } else {
      userData.teamLabels = 'Team Not Assigned';
    }
    return userData;
  });
};

export default ManageUsersReducer;
