import { teamInitialSize } from '../../../constants/constants';
import {
  TEAM_REQUEST,
  TEAM_SUCCESS,
  TEAM_FAIL,
  MORE_TEAM_SUCCESS,
  SET_SELECTED_TEAM,
  SET_SELECTED_TEAM_REQUEST,
  MORE_TEAM_REQUEST,
  MORE_TEAM_FAIL,
  RESET_TEAM_LABEL,
  RESET_TEAM,
  RESET_TEAMS,
  GET_CHILD_TEAM_DATA_REQUEST,
  GET_CHILD_TEAM_DATA_SUCCESS,
  GET_CHILD_TEAM_DATA_FAILED,
  TRIGGER_IAP_TAB_CLICK,
} from '../../actionTypes/Team/TeamAction';
import {
  USER_TYPE_REQUEST,
  USER_TYPE_SUCCESS,
} from '../../actionTypes/User/User';
const initialState = {
  teamIdData: '',
  playerLabelIdData: '',
  teamNameData: '',
  teamIdData_error: null,
  teamData: '',
  teamData_error: null,
  teamInitialPage: 1,
  teamInitialSize: teamInitialSize,
  teamPage: 1,
  teamSize: 1,
  teamTotalRecords: 0,
  selectedTeam: null,
  userTypeData: 'player',
  teamDataLoading: false,
  moreTeamsLoading: false,
  childTeamData: [],
};
import {
  TEAM_ID_REQUEST,
  TEAM_ID_SUCCESS,
  TEAM_ID_FAIL,
  SET_PLAYER_LABEL_ID,
} from '../../actionTypes/TeamID/TeamID';

const TeamReducer = (state = initialState, action) => {
  const { data, page, size, totalRecords } = action?.payload || {};
  switch (action.type) {
    case GET_CHILD_TEAM_DATA_REQUEST:
      return {
        ...state,
        teamData: { data: [] },
        teamData_error: null,
        teamDataLoading: true,
      };
    case GET_CHILD_TEAM_DATA_SUCCESS:
      return {
        ...state,
        teamData: data ? { data } : state.teamData,
        teamPage: page || state.teamPage,
        teamSize: size || state.teamSize,
        teamTotalRecords: totalRecords || 0,
        teamDataLoading: false,
      };
    case GET_CHILD_TEAM_DATA_FAILED:
      return { ...state, teamDataLoading: false };
    case TRIGGER_IAP_TAB_CLICK:
      return {
        ...state,
        playerLabelIdData: 2,
      };

    case TEAM_REQUEST:
      return {
        ...state,
        teamData: { data: [] },
        teamData_error: null,
        teamDataLoading: true,
      };
    case MORE_TEAM_REQUEST:
      return {
        ...state,
        moreTeamsLoading: true,
      };
    case TEAM_SUCCESS:
      return {
        ...state,
        teamData: data ? { data } : state.teamData,
        teamPage: page || state.teamPage,
        teamSize: size || state.teamSize,
        teamTotalRecords: totalRecords || 0,
        teamDataLoading: false,
      };

    case MORE_TEAM_SUCCESS:
      return {
        ...state,
        teamData: data
          ? { data: [...state.teamData.data, ...data] }
          : state.teamData,
        teamPage: page || state.teamPage,
        teamSize: size || state.teamSize,
        teamDataLoading: false,
        moreTeamsLoading: false,
      };

    case TEAM_FAIL:
      return {
        ...state,
        teamData_error: action.payload,
        teamDataLoading: false,
      };
    case MORE_TEAM_FAIL:
      return {
        ...state,
        moreTeamsLoading: false,
      };

    case RESET_TEAM:
      return {
        ...initialState,
      };

    case RESET_TEAM_LABEL:
      return {
        ...initialState,
      };

    case SET_SELECTED_TEAM_REQUEST:
      return {
        ...state,
        selectedTeam: null,
      };
    case SET_SELECTED_TEAM:
      return {
        ...state,
        selectedTeam: action?.payload,
      };
    //Team ID and Name
    case TEAM_ID_REQUEST:
      return {
        ...state,
        teamIdData: '',
        teamIdData_error: null,
      };
    case TEAM_ID_SUCCESS:
      return {
        ...state,
        teamIdData: action.TeamIdData,
        teamNameData: action.TeamNameData,
      };

    case SET_PLAYER_LABEL_ID:
      return {
        ...state,
        playerLabelIdData: action.PlayerLabelId,
      };

    case TEAM_ID_FAIL:
      return {
        ...state,
        teamIdData_error: action.payload,
      };

    case USER_TYPE_REQUEST:
      return {
        ...state,
        userTypeData: null,
      };
    case USER_TYPE_SUCCESS:
      return {
        ...state,
        userTypeData: action?.userTypeData,
      };

    case RESET_TEAMS:
      return { ...initialState, selectedTeam: state.selectedTeam };

    default:
      return state;
  }
};

export default TeamReducer;
