import {
  UPCOMING_MATCHES_REQUEST,
  UPCOMING_MATCHES_SUCCESS,
  UPCOMING_MATCHES_FAIL,
  UPCOMING_TRAINING_REQUEST,
  UPCOMING_TRAINING_SUCCESS,
  UPCOMING_TRAINING_FAIL,
  SEASON_UPDATE_REQUEST,
  SEASON_UPDATE_SUCCESS,
  SEASON_UPDATE_FAIL,
} from '../../actionTypes/SeasonUpdate/SeasonUpdateAction';

const initailState = {
  UpcomingMatchesData: {},
  UpcomingTrainingData: {},
  SeasonUpdatesData: {},
  SeasonUpdatesData_error: null,
  UpcomingMatchesData_error: null,
  UpcomingTrainingData_error: null,
  UpcomingMatchesLoading: false,
  UpcomingTrainingLoading: false,
  SeasonUpdatesLoading: false,
};

const SeasonUpdateReducer = (state = initailState, action) => {
  switch (action.type) {
    case UPCOMING_MATCHES_REQUEST:
      return {
        ...state,
        UpcomingMatchesData: {},
        UpcomingMatchesLoading: true,
      };
    case UPCOMING_MATCHES_SUCCESS:
      return {
        ...state,
        UpcomingMatchesData: action?.payload,
        UpcomingMatchesLoading: false,
      };
    case UPCOMING_MATCHES_FAIL:
      return {
        ...state,
        UpcomingMatchesData_error: action?.payload,
        UpcomingMatchesLoading: false,
      };

    case UPCOMING_TRAINING_REQUEST:
      return {
        ...state,
        UpcomingTrainingData: {},
        UpcomingTrainingLoading: true,
      };
    case UPCOMING_TRAINING_SUCCESS:
      return {
        ...state,
        UpcomingTrainingData: action?.payload,
        UpcomingTrainingLoading: false,
      };
    case UPCOMING_TRAINING_FAIL:
      return {
        ...state,
        UpcomingTrainingData_error: action?.payload,
        UpcomingTrainingLoading: false,
      };

    case SEASON_UPDATE_REQUEST:
      return {
        ...state,
        SeasonUpdatesData: {},
        SeasonUpdatesLoading: true,
      };
    case SEASON_UPDATE_SUCCESS:
      return {
        ...state,
        SeasonUpdatesData: action?.payload,
        SeasonUpdatesLoading: false,
      };
    case SEASON_UPDATE_FAIL:
      return {
        ...state,
        SeasonUpdatesData_error: action?.payload,
        SeasonUpdatesLoading: false,
      };

    default:
      return state;
  }
};

export default SeasonUpdateReducer;
