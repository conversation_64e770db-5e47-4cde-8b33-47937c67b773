import {
  TEAM_REMOVE_PLAYER_FAILED,
  TEAM_REMOVE_PLAYER_REQUEST,
  TEAM_REMOVE_PLAYER_RESET,
  TEAM_REMOVE_PLAYER_SUCCESS,
} from '../../actionTypes/Team/TeamAction';
const initialState = {
  playerRemoveLoading: false,
  playerRemoveSuccess: null,
  playerRemoveFailed: false,
  playerRemoveFailedErrorMessage: '',
};

const TeamIDReducer = (state = initialState, action) => {
  switch (action.type) {
    case TEAM_REMOVE_PLAYER_REQUEST:
      return {
        ...state,
        playerRemoveLoading: true,
        playerRemoveSuccess: null,
        playerRemoveFailed: false,
        playerRemoveFailedErrorMessage: '',
      };
    case TEAM_REMOVE_PLAYER_SUCCESS:
      return {
        ...state,
        playerRemoveLoading: false,
        playerRemoveSuccess: true,
        playerRemoveFailed: false,
        playerRemoveFailedErrorMessage: '',
      };
    case TEAM_REMOVE_PLAYER_FAILED:
      return {
        ...state,
        playerRemoveLoading: false,
        playerRemoveSuccess: false,
        playerRemoveFailed: true,
        playerRemoveFailedErrorMessage: action.payload,
      };

    case TEAM_REMOVE_PLAYER_RESET:
      return {
        ...initialState,
      };

    default:
      return state;
  }
};

export default TeamIDReducer;
