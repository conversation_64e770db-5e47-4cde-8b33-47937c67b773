import colors from '../../config/colors';
import { userExistStatusType } from '../../constants/constants';
import {
  CHANGE_PASSWORD_FAILED,
  CHANGE_PASSWORD_REQUEST,
  <PERSON>AN<PERSON>_PASSWORD_RESET,
  <PERSON><PERSON><PERSON>_PASSWORD_SUCCESS,
  <PERSON>ET<PERSON>_SPORTS_PROFILE_FAIL,
  FETCH_SPORTS_PROFILE_REQUEST,
  FETCH_SPORTS_PROFILE_SUCCESS,
  LOGIN_FAILED,
  LOGIN_INITIALIZE,
  LOGIN_REQUEST,
  LOGIN_SUCCESS,
  LOGOUT_SUCCESS,
  RESET_LAST_SYNC_DATE_TIME,
  SEND_PUSH_NOTIFICATION_TOKEN_FAIL,
  SEND_PUSH_NOTIFICATION_TOKEN_REQUEST,
  SEND_PUSH_NOTIFICATION_TOKEN_SUCCESS,
  SET_API_INFORMATION,
  SET_CALENDER_SYNC_STATUS,
  SET_CALENDER_SYNC_TIME_DURATION,
  SET_CURRENT_ENVIRONMENT,
  SET_CURRENT_ENVIRONMENT_SUCCESS,
  SET_CURRENT_PUSH_NOTIFICATION_TOKEN,
  SET_CURRENT_USER,
  SET_THEME_INFORMATION,
  SET_USER_CALENDER,
  UPDATE_ACCESS_TOKEN,
  UPDATE_LAST_SYNC_DATE_TIME,
  USER_EXIST_FAILED,
  USER_EXIST_REQUEST,
  USER_EXIST_SUCCESS,
  VERIFY_EMAIL_SEND_FAILED,
  VERIFY_EMAIL_SEND_REQUEST,
  VERIFY_EMAIL_SEND_RESET,
  VERIFY_EMAIL_SEND_SUCCESS,
} from '../actionTypes/auth';
import {
  USER_IMAGE_UPDATE,
  USER_TYPE_FAILED,
  USER_TYPE_REQUEST,
  USER_TYPE_SUCCESS,
} from '../actionTypes/userType/userType';

interface AuthState {
  tokens: any;
  accessKeyId: any;
  secretAccessKey: any;
  sessionToken: any;
  refreshToken: any;
  loading: any;
  error: any;
  errorMessage: any;
  errorCode: any;
  user: any;
  configurationData: any;
  themInfo: any;
  userData: any;
  userRole: any;
  isNotificationTokenUpdated: any;
  expoPushNotificationToken: any;
  userDataFetchError: any;
  userExistStatus: any;
  selectedCalenderID: any;
  isCalenderSyncEnabled: boolean;
  lastCalenderSyncDateTime: Date | null;
  calenderSyncTimeDuration: { start: string; end: string } | null;
  services: any;
  preferences: any;
  currentEnvironment: string | null;
  verifyEmailSent: boolean;
  verifyEmailErrorCode: any;
  changePasswordSuccess: boolean;
  changePasswordErrorCode: any;
  password: string | null;
  username: string | null;
  clubId: string | null;
}

const INITIAL_STATE: AuthState = {
  tokens: null,
  accessKeyId: null,
  secretAccessKey: null,
  sessionToken: null,
  refreshToken: null,
  loading: false,
  error: false,
  errorMessage: null,
  errorCode: null,
  user: null,
  configurationData: null,
  themInfo: { ...colors },
  userData: null,
  userRole: null,
  isNotificationTokenUpdated: false,
  expoPushNotificationToken: '',
  userDataFetchError: false,
  userExistStatus: null,
  selectedCalenderID: null,
  isCalenderSyncEnabled: false,
  lastCalenderSyncDateTime: null,
  calenderSyncTimeDuration: null,
  services: null,
  preferences: null,
  currentEnvironment: null,
  verifyEmailSent: false,
  verifyEmailErrorCode: null,
  changePasswordSuccess: false,
  changePasswordErrorCode: null,
  password: null,
  username: null,
  clubId: null,
};

const auth = (state = { ...INITIAL_STATE }, action: any): AuthState => {
  switch (action.type) {
    case SET_CURRENT_ENVIRONMENT:
      return {
        ...state,
        currentEnvironment: action?.payload || null,
      };
    case SET_CURRENT_ENVIRONMENT_SUCCESS:
      return {
        ...state,
        currentEnvironment: action?.payload?.name || null,
      };
    case LOGIN_REQUEST:
      return {
        ...state,
        loading: true,
        error: false,
        errorMessage: null,
        errorCode: null,
      };
    case LOGIN_SUCCESS:
      return {
        ...state,
        // tokens: action.payload,
        accessKeyId: action.payload.accessKeyId,
        secretAccessKey: action.payload.secretAccessKey,
        sessionToken: action.payload.sessionToken,
        refreshToken: action.payload.refreshToken,
        error: false,
        errorMessage: null,
        errorCode: null,
      };
    case LOGIN_FAILED:
      return {
        ...state,
        loading: false,
        error: true,
        errorMessage: action.payload.message,
        errorCode: action.payload.code,
      };
    case UPDATE_ACCESS_TOKEN:
      return {
        ...state,
        tokens: {
          ...state.tokens,
          accessToken: action.payload,
        },
      };
    case LOGOUT_SUCCESS:
      return {
        ...state,
        ...INITIAL_STATE,
      };
    case SET_CURRENT_USER:
      return {
        ...state,
        user: action.payload,
      };
    case LOGIN_INITIALIZE:
      return {
        ...state,
        ...INITIAL_STATE,
      };
    case SET_API_INFORMATION:
      return {
        ...state,
        configurationData: action.payload?.configurationData,
        services: action.payload?.services,
        preferences: action.payload?.preferences,
      };
    case SET_THEME_INFORMATION:
      return {
        ...state,
        themInfo: { ...INITIAL_STATE?.themInfo, ...action?.payload },
      };

    //get User Role Type
    case USER_TYPE_REQUEST:
      return {
        ...state,
        userData: null,
        userRole: null,
        userDataFetchError: false,
      };

    case USER_TYPE_SUCCESS:
      return {
        ...state,
        userRole: action.payload.data[0].type,
        userData: action.payload.data[0],
        loading: false,
      };

    case USER_TYPE_FAILED:
      return {
        ...state,
        userDataFetchError: true,
        loading: false,
      };
    case USER_IMAGE_UPDATE: {
      const tmpUserData = {
        ...state.userData,
        profileImageUrl: action.payload,
      };
      return {
        ...state,
        userData: tmpUserData,
        loading: false,
      };
    }
    case FETCH_SPORTS_PROFILE_REQUEST:
      return {
        ...state,
        loading: true,
      };

    case FETCH_SPORTS_PROFILE_SUCCESS:
      return {
        ...state,
        userData: {
          ...state?.userData,
          ...action?.payload,
          ...action?.payload?.data?.[0],
        },
        loading: false,
      };

    case FETCH_SPORTS_PROFILE_FAIL:
      return {
        ...state,
        loading: false,
      };
    case SEND_PUSH_NOTIFICATION_TOKEN_REQUEST:
      return {
        ...state,
        isNotificationTokenUpdated: false,
      };

    case SEND_PUSH_NOTIFICATION_TOKEN_SUCCESS:
      return {
        ...state,
        isNotificationTokenUpdated: true,
      };

    case SEND_PUSH_NOTIFICATION_TOKEN_FAIL:
      return {
        ...state,
        isNotificationTokenUpdated: false,
      };

    case SET_CURRENT_PUSH_NOTIFICATION_TOKEN:
      return {
        ...state,
        expoPushNotificationToken: action?.payload,
      };
    case USER_EXIST_REQUEST:
      return {
        ...state,
        userExistStatus: null,
      };
    case USER_EXIST_SUCCESS:
      return {
        ...state,
        userExistStatus: action?.payload?.id
          ? userExistStatusType.userExistTrue
          : userExistStatusType.userExistFalse,
      };
    case USER_EXIST_FAILED:
      return {
        ...state,
        userExistStatus: userExistStatusType.userExistFail,
      };

    case VERIFY_EMAIL_SEND_REQUEST:
      return {
        ...state,
      };
    case VERIFY_EMAIL_SEND_SUCCESS:
      return {
        ...state,
        verifyEmailSent: true,
      };
    case VERIFY_EMAIL_SEND_FAILED:
      return {
        ...state,
        verifyEmailErrorCode: action?.payload,
      };

    case VERIFY_EMAIL_SEND_RESET:
      return {
        ...state,
        verifyEmailSent: false,
        verifyEmailErrorCode: null,
      };

    case CHANGE_PASSWORD_REQUEST:
      return {
        ...state,
      };
    case CHANGE_PASSWORD_SUCCESS:
      return {
        ...state,
        changePasswordSuccess: true,
      };
    case CHANGE_PASSWORD_FAILED:
      return {
        ...state,
        changePasswordErrorCode: action?.payload,
      };

    case CHANGE_PASSWORD_RESET:
      return {
        ...state,
        changePasswordSuccess: false,
        changePasswordErrorCode: null,
      };

    case SET_USER_CALENDER:
      return {
        ...state,
        selectedCalenderID: action?.payload || null,
      };
    case UPDATE_LAST_SYNC_DATE_TIME:
      return {
        ...state,
        lastCalenderSyncDateTime: new Date(),
      };

    case RESET_LAST_SYNC_DATE_TIME:
      return {
        ...state,
        lastCalenderSyncDateTime: null,
        isCalenderSyncEnabled: false,
      };

    case SET_CALENDER_SYNC_STATUS:
      return {
        ...state,
        isCalenderSyncEnabled: !state.isCalenderSyncEnabled,
      };

    case SET_CALENDER_SYNC_TIME_DURATION:
      return {
        ...state,
        calenderSyncTimeDuration: action?.payload || null,
      };

    default:
      return state;
  }
};

export default auth;
