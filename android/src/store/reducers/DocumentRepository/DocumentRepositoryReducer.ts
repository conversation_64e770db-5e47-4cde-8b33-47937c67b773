import {
  CREATE_DOCUMENT_REPOSITORY_SUCCESS,
  REMOVE_DOCUMENT_REPOSITORY,
  SET_PROFILE_INFO_UPLOAD_DOCUMENT,
  USER_DOCUMENT_TYPE_FAIL,
  USER_DOCUMENT_TYPE_REQUEST,
  USER_DOCUMENT_TYPE_SUCCESS,
} from '../../actionTypes/DocumentRepository/DocumentRepositoryAction';

type documentRepositoryType = {
  userDocumentTypes: any[];
  isUserDocumentTypeLoaded: boolean;
  userDocumentTypesLoading: boolean;
  userDocumentTypesError: boolean;
  uploadedDocument: null | any;
  uploadedDocumentError: boolean;
  isDocumentUploaded: boolean;
  documentRepositoryCreatedOrUpdated: null | any;
};

/**
 * Add some dummy value
 */

const initialState = {
  userDocumentTypes: [],
  isUserDocumentTypeLoaded: false,
  userDocumentTypesLoading: false,
  userDocumentTypesError: false,
  uploadedDocument: null,
  uploadedDocumentError: false,
  isDocumentUploaded: false,
  documentRepositoryCreatedOrUpdated: null,
};

const documentRepository = (
  state: documentRepositoryType = initialState,
  action: any
): documentRepositoryType => {
  switch (action.type) {
    case SET_PROFILE_INFO_UPLOAD_DOCUMENT:
      return {
        ...state,
        uploadedDocument: action.payload,
        isDocumentUploaded: true,
      };
    case USER_DOCUMENT_TYPE_REQUEST:
      return {
        ...state,
        userDocumentTypesLoading: true,
        userDocumentTypesError: false,
        userDocumentTypes: [],
        isUserDocumentTypeLoaded: false,
      };
    case USER_DOCUMENT_TYPE_SUCCESS:
      return {
        ...state,
        userDocumentTypesLoading: false,
        userDocumentTypes: [...(action?.payload?.data || [])],
        isUserDocumentTypeLoaded: true,
      };
    case USER_DOCUMENT_TYPE_FAIL:
      return {
        ...state,
        userDocumentTypesLoading: false,
        userDocumentTypesError: true,
        userDocumentTypes: [],
        isUserDocumentTypeLoaded: false,
      };

    case REMOVE_DOCUMENT_REPOSITORY:
      return {
        ...state,
        uploadedDocument: null,
        uploadedDocumentError: false,
        isDocumentUploaded: false,
      };

    case CREATE_DOCUMENT_REPOSITORY_SUCCESS:
      return {
        ...state,
        documentRepositoryCreatedOrUpdated: Number(new Date()),
      };

    default:
      return state;
  }
};

export default documentRepository;
