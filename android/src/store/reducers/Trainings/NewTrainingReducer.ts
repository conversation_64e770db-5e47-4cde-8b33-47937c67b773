const SET_SHOW_EVENT_SCREEN = 'SET_SHOW_EVENT_SCREEN';
const SET_SELECTED_TEAM = 'SET_SELECTED_TEAM';
const SET_SELECTED_MONTH = 'SET_SELECTED_MONTH';
const SET_FUTURE_MONTHS = 'SET_FUTURE_MONTHS';
const SET_SELECTED_WEEK = 'SET_SELECTED_WEEK';
const SET_SELECTED_WEEK_INDEX = 'SET_SELECTED_WEEK_INDEX';
const SET_LOADING = 'SET_LOADING';
const SET_RPE_MODAL_VIEW = 'SET_RPE_MODAL_VIEW';
const SET_RPE_STATIC_LIST = 'SET_RPE_STATIC_LIST';
const SET_RPE_PLAYER_LIST = 'SET_RPE_PLAYER_LIST';
const SET_PLAYER_RPE_RESPONSE = 'SET_PLAYER_RPE_RESPONSE';
const SET_RPE_STATIC_MAP = 'SET_RPE_STATIC_MAP';
const SET_RPE_RESPONSE_COUNT = 'SET_RPE_RESPONSE_COUNT';
const SET_INITIAL_EVENT_LIST = 'SET_INITIAL_EVENT_LIST';
const SET_EVENT_LIST = 'SET_EVENT_LIST';
const SET_EVENT_LOADING = 'SET_EVENT_LOADING';
const SET_RPE_PLAYER_LIST_LOADING = 'SET_RPE_PLAYER_LIST_LOADING';
const SET_RESPONSE_SUMMERY_SELECTION = 'SET_RESPONSE_SUMMERY_SELECTION';
const RESET_RPE_PLAYER_LIST = 'RESET_RPE_PLAYER_LIST';
const SET_ATTENDACE_MODAL = 'SET_ATTENDACE_MODAL';
const SET_UPLOAD_SESSION = 'SET_UPLOAD_SESSION';
const SET_UPDATED_EVENT = 'SET_UPDATED_EVENT';
const RESET_RPE_SUMMARY_COUNT = 'RESET_RPE_SUMMARY_COUNT'
const RESET_PLAYER_RPE_RESPONSE = 'RESET_PLAYER_RPE_RESPONSE'

interface IMonth {
  year: number;
  monthName: string;
  month: number;
  index: number;
}

interface IselectedWeekStartNdEnd {
  start: string;
  end: string;
}

interface ITeam {
  coachIds: string[];
  colour: string;
  teamName: string;
  _id: string;
}
interface IRpeResponseCount {
  noResponse: number;
  responded: number;
}

export interface ITraningListPayload {
  data: ITraningEvent[];
  totalRecords: number;
  currentPage: number;
}
export interface ITraningEvent {
  endTime: string;
  fileUploads: string[];
  location: {
    latitude: string;
    longitude: string;
    name: string;
  };
  name: string;
  note: string;
  startTime: string;
  team: { _id: string; name: string };
  teamId: string;
  type: 'TRAINING';
  _id: string;
}

interface RPERatingsMap {
  [eventId: string]: string; // eventId to rpeRatingId mapping
}

export interface IRPEPlayerListPayload {
  data: any[];
  totalRecords: number;
  currentPage: number;
}
interface TrainingEventsState {
  isLoading: boolean;
  showEventScreen: boolean;
  targetedSelectedTeam: ITeam | undefined;
  selectedMonth: IMonth | undefined;
  futureMonths: IMonth[] | undefined;
  selectedWeek: IselectedWeekStartNdEnd | undefined;
  selectedWeekIndex: number;
  eventList: ITraningEvent[];
  eventListLoading: boolean;
  eventListCurrentPage: number;
  eventListTotalRecords: number;
  showRPEModal: boolean;
  selectedEventId: string | undefined;
  RPEList: any[];
  RPEMap: any;
  SelectedRPEPlayerList: any[];
  rpePlayerListTotalRecord: number;
  rpePlayerListCurrentPage: number;
  rpePlayerListLoading: boolean;
  playerRPEResponse: RPERatingsMap;
  repResponseCount: IRpeResponseCount;
  responseSummerySelection: boolean;
  isAttendaceModal: boolean;
  isSessionModal: boolean;
  selectedEvent: any;
}

const initialState: TrainingEventsState = {
  isLoading: false,
  showEventScreen: false,
  targetedSelectedTeam: undefined,
  selectedMonth: undefined,
  futureMonths: undefined,
  selectedWeek: undefined,
  selectedWeekIndex: 0,
  eventList: [],
  eventListCurrentPage: 0,
  eventListLoading: false,
  eventListTotalRecords: 0,
  showRPEModal: false,
  selectedEventId: undefined,
  RPEList: [],
  SelectedRPEPlayerList: [],
  playerRPEResponse: {},
  RPEMap: {},
  repResponseCount: { responded: 0, noResponse: 0 },
  rpePlayerListTotalRecord: 0,
  rpePlayerListCurrentPage: 0,
  rpePlayerListLoading: false,
  responseSummerySelection: true,
  isAttendaceModal: false,
  isSessionModal: false,
  selectedEvent: undefined,
};

const newTrainingEventsReducer = (
  state = initialState,
  action: { type: string; payload: any }
): TrainingEventsState => {
  switch (action.type) {
    case SET_SHOW_EVENT_SCREEN:
      return { ...state, showEventScreen: action.payload };
    case SET_SELECTED_TEAM:
      return { ...state, targetedSelectedTeam: action.payload };
    case SET_SELECTED_MONTH:
      return { ...state, selectedMonth: action.payload };
    case SET_FUTURE_MONTHS:
      return { ...state, futureMonths: action.payload };
    case SET_SELECTED_WEEK:
      return { ...state, selectedWeek: action.payload };
    case SET_SELECTED_WEEK_INDEX:
      return { ...state, selectedWeekIndex: action.payload };
    case SET_INITIAL_EVENT_LIST:
      return {
        ...state,
        eventList: action.payload.data,
        eventListCurrentPage: action.payload.currentPage,
        eventListTotalRecords: action.payload.totalRecords,
        eventListLoading: false,
      };
    case SET_EVENT_LIST:
      return {
        ...state,
        eventList: [...state.eventList, ...action.payload.data],
        eventListCurrentPage: action.payload.currentPage,
        eventListTotalRecords: action.payload.totalRecords,
      };
    case SET_EVENT_LOADING:
      return {
        ...state,
        eventListLoading: action.payload,
      };
    case SET_RPE_MODAL_VIEW:
      return {
        ...state,
        showRPEModal: action.payload.showRPEModal,
        selectedEventId: action.payload.eventId,
      };
    case SET_LOADING:
      return { ...state, isLoading: action.payload };
    case SET_RPE_STATIC_LIST:
      return { ...state, RPEList: action.payload };
    case SET_RPE_STATIC_MAP:
      return { ...state, RPEMap: action.payload };
    case SET_RPE_PLAYER_LIST:
      return {
        ...state,
        SelectedRPEPlayerList: action.payload.currentPage == 1 ? action.payload.data : getPlayerRpeList(
          state.SelectedRPEPlayerList,
          action.payload.data
        ),
        rpePlayerListTotalRecord: action.payload.totalRecords,
        rpePlayerListCurrentPage: action.payload.currentPage,
      };
    case SET_PLAYER_RPE_RESPONSE:
      return {
        ...state,
        playerRPEResponse: {
          ...state.playerRPEResponse,
          ...action.payload,
        },
      };
    case RESET_PLAYER_RPE_RESPONSE:
      return {
        ...state,
        playerRPEResponse: {},
      };
    case SET_RPE_RESPONSE_COUNT:
      return { ...state, repResponseCount: action.payload };
    case SET_RPE_PLAYER_LIST_LOADING:
      return { ...state, rpePlayerListLoading: action.payload };
    case SET_RESPONSE_SUMMERY_SELECTION:
      return { ...state, responseSummerySelection: action.payload };
    case RESET_RPE_PLAYER_LIST:
      return {
        ...state,
        SelectedRPEPlayerList: [],
        rpePlayerListTotalRecord: 0,
        rpePlayerListCurrentPage: 0,
        rpePlayerListLoading: false,
      };
    case RESET_RPE_SUMMARY_COUNT:
      return {
        ...state,
        repResponseCount: { responded: 0, noResponse: 0 },
      };
    case SET_ATTENDACE_MODAL:
      return {
        ...state,
        selectedEventId: action.payload.eventId,
        isAttendaceModal: action.payload.isShow,
      };
    case SET_UPLOAD_SESSION:
      return {
        ...state,
        selectedEvent: action.payload.event,
        isSessionModal: action.payload.isSessionModal,
      };
    case SET_UPDATED_EVENT:
      const updatedEventList = [...state.eventList];
      updatedEventList[action.payload.index] = action.payload.event;
      return {
        ...state,
        eventList: updatedEventList,
        selectedEvent: undefined,
        isSessionModal: false,
      };
    default:
      return state;
  }
};

const getPlayerRpeList = (appData: any[], dataList: any[]) => {
  if (appData.length) {
    return [...appData, ...dataList];
  } else {
    return [...dataList];
  }
};

export const setLoading = (isLoading: boolean) => ({
  type: SET_LOADING,
  payload: isLoading,
});

export const setShowEventScreen = (show: boolean) => ({
  type: SET_SHOW_EVENT_SCREEN,
  payload: show,
});

export const setSelectedTeam = (team: ITeam | undefined) => ({
  type: SET_SELECTED_TEAM,
  payload: team,
});

export const setSelectedMonth = (month: IMonth | undefined) => ({
  type: SET_SELECTED_MONTH,
  payload: month,
});

export const setFutureMonths = (months: IMonth[] | undefined) => ({
  type: SET_FUTURE_MONTHS,
  payload: months,
});

export const setSelectedWeek = (week: IselectedWeekStartNdEnd | undefined) => ({
  type: SET_SELECTED_WEEK,
  payload: week,
});

export const setSelectedWeekIndex = (index: number) => ({
  type: SET_SELECTED_WEEK_INDEX,
  payload: index,
});

export const setInitalEventList = (payload: ITraningListPayload) => ({
  type: SET_INITIAL_EVENT_LIST,
  payload: payload,
});

export const setEventList = (payload: ITraningListPayload) => ({
  type: SET_EVENT_LIST,
  payload: payload,
});

export const setRPEList = (list: any[]) => ({
  type: SET_RPE_STATIC_LIST,
  payload: list,
});

export const setRPEMap = (RPEmap: any) => ({
  type: SET_RPE_STATIC_MAP,
  payload: RPEmap,
});

export const setRPEModalView = ({
  eventId,
  showRPEModal,
}: {
  eventId: string;
  showRPEModal: boolean;
}) => ({
  type: SET_RPE_MODAL_VIEW,
  payload: {
    eventId,
    showRPEModal,
  },
});

export const setInitalRPEPlayerList = (payload: IRPEPlayerListPayload) => ({
  type: SET_RPE_PLAYER_LIST,
  payload: payload,
});

export const setPlayerRPEResponse = (rpeRatingsMap: RPERatingsMap) => ({
  type: SET_PLAYER_RPE_RESPONSE,
  payload: rpeRatingsMap,
});

export const resetPlayerRPEResponse = () => ({
  type: RESET_PLAYER_RPE_RESPONSE
});

export const setREPresponceCount = (repResponseCount: IRpeResponseCount) => ({
  type: SET_RPE_RESPONSE_COUNT,
  payload: repResponseCount,
});

export const setEventLoading = (loader: boolean) => ({
  type: SET_EVENT_LOADING,
  payload: loader,
});
export const setRpePlayerListLoading = (loader: boolean) => ({
  type: SET_RPE_PLAYER_LIST_LOADING,
  payload: loader,
});

export const setResponseSummerySelection = (value: boolean) => ({
  type: SET_RESPONSE_SUMMERY_SELECTION,
  payload: value,
});

export const resetRpePlayerList = () => ({
  type: RESET_RPE_PLAYER_LIST,
  payload: '',
});
export const resetRpeSummaryCount = () => ({
  type: RESET_RPE_SUMMARY_COUNT,
  payload: '',
});

export const setAttendanceModal = ({
  eventId,
  isShow,
}: {
  eventId: string;
  isShow: boolean;
}) => ({
  type: SET_ATTENDACE_MODAL,
  payload: { eventId, isShow },
});

export const setSessionUploadModal = ({
  event,
  isSessionModal,
}: {
  event: any;
  isSessionModal: boolean;
}) => ({
  type: SET_UPLOAD_SESSION,
  payload: { event, isSessionModal },
});

export const setUpdatedEventObj = ({
  event,
  index,
}: {
  event: any;
  index: number;
}) => ({
  type: SET_UPDATED_EVENT,
  payload: { event, index },
});

export default newTrainingEventsReducer;
