import {
  TRAINING_EVENTS_FAILED,
  TRAINING_EVENTS_REQUEST,
  TRAINING_EVENTS_SUCCESS,
  TRAINING_MORE_EVENTS_FAILED,
  TRAINING_MORE_EVENTS_REQUEST,
  TRAINING_MORE_EVENTS_SUCCESS,
  TRAINING_SET_SELECTED_TEAM,
  TRAINING_SET_SELECTED_EVENT,
  TRAINING_SET_SELECTED_DATE,
  MATCH_TRAINING_REPORT_SAVE_REQUEST,
  MATCH_TRAINING_REPORT_SAVE_SUCCESS,
  MATCH_TRAINING_REPORT_SAVE_FAILED,
  RESET_SELECTED_DATE_TRAININGS,
} from '../../actionTypes/Training/TrainingActions';

const initialState = {
  trainings: {},
  trainingsLoading: false,
  trainingsError: false,
  trainingsCurrentPage: 0,
  moreTrainingsLoading: false,
  selectedEvent: null,
  selectedTeamId: null,
  selectedEventIndex: 0,
  selectedDate: null,
  invitees: [],
  isInviteesSaved: false,
  isInviteesSaving: false,
  isInviteesLoading: false,
  summery: [],
  isSummeryLoading: false,
  eventLogs: {},
  summeryPage: 1,
  summerySize: 10,
  summeryInitialPage: 1,
  summeryInitialSize: 10,
  inviteesTotalRecords: 0,
  isOngoingMatchAvailable: false,
  latestOngoingMatch: null,
  gotoLatestOngoingMatch: false,
  ongoingMatchList: [],
  loadingTrainingTrigger: false,
};

const TrainingReducer = (state = initialState, action) => {
  const { selectedEvent, selectedTeamId, selectedEventIndex } = state;
  switch (action.type) {
    case TRAINING_EVENTS_REQUEST:
      return {
        ...state,
        trainingsLoading: true,
        trainingsError: false,
      };
    case TRAINING_EVENTS_SUCCESS:
      return {
        ...state,
        trainings: {
          ...state.trainings,
          [selectedTeamId]:
            action.payload?.data || state.trainings[selectedTeamId] || [],
        },
        trainingsLoading: false,
        trainingsError: false,
        trainingsCurrentPage:
          action.payload?.page || state.trainingsCurrentPage,
      };

    case TRAINING_EVENTS_FAILED:
      return {
        ...state,
        trainingsLoading: false,
        trainingsError: true,
      };

    case TRAINING_MORE_EVENTS_REQUEST:
      return {
        ...state,
        moreTrainingsLoading: true,
      };
    case TRAINING_MORE_EVENTS_SUCCESS:
      if (action.payload?.data) {
        return {
          ...state,
          moreTrainingsLoading: false,
          trainings: {
            ...state.trainings,
            [selectedTeamId]: [
              ...state.trainings[selectedTeamId],
              ...(action.payload?.data || []),
            ],
          },
          trainingsCurrentPage:
            action.payload?.page || state.trainingsCurrentPage,
        };
      } else {
        return {
          ...state,
          moreTrainingsLoading: false,
        };
      }
    case TRAINING_MORE_EVENTS_FAILED:
      return {
        ...state,
        moreTrainingsLoading: false,
      };
    case TRAINING_SET_SELECTED_EVENT:
      return {
        ...state,
        selectedEvent: action.payload,
        selectedEventIndex:
          state.trainings[selectedTeamId]?.findIndex(
            i => i._id === action.payload._id
          ) || 0,
      };

    case TRAINING_SET_SELECTED_DATE:
      return {
        ...state,
        selectedDate: action.payload,
      };
    case RESET_SELECTED_DATE_TRAININGS:
      return {
        ...state,
        selectedDate: null,
      };

    case TRAINING_SET_SELECTED_TEAM:
      return {
        ...state,
        selectedTeamId: action.payload,
        eventLogs: state.eventLogs[action.payload]
          ? { ...state.eventLogs }
          : { ...state.eventLogs, [action.payload]: [] },
      };

    case MATCH_TRAINING_REPORT_SAVE_REQUEST:
      return {
        ...state,
        loadingTrainingTrigger: false,
      };

    case MATCH_TRAINING_REPORT_SAVE_SUCCESS:
      return {
        ...state,
        loadingTrainingTrigger: true,
      };

    case MATCH_TRAINING_REPORT_SAVE_FAILED:
      return {
        ...state,
        loadingTrainingTrigger: true,
      };

    default:
      return state;
  }
};

export default TrainingReducer;
