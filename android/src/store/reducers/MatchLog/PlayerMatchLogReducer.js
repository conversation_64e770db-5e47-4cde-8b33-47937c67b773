import {
  avoidedDuplicationData,
  dateTimeUTCConversion,
} from '../../../helpers';
import {
  FETCH_PLAYER_MATCH_LOG_TEAMS_FAILED,
  FETCH_PLAYER_MATCH_LOG_TEAMS_REQUEST,
  FET<PERSON>_PLAYER_MATCH_LOG_TEAMS_RESET,
  FETCH_PLAYER_MATCH_LOG_TEAMS_SUCCESS,
  PLAYER_MATCH_LOG_ACTIVITIES_FAILED,
  PLAYER_MATCH_LOG_ACTIVITIES_REQUEST,
  PLAYER_MATCH_LOG_ACTIVITIES_RESET,
  PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS,
  PLAYER_MATCH_LOG_ALL_RESET,
  PLAYER_MATCH_LOG_EVENTS_FAILED,
  PLAYER_MATCH_LOG_EVENTS_REQUEST,
  PLAYER_MATCH_LOG_EVENTS_SUCCESS,
  PLAY<PERSON>_MATCH_LOG_FORMATIONS_FAILED,
  PLAYER_MATCH_LOG_FORMATIONS_REQUEST,
  PLAYER_MATCH_LOG_FORMATIONS_SUCCESS,
  PLAYER_MATCH_LOG_MATCH_PLAN_FAILED,
  PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST,
  PLAYER_MATCH_LOG_MATCH_PLAN_RESET,
  PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS,
  PLAYER_MATCH_LOG_RESET,
  PLAYER_MATCH_LOG_SCORE_FAILED,
  PLAYER_MATCH_LOG_SCORE_REQUEST,
  PLAYER_MATCH_LOG_SCORE_SUCCESS,
  SET_CURRENT_PLAYER_LOG_MATCH,
  PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_SUCCESS,
  UPDATE_PLAYER_MATCH_SCORE_SOCKET,
  PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_REVERSE,
} from '../../actionTypes/MatchLog/PlayerMatchLogActions';

const initialState = {
  matchesLoading: false,
  matches: [],
  matchesError: false,
  matchesPageNo: 0,
  matchesTotalRecords: 0,
  matchActivityLoading: false,
  matchActivity: null,
  matchActivityError: false,
  matchPlanLoading: false,
  matchPlan: null,
  matchPlanError: false,
  matchFormationLoading: false,
  matchFormation: null,
  matchFormationError: false,
  currentMatch: null,
  matchScoreLoading: false,
  matchScore: null,
  matchScoreError: false,
};

const teamsState = {
  teamsLoading: false,
  teams: null,
  teamsPageNo: 0,
  teamsTotalRecords: 0,
  teamsError: null,
  stopFetchingTeams: false,
};

const PlayerMatchLogReducer = (
  state = { ...initialState, ...teamsState },
  action
) => {
  switch (action.type) {
    case PLAYER_MATCH_LOG_EVENTS_REQUEST:
      return {
        ...state,
        matchesLoading: true,
        matches: [],
        matchesError: false,
      };
    case PLAYER_MATCH_LOG_EVENTS_SUCCESS:
      return {
        ...state,
        matchesLoading: false,
        matches: organizedMatchData(action?.payload?.data),
        matchesError: false,
      };
    case PLAYER_MATCH_LOG_EVENTS_FAILED:
      return {
        ...state,
        matchesLoading: false,
        matches: [],
        matchesError: true,
      };

    case PLAYER_MATCH_LOG_ACTIVITIES_REQUEST:
      return {
        ...state,
        matchActivityLoading: true,
        matchActivityError: null,
      };

    case PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS:
      return {
        ...state,
        matchActivityLoading: false,
        matchActivity: action?.payload?.data,
        matchActivityError: false,
      };

    case PLAYER_MATCH_LOG_ACTIVITIES_FAILED:
      return {
        ...state,
        matchActivityLoading: false,
        matchActivityError: true,
      };

    case PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_SUCCESS:
      return {
        ...state,
        matchActivity: appendMatchActivity(
          state?.matchActivity,
          action?.payload
        ),
      };

    case PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_REVERSE:
      return {
        ...state,
        matchActivity: state?.matchActivity?.length
          ? getDeleteLog(state?.matchActivity, action?.payload)
          : state?.matchActivity,
      };

    case PLAYER_MATCH_LOG_ACTIVITIES_RESET:
      return {
        ...state,
        matchActivityLoading: true,
        matchActivity: action?.payload?.data,
        matchActivityError: false,
      };

    case PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST:
      return {
        ...state,
        matchPlanLoading: true,
        matchPlan: null,
        matchPlanError: false,
      };
    case PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS:
      return {
        ...state,
        matchPlanLoading: false,
        matchPlan: action?.payload,
        matchPlanError: false,
      };
    case PLAYER_MATCH_LOG_MATCH_PLAN_FAILED:
      return {
        ...state,
        matchPlanLoading: false,
        matchPlan: null,
        matchPlanError: true,
      };
    case PLAYER_MATCH_LOG_MATCH_PLAN_RESET:
      return {
        ...state,
        matchPlanLoading: false,
        matchPlan: null,
        matchPlanError: false,
      };

    case PLAYER_MATCH_LOG_FORMATIONS_REQUEST:
      return {
        ...state,
        matchFormationLoading: true,
        matchFormation: null,
        matchFormationError: false,
      };
    case PLAYER_MATCH_LOG_FORMATIONS_SUCCESS:
      return {
        ...state,
        matchFormationLoading: false,
        matchFormation: action?.payload,
        matchFormationError: false,
      };
    case PLAYER_MATCH_LOG_FORMATIONS_FAILED:
      return {
        ...state,
        matchFormationLoading: false,
        matchFormation: null,
        matchFormationError: true,
      };

    case SET_CURRENT_PLAYER_LOG_MATCH:
      return {
        ...state,
        currentMatch: action?.payload,
      };

    case PLAYER_MATCH_LOG_SCORE_REQUEST:
      return {
        ...state,
        matchScoreLoading: true,
        matchScore: null,
        matchScoreError: false,
      };
    case PLAYER_MATCH_LOG_SCORE_SUCCESS:
      return {
        ...state,
        matchScoreLoading: false,
        matchScore: action?.payload,
        matchScoreError: false,
      };
    case UPDATE_PLAYER_MATCH_SCORE_SOCKET:
      return {
        ...state,
        matchScore: getUpdatedScore(state.matchScore, action.payload),
      };
    case PLAYER_MATCH_LOG_SCORE_FAILED:
      return {
        ...state,
        matchScoreLoading: false,
        matchScore: null,
        matchScoreError: true,
      };

    case FETCH_PLAYER_MATCH_LOG_TEAMS_REQUEST:
      return {
        ...state,
        teamsLoading: true,
        teamsError: null,
      };

    case FETCH_PLAYER_MATCH_LOG_TEAMS_SUCCESS:
      return {
        ...state,
        teamsLoading: false,
        teams: action?.payload?.data
          ? [...(state.teams || []), ...action?.payload?.data]
          : [...state.teams],
        teamsPageNo: action?.payload?.data
          ? action?.payload?.page
          : state.teamsPageNo,
        teamsTotalRecords: action?.payload?.data
          ? action?.payload?.totalRecords
          : state.teamsTotalRecords,
        teamsError: null,
        stopFetchingTeams: action?.payload?.data === undefined,
      };
    case FETCH_PLAYER_MATCH_LOG_TEAMS_FAILED:
      return {
        ...state,
        teamsLoading: false,
        teamsError: action?.payload,
      };
    case FETCH_PLAYER_MATCH_LOG_TEAMS_RESET:
      return {
        ...state,
        teamsLoading: false,
        teams: null,
        teamsPageNo: 0,
        teamsTotalRecords: 0,
        teamsError: null,
        stopFetchingTeams: false,
      };

    case PLAYER_MATCH_LOG_RESET:
      return {
        ...state,
        ...initialState,
      };

    case PLAYER_MATCH_LOG_ALL_RESET:
      return {
        ...teamsState,
        ...initialState,
      };

    default:
      return state;
  }
};

const getDeleteLog = (currentData, index) => {
  if (currentData?.length && index >= 0) {
    currentData?.splice(index, 1);
  }
  return currentData || [];
};

const appendMatchActivity = (currentData, newData) => {
  if (!currentData) {
    return [newData];
  }

  return [newData, ...currentData];
};

const getUpdatedScore = (currentScore, payload) => {
  const performupdate = (score, isAdd) => {
    if (isAdd) {
      return score + 1;
    } else {
      return score - 1 < 0 ? 0 : score - 1;
    }
  };

  const isCurrentScoreExist = Object.keys(currentScore || {})?.length;
  let newScore = isCurrentScoreExist
    ? { ...currentScore }
    : { score: 0, opponentScore: 0 };

  if (payload.isOurs) {
    newScore.score = performupdate(currentScore?.score || 0, payload.isAdd);
  } else {
    newScore.opponentScore = performupdate(
      currentScore?.opponentScore || 0,
      payload?.isAdd
    );
  }
  return newScore;
};

const organizedMatchData = matchData => {
  if (!matchData) {
    return null;
  }
  let organizedMatches = matchData.map(match => {
    const {
      monthString,
      date,
      dateString,
      hours12: eventHours,
      amPm,
      minutesString,
    } = dateTimeUTCConversion(match.startTime);

    return {
      ...match,
      startDate: `${monthString.substring(0, 3)} ${date}, ${dateString}`,
      startTime: `${eventHours}:${minutesString} ${amPm}`,
    };
  });

  let groupedMatches = [];

  const grouped = (array, key) =>
    array.reduce((previous, currentItem) => {
      const group = currentItem[key];
      if (!previous[group]) previous[group] = [];
      previous[group].push(currentItem);
      return previous;
    }, {});

  for (const [key, value] of Object.entries(
    grouped(organizedMatches, 'startDate')
  )) {
    groupedMatches.push({
      startDate: key,
      matchData: value,
    });
  }

  return groupedMatches;
};

export default PlayerMatchLogReducer;
