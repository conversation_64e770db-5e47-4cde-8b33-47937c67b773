import { MatchLogStages } from '../../../constants/constants';
import { avoidedDuplicationData } from '../../../helpers';
import {
  C<PERSON>AR_MATCH_SOCKET_MESSAGES,
  CLOSE_INVALID_ACTION,
  <PERSON>ET<PERSON>_INITIAL_FORMATIONS_FAILED,
  <PERSON><PERSON><PERSON>_INITIAL_FORMATIONS_REQUEST,
  <PERSON>ETCH_INITIAL_FORMATIONS_SUCCESS,
  FETCH_INITIAL_PLAYERS_FAIL,
  FETCH_INITIAL_PLAYERS_REQUEST,
  FETCH_INITIAL_PLAYERS_SUCCESS,
  FETCH_ONGOING_MATCH_FAIL,
  FETCH_ONGOING_MATCH_SUCCESS,
  GO_TO_LATEST_ONGOING_MATCH,
  INVITEES_STATUS_SUCCESS,
  MATCH_ATTENDANCE_SAVE_FAILED,
  MATCH_ATTENDANCE_SAVE_REQUEST,
  MATCH_ATTENDANCE_SAVE_SUCCESS,
  MATCH_INVITEES_FAILED,
  MATCH_INVITEES_REQUEST,
  MATCH_INVITEES_SUCCESS,
  MATCH_LOG_ACTIVITIES_FAILED,
  MATCH_LOG_ACTIVITIES_REQUEST,
  MATCH_LOG_ACTIVITIES_SUCCESS,
  MATCH_LOG_ADD_NEW_ACTION,
  MATCH_LOG_BULK_DELETE_FAILED,
  MATCH_LOG_BULK_DELETE_REQUEST,
  MATCH_LOG_BULK_DELETE_SUCCESS_CUSTOM_CONTENT,
  MATCH_LOG_BULK_SAVE_FAILED,
  MATCH_LOG_BULK_SAVE_REQUEST,
  MATCH_LOG_BULK_SAVE_SUCCESS_CUSTOM_CONTENT,
  MATCH_LOG_CONCLUDED_EVENTS_SUCCESS_CUSTOM_CONTENT,
  MATCH_LOG_DELETE_ACTIVITY_FAILED,
  MATCH_LOG_DELETE_ACTIVITY_REQUEST,
  MATCH_LOG_DELETE_ACTIVITY_SUCCESS_CUSTOM_CONTENT,
  MATCH_LOG_EVENTS_FAILED,
  MATCH_LOG_EVENTS_REQUEST,
  MATCH_LOG_EVENTS_SUCCESS,
  MATCH_LOG_GET_LOG_FAILED,
  MATCH_LOG_GET_LOG_REQUEST,
  MATCH_LOG_GET_LOG_SUCCESS_CUSTOM_CONTENT,
  MATCH_LOG_GET_SCORE_SUCCESS,
  MATCH_LOG_MATCH_FORMATION_SUCCESS,
  MATCH_LOG_MATCH_PLAN_FAILED,
  MATCH_LOG_MATCH_PLAN_REQUEST,
  MATCH_LOG_MATCH_PLAN_SUCCESS,
  MATCH_LOG_MORE_ACTIVITIES_FAILED,
  MATCH_LOG_MORE_ACTIVITIES_REQUEST,
  MATCH_LOG_MORE_ACTIVITIES_SUCCESS,
  MATCH_LOG_MORE_EVENTS_FAILED,
  MATCH_LOG_MORE_EVENTS_REQUEST,
  MATCH_LOG_MORE_EVENTS_SUCCESS,
  MATCH_LOG_OPPONENT_DETAILS_REQUEST,
  MATCH_LOG_OPPONENT_DETAILS_SUCCESS,
  MATCH_LOG_REMOVE_LOG_ITEM,
  MATCH_LOG_RESET_DATA,
  MATCH_LOG_SAVE_ACTIVITY_FAILED,
  MATCH_LOG_SAVE_ACTIVITY_REQUEST,
  MATCH_LOG_SAVE_ACTIVITY_SUCCESS_CUSTOM_CONTENT,
  MATCH_LOG_SAVE_GAME_FAILED,
  MATCH_LOG_SAVE_GAME_REQUEST,
  MATCH_LOG_SAVE_GAME_SUCCESS,
  MATCH_LOG_SET_CURRENT_STAGE,
  MATCH_LOG_SET_GAME_STATUS,
  MATCH_LOG_SET_PLAYER_LIST,
  MATCH_LOG_SET_SELECTED_DATE,
  MATCH_LOG_SET_SELECTED_EVENT,
  MATCH_LOG_SET_SELECTED_TEAM,
  MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES,
  MATCH_LOG_UPDATE_SCORE,
  MATCH_MORE_SUMMERY_SUCCESS,
  MATCH_SUMMERY_FAILED,
  MATCH_SUMMERY_REQUEST,
  MATCH_SUMMERY_SUCCESS,
  RESET_INVITEES,
  RESET_SELECTED_DATE_MATCH_LOG,
  RESET_SUMMERY,
  SELECT_ONGOING_MATCH,
  SET_MATCH_INVITEES_ATTENDANT_STATE,
  SET_ONGOING_MATCH,
  UPDATE_MATCH_SOCKET_MESSAGE,
  SET_CURRENT_MATCH_PLAN,
  SET_MATCH_FORMATION_COORDINATES,
  UPDATE_MATCH_SOCKET_REVERSE_MESSAGE,
  SET_MATCH_CONCLUDE,
} from '../../actionTypes/MatchLog/MatchLogActions';

const initialEventData = {
  eventId: null,
  matchPlan: null,
  matchPlanLoading: false,
  matchPlanError: false,
  matchLog: [],
  unsyncedLogs: [],
  unsyncedDeletedLogs: [],
  isGameOngoing: false,
  score: {
    our: 0,
    opponent: 0,
  },
  opponentDetails: null,
  currentStage: MatchLogStages.NOT_STARTED,
  formation: null,
  formationNames: {},
  playerList: {},
  logsLoading: false,
  logsLoaded: false,
  saveLoading: false,
  saveSuccess: null,
  saveError: false,
  matchLogActivitySaveFailed: false,
  matchLogActivitySaveFailedCount: 0,
  matchLogActivityDeleteFailed: false,
  matchLogActivityDeleteFailedCount: 0,
  bulkSaveLoading: false,
  bulkDeleteLoading: false,
};

const initialState = {
  activities: [],
  activitiesLoading: false,
  activitiesPage: 0,
  matches: {},
  matchesLoading: false,
  matchesError: false,
  matchesCurrentPage: 0,
  moreMatchesLoading: false,
  selectedEvent: null,
  selectedTeamId: null,
  selectedEventIndex: 0,
  selectedDate: null,
  invitees: [],
  inviteesTotalRecords: 0,
  inviteesPage: 1,
  isInviteesSaved: false,
  isInviteesSaving: false,
  isInviteesLoading: false,
  summery: [],
  isSummeryLoading: false,
  eventLogs: {},
  summeryPage: 1,
  summerySize: 10,
  summeryInitialPage: 1,
  summeryInitialSize: 10,
  inviteesTotalRecords: 0,
  isOngoingMatchAvailable: false,
  latestOngoingMatch: null,
  gotoLatestOngoingMatch: false,
  ongoingMatchList: [],
  isInvalidRequest: false,
  isLoadingMatchActivity: false,
  matchSocketMessages: [],
  matchSocketReverseMessages: {},
  eventInitialPlayers: {},
  eventInitialFormation: {},
  matchFormationCoordinates: {},
};
//TODO convert to ts
const MatchLogReducer = (state = initialState, action) => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  const { selectedEvent, selectedTeamId, selectedEventIndex } = state;
  switch (action.type) {
    case CLEAR_MATCH_SOCKET_MESSAGES:
      return { ...state, matchSocketMessages: [] };
    case MATCH_LOG_EVENTS_REQUEST:
      return {
        ...state,
        matchesLoading: true,
        matchesError: false,
      };
    case MATCH_LOG_EVENTS_SUCCESS:
      return {
        ...state,
        matches: {
          ...state.matches,
          [selectedTeamId]:
            action.payload?.data || state.matches[selectedTeamId] || [],
        },
        matchesLoading: false,
        matchesError: false,
        matchesCurrentPage: action.payload?.page || state.matchesCurrentPage,
        eventLogs: generateEventLogs(state, action.payload?.data),
      };

    case MATCH_LOG_CONCLUDED_EVENTS_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        matches: {
          ...state.matches,
          [selectedTeamId]:
            action.payload?.customInput === selectedTeamId
              ? addConcludedEventsToEventList(
                  action.payload?.data?.data,
                  state.matches[selectedTeamId]
                )
              : [],
        },
        eventLogs: generateEventLogs(state, action.payload?.data?.data),
      };
    case MATCH_LOG_EVENTS_FAILED:
      return {
        ...state,
        matchesLoading: false,
        matchesError: true,
      };

    case MATCH_LOG_MORE_EVENTS_REQUEST:
      return {
        ...state,
        moreMatchesLoading: true,
      };
    case MATCH_LOG_MORE_EVENTS_SUCCESS:
      if (action.payload?.data) {
        return {
          ...state,
          moreMatchesLoading: false,
          matches: {
            ...state.matches,
            [selectedTeamId]: [
              ...state.matches[selectedTeamId],
              ...(action.payload?.data || []),
            ],
          },
          matchesCurrentPage: action.payload?.page || state.matchesCurrentPage,
          eventLogs: {
            ...generateEventLogs(state, action.payload?.data),
          },
        };
      } else {
        return {
          ...state,
          moreMatchesLoading: false,
        };
      }
    case MATCH_LOG_MORE_EVENTS_FAILED:
      return {
        ...state,
        moreMatchesLoading: false,
      };
    case MATCH_LOG_SET_SELECTED_EVENT:
      return {
        ...state,
        isLoadingMatchActivity: false,
        selectedEvent: action.payload,
        selectedEventIndex:
          state.matches[selectedTeamId]?.findIndex(
            i => i._id === action.payload._id
          ) || 0,
      };

    case MATCH_LOG_MATCH_PLAN_REQUEST:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          matchPlan: null,
          matchPlanLoading: true,
          matchPlanError: false,
        }),
      };
    case MATCH_LOG_MATCH_PLAN_SUCCESS:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          matchPlan: action.payload,
          matchPlanLoading: false,
          matchPlanError: false,
        }),
      };
    case MATCH_LOG_MATCH_PLAN_FAILED:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          matchPlanLoading: false,
          matchPlanError: true,
        }),
      };

    case MATCH_LOG_ACTIVITIES_REQUEST:
      return {
        ...state,
        activitiesLoading: true,
        activities: [],
      };
    case MATCH_LOG_ACTIVITIES_SUCCESS:
      return {
        ...state,
        activitiesLoading: false,
        activities: action.payload?.data || [],
        activitiesPage: action.payload?.page,
      };
    case MATCH_LOG_ACTIVITIES_FAILED:
      return {
        ...state,
        activitiesLoading: false,
      };

    case MATCH_LOG_MORE_ACTIVITIES_REQUEST:
      return {
        ...state,
        activitiesLoading: true,
      };
    case MATCH_LOG_MORE_ACTIVITIES_SUCCESS:
      return {
        ...state,
        activitiesLoading: false,
        activities: [...state.activities, ...(action.payload?.data || [])],
        activitiesPage: action.payload?.page || state.activitiesPage,
      };
    case MATCH_LOG_MORE_ACTIVITIES_FAILED:
      return {
        ...state,
        activitiesLoading: false,
      };
    case MATCH_LOG_ADD_NEW_ACTION:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          matchLog: [
            ...(state?.eventLogs?.[selectedTeamId]?.[selectedEventIndex]
              ?.matchLog || []),
            action.payload || {},
          ],
          unsyncedLogs: [
            ...(state?.eventLogs?.[selectedTeamId]?.[selectedEventIndex]
              ?.unsyncedLogs || []),
            action.payload || {},
          ],
        }),
      };
    case MATCH_LOG_SET_GAME_STATUS:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          isGameOngoing: action.payload,
        }),
      };

    case MATCH_LOG_UPDATE_SCORE:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          score: getUpdatedScore(
            state.eventLogs[selectedTeamId][selectedEventIndex].score,
            action.payload
          ),
        }),
      };
    case MATCH_LOG_OPPONENT_DETAILS_REQUEST:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          opponentDetails: null,
        }),
      };
    case MATCH_LOG_OPPONENT_DETAILS_SUCCESS:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          opponentDetails: action.payload?.data?.[0],
        }),
      };

    case MATCH_INVITEES_SUCCESS:
      return {
        ...state,
        invitees:
          state?.invitees?.length && page > 1
            ? avoidedDuplicationData(state?.invitees, data || null, '_id')
            : data || null,
        inviteesTotalRecords:
          action.payload?.totalRecords || state.inviteesTotalRecords,
        inviteesPage: action.payload?.page || state.inviteesPage,
        isInviteesLoading: false,
      };
    case MATCH_INVITEES_REQUEST:
      return {
        ...state,
        isInviteesLoading: true,
      };

    case MATCH_INVITEES_FAILED:
      return { ...state, isInviteesLoading: false };

    case MATCH_ATTENDANCE_SAVE_REQUEST:
      return {
        ...state,
        isInviteesSaved: false,
        isInviteesSaving: true,
      };

    case MATCH_ATTENDANCE_SAVE_SUCCESS:
      return {
        ...state,
        isInviteesSaved: true,
        isInviteesSaving: false,
      };
    case RESET_INVITEES:
      return {
        ...state,
        invitees: [],
        inviteesPage: 1,
        inviteesTotalRecords: 0,
      };

    case RESET_SUMMERY:
      return {
        ...state,
        summery: [],
      };

    case MATCH_ATTENDANCE_SAVE_FAILED:
      return {
        ...state,
        isInviteesSaved: false,
        isInviteesSaving: false,
      };

    case SET_MATCH_INVITEES_ATTENDANT_STATE: {
      return {
        ...state,
        invitees: updateInviteesAttendant(state.invitees, action.payload),
        isInviteesLoading: false,
      };
    }
    case MATCH_LOG_SET_CURRENT_STAGE:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          currentStage: action.payload,
        }),
      };
    case MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          unsyncedDeletedLogs: [
            ...(state.eventLogs[selectedTeamId][selectedEventIndex]
              .unsyncedDeletedLogs || []),
            action.payload || {},
          ],
        }),
      };
    case MATCH_LOG_DELETE_ACTIVITY_REQUEST:
      return {
        ...state,
        isLoadingMatchActivity: true,
      };

    case MATCH_LOG_DELETE_ACTIVITY_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        isLoadingMatchActivity: false,

        eventLogs: getUpdatedEventLogs(state, {
          unsyncedDeletedLogs: state.eventLogs[selectedTeamId][
            selectedEventIndex
          ].unsyncedDeletedLogs.filter(
            log => log._id !== action.payload?.customInput
          ),
        }),
      };
    case MATCH_LOG_DELETE_ACTIVITY_FAILED:
      return {
        ...state,
        isLoadingMatchActivity: false,

        eventLogs: getUpdatedEventLogs(state, {
          matchLogActivityDeleteFailed: true,
          matchLogActivityDeleteFailedCount: 1,
        }),
        isInvalidRequest: action?.payload,
      };
    case MATCH_LOG_BULK_DELETE_REQUEST:
      return {
        ...state,
        isLoadingMatchActivity: true,
        eventLogs: getUpdatedEventLogs(state, {
          bulkDeleteLoading: true,
        }),
      };
    case MATCH_LOG_BULK_DELETE_FAILED:
      return {
        ...state,
        isLoadingMatchActivity: false,
        eventLogs: getUpdatedEventLogs(state, {
          matchLogActivityDeleteFailed: true,
          matchLogActivityDeleteFailedCount:
            state.eventLogs[selectedTeamId][selectedEventIndex]
              .matchLogActivityDeleteFailedCount + 1,
          bulkDeleteLoading: false,
        }),
        isInvalidRequest: action?.payload,
      };
    case CLOSE_INVALID_ACTION: {
      return {
        ...state,
        isInvalidRequest: false,
      };
    }
    case MATCH_LOG_BULK_DELETE_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        isLoadingMatchActivity: false,
        eventLogs: getUpdatedEventLogs(state, {
          unsyncedDeletedLogs: filterOutSyncedLogs(
            state.eventLogs[selectedTeamId][selectedEventIndex]
              .unsyncedDeletedLogs,
            action.payload.customInput
          ),
          matchLogActivityDeleteFailed: false,
          matchLogActivityDeleteFailedCount: 0,
          bulkDeleteLoading: false,
        }),
      };
    case MATCH_LOG_REMOVE_LOG_ITEM:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          matchLog: state.eventLogs[selectedTeamId][
            selectedEventIndex
          ].matchLog.filter((e, i) => i !== action.payload),
        }),
      };

    case INVITEES_STATUS_SUCCESS:
      return {
        ...state,
        invitees: [...updateData(state.invitees, action?.payload?.data)],
      };

    case MATCH_LOG_MATCH_FORMATION_SUCCESS:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          formation: action.payload,
          formationNames: getFormationNames(action.payload),
        }),
      };

    case MATCH_LOG_SET_PLAYER_LIST:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          playerList: action.payload,
        }),
      };
    case MATCH_LOG_SAVE_ACTIVITY_REQUEST:
      return {
        ...state,
        isLoadingMatchActivity: true,
      };

    case MATCH_LOG_SAVE_ACTIVITY_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        isLoadingMatchActivity: false,
        eventLogs: getUpdatedEventLogs(state, {
          matchLog: addIdToLogs(
            state.eventLogs[selectedTeamId][selectedEventIndex].matchLog,
            action.payload?.data
          ),
          unsyncedLogs: state.eventLogs[selectedTeamId][
            selectedEventIndex
          ].unsyncedLogs.filter(
            log => log.uuid !== action.payload?.customInput?.uuid
          ),
        }),
      };

    case MATCH_LOG_SAVE_ACTIVITY_FAILED: {
      if (action?.payload && isDuplicateMatchActivity(action.payload)) {
        const duplicatedData = getDuplicatedData(action.payload);
        return {
          ...state,
          isLoadingMatchActivity: false,
          eventLogs: getUpdatedEventLogs(state, {
            unsyncedLogs: state.eventLogs[selectedTeamId][
              selectedEventIndex
            ].unsyncedLogs?.filter(log => {
              return (
                log.activityId !== duplicatedData?.activityId &&
                log.comment !== duplicatedData?.comment
              );
            }),
          }),
          isInvalidRequest: action?.payload,
        };
      } else {
        return {
          ...state,
          isLoadingMatchActivity: false,
          eventLogs: getUpdatedEventLogs(state, {
            matchLogActivitySaveFailed: true,
            matchLogActivitySaveFailedCount: 1,
          }),
          isInvalidRequest: action?.payload,
        };
      }
    }

    case MATCH_LOG_BULK_SAVE_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        isLoadingMatchActivity: false,
        eventLogs: getUpdatedEventLogs(state, {
          unsyncedLogs: filterOutSyncedLogs(
            state.eventLogs[selectedTeamId][selectedEventIndex].unsyncedLogs,
            action.payload.customInput
          ),
          matchLog: action.payload?.data?.reduce(
            (currentLogs, i) => addIdToLogs(currentLogs, i),
            state.eventLogs[selectedTeamId][selectedEventIndex].matchLog
          ),
          matchLogActivitySaveFailed: false,
          matchLogActivitySaveFailedCount: 0,
          bulkSaveLoading: false,
        }),
      };

    case MATCH_LOG_BULK_SAVE_FAILED: {
      if (action?.payload && isDuplicateMatchActivity(action.payload)) {
        const duplicatedData = getDuplicatedData(action.payload);
        return {
          ...state,
          isLoadingMatchActivity: false,
          eventLogs: getUpdatedEventLogs(state, {
            unsyncedLogs: state.eventLogs[selectedTeamId][
              selectedEventIndex
            ].unsyncedLogs?.filter(log => {
              return (
                log.activityId !== duplicatedData?.activityId &&
                log.comment !== duplicatedData?.comment
              );
            }),
          }),
          isInvalidRequest: action?.payload,
        };
      } else {
        return {
          ...state,
          isLoadingMatchActivity: false,
          eventLogs: getUpdatedEventLogs(state, {
            matchLogActivitySaveFailed: true,
            matchLogActivitySaveFailedCount:
              state.eventLogs[selectedTeamId][selectedEventIndex]
                .matchLogActivitySaveFailedCount + 1,
            bulkSaveLoading: false,
          }),
          isInvalidRequest: action?.payload,
        };
      }
    }
    case MATCH_LOG_BULK_SAVE_REQUEST:
      return {
        ...state,
        isLoadingMatchActivity: true,
        eventLogs: getUpdatedEventLogs(state, {
          bulkSaveLoading: true,
        }),
      };
    case MATCH_LOG_GET_SCORE_SUCCESS:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          score: {
            our: action.payload?.score || 0,
            opponent: action.payload?.opponentScore || 0,
          },
        }),
      };

    case MATCH_LOG_SET_SELECTED_DATE:
      return {
        ...state,
        selectedDate: action.payload,
      };
    case RESET_SELECTED_DATE_MATCH_LOG:
      return {
        ...state,
        selectedDate: null,
      };
    case MATCH_SUMMERY_REQUEST:
      return {
        ...state,
        isSummeryLoading: true,
      };

    case MATCH_SUMMERY_FAILED:
      return {
        ...state,
        isSummeryLoading: false,
      };

    case MATCH_SUMMERY_SUCCESS:
      return {
        ...state,
        summery: action.payload?.data,
        isSummeryLoading: false,
        summeryPage: action.payload?.page,
        summerySize: action.payload?.size,
      };

    case MATCH_MORE_SUMMERY_SUCCESS:
      return {
        ...state,
        summery: [...updateDataSummery(state.summery, action?.payload?.data)],
        summeryPage: action.payload?.page,
        summerySize: action.payload?.size,
      };

    case MATCH_LOG_GET_LOG_REQUEST:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          logsLoaded: false,
          logsLoading: true,
        }),
      };
    case MATCH_LOG_GET_LOG_FAILED:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          logsLoaded: true,
          logsLoading: false,
        }),
      };
    case MATCH_LOG_GET_LOG_SUCCESS_CUSTOM_CONTENT:
      return {
        ...state,
        eventLogs: getUpdatedEventLogs(state, {
          matchLog:
            selectedEvent._id === action.payload.customInput
              ? action.payload?.data?.data?.sort((a, b) => {
                  return new Date(a.timeStamp) - new Date(b.timeStamp);
                }) || []
              : [
                  ...state.eventLogs[selectedTeamId][selectedEventIndex]
                    .matchLog,
                ],
          logsLoaded: true,
          logsLoading: false,
          currentStage: MatchLogStages.NOT_STARTED,
        }),
      };

    case MATCH_LOG_SET_SELECTED_TEAM:
      return {
        ...state,
        isLoadingMatchActivity: false,

        selectedTeamId: action.payload,
        eventLogs: state.eventLogs[action.payload]
          ? { ...state.eventLogs }
          : { ...state.eventLogs, [action.payload]: [] },
      };

    case MATCH_LOG_SAVE_GAME_REQUEST:
      return {
        ...state,
        isLoadingMatchActivity: true,
        eventLogs: getUpdatedEventLogs(state, {
          saveLoading: true,
          saveSuccess: null,
          saveError: false,
        }),
      };
    case MATCH_LOG_SAVE_GAME_SUCCESS:
      return {
        ...state,
        isLoadingMatchActivity: false,
        eventLogs: getUpdatedEventLogs(state, {
          saveLoading: false,
          saveSuccess: true,
          saveError: false,
        }),
      };
    case MATCH_LOG_SAVE_GAME_FAILED:
      return {
        ...state,
        isLoadingMatchActivity: false,
        eventLogs: getUpdatedEventLogs(state, {
          saveLoading: false,
          saveSuccess: false,
          saveError: true,
        }),
      };

    case MATCH_LOG_RESET_DATA:
      return {
        ...initialState,
      };

    case FETCH_ONGOING_MATCH_SUCCESS:
      return {
        ...state,
        isOngoingMatchAvailable: !!action?.payload?.data,
        ongoingMatchList: getSortedOngoingMatches(action?.payload?.data),
      };
    case FETCH_ONGOING_MATCH_FAIL:
      return {
        ...state,
        isOngoingMatchAvailable: false,
      };
    case SET_ONGOING_MATCH:
      return {
        ...state,
        isOngoingMatchAvailable: true,
      };
    case GO_TO_LATEST_ONGOING_MATCH:
      return {
        ...state,
        gotoLatestOngoingMatch: action.payload,
      };
    case SELECT_ONGOING_MATCH:
      return {
        ...state,
        latestOngoingMatch: action.payload,
      };
    case UPDATE_MATCH_SOCKET_MESSAGE:
      return {
        ...state,
        matchSocketMessages: state.matchSocketMessages?.length
          ? [action?.payload, ...state.matchSocketMessages]?.slice(0, 5)
          : [action?.payload],
      };
    case UPDATE_MATCH_SOCKET_REVERSE_MESSAGE:
      return {
        ...state,
        matchSocketReverseMessages: action?.payload || {},
      };

    case FETCH_INITIAL_PLAYERS_REQUEST:
      return { ...state, eventInitialPlayers: {} };
    case FETCH_INITIAL_PLAYERS_SUCCESS:
      return {
        ...state,
        eventInitialPlayers: getAllPlayersInfoFromEvent(action.payload),
      };
    case FETCH_INITIAL_PLAYERS_FAIL:
      return { ...state, eventInitialPlayers: {} };
    case FETCH_INITIAL_FORMATIONS_REQUEST:
      return { ...state, eventInitialFormation: {} };
    case FETCH_INITIAL_FORMATIONS_SUCCESS:
      return {
        ...state,
        eventInitialFormation: mapFormationCoordination(action.payload),
      };
    case FETCH_INITIAL_FORMATIONS_FAILED:
      return { ...state, eventInitialFormation: {} };
    case SET_CURRENT_MATCH_PLAN:
      return {
        ...state,
        currentMatchPlan: action.payload,
      };
    case SET_MATCH_FORMATION_COORDINATES:
      return {
        ...state,
        matchFormationCoordinates: action?.payload?.data || {},
      };
    case SET_MATCH_CONCLUDE:
      return {
        ...state,
        selectedEvent: {
          ...state.selectedEvent,
          concluded: true,
        },
      };
    default:
      return state;
  }
};

const getAllPlayersInfoFromEvent = payload => {
  if (!payload) {
    return {};
  } else {
    const playersFromCoordination = payload?.playerCoordinates
      .filter(coordinate => coordinate?.playerData)
      ?.map(coordinate => coordinate?.playerData);
    const allPlayers = [
      ...(playersFromCoordination || []),
      ...(payload?.substitutePlayers || []),
    ];
    const playerObject = allPlayers?.reduce(
      (player, item) =>
        Object.assign(player, { [item?.sportsProfileId]: item }),
      {}
    );
    return playerObject;
  }
};

const mapFormationCoordination = payload => {
  if (!payload) {
    return {};
  } else {
    const formationObject = payload?.formationCoordinates.reduce(
      (formation, item) => Object.assign(formation, { [item?._id]: item }),
      {}
    );
    return formationObject;
  }
};

const filterOutSyncedLogs = (unsyncedLogs, dataSet) => {
  const unsynced = unsyncedLogs.filter(log => {
    let isNotSynced = true;
    dataSet.forEach(activity => {
      if (activity.uuid === log.uuid) {
        isNotSynced = false;
      }
    });
    return isNotSynced;
  });

  return unsynced;
};

/**
 *update content
 */
const updateDataSummery = (state, payload) => {
  if (payload) {
    state = [...state, ...payload];
  }
  return state;
};
/**
 *update content
 */
const updateData = (state, payload) => {
  payload &&
    state.forEach((element, index) => {
      const updateIndex = getInviteesArrayIndex(payload, element.userId);

      const payloadEvent = payload[updateIndex];

      if (updateIndex >= 0) {
        state[index].isAvailable = `${payloadEvent.isAvailable}`;
      } else {
        state[index].isAvailable = `false`;
      }
    });
  return state;
};

const updateInviteesAttendant = (state, data) => {
  const { type, userId } = data;
  if (state.length) {
    const index = getInviteesArrayIndex(state, userId);

    state[index].isAttended = type;
  }
  return state;
};

const generateEventLogs = (state, data) => {
  const { eventLogs, selectedTeamId } = state;
  let updatedLogs = {
    ...eventLogs,
  };

  if (!data?.length) {
    return updatedLogs;
  }

  const currentTeamLogs = [...(eventLogs[selectedTeamId] || [])];

  const currentEventIds = currentTeamLogs?.map(l => l.eventId) || [];

  let newLogs = [];
  data.forEach(i => {
    if (!currentEventIds.includes(i._id)) {
      newLogs.push({ ...initialEventData, eventId: i._id });
    }
  });

  updatedLogs[selectedTeamId] = [...currentTeamLogs, ...newLogs];

  return updatedLogs;
};

const getUpdatedEventLogs = (state, data) => {
  const { eventLogs, selectedTeamId, selectedEventIndex } = state;
  let currentEventLogs = { ...eventLogs };
  if (currentEventLogs[selectedTeamId]?.[selectedEventIndex]) {
    currentEventLogs[selectedTeamId][selectedEventIndex] = {
      ...currentEventLogs[selectedTeamId][selectedEventIndex],
      ...data,
    };
  }
  return currentEventLogs;
};

const getUpdatedScore = (currentScore, payload) => {
  const performupdate = (score, isAdd) => {
    if (isAdd) {
      return score + 1;
    } else {
      return score - 1 < 0 ? 0 : score - 1;
    }
  };
  const isCurrentScoreExist = Object.keys(currentScore || {})?.length;
  let newScore = isCurrentScoreExist
    ? { ...currentScore }
    : { our: 0, opponent: 0 };

  if (payload.isOurs) {
    newScore.our = performupdate(currentScore.our || 0, payload.isAdd);
  } else {
    newScore.opponent = performupdate(
      currentScore.opponent || 0,
      payload.isAdd
    );
  }
  return newScore;
};

const getInviteesArrayIndex = (state, id) => {
  return state.findIndex(element => element.userId === id);
};

const addIdToLogs = (currentLogs, data) => {
  if (!data) return [...currentLogs];

  const newLogs = currentLogs.map(l => {
    if (l.activityId === data.activityId && l.timeStamp === data.timeStamp) {
      if (l?.playerId && data?.playerId) {
        //handle player swap actions
        if (l.playerId === data.playerId) {
          return {
            ...l,
            _id: data._id,
          };
        } else {
          return l;
        }
      } else {
        return {
          ...l,
          _id: data._id,
        };
      }
    } else {
      return l;
    }
  });

  return [...newLogs];
};

const addConcludedEventsToEventList = (data = [], currentEvents = []) => {
  if (!data?.length) {
    return [...currentEvents];
  }
  if (!currentEvents?.length) {
    return [...data];
  }
  const currentEventIds = currentEvents.map(e => e._id);
  const newConcludedEvents = data.filter(d => !currentEventIds.includes(d._id));

  return [...newConcludedEvents, ...currentEvents];
};

const getFormationNames = data => {
  let namesObj = {};
  data?.formationCoordinates?.forEach(c => {
    namesObj[c._id] = c.name;
  });
  return namesObj;
};

const getSortedOngoingMatches = ongoingMatches => {
  return (
    ongoingMatches?.sort(
      (a, b) => new Date(b?.startTime) - new Date(a?.startTime)
    ) || []
  );
};

const isDuplicateMatchActivity = (message = '') => {
  return message.startsWith('Duplicating match activity');
};

const getDuplicatedData = message => {
  let duplicatedDataItem = JSON.parse(
    message?.split('activity.')?.[1]?.trim()?.replace(/\'/g, '"')
  );

  return duplicatedDataItem || {};
};

export default MatchLogReducer;
