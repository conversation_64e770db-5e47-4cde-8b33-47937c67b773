import {
  IAPStatsPageSize,
  IAPCommentsPageSize,
} from '../../../constants/constants';
import {
  IAP_STAT_GRAPH_DATE_RANGE_CHANGED,
  IAP_STAT_SET_GRAPH_TITLE,
  IAP_STAT_SET_GRAPH_VIEW,
  P<PERSON>Y<PERSON>_IAP_CATEGORY_FAILED,
  PLAYER_IAP_CATEGORY_REQUEST,
  PLAYER_IAP_CATEGORY_SET,
  PLAYER_IAP_CATEGORY_SUCCESS,
  PLAYER_IAP_COMMENTS_FAILED,
  PLAYER_IAP_COMMENTS_REQUEST,
  PLAYER_IAP_COMMENTS_SUCCESS,
  P<PERSON>Y<PERSON>_I<PERSON>_COMMENTS_UPDATE_FAILED,
  PLAYER_IAP_COMMENTS_UPDATE_SUCCESS,
  PLAYER_IAP_COMMENT_SAVE_FAILED,
  PLAYER_IAP_COMMENT_SAVE_REQUEST,
  PLAYER_IAP_COMMENT_SAVE_SUCCESS,
  PLAYER_IAP_CRITERIA_FAILED,
  PLAYER_IAP_CRITERIA_REQUEST,
  PLAYER_IAP_CRITERIA_SUCCESS,
  PLAYER_IAP_STAT_FAILED,
  PLAYER_IAP_STAT_GRAPH_DATA_FAILED,
  PLAYER_IAP_STAT_GRAPH_DATA_REQUEST,
  PLAYER_IAP_STAT_GRAPH_DATA_SUCCESS,
  PLAYER_IAP_STAT_REQUEST,
  PLAYER_IAP_STAT_SAVE_FAILED,
  PLAYER_IAP_STAT_SAVE_REQUEST,
  PLAYER_IAP_STAT_SAVE_SUCCESS,
  PLAYER_IAP_STAT_SUCCESS,
  PLAYER_IAP_STAT_UPDATE_SUCCESS,
  PLAYER_IAP_SET_SHOW_STATS_GRAPH,
  PLAYER_STATS_CATEGORY_SET,
  PLAYER_IAP_REPORT_FAILED,
  PLAYER_IAP_REPORT_REQUEST,
  PLAYER_IAP_REPORT_SUCCESS,
} from '../../actionTypes/PlayerIAP/PlayerIapAction';

const initialState = {
  selectedIAPCategory: null,
  categories: [],
  categoryError: false,
  categoryLoading: false,
  criteria: [],
  criteriaError: false,
  criteriaLoading: false,
  stats: [],
  statsError: false,
  statsLoading: false,
  statsCurrentPage: 0,
  statsTotalRecords: 0,
  statsPageSize: IAPStatsPageSize,
  statsSaveLoading: false,
  statsSaveError: false,
  statsSaveSuccess: null,
  commmentSaveLoading: false,
  commentSaveError: false,
  commentSaveSuccess: null,
  comments: [],
  commentsCurrentPage: 0,
  commentsError: false,
  commentsLoading: false,
  commentsPageSize: IAPCommentsPageSize,
  commentsTotalRecords: 0,
  statGraphData: [],
  statGraphLoading: false,
  statGraphError: false,
  statGraphDateRange: { from: null, to: null },
  statGraphView: false,
  statGraphCategoryName: '',
  statGraphCriteriaName: '',
  statGraphCriteriaId: '',
  shareReportLoading: false,
  showStatGraph: false,
  iapReportUrl: null,
};

const PlayerIAPReducer = (state = initialState, action) => {
  const { payload } = action;
  switch (action.type) {
    case PLAYER_IAP_SET_SHOW_STATS_GRAPH:
      return {
        ...state,
        showStatGraph: action.payload,
      };

    case PLAYER_IAP_CATEGORY_SET:
    case PLAYER_STATS_CATEGORY_SET:
      return {
        ...state,
        selectedIAPCategory: payload,
        statsCurrentPage: 0,
      };
    case PLAYER_IAP_CATEGORY_REQUEST:
      return {
        ...state,
        categoryLoading: true,
        categoryError: false,
        categories: [],
      };

    case PLAYER_IAP_CATEGORY_SUCCESS:
      return {
        ...state,
        categoryLoading: false,
        categoryError: false,
        categories: payload,
      };

    case PLAYER_IAP_CATEGORY_FAILED:
      return {
        ...state,
        categoryLoading: false,
        categoryError: true,
        categories: [],
      };

    case PLAYER_IAP_CRITERIA_REQUEST:
      return {
        ...state,
        criteriaLoading: true,
        criteriaError: false,
        criteria: [],
      };
    case PLAYER_IAP_CRITERIA_SUCCESS:
      return {
        ...state,
        criteriaLoading: false,
        criteriaError: false,
        criteria: payload,
      };
    case PLAYER_IAP_CRITERIA_FAILED:
      return {
        ...state,
        criteriaLoading: false,
        criteriaError: true,
        criteria: [],
      };
    case PLAYER_IAP_STAT_REQUEST:
      return {
        ...state,
        statsError: false,
        statsLoading: true,
        stats: [],
        statsCurrentPage: 0,
        statsTotalRecords: 0,
      };
    case PLAYER_IAP_STAT_SUCCESS:
      return {
        ...state,
        statsError: false,
        statsLoading: false,
        stats: getPlayerIAPStatData(payload),
        statsCurrentPage: payload?.page || state.statsCurrentPage,
        statsTotalRecords: payload?.totalRecords || state.statsTotalRecords,
      };
    case PLAYER_IAP_STAT_FAILED:
      return {
        ...state,
        statsError: true,
        statsLoading: false,
        stats: [],
        statsCurrentPage: 0,
        statsTotalRecords: 0,
      };
    case PLAYER_IAP_STAT_UPDATE_SUCCESS:
      return {
        ...state,
        statsError: false,
        statsLoading: false,
        stats: addMorePlayerIAPStatData(state, payload),
        statsCurrentPage: payload?.page || state.statsCurrentPage,
        statsTotalRecords: payload?.totalRecords || state.statsTotalRecords,
      };

    case PLAYER_IAP_STAT_SAVE_REQUEST:
      return {
        ...state,
        statsSaveLoading: true,
        statsSaveError: false,
        statsSaveSuccess: null,
      };
    case PLAYER_IAP_STAT_SAVE_SUCCESS:
      return {
        ...state,
        statsSaveLoading: false,
        statsSaveError: false,
        statsSaveSuccess: true,
      };
    case PLAYER_IAP_STAT_SAVE_FAILED:
      return {
        ...state,
        statsSaveLoading: false,
        statsSaveError: true,
        statsSaveSuccess: false,
      };
    case PLAYER_IAP_COMMENT_SAVE_REQUEST:
      return {
        ...state,
        commmentSaveLoading: true,
        commentSaveError: false,
        commentSaveSuccess: null,
      };
    case PLAYER_IAP_COMMENT_SAVE_SUCCESS:
      return {
        ...state,
        commmentSaveLoading: false,
        commentSaveError: false,
        commentSaveSuccess: true,
      };
    case PLAYER_IAP_COMMENT_SAVE_FAILED:
      return {
        ...state,
        commmentSaveLoading: false,
        commentSaveError: true,
        commentSaveSuccess: false,
      };
    case PLAYER_IAP_COMMENTS_REQUEST:
      return {
        ...state,
        comments: [],
        commentsLoading: true,
        commentsError: false,
        commentsCurrentPage: 0,
        commentsTotalRecords: 0,
      };
    case PLAYER_IAP_COMMENTS_SUCCESS:
      return {
        ...state,
        comments: getPlayerIAPCommentData(payload),
        commentsLoading: false,
        commentsError: false,
        commentsCurrentPage: payload?.page || state.commentsCurrentPage,
        commentsTotalRecords:
          payload?.totalRecords || state.commentsTotalRecords,
      };
    case PLAYER_IAP_COMMENTS_FAILED:
      return {
        ...state,
        comments: [],
        commentsLoading: false,
        commentsError: true,
        commentsCurrentPage: 0,
        commentsTotalRecords: 0,
      };
    case PLAYER_IAP_COMMENTS_UPDATE_SUCCESS:
      return {
        ...state,
        comments: addMorePlayerIAPCommentData(state, payload),
        commentsLoading: false,
        commentsError: false,
        commentsCurrentPage: payload?.page || state.commentsCurrentPage,
        commentsTotalRecords: payload?.totalRecords || state.totalRecords,
      };
    case PLAYER_IAP_STAT_GRAPH_DATA_REQUEST:
      return {
        ...state,
        statGraphLoading: true,
        statGraphError: false,
        statGraphData: [],
      };
    case PLAYER_IAP_STAT_GRAPH_DATA_SUCCESS:
      return {
        ...state,
        statGraphLoading: false,
        statGraphError: false,
        statGraphData: getGraphData(payload),
      };
    case PLAYER_IAP_STAT_GRAPH_DATA_FAILED:
      return {
        ...state,
        statGraphLoading: false,
        statGraphError: true,
        statGraphData: [],
      };

    case IAP_STAT_GRAPH_DATE_RANGE_CHANGED:
      return {
        ...state,
        statGraphDateRange: payload,
      };

    case IAP_STAT_SET_GRAPH_VIEW:
      return {
        ...state,
        statGraphView: payload,
      };
    case IAP_STAT_SET_GRAPH_TITLE:
      return {
        ...state,
        statGraphCategoryName: payload.category,
        statGraphCriteriaName: payload.criteria,
        statGraphCriteriaId: payload.criteriaId,
      };

    case 'SHARE_MATCH_REPORT':
      return {
        ...state,
        shareReportLoading: action?.payload,
        iapReportUrl: null,
      };
    case PLAYER_IAP_REPORT_REQUEST:
      return {
        ...state,
        iapReportUrl: null,
        shareReportLoading: true,
      };
    case PLAYER_IAP_REPORT_SUCCESS:
      return {
        ...state,
        iapReportUrl: action.payload?.preSignedUrl,
        shareReportLoading: false,
      };
    case PLAYER_IAP_REPORT_FAILED:
      return {
        ...state,
        iapReportUrl: null,
        shareReportLoading: false,
      };
    default:
      return { ...state };
  }
};

const getPlayerIAPStatData = payload => {
  return payload?.data || [];
};
const addMorePlayerIAPStatData = (state, payload) => {
  return payload?.data?.length
    ? [...state.stats, ...payload.data]
    : state.stats;
};
const getPlayerIAPCommentData = payload => {
  return payload?.data || [];
};
const addMorePlayerIAPCommentData = (state, payload) => {
  return payload?.data?.length
    ? [...state.comments, ...payload.data]
    : state.comments;
};
const getGraphData = payload => {
  return payload?.data || [];
};

export default PlayerIAPReducer;
