import { deviceStatfilterList } from '../../../constants/constants';
import { DEVICE_STATS_CATEGORY_LIST } from '../../../constants/data';
import { avoidedDuplicationData } from '../../../helpers';
import {
  DELETE_MAPPED_USER_REQUEST,
  DELETE_MAPPED_USER_SUCCESS,
  DELETE_MAPPED_USER_FAIL,
  FETCH_SESSION_DATA_REQUEST,
  FETCH_SESSION_DATA_SUCCESS,
  FETCH_SESSION_DATA_FAIL,
  SET_SELECTED_EVENT_TYPE,
  <PERSON><PERSON>AR_SESSION_DATA,
  SET_SESSION_PAGE_COUNT,
  SET_SELECTED_FILTER_TYPE,
  FETCH_SESSION_STAT_REQUEST,
  FETCH_SESSION_STAT_SUCCESS,
  FETCH_SESSION_STAT_FAIL,
  SET_SELECTED_DEVICE,
  IS_USER_CONNECTED_WITH_PLAYER_MAKER_REQUEST,
  IS_USER_CONNECTED_WITH_PLAYER_MAKER_SUCCESS,
  IS_USER_CONNECTED_WITH_PLAYER_MAKER_FAIL,
  GET_USER_DETAILS_FROM_PM_REQUEST,
  GET_USER_DETAILS_FROM_PM_SUCCESS,
  GET_USER_DETAILS_FROM_PM_FAIL,
  CLEAR_SEARCH_PM_DATA,
  POST_USER_DETAILS_PM_REQUEST,
  POST_USER_DETAILS_PM_SUCCESS,
  POST_USER_DETAILS_PM_FAIL,
  CLEAR_PLAYER_MAKER_DATA,
  GET_TEAM_LIST_REQUEST,
  GET_TEAM_LIST_SUCCESS,
  GET_TEAM_LIST_FAIL,
  GET_POSITION_LIST_REQUEST,
  GET_POSITION_LIST_SUCCESS,
  GET_POSITION_LIST_FAIL,
  SET_DEVICE_STAT_TYPE,
  SET_IS_SUMMARY_MODAL,
  SET_IS_SUMMARY_DETAILS,
  FETCH_SUMMARY_STAT_REQUEST,
  FETCH_SUMMARY_STAT_SUCCESS,
  FETCH_SUMMARY_STAT_FAIL,
  SET_HAS_SESSION_DATA,
  SET_HAS_SUMMARY_DATA,
} from '../../actionTypes/DeviceStats/DeviceStatsActions';

type isPlayerMakerSyncType = {
  _id: string;
  userId: string;
  email: string;
  pmUserId: string;
};

export interface userDetailsFromPMType {
  pmUserName: string;
  pmUserId: string;
}

export interface sessionDataType {
  startDateTime: string;
  sessionType: string;
  position: string;
  teamName: string;
  phaseDurationInMints: string;
  sessionId: string;
  phaseId: string;
  phaseType: string;
}

interface ISessionPercentageData {
  [id: string]: any;
}

interface ISummarySelectedDetails {
  summaryFromDate?: Date;
  summaryToDate?: Date;
  summarySelectedTeam?: {
    value: string;
    label: string;
  }[];
  summarySelectedPosition?: {
    value: string;
    label: string;
  }[];
}

export interface IDeviceStatsType {
  selectedDeviceStateType: string;
  isSummaryOpenModal: boolean;
  sessionData: sessionDataType[];
  currentSessionId: string;
  selectedEventType: string | null;
  sessionDataLoading: boolean;
  sessionDataTotalRecords: number;
  sessionDataCurrentPageCount: number;
  sessionPaginationCount: number;
  selectedFilterType: string;
  sessionPercentageLoading: boolean;
  sessionPercentageData?: ISessionPercentageData;
  teamListLoading: boolean;
  teamList: any[];
  teamTotalCount: number;
  teamPage: number;
  positionListLoading: boolean;
  positionList: any[];
  positionTotalCount: number;
  positionPage: number;
  selectedDevice: string | null;
  PlayerMakerSyncedData: isPlayerMakerSyncType | null;
  isPlayerMakerSyncedLoading: boolean;
  userDetailsFromPM: userDetailsFromPMType | null;
  userDetailsFromPMLoading: boolean;
  postPMuserDataLoading: boolean;
  summarySelectedDetails?: ISummarySelectedDetails;
  summaryGraphLoading: boolean;
  summaryGraphStat: any[];
  hasTrainingDataForSession: boolean;
  hasMatchDataForSession: boolean;
  hasTrainingDataForSummary: boolean;
  hasMatchDataForSummary: boolean;
}

const INITIAL_STATE: IDeviceStatsType = {
  selectedDeviceStateType: DEVICE_STATS_CATEGORY_LIST[0].key,
  isSummaryOpenModal: false,
  sessionData: [],
  sessionDataLoading: false,
  sessionDataTotalRecords: 0,
  sessionDataCurrentPageCount: 1,
  currentSessionId: '',
  selectedEventType: '',
  sessionPaginationCount: 0,
  selectedFilterType: deviceStatfilterList[0].id,
  sessionPercentageLoading: false,
  sessionPercentageData: undefined,
  selectedDevice: null,
  PlayerMakerSyncedData: null,
  isPlayerMakerSyncedLoading: false,
  userDetailsFromPM: null,
  userDetailsFromPMLoading: false,
  postPMuserDataLoading: false,
  teamListLoading: false,
  teamList: [],
  teamTotalCount: 0,
  teamPage: 0,
  positionListLoading: false,
  positionList: [],
  positionTotalCount: 0,
  positionPage: 0,
  summaryGraphLoading: false,
  summaryGraphStat: [],
  hasTrainingDataForSession: true,
  hasMatchDataForSession: true,
  hasTrainingDataForSummary: true,
  hasMatchDataForSummary: true,
};

const deviceStatsReducer = (
  state = INITIAL_STATE,
  action: any
): IDeviceStatsType => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  const customInput = action?.payload?.customInput;

  switch (action.type) {
    case IS_USER_CONNECTED_WITH_PLAYER_MAKER_REQUEST:
      return {
        ...state,
        isPlayerMakerSyncedLoading: true,
      };
    case IS_USER_CONNECTED_WITH_PLAYER_MAKER_SUCCESS:
      return {
        ...state,
        isPlayerMakerSyncedLoading: false,
        PlayerMakerSyncedData: payload || null,
      };
    case IS_USER_CONNECTED_WITH_PLAYER_MAKER_FAIL:
      return {
        ...state,
        isPlayerMakerSyncedLoading: false,
      };
    case GET_USER_DETAILS_FROM_PM_REQUEST:
      return {
        ...state,
        userDetailsFromPMLoading: true,
      };
    case GET_USER_DETAILS_FROM_PM_SUCCESS:
      return {
        ...state,
        userDetailsFromPMLoading: false,
        userDetailsFromPM: payload || {},
      };
    case GET_USER_DETAILS_FROM_PM_FAIL:
      return {
        ...state,
        userDetailsFromPMLoading: false,
      };
    case CLEAR_SEARCH_PM_DATA:
      return {
        ...state,
        userDetailsFromPM: null,
        userDetailsFromPMLoading: false,
        postPMuserDataLoading: false,
      };
    case POST_USER_DETAILS_PM_REQUEST:
      return {
        ...state,
        postPMuserDataLoading: true,
      };
    case POST_USER_DETAILS_PM_SUCCESS:
      return {
        ...state,
        postPMuserDataLoading: false,
      };
    case POST_USER_DETAILS_PM_FAIL:
      return {
        ...state,
        postPMuserDataLoading: false,
      };
    case SET_SELECTED_DEVICE:
      return {
        ...state,
        selectedDevice: data || null,
      };
    case DELETE_MAPPED_USER_REQUEST:
      return { ...state, isPlayerMakerSyncedLoading: true };
    case DELETE_MAPPED_USER_SUCCESS:
      return {
        ...state,
        PlayerMakerSyncedData: null,
        isPlayerMakerSyncedLoading: false,
        userDetailsFromPM: null,
        userDetailsFromPMLoading: false,
        postPMuserDataLoading: false,
        sessionPercentageData: undefined,
        selectedFilterType: deviceStatfilterList[0].id,
        sessionData: [],
        sessionDataTotalRecords: 0,
        sessionDataCurrentPageCount: 1,
      };
    case DELETE_MAPPED_USER_FAIL:
      return {
        ...state,
        isPlayerMakerSyncedLoading: false,
      };
    case FETCH_SESSION_DATA_REQUEST:
      return { ...state, sessionDataLoading: true };
    case FETCH_SESSION_DATA_SUCCESS:
      return {
        ...state,
        sessionData:
          state?.sessionData && page > 1
            ? avoidedDuplicationData(
                state?.sessionData,
                data || null,
                'phaseId'
              )
            : data || [],
        sessionDataLoading: false,
        sessionDataTotalRecords: totalRecords || state?.sessionDataTotalRecords,
        sessionDataCurrentPageCount: page || state?.sessionDataCurrentPageCount,
      };
    case FETCH_SESSION_DATA_FAIL:
      return { ...state, sessionDataLoading: false };
    case SET_SELECTED_EVENT_TYPE:
      return {
        ...state,
        selectedEventType: action?.payload?.data || null,
        sessionPercentageData: undefined,
        selectedFilterType: deviceStatfilterList[0].id,
        sessionData: [],
        sessionDataTotalRecords: 0,
        sessionDataCurrentPageCount: 1,
      };
    case SET_SESSION_PAGE_COUNT:
      return { ...state, sessionPaginationCount: action.payload.data };
    case CLEAR_SESSION_DATA:
      return {
        ...state,
        sessionData: [],
        sessionDataTotalRecords: 0,
        sessionDataCurrentPageCount: 1,
      };
    case CLEAR_PLAYER_MAKER_DATA:
      return {
        ...state,
        PlayerMakerSyncedData: null,
        isPlayerMakerSyncedLoading: false,
        userDetailsFromPM: null,
        userDetailsFromPMLoading: false,
        postPMuserDataLoading: false,
        sessionPercentageData: undefined,
        selectedFilterType: deviceStatfilterList[0].id,
        summarySelectedDetails: undefined,
        selectedDeviceStateType: DEVICE_STATS_CATEGORY_LIST[0].key,
        summaryGraphStat: [],
      };
    case SET_SELECTED_FILTER_TYPE:
      return {
        ...state,
        selectedFilterType: data,
      };
    case FETCH_SESSION_STAT_REQUEST:
      return {
        ...state,
        sessionPercentageLoading: true,
        sessionPercentageData: undefined,
      };
    case FETCH_SESSION_STAT_SUCCESS:
      return {
        ...state,
        sessionPercentageLoading: false,
        sessionPercentageData: action?.payload,
      };
    case FETCH_SESSION_STAT_FAIL:
      return {
        ...state,
        sessionPercentageLoading: false,
        sessionPercentageData: undefined,
      };
    case GET_TEAM_LIST_REQUEST:
      return {
        ...state,
        teamListLoading: true,
      };
    case GET_TEAM_LIST_SUCCESS:
      return {
        ...state,
        teamListLoading: false,
        teamList: action?.payload?.data,
      };
    case GET_TEAM_LIST_FAIL:
      return {
        ...state,
        teamListLoading: false,
      };
    case GET_POSITION_LIST_REQUEST:
      return {
        ...state,
        positionListLoading: true,
      };
    case GET_POSITION_LIST_SUCCESS:
      return {
        ...state,
        positionList: action?.payload?.data,
        positionListLoading: false,
      };
    case GET_POSITION_LIST_FAIL:
      return {
        ...state,
        positionListLoading: false,
      };
    case SET_DEVICE_STAT_TYPE:
      return {
        ...state,
        selectedDeviceStateType: payload,
      };
    case SET_IS_SUMMARY_MODAL:
      return {
        ...state,
        isSummaryOpenModal: payload,
      };
    case SET_IS_SUMMARY_DETAILS:
      return {
        ...state,
        summarySelectedDetails: payload,
      };
    case FETCH_SUMMARY_STAT_REQUEST:
      return {
        ...state,
        summaryGraphLoading: true,
        summaryGraphStat: [],
      };
    case FETCH_SUMMARY_STAT_SUCCESS:
      return {
        ...state,
        summaryGraphLoading: false,
        summaryGraphStat: action?.payload?.data || [],
      };
    case FETCH_SUMMARY_STAT_FAIL:
      return {
        ...state,
        summaryGraphLoading: false,
        summaryGraphStat: [],
      };
    case SET_HAS_SESSION_DATA:
      return {
        ...state,
        hasTrainingDataForSession:
          action?.payload?.hasTrainingDataForSession || false,
        hasMatchDataForSession:
          action?.payload?.hasMatchDataForSession || false,
      };
    case SET_HAS_SUMMARY_DATA:
      return {
        ...state,
        hasTrainingDataForSummary:
          action?.payload?.hasTrainingDataForSummary || false,
        hasMatchDataForSummary:
          action?.payload?.hasMatchDataForSummary || false,
      };
    default:
      return state;
  }
};

export default deviceStatsReducer;
