import {
  D<PERSON><PERSON>OADING_REPORT,
  FETCH_MATCH_REPORT_FAILED,
  FETCH_MATCH_REPORT_REQUEST,
  FETCH_MATCH_REPORT_SUCCESS_CUSTOM_CONTENT,
  FETCH_MATCH_REPORT_TEAMS_FAILED,
  FETCH_MATCH_REPORT_TEAMS_REQUEST,
  FETCH_MATCH_REPORT_TEAMS_RESET,
  FETCH_MATCH_REPORT_TEAMS_SUCCESS,
  FETCH_MATCH_REPORT_URL_FAILED,
  FETCH_MATCH_REPORT_URL_REQUEST,
  FETCH_MATCH_REPORT_URL_SUCCESS,
  MATCH_REPORT_PARAMS_RESET,
  MATCH_REPORT_REDUCER_RESET,
  MATCH_REPORT_RESET,
  SET_MATCH_REPORT_PARAMS,
} from '../../actionTypes/matchReport/matchReportActions';

const reportsState = {
  reportsLoading: false,
  reports: null,
  reportsError: null,
  reportsCurrentPageNo: 0,
  stopFetchingReports: false,
  matchReportDownloading: false,
  matchReportUrl: null,
  matchReportUrlFetching: false,
};

const teamsState = {
  teamsLoading: false,
  teams: null,
  teamsPageNo: 0,
  teamsTotalRecords: 0,
  teamsError: null,
  stopFetchingTeams: false,
};

const filterParams = {
  filterParams: {
    teamId: null,
    startDate: null,
    endDate: null,
    keyWord: null,
  },
};

const MatchReportReducer = (
  state = { ...reportsState, ...teamsState, ...filterParams },
  action
) => {
  switch (action.type) {
    case FETCH_MATCH_REPORT_TEAMS_REQUEST:
      return {
        ...state,
        teamsLoading: true,
        teamsError: null,
      };

    case FETCH_MATCH_REPORT_TEAMS_SUCCESS:
      return {
        ...state,
        teamsLoading: false,
        teams: action?.payload?.data
          ? [...(state.teams || []), ...action?.payload?.data]
          : [...state.teams],
        teamsPageNo: action?.payload?.data
          ? action?.payload?.page
          : state.teamsPageNo,
        teamsTotalRecords: action?.payload?.data
          ? action?.payload?.totalRecords
          : state.teamsTotalRecords,
        teamsError: null,
        stopFetchingTeams: action?.payload?.data === undefined,
      };

    case FETCH_MATCH_REPORT_TEAMS_FAILED:
      return {
        ...state,
        teamsLoading: false,
        teamsError: action?.payload,
      };

    case FETCH_MATCH_REPORT_TEAMS_RESET:
      return {
        ...state,
        teamsLoading: false,
        teams: null,
        teamsPageNo: 0,
        teamsTotalRecords: 0,
        teamsError: null,
        stopFetchingTeams: false,
      };

    case FETCH_MATCH_REPORT_REQUEST:
      return {
        ...state,
        reportsLoading: true,
        reportsError: null,
      };

    case FETCH_MATCH_REPORT_SUCCESS_CUSTOM_CONTENT:
      const isMatched =
        JSON.stringify(action?.payload?.customInput?.filterParams) ===
        JSON.stringify(state.filterParams);

      return {
        ...state,
        reportsLoading: false,
        reports: isMatched
          ? action?.payload?.data?.data
            ? addDataWithoutDuplication(
                state.reports || [],
                action?.payload?.data?.data
              )
            : state.reports
            ? [...state.reports]
            : []
          : [...state.reports],
        reportsCurrentPageNo: isMatched
          ? action?.payload?.data
            ? action?.payload?.data?.page
            : state.reportsPageNo
          : state.reportsPageNo,
        reportsError: null,
        stopFetchingReports: action?.payload?.data?.data === undefined,
      };

    case FETCH_MATCH_REPORT_FAILED:
      return {
        ...state,
        reportsLoading: false,
        reportsError: action?.payload,
      };

    case MATCH_REPORT_RESET:
      return {
        ...state,
        ...reportsState,
      };

    case MATCH_REPORT_REDUCER_RESET:
      return {
        ...teamsState,
        ...reportsState,
        ...filterParams,
      };

    case SET_MATCH_REPORT_PARAMS:
      return {
        ...state,
        filterParams: action?.payload,
      };

    case MATCH_REPORT_PARAMS_RESET:
      return {
        ...state,
        ...filterParams,
      };

    case DOWNLOADING_REPORT:
      return {
        ...state,
        matchReportDownloading: action.payload,
      };

    case FETCH_MATCH_REPORT_URL_REQUEST:
      return {
        ...state,
        matchReportUrlFetching: true,
        matchReportUrl: null,
      };

    case FETCH_MATCH_REPORT_URL_SUCCESS:
      return {
        ...state,
        matchReportUrlFetching: false,
        matchReportUrl: action.payload?.preSignedUrl,
      };

    case FETCH_MATCH_REPORT_URL_FAILED:
      return {
        ...state,
        matchReportUrlFetching: false,
        matchReportUrl: null,
      };

    default:
      return state;
  }
};

const addDataWithoutDuplication = (currentData, newData) => {
  if (!newData?.length) {
    return currentData;
  }

  const existingIds = currentData?.map(({ _id }) => _id);

  const filteredNewData = newData?.filter(
    ({ _id }) => !existingIds?.includes(_id)
  );

  return [...currentData, ...filteredNewData];
};

export default MatchReportReducer;
