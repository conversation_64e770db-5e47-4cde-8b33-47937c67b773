import {
  EVENT_ADD_REQUEST,
  EVENT_ADD_SUCCESS,
  EVENT_ADD_FAIL,
  EVENT_DELETE_REQUEST,
  EVENT_DELETE_SUCCESS,
  EVENT_DELETE_FAIL,
  CREATE_EVENT_RELATED_DATA_REQUEST,
  CREATE_EVENT_RELATED_DATA_SUCCESS,
  CREATE_EVENT_RELATED_DATA_FAIL,
  FETCH_EVENT_RELATED_DATA_REQUEST,
  FETCH_EVENT_RELATED_DATA_SUCCESS,
  FETCH_EVENT_RELATED_DATA_FAIL,
  EVENT_DELETE_RESET,
  RESET_EVENT_RELATED_LIST_DATA,
} from '../../actionTypes/Event/EventActionTypes';
import { avoidedDuplicationData } from '../../../helpers';

const initailState = {
  eventRelatedListData: [],
  eventRelatedListTotalRecords: 0,
  eventRelatedListPage: 1,
  createEventLoading: false,
  createEventSuccess: false,
  createEventFailError: false,
  deleteEventLoading: false,
  deleteEventSuccess: false,
  deleteEventFailError: false,
  createEventRelatedListDataLoading: false,
  createEventRelatedListDataResult: false,
  createEventRelatedListDataFail: false,
};

const EventReducer = (state = initailState, action) => {
  const payload = action?.payload;
  const { data = '', totalRecords = 0, page = 0, size = 0 } = payload || {};
  switch (action.type) {
    case CREATE_EVENT_RELATED_DATA_REQUEST:
      return {
        ...state,
        createEventRelatedListDataLoading: true,
        createEventRelatedListDataResult: null,
        createEventRelatedListDataFail: false,
      };
    case CREATE_EVENT_RELATED_DATA_SUCCESS:
      return {
        ...state,
        createEventRelatedListDataLoading: false,
        createEventRelatedListDataResult: action.payload,
        createEventRelatedListDataFail: false,
      };
    case CREATE_EVENT_RELATED_DATA_FAIL:
      return {
        ...state,
        createEventRelatedListDataLoading: false,
        createEventRelatedListDataResult: false,
        createEventRelatedListDataFail: action.payload,
      };
    case RESET_EVENT_RELATED_LIST_DATA:
      return {
        ...state,
        eventRelatedListData: [],
      };
    case FETCH_EVENT_RELATED_DATA_REQUEST:
      return {
        ...state,
      };

    case FETCH_EVENT_RELATED_DATA_SUCCESS:
      return {
        ...state,
        eventRelatedListData:
          state?.eventRelatedListData?.length && page > 1
            ? avoidedDuplicationData(
                state?.eventRelatedListData,
                data || null,
                '_id'
              )
            : data || null,

        eventRelatedListTotalRecords:
          totalRecords || state?.eventRelatedListTotalRecords,
        eventRelatedListPage: page || state?.eventRelatedListPage,
      };
    case FETCH_EVENT_RELATED_DATA_FAIL:
      return {
        ...state,
      };

    case EVENT_ADD_REQUEST:
      return {
        ...state,
        createEventLoading: true,
        createEventSuccess: false,
        createEventFailError: false,
      };
    case EVENT_ADD_SUCCESS:
      return {
        ...state,
        createEventLoading: false,
        createEventSuccess: true,
        createEventFailError: false,
      };
    case EVENT_ADD_FAIL:
      return {
        ...state,
        createEventLoading: false,
        createEventSuccess: false,
        createEventFailError: action?.payload,
      };
    case EVENT_DELETE_REQUEST:
      return {
        ...state,
        deleteEventLoading: true,
        deleteEventSuccess: false,
        deleteEventFailError: false,
      };
    case EVENT_DELETE_SUCCESS:
      return {
        ...state,
        deleteEventLoading: false,
        deleteEventSuccess: true,
        deleteEventFailError: false,
      };
    case EVENT_DELETE_FAIL:
      return {
        ...state,
        deleteEventLoading: false,
        deleteEventSuccess: false,
        deleteEventFailError: action.payload,
      };
    case EVENT_DELETE_RESET:
      return {
        ...state,
        deleteEventLoading: false,
        deleteEventSuccess: false,
        deleteEventFailError: false,
      };
    default:
      return state;
  }
};

export default EventReducer;
