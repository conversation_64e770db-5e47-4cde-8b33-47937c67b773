import { AnyAction, combineReducers } from 'redux';
import addUser from './addUser/addUserReducer';
import auth from './authReducer';
import event from './Event/EventReducer';
import manageUsers from './ManageUsers/ManageUsersReducer';
import matches from './MatchesInfo/MatchesReducer';
import matchLog from './MatchLog/MatchLogReducer';
import playerMatchLog from './MatchLog/PlayerMatchLogReducer';
import matchPlan from './MatchPlan/MatchPlanReducer';
import matchReport from './matchReport/matchReportReducer';
import planner from './Planner/PlannerReducer';
import player from './player/playerReducer';
import playerIAP from './PlayerIAP/PlayerIapReducer';
import playerInfo from './playerInfo/playerInfoReducer';
import SeasonUpdate from './SeasonUpdate/SeasonUpdateReducer';
import teamID from './TeamID/TeamIDReducer';
import team from './TeamLabel/TeamReducer';
import trainings from './Trainings/TrainingReducer';
import common from './Common/CommonReducer';
import documentRepository from './DocumentRepository/DocumentRepositoryReducer';
import message from './Message/MessageReducer';
import { LOGOUT_SUCCESS } from '../actionTypes/auth';
import playerStats from './PlayerStats/PlayerStatsReducer';
import deviceStats from './DeviceStats/DeviceStatsReducer';
import TermAndCondition from './TermAndCondition/termAndConditionReducer';
import newTrainingEventsReducer from './Trainings/NewTrainingReducer';

const appReducer = combineReducers({
  auth,
  team,
  player,
  teamID,
  playerIAP,
  playerInfo,
  matchPlan,
  matches,
  manageUsers,
  playerStats,
  deviceStats,
  addUser,
  planner,
  matchLog,
  SeasonUpdate,
  event,
  playerMatchLog,
  matchReport,
  trainings,
  common,
  documentRepository,
  message,
  TermAndCondition,
  newTrainingEventsReducer
});

type RootReducerType = ReturnType<typeof appReducer>;

const rootReducer = (state: RootReducerType | undefined, action: AnyAction) => {
  if (action.type === LOGOUT_SUCCESS) {
    state = {} as RootReducerType;
  }

  return appReducer(state, action);
};

export default rootReducer;
