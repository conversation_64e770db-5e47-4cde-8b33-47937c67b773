import { useDispatch } from 'react-redux';
import {
  deviceStatfilterList,
  deviceStatsEventType,
} from '../constants/constants';
import { PLAYER_MAKER_SERVICE } from '../constants/services';
import useApiPromise from './useApiPromise';
import {
  FETCH_SESSION_DATA_FAIL,
  FETCH_SESSION_DATA_REQUEST,
  FETCH_SESSION_DATA_SUCCESS,
  SET_SELECTED_EVENT_TYPE,
  FETCH_SESSION_STAT_REQUEST,
  FETCH_SESSION_STAT_SUCCESS,
  FETCH_SESSION_STAT_FAIL,
  GET_TEAM_LIST_REQUEST,
  GET_TEAM_LIST_SUCCESS,
  GET_TEAM_LIST_FAIL,
  GET_POSITION_LIST_REQUEST,
  GET_POSITION_LIST_SUCCESS,
  GET_POSITION_LIST_FAIL,
  FETCH_SUMMARY_STAT_REQUEST,
  FETCH_SUMMARY_STAT_SUCCESS,
  <PERSON>ETCH_SUMMARY_STAT_FAIL,
  SET_HAS_SESSION_DATA,
  SET_HAS_SUMMARY_DATA,
} from '../store/actionTypes/DeviceStats/DeviceStatsActions';

const useGetSessionData = () => {
  const [fetchLatestEventData] = useApiPromise();
  const [fetchSessionData] = useApiPromise();
  const [fetchTechnicalStat] = useApiPromise();
  const [fetchPoitionsByPMUser] = useApiPromise();
  const [fetchSummaryStat] = useApiPromise();
  const [fetchTeamsByPMUser] = useApiPromise();
  const dispatch = useDispatch();

  const dateConvert = (date: string, isStart: boolean) => {
    if (isStart) {
      return date;
    } else {
      return `${date.split('T')[0]}T23:59:59.000Z`;
    }
  };

  const hasSummaryStat = async (
    pmUserId: string,
    phaseStartFromDate: string,
    phaseStartToDate: string,
    pmTeamName: string,
    position: string
  ) => {
    const filterType = deviceStatfilterList[0].summaryId;
    const [trainingSummaryData, matchSummaryData] = await Promise.all([
      fetchLatestEventData(
        `/api/v1/playermaker-users/${pmUserId}/${filterType}?phaseStartFromDate=${dateConvert(
          phaseStartFromDate,
          true
        )}&phaseStartToDate=${dateConvert(
          phaseStartToDate,
          false
        )}&sessionType=${
          deviceStatsEventType.TRAINING
        }&pmTeamName=${pmTeamName}&pmPosition=${position}&page=1&size=1`,
        '',
        '',
        '',
        null,
        null,
        'GET',
        false,
        PLAYER_MAKER_SERVICE
      ),
      fetchLatestEventData(
        `/api/v1/playermaker-users/${pmUserId}/${filterType}?phaseStartFromDate=${dateConvert(
          phaseStartFromDate,
          true
        )}&phaseStartToDate=${dateConvert(
          phaseStartToDate,
          false
        )}&sessionType=${
          deviceStatsEventType.MATCH
        }&pmTeamName=${pmTeamName}&pmPosition=${position}&page=1&size=1`,
        '',
        '',
        '',
        null,
        null,
        'GET',
        false,
        PLAYER_MAKER_SERVICE
      ),
    ]);

    const hasTrainingDataForSummary = trainingSummaryData?.status === 200;
    const hasMatchDataForSummary = matchSummaryData?.status === 200;

    dispatch({
      type: SET_HAS_SUMMARY_DATA,
      payload: {
        hasTrainingDataForSummary: hasTrainingDataForSummary,
        hasMatchDataForSummary: hasMatchDataForSummary,
      },
    });
    return {
      hasTrainingDataForSummary: hasTrainingDataForSummary || false,
      hasMatchDataForSummary: hasMatchDataForSummary || false,
    };
  };

  const getSummaryStat = async (
    pmUserId: string,
    phaseStartFromDate: string,
    phaseStartToDate: string,
    selectedFilterType: string,
    sessionType: string,
    pmTeamName: string,
    position: string
  ) => {
    const filterType =
      selectedFilterType === deviceStatfilterList[0].id
        ? deviceStatfilterList[0].summaryId
        : deviceStatfilterList[1].summaryId;
    return await fetchSummaryStat(
      `/api/v1/playermaker-users/${pmUserId}/${filterType}?phaseStartFromDate=${dateConvert(
        phaseStartFromDate,
        true
      )}&phaseStartToDate=${dateConvert(
        phaseStartToDate,
        false
      )}&sessionType=${sessionType}&pmTeamName=${pmTeamName}&pmPosition=${position}&page=1&size=100000`,
      FETCH_SUMMARY_STAT_REQUEST,
      FETCH_SUMMARY_STAT_SUCCESS,
      FETCH_SUMMARY_STAT_FAIL,
      null,
      null,
      'GET',
      false,
      PLAYER_MAKER_SERVICE
    );
  };

  const getSessionStat = async (
    pmUserId: string,
    sessionId: string,
    filterType: string,
    phaseId: string
  ) => {
    sessionId?.length &&
      (await fetchTechnicalStat(
        `/api/v1/playermaker-users/${pmUserId}/sessions/${sessionId}/phases/${phaseId}/${filterType}`,
        FETCH_SESSION_STAT_REQUEST,
        FETCH_SESSION_STAT_SUCCESS,
        FETCH_SESSION_STAT_FAIL,
        null,
        null,
        'GET',
        false,
        PLAYER_MAKER_SERVICE,
        { data: filterType }
      ));
  };

  const getSessionData = async (
    pmUserId: string,
    sessionType: string,
    page: number = 1
  ) => {
    const selectedSessionData: any = await fetchSessionData(
      `/api/v1/playermaker-users/${pmUserId}/phases?page=${page}&size=5&sessionType=${sessionType}`,
      FETCH_SESSION_DATA_REQUEST,
      FETCH_SESSION_DATA_SUCCESS,
      FETCH_SESSION_DATA_FAIL,
      null,
      null,
      'GET',
      false,
      PLAYER_MAKER_SERVICE
    );
    const selectedEvent = selectedSessionData?.data?.data[0] || null;

    selectedEvent &&
      (await getSessionStat(
        pmUserId,
        selectedEvent.sessionId,
        deviceStatfilterList[0].id,
        selectedEvent?.phaseId
      ));

    return selectedSessionData;
  };

  const getInitialSessionData = async (pmUserId: string) => {
    const [trainingSessionData, matchSessionData] = await Promise.all([
      fetchLatestEventData(
        `/api/v1/playermaker-users/${pmUserId}/phases?page=1&size=1&sessionType=${deviceStatsEventType.TRAINING}`,
        '',
        '',
        '',
        null,
        null,
        'GET',
        false,
        PLAYER_MAKER_SERVICE
      ),
      fetchLatestEventData(
        `/api/v1/playermaker-users/${pmUserId}/phases?page=1&size=1&sessionType=${deviceStatsEventType.MATCH}`,
        '',
        '',
        '',
        null,
        null,
        'GET',
        false,
        PLAYER_MAKER_SERVICE
      ),
    ]);
    const hasTrainingDataForSession = trainingSessionData?.status === 200;
    const hasMatchDataForSession = matchSessionData?.status === 200;

    dispatch({
      type: SET_HAS_SESSION_DATA,
      payload: {
        hasTrainingDataForSession: hasTrainingDataForSession,
        hasMatchDataForSession: hasMatchDataForSession,
      },
    });

    if (!hasTrainingDataForSession && !hasMatchDataForSession) {
      return;
    }

    await fetchLatestEventData(
      `/api/v1/playermaker-users/${pmUserId}/phases?page=1&size=1`,
      '',
      '',
      '',
      null,
      null,
      'GET',
      false,
      PLAYER_MAKER_SERVICE
    ).then(async res => {
      const {
        data: {
          data: [{ sessionType, sessionId }],
        },
      } = res;
      if (sessionType) {
        dispatch({
          type: SET_SELECTED_EVENT_TYPE,
          payload: { data: sessionType },
        });
        const {
          data: { data },
        } = await getSessionData(pmUserId, sessionType);
        if (data?.length) {
          await getSessionStat(
            pmUserId,
            sessionId,
            deviceStatfilterList[0].id,
            data?.[0]?.phaseId
          );
        }
      }
    });
  };

  const getTeamList = async (
    pmUserId: string,
    startDate: string,
    endDate: string,
    page: number,
    size: number
  ) => {
    return await fetchTeamsByPMUser(
      `/api/v1/playermaker-users/${pmUserId}/teams?phaseStartFromDate=${startDate}&phaseStartToDate=${endDate}&page=${page}&size=${size}`,
      GET_TEAM_LIST_REQUEST,
      GET_TEAM_LIST_SUCCESS,
      GET_TEAM_LIST_FAIL,
      null,
      null,
      'GET',
      false,
      PLAYER_MAKER_SERVICE
    );
  };
  const getPositionList = async (
    pmUserId: string,
    startDate: string,
    endDate: string,
    page: number,
    size: number,
    pmTeamName: string
  ) => {
    return await fetchPoitionsByPMUser(
      `/api/v1/playermaker-users/${pmUserId}/positions?phaseStartFromDate=${startDate}&phaseStartToDate=${endDate}&pmTeamName=${pmTeamName}&page=${page}&size=${size}`,
      GET_POSITION_LIST_REQUEST,
      GET_POSITION_LIST_SUCCESS,
      GET_POSITION_LIST_FAIL,
      null,
      null,
      'GET',
      false,
      PLAYER_MAKER_SERVICE
    );
  };

  const getTeamNdPosition = async (
    pmUserId: string,
    startDate: string,
    endDate: string,
    teamId?: string
  ) => {
    try {
      if (teamId) {
        return await getPositionList(
          pmUserId,
          startDate,
          endDate,
          1,
          20,
          teamId
        );
      } else {
        return await getTeamList(pmUserId, startDate, endDate, 1, 20);
      }
    } catch (error) {
      return error;
    }
  };

  return {
    getInitialSessionData,
    getSessionData,
    getSessionStat,
    getTeamNdPosition,
    getSummaryStat,
    hasSummaryStat,
  } as const;
};

export default useGetSessionData;
