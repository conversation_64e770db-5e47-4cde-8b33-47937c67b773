import { useNavigation } from '@react-navigation/native';
import { FOOTBALL_SERVICE } from '../../../constants/services';
import useApiPromise from '../../useApiPromise';

const useProfileHook = () => {
  const navigation = useNavigation();
  const [makeApiRequest] = useApiPromise();
  const navigateProfile = async (playerData: any, team: any) => {
    const response = await makeApiRequest(
      `/api/v1/sport-profiles?userIds=${playerData.id}`,
      '_',
      '_',
      '_',
      null,
      '',
      'GET',
      false,
      FOOTBALL_SERVICE
    );

    const UserSportData = response?.data?.data[0];

    if (UserSportData) {
      navigation.navigate('PlayerInfoScreen', {
        teamID: team?._id || '',
        profileID: UserSportData?.sportsProfileId,
        isNotAvailable: UserSportData?.isAvailable || false,
      });
    }
  };

  const navigateMessageById = async (playerData: any) => {
    navigation.navigate('Messaging', {
      playerData: playerData,
    });
  };

  return {
    navigateProfile,
    navigateMessageById,
  };
};
export default useProfileHook;