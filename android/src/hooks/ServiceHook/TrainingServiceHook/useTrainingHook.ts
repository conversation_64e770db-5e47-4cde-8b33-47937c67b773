import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  addLeadingZeros,
  calendarDays,
  calendarFutureMonths,
  dateTimeUTCConversion,
} from '../../../helpers';
import { RootStore } from '../../../store/store';

import { useNavigation } from '@react-navigation/native';
import { isTabDevice } from '../../../config/appConfig';
import {
  setInitalEventList,
  setFutureMonths,
  setInitalRPEPlayerList,
  setLoading,
  setPlayerRPEResponse,
  setRPEList,
  setRPEMap,
  setRPEModalView,
  setSelectedMonth,
  setSelectedTeam,
  setSelectedWeek,
  setSelectedWeekIndex,
  setShowEventScreen,
  setREPresponceCount,
  setEventList,
  setEventLoading,
  setRpePlayerListLoading,
  setResponseSummerySelection,
  resetRpePlayerList,
  setAttendanceModal,
  setSessionUploadModal,
  setUpdatedEventObj,
  resetRpeSummaryCount,
  resetPlayerRPEResponse,
} from '../../../store/reducers/Trainings/NewTrainingReducer';
import useTrainingServiceHook, {
  SingleRPERating,
} from './useTrainingServiceHook';
import { userRoleType } from '../../../constants/constants';
import { setSelectedChild } from '../../../store/reducers/Common/CommonReducer';
import { getDateRangeInLocalTimezone } from '../../../helpers/DateHelper';

interface IWeek {
  week: number;
  dates: { date: any; index: number }[];
}

interface IFetchTrainingEventList {
  teamId: string;
  startDate: string;
  endDate: string;
  page: number;
  size: number;
}

interface ITeam {
  coachIds: string[];
  colour: string;
  teamName: string;
  _id: string;
}

interface IMonth {
  year: number;
  monthName: string;
  month: number;
  index: number;
}

interface RPERatingsMap {
  [eventId: string]: string; // eventId to rpeRatingId mapping
}

const useTrainingHook = ({
  isInitalComponent,
}: {
  isInitalComponent: boolean | undefined;
}) => {
  const {
    fetchTrainingEvents,
    fetchRPERatingList,
    removeRPERating,
    updateRPERating,
    fetchRPETotalResponseSummary,
    fetchPlayerRPEList,
    fetchPlayerRPERatings,
    updateSessionUploadByEventId,
  } = useTrainingServiceHook();
  const dispatch = useDispatch();

  const navigation = useNavigation();

  const {
    showEventScreen,
    targetedSelectedTeam,
    selectedMonth,
    futureMonths,
    selectedWeek,
    selectedWeekIndex,
    eventList,
    isLoading,
    showRPEModal,
    selectedEventId,
    RPEList,
    SelectedRPEPlayerList,
    RPEMap,
    playerRPEResponse,
    repResponseCount,
    eventListLoading,
    eventListCurrentPage,
    eventListTotalRecords,
    rpePlayerListTotalRecord,
    rpePlayerListCurrentPage,
    rpePlayerListLoading,
    responseSummerySelection,
    isAttendaceModal,
    isSessionModal,
    selectedEvent,
  } = useSelector((state: RootStore) => state.newTrainingEventsReducer);

  
  
  const { children, selectedChild } = useSelector(
    (state: RootStore) => state.common
  );
  const { selectedTeam } = useSelector((state: RootStore) => state.team);
  const { userData, userRole } = useSelector((state: RootStore) => state?.auth);

  const isParent = userRole === userRoleType.PARENT;
  const isPlayer = userRole === userRoleType.PLAYER;

  const isPlayerOrParent = isParent || isPlayer;
  const isCoaches =
    userRole === userRoleType.COACH || userRole === userRoleType.HEAD_COACH;

  const { month = null, year = null } = selectedMonth ?? {};
  const { teamName } = selectedTeam ?? {};
  const { id: userId } = userData ?? {};

  const selectedMonthCalendar: {
    calendar: IWeek[];
    currentWeek: number;
  } = useMemo(() => {
    if (selectedMonth?.year) {
      return calendarDays(
        `${selectedMonth.year}-${addLeadingZeros(selectedMonth.month)}-01`
      );
    }
    return {
      calendar: [],
      currentWeek: -1,
    };
  }, [selectedMonth]);

  useEffect(() => {
    if (isInitalComponent) {
      const monthList = calendarFutureMonths(12);
      dispatch(setSelectedMonth(monthList[1]));
      const currentMonth =  calendarDays(
        `${monthList[1].year}-${addLeadingZeros(monthList[1].month)}-01`
      );

      if (currentMonth?.currentWeek >= 0) {
        weekStartEndDate(
          currentMonth.calendar[currentMonth.currentWeek]
        );
        dispatch(
          setSelectedWeekIndex(
            Number(
              `${monthList[1].year}${addLeadingZeros(monthList[1].month)}${currentMonth.currentWeek}`
            )
          )
        );
      }

      dispatch(setFutureMonths(monthList));
      fetchRPERatingStaticData();
    }
  }, [isInitalComponent]);

  const setMatchScreen = (response: boolean) => {
    console.log(response);
  };

  const handleChildSelection = (data: any) => {
    let payload = {
      data: [],
      currentPage: 0,
      totalRecords: 0,
    };
    dispatch(setInitalEventList(payload));
    dispatch(setLoading(true));
    dispatch(setSelectedChild(data))
    dispatch(resetPlayerRPEResponse())
  };

  const weekStartEndDate = (
    week: IWeek
  ): { start: string; end: string } | undefined => {
    const nonNullDates = week?.dates?.filter((date: any) => date.date !== null);

    if (!nonNullDates?.length) {
      return undefined;
    }
    const start = dateTimeUTCConversion(nonNullDates[0].date);
    const end = dateTimeUTCConversion(
      nonNullDates[nonNullDates.length - 1].date
    );

    const startLocalDate = new Date(start.year, start.month - 1, start.date, 0, 0, 0);
    const endLocalDate = new Date(end.year, end.month - 1, end.date, 23, 59, 59, 999);

    const startDate = startLocalDate.toISOString();
    const endDate = endLocalDate.toISOString();

    const value = {
      start: startDate,
      end: endDate,
    };
    dispatch(setSelectedWeek(value));
    return value;
  };

  const onSelectedTeam = async (team: ITeam) => {
    if (!isTabDevice()) {
      navigation.navigate('TrainingEvents');
    }
    if(isTabDevice() && !team){
      dispatch(setLoading(false));
      return
    }
    if (selectedWeek) {
      dispatch(setSelectedTeam(team));
      await fetchIntialTrainingEventList({
        teamId: team._id,
        startDate: selectedWeek.start,
        endDate: selectedWeek.end,
        page: 1,
        size: 9,
      });
    }
  };

  const onSelectedWeek = (week: IWeek , selectedMonth? : IMonth) => {
    if(selectedMonth){
      dispatch(
        setSelectedWeekIndex(
          Number(`${selectedMonth?.year}${addLeadingZeros(selectedMonth?.month)}${week.week}`)
        )
      );
    }else{
      dispatch(
        setSelectedWeekIndex(
          Number(`${year}${addLeadingZeros(month)}${week.week}`)
        )
      );
    }
    const result = weekStartEndDate(week);
    if (result) {
      fetchIntialTrainingEventList({
        teamId: targetedSelectedTeam?._id || '',
        startDate: result.start,
        endDate: result.end,
        page: 1,
        size: 9,
      });
    }
  };

  const onSelectMonth = (month: IMonth) => {
    dispatch(setSelectedMonth(month));
    const today = new Date();

    const currentMonthYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;
    
    const isCurrentMonthSelected = 
      month.year === currentMonthYear && 
      month.month === currentMonth;
    
      const selectedMonth =  calendarDays(
        `${month.year}-${addLeadingZeros(month.month)}-01`
      );
      const targetWeek = isCurrentMonthSelected 
      ? selectedMonth.calendar[selectedMonth.currentWeek] 
      : selectedMonth.calendar[0];
    
    onSelectedWeek(targetWeek, month);
  };

  const fetchIntialTrainingEventList = async ({
    teamId,
    startDate,
    endDate,
    page = 1,
    size = 9,
  }: IFetchTrainingEventList) => {

   
    let payload = {
      data: [],
      currentPage: 0,
      totalRecords: 0,
    };
    dispatch(setPlayerRPEResponse({}));
    dispatch(setInitalEventList(payload));
    try {
      dispatch(setLoading(true));
      const rpeResponse = await fetchTrainingEvents({
        teamId,
        startDate: startDate,
        endDate: endDate,
        page,
        pageSize: size,
      });
      if (rpeResponse?.status === 204) {
        dispatch(setInitalEventList(payload));
      } else if (rpeResponse?.data) {
        dispatch(
          setInitalEventList({
            data: rpeResponse?.data?.data,
            currentPage: 1,
            totalRecords: rpeResponse?.data?.totalRecords || 0,
          })
        );
        if (isPlayerOrParent) {
          const eventIds = rpeResponse.data.data.map(rpe => rpe._id);
          await getAllRPEValueByEvents(eventIds);
        }
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const onLoadTrainingEvent = async () => {
    if (
      !eventListLoading &&
      eventListCurrentPage > 0 &&
      eventList.length < eventListTotalRecords &&
      selectedWeek
    ) {
      dispatch(setEventLoading(true));
      try {
        const rpeResponse = await fetchTrainingEvents({
          endDate: selectedWeek?.end,
          startDate: selectedWeek?.start,
          page: eventListCurrentPage + 1,
          pageSize: 9,
          teamId: targetedSelectedTeam?._id || '',
        });

        if (rpeResponse?.status === 204) {
          return;
        } else if (rpeResponse?.data) {
          dispatch(
            setEventList({
              currentPage: rpeResponse?.data.page,
              data: rpeResponse?.data.data,
              totalRecords: rpeResponse?.data?.totalRecords,
            })
          );
          if (isPlayerOrParent) {
            const eventIds = rpeResponse.data.data.map(rpe => rpe._id);
            await getAllRPEValueByEvents(eventIds);
          }
        }
      } catch (error) {
        console.log('Error', error);
      } finally {
        dispatch(setEventLoading(false));
      }
    }
  };

  const getAllRPEValueByEvents = async (eventIds: string[]) => {
    try {
      const selectedPlayerId = isParent  ? selectedChild?.id ||  "" : userId
      const response = await fetchPlayerRPERatings({
        eventIds: eventIds,
        userId: selectedPlayerId,
      });
      const rpeRatingsMap: RPERatingsMap = {};
      response?.data?.forEach((item: SingleRPERating) => {
        rpeRatingsMap[item.eventId] = item.rpeRatingId;
      });
      dispatch(setPlayerRPEResponse(rpeRatingsMap));
    } catch (error) {
      console.error('Error fetching RPE ratings:', error);
    }
  };

  const fetchRPERatingStaticData = async () => {
    try {
      const result = await fetchRPERatingList({ page: 1, pageSize: 20 });
      result?.data && dispatch(setRPEList(result?.data?.data.reverse()));
      const rpeRatingsMapList: RPERatingsMap = {};
      result?.data &&
        result?.data?.data.forEach((item: any) => {
          rpeRatingsMapList[item._id] = item;
        });
      dispatch(setRPEMap(rpeRatingsMapList));
    } catch (error) {
      console.log('error', error);
    }
  };

 const  handelCloseTrainingRating = () => {
  dispatch(
    setRPEModalView({
      eventId: '',
      showRPEModal: false,
    })
  );
 }

  const handleRPERating = async (RPEId: string | undefined) => {
    try {
      if (RPEId) {
        const selectedPlayerId = isParent  ? selectedChild?.id ||  "" : userId
        const response = await updateRPERating({
          eventId: selectedEventId || '',
          userId: selectedPlayerId,
          rpeRatingId: RPEId,
        });

        if (response?.status == 200) {
          const rpeRatingsMap: RPERatingsMap = {};
          rpeRatingsMap[selectedEventId || ''] = RPEId;
          dispatch(setPlayerRPEResponse(rpeRatingsMap));
        }
      } else {
        const response = await removeRPERating({
          eventId: selectedEventId || '',
          userId: userId,
        });
        if (response) {
          const rpeRatingsMap: RPERatingsMap = {};
          rpeRatingsMap[selectedEventId || ''] = '';
          dispatch(setPlayerRPEResponse(rpeRatingsMap));
        }
      }
    } catch (error) {
    } finally {
      dispatch(
        setRPEModalView({
          eventId: '',
          showRPEModal: false,
        })
      );
    }
  };

  const handleRPEModal = async ({
    eventId,
    showRPEModal,
  }: {
    eventId: string;
    showRPEModal: boolean;
  }) => {
    dispatch(resetRpePlayerList());
    dispatch(resetRpeSummaryCount());
    dispatch(setResponseSummerySelection(true));
    if (isCoaches && eventId) {
      const RPECountResponse = await fetchRPETotalResponseSummary(eventId);
      dispatch(
        setRPEModalView({
          eventId,
          showRPEModal,
        })
      );
      await getPlayerRPEList({
        eventId: eventId,
        isResponded: responseSummerySelection,
        page: 1,
        size: 10,
      });
      RPECountResponse && dispatch(setREPresponceCount(RPECountResponse));
    }

    dispatch(
      setRPEModalView({
        eventId,
        showRPEModal,
      })
    );
  };

  const getPlayerRPEList = async ({
    eventId,
    isResponded,
    page,
    size,
    filterByRating,
    isRPEScoreCal,
  }: {
    eventId: string;
    isResponded: boolean;
    page: number;
    size: number;
    filterByRating?: any;
    isRPEScoreCal?: boolean;
  }) => {
    dispatch(setRpePlayerListLoading(true));
    try {
      const response = await fetchPlayerRPEList({
        eventId: eventId,
        isResponded: isRPEScoreCal ? undefined : isResponded,
        page: page,
        size: size,
        filterByRating: filterByRating,
        isRPEScoreCal: isRPEScoreCal ? isRPEScoreCal : false,
      });
      response?.data &&
        dispatch(
          setInitalRPEPlayerList({
            currentPage: response?.page,
            data: response?.data,
            totalRecords: response?.totalRecords,
          })
        );
    } catch (error) {
      console.log('Error while fetching RPE list', error);
    } finally {
      dispatch(setRpePlayerListLoading(false));
    }
  };

  const handlePlayerResponsePagination = ({
    isRPEScoreCal,
    isResponse,
  }: {
    isRPEScoreCal: boolean;
    isResponse: boolean;
  }) => {
    if (
      !rpePlayerListLoading &&
      rpePlayerListCurrentPage > 0 &&
      SelectedRPEPlayerList?.length < rpePlayerListTotalRecord
    ) {
      getPlayerRPEList({
        eventId: selectedEventId || '',
        isResponded: isResponse,
        page: rpePlayerListCurrentPage + 1,
        size: 10,
        isRPEScoreCal: isRPEScoreCal ? isRPEScoreCal : false,
      });
    }
  };

  const handlePlayerResponseFilter = (value: string) => {
    dispatch(resetRpePlayerList());
    if (!rpePlayerListLoading) {
      getPlayerRPEList({
        eventId: selectedEventId || '',
        isResponded: true,
        page: 1,
        size: 10,
        filterByRating: value === '_' ? '' : value,
      });
    }
  };

  const handleResponseSummerySelection = ({
    isRPEScoreCal,
    isResponse,
  }: {
    isRPEScoreCal: boolean;
    isResponse: boolean;
  }) => {
    dispatch(resetRpePlayerList());
    dispatch(setResponseSummerySelection(isResponse));
    getPlayerRPEList({
      eventId: selectedEventId || '',
      isResponded: isResponse,
      page: 1,
      size: 10,
      isRPEScoreCal: isRPEScoreCal,
    });
  };

  const handleAttendanceModal = ({
    eventId,
    isShow,
  }: {
    eventId: string;
    isShow: boolean;
  }) => {
    dispatch(setAttendanceModal({ eventId, isShow }));
  };

  const handleSessionModal = ({
    event,
    isSessionModal,
  }: {
    event: any;
    isSessionModal: boolean;
  }) => {
    dispatch(setSessionUploadModal({ event, isSessionModal }));
  };

  const onSaveDocuments = async (fileUploads: any) => {
    let updatedEventObj = { ...selectedEvent };
    if (fileUploads) {
      updatedEventObj = { ...updatedEventObj, fileUploads };
    }
    try {
      await updateSessionUploadByEventId({ updatedEvent: updatedEventObj });
      const targetedIndex = eventList?.findIndex(
        event => event?._id === updatedEventObj?._id
      );
      dispatch(
        setUpdatedEventObj({
          index: targetedIndex,
          event: updatedEventObj,
        })
      );
    } catch (error) {
      console.log(error);
    }
  };

  return {
    children,
    selectedChild,
    selectedMonth,
    futureMonths,
    selectedWeek,
    year,
    month,
    selectedMonthCalendar,
    selectedWeekIndex,
    showEventScreen,
    selectedTeam,
    eventList,
    isLoading,
    selectedTeamName: teamName,
    showRPEModal,
    RPEList,
    SelectedRPEPlayerList,
    RPEMap,
    playerRPEResponse,
    selectedEventId,
    repResponseCount,
    eventListLoading,
    rpePlayerListLoading,
    responseSummerySelection,
    isAttendaceModal,
    isSessionModal,
    selectedEvent,
    handleChildSelection,
    setMatchScreen,
    onSelectedTeam,
    onSelectedWeek,
    onSelectMonth,
    setShowEventScreen: (show: boolean) => dispatch(setShowEventScreen(show)),
    fetchRPERatingStaticData,
    handleRPEModal,
    handleRPERating,
    onLoadTrainingEvent,
    handlePlayerResponsePagination,
    handlePlayerResponseFilter,
    handleResponseSummerySelection,
    handleAttendanceModal,
    handleSessionModal,
    onSaveDocuments,
    handelCloseTrainingRating
  };
};
export default useTrainingHook;
