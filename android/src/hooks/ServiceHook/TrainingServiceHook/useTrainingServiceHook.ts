import {
  EVENT_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../../constants/services';
import { ITraningEvent } from '../../../store/reducers/Trainings/NewTrainingReducer';
import useApiPromise from '../../useApiPromise';

interface FetchTrainingEventsParams {
  teamId: string;
  startDate: string;
  endDate: string;
  page: number;
  pageSize: number;
}

interface FetchRPERatingListParams {
  page: number;
  pageSize: number;
}

interface TrainingEventsResponse {
  status: number;
  data:
    | {
        data: ITraningEvent[];
        totalRecords: number;
        page: number
      }
    | '';
}

interface TrainingRPEListResponse {
  status: number;
  data:
    | {
        data: any[];
        totalCount: number;
      }
    | '';
}

interface RPERatingResponse {
  data: {
    _id: string;
    trainingId: string;
    rpeRatingId: string;
    userId: string;
  };
  status: number;
}

interface UpdateRPERatingParams {
  eventId: string;
  userId: string;
  rpeRatingId: string;
}

interface RemoveRPERatingParams {
  eventId: string;
  userId: string;
}

interface FetchPlayerRPERatingsParams {
  eventIds: string[];
  userId: string;
}

export interface SingleRPERating {
  eventId: string;
  rpeRatingId: string;
  userId: string;
}

export interface PlayerRPERating {
  data: SingleRPERating[];
  page: number;
  size: number;
  totalRecords: number;
}

interface RPETotalResponseSummary {
  responded: number;
  noResponse: number;
}

export interface singlePlayerRPEResponse {
  _id: string;
  trainingId: string;
  rpeRatingId: string;
  userId: string;
}

interface PlayerRPEResponse {
  data: singlePlayerRPEResponse[];
  page?: number;
  size?: number;
}

interface FetchPlayerRPEListParams {
  eventId: string;
  isResponded?: boolean;
  page?: number;
  size?: number;
  filterByRating?: number;
  isRPEScoreCal?: boolean;
}

interface UserResponse {
  id: string;
  firstName: string;
  lastName: string;
}

const useTrainingService = () => {
  const [makeApiRequest] = useApiPromise();

  const fetchTrainingEvents = async ({
    teamId,
    startDate,
    endDate,
    page,
    pageSize,
  }: FetchTrainingEventsParams): Promise<TrainingEventsResponse | null> => {
    try {
      const queryParams = new URLSearchParams({
        teamId,
        type: 'training',
        startDate: new Date(startDate).toISOString(),
        endDate: new Date(endDate).toISOString(),
        page: page.toString(),
        size: pageSize.toString(),
      });
      const response = await makeApiRequest(
        `/api/v1/events?${queryParams}`,
        '_',
        '_',
        '_',
        null,
        '',
        'GET',
        undefined,
        EVENT_SERVICE
      );
      return response as TrainingEventsResponse;
    } catch (error) {
      console.error('Failed to fetch training events:', error);
      return null;
    }
  };

  const fetchRPERatingList = async ({
    page,
    pageSize,
  }: FetchRPERatingListParams): Promise<any | null> => {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        size: pageSize.toString(),
      });
      const response = await makeApiRequest(
        `/api/v1/rpe-ratings?${queryParams}`,
        '_',
        '_',
        '_',
        null,
        '',
        'GET',
        undefined,
        EVENT_SERVICE
      );
      return response as TrainingRPEListResponse;
    } catch (error) {
      console.error('Failed to fetch training events:', error);
      return null;
    }
  };

  const updateRPERating = async ({
    eventId,
    userId,
    rpeRatingId,
  }: UpdateRPERatingParams): Promise<RPERatingResponse | null> => {
    try {
      const response = await makeApiRequest(
        `/api/v1/events/${eventId}/users/${userId}/player-rpe-ratings`,
        '_',
        '_',
        '_',
        { rpeRatingId },
        '',
        'PUT',
        undefined,
        EVENT_SERVICE
      );
      return response as RPERatingResponse;
    } catch (error) {
      console.error('Failed to update RPE rating:', error);
      return null;
    }
  };

  const removeRPERating = async ({
    eventId,
    userId,
  }: RemoveRPERatingParams): Promise<boolean> => {
    try {
      await makeApiRequest(
        `/api/v1/events/${eventId}/users/${userId}/player-rpe-ratings`,
        '_',
        '_',
        '_',
        null,
        '',
        'DELETE',
        undefined,
        EVENT_SERVICE
      );
      return true;
    } catch (error) {
      console.error('Failed to remove RPE rating:', error);
      return false;
    }
  };

  const fetchPlayerRPERatings = async ({
    eventIds,
    userId,
  }: FetchPlayerRPERatingsParams): Promise<PlayerRPERating | null> => {
    try {
      const queryParams = new URLSearchParams({
        userId: userId.toString(),
        eventIds: eventIds.join(',').toString(),
      });

      const response = await makeApiRequest(
        `/api/v1/player-rpe-ratings?${queryParams}`,
        '_',
        '_',
        '_',
        null,
        '',
        'GET',
        undefined,
        EVENT_SERVICE
      );

      return response.data as PlayerRPERating;
    } catch (error) {
      console.error('Failed to fetch player RPE ratings:', error);
      return null;
    }
  };

  const fetchRPETotalResponseSummary = async (
    eventId: string
  ): Promise<RPETotalResponseSummary | null> => {
    try {
      const response = await makeApiRequest(
        `/api/v1/events/${eventId}/player-rpe-ratings/response-summary`,
        '_',
        '_',
        '_',
        null,
        '',
        'GET',
        undefined,
        EVENT_SERVICE
      );
      return response.data as RPETotalResponseSummary;
    } catch (error) {
      console.error('Failed to fetch RPE response summary:', error);
      return null;
    }
  };

  const fetchUserDetails = async (
    userIds: string[]
  ): Promise<UserResponse[]> => {
    try {
      const response = await makeApiRequest(
        `/api/v1/users?page=1&size=${userIds.length}&userIds=${userIds.toString()}`,
        '_',
        '_',
        '_',
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch user details:', error);
      return [];
    }
  };

  const fetchPlayerRPEList = async ({
    eventId,
    isResponded,
    page = 1,
    size = 10,
    filterByRating,
    isRPEScoreCal
  }: FetchPlayerRPEListParams): Promise<{
    data: singlePlayerRPEResponse[];
    page: number;
    size: 10;
    totalRecords: number;
  } | null> => {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        size: size.toString(),
      });

      if (isResponded) {
        queryParams.append('isResponded', isResponded.toString());
      }


      if (filterByRating) {
        queryParams.append('filterByRating', filterByRating.toString());
      }

      if(isRPEScoreCal){
        queryParams.append('isRPEScoreCal', isRPEScoreCal.toString());
      }



      const rpeResponse = await makeApiRequest(
        `/api/v1/events/${eventId}/player-rpe-ratings?${queryParams}`,
        '_',
        '_',
        '_',
        null,
        '',
        'GET',
        undefined,
        EVENT_SERVICE
      );

      const rpeData = rpeResponse.data as PlayerRPEResponse;
      const userIds = rpeData?.data.map((data) => data.userId);
      const userDetails = await fetchUserDetails(userIds);

      const userMap: { [key: string]: UserResponse } = {};
      userDetails.forEach(user => {
        userMap[user.id] = user;
      });

      const mergedData = rpeData.data.map(rpe => ({
        ...rpe,
        userDetails: userMap[rpe.userId],
      }));

      return {
        data: mergedData,
        page: rpeResponse?.data?.page,
        size: rpeResponse?.data?.size,
        totalRecords: rpeResponse.data.totalRecords,
      };
    } catch (error) {
      console.error('Failed to fetch player RPE list:', error);
      return null;
    }
  };

  const updateSessionUploadByEventId = async ({
    updatedEvent,
  }: {updatedEvent : any}): Promise<any> => {
    try {
      const response = await makeApiRequest(
       `/api/v1/events`,
        '_',
        '_',
        '_',
        { ...updatedEvent },
        '',
        'PUT',
        undefined,
        EVENT_SERVICE
      );
      return response as any;
    } catch (error) {
      console.error('Failed to update RPE rating:', error);
      return null;
    }
  };
  return {
    fetchTrainingEvents,
    fetchRPERatingList,
    updateRPERating,
    removeRPERating,
    fetchPlayerRPERatings,
    fetchRPETotalResponseSummary,
    fetchPlayerRPEList,
    updateSessionUploadByEventId
  };
};

export default useTrainingService;
