import { useSelector } from 'react-redux';
import { EVENT_SERVICE } from '../../../constants/services';
import { userRoleType } from '../../../constants/constants';
import useApiPromise from '../../useApiPromise';

const useMatchPlanService = () => {
  const [makeApiRequest] = useApiPromise();

  const fetchMatchPlanDataByMatchId = async (matchId) => {
    try {
      const response = await makeApiRequest(
        `/api/v1/matches/${matchId}/match-plan`,
        '_',
        '_',
        '_',
        null,
        null,
        'GET',
        undefined,
        EVENT_SERVICE
      );
      
      return response.data
    } catch (error) {
      console.error('Failed to fetch match plan data:', error);
      return null;
    }
  };

  return {
    fetchMatchPlanDataByMatchId,
  };
};

export default useMatchPlanService;