import { useState } from 'react';

const useInputSelectModal = () => {
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [dropDownModalIsOpen, setDropDownModalIsOpen] =
    useState<boolean>(false);
  const [isNewDataAddingCompleted, setIsNewDataAddingCompleted] =
    useState<boolean>(false);

  return [
    setDropDownModalIsOpen,
    dropDownModalIsOpen,
    setSelectedItems,
    selectedItems,
    setIsNewDataAddingCompleted,
    isNewDataAddingCompleted,
  ] as const;
};

export default useInputSelectModal;
