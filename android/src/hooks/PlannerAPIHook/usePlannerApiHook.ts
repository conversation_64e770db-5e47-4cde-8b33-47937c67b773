import { userRoleType } from '../../constants/constants';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import {
  EVENT_RSVP_LIST_INITIAL_FAILED,
  EVENT_RSVP_LIST_INITIAL_REQUEST,
  EVENT_RSVP_LIST_INITIAL_SUCCESS,
  EVENT_RSVP_LIST_MORE_FAILED,
  EVENT_RSVP_LIST_MORE_REQUEST,
  EVENT_RSVP_LIST_MORE_SUCCESS,
  EVENT_RSVP_LIST_TOTAL_RECORDS_FAILED,
  EVENT_RSVP_LIST_TOTAL_RECORDS_REQUEST,
  EVENT_RSVP_LIST_TOTAL_RECORDS_SUCCESS,
  FETCH_ALL_EVENTS_FAIL,
  FETCH_ALL_EVENTS_REQUEST,
  FETCH_ALL_EVENTS_SUCCESS,
} from '../../store/actionTypes/Planner/PlannerAction';
import {
  MORE_TEAM_FAIL,
  MORE_TEAM_REQUEST,
  MORE_TEAM_SUCCESS,
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import useApi from '../useApi';

export const usePlannerApiHook = () => {
  const [fetchTeamData] = useApi();

  const requestTeamUrlInt = (
    userData: any,
    teamInitialPage: number,
    teamInitialSize: number,
    childInformation: any
  ) => {
    switch (userData?.type) {
      case userRoleType.PLAYER:
        return `/api/v1/sport-profiles/${userData?.sportsProfileId}/teams?page=${teamInitialPage}&size=${teamInitialSize}`;
      case userRoleType.PARENT:
        return `/api/v1/sport-profiles/${childInformation?.sportsProfileId}/teams?page=${teamInitialPage}&size=${teamInitialSize}`;
      default:
        return `/api/v1/teams?coachId=${
          userData?.id
        }&page=${teamInitialPage}&size=${50}`;
    }
  };

  const requestTeamUrl = (
    userData: any,
    teamPage: number,
    teamSize: number,
    childInformation: any
  ) => {
    switch (userData?.type) {
      case userRoleType.PLAYER:
        return `/api/v1/sport-profiles/${
          userData?.sportsProfileId
        }/teams?page=${teamPage + 1}&size=${teamSize}`;
      case userRoleType.PARENT:
        return `/api/v1/sport-profiles/${
          childInformation?.sportsProfileId
        }/teams?page=${teamPage + 1}&size=${teamSize}`;
      default:
        return `/api/v1/teams?coachId=${userData?.id}&page=${
          teamPage + 1
        }&size=${50}`;
    }
  };
  const getFetchTeamDetails = (
    userData: any,
    teamInitialPage: number,
    teamInitialSize: number,
    childInformation: any
  ) => {
    fetchTeamData(
      requestTeamUrlInt(
        userData,
        teamInitialPage,
        teamInitialSize,
        childInformation
      ),
      TEAM_REQUEST,
      TEAM_SUCCESS,
      TEAM_FAIL,
      null,
      '',
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };
  const getMoreTeamDetails = (
    userData: any,
    teamPage: number,
    teamSize: number,
    childInformation: any
  ) => {
    fetchTeamData(
      requestTeamUrl(userData, teamPage, teamSize, childInformation),
      MORE_TEAM_REQUEST,
      MORE_TEAM_SUCCESS,
      MORE_TEAM_FAIL,
      null,
      '',
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  return { getFetchTeamDetails, getMoreTeamDetails } as const;
};

export const useCalenderEventsApiHook = () => {
  const [fetchAllEvents] = useApi();

  const fetchCalenderData = (requestUrl: any) => {
    fetchAllEvents(
      requestUrl,
      FETCH_ALL_EVENTS_REQUEST,
      FETCH_ALL_EVENTS_SUCCESS,
      FETCH_ALL_EVENTS_FAIL,
      null,
      '',
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  return { fetchCalenderData } as const;
};

export const useRSVAPHook = () => {
  const [apiClient] = useApi();
  const [getTotalRecordsOfRsvp] = useApi();

  const getRSVData = (
    eventId: string,
    selecteduserRoleType: string,
    rsvpListPageSize: number,
    response: string
  ) => {
    apiClient(
      `/api/v1/events/${eventId}/invitees?type=PLAYER&page=1&size=${rsvpListPageSize}&rsvpResponse=${response}`,
      EVENT_RSVP_LIST_INITIAL_REQUEST,
      EVENT_RSVP_LIST_INITIAL_SUCCESS,
      EVENT_RSVP_LIST_INITIAL_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
    getTotalRecordsOfRsvp(
      `/api/v1/events/${eventId}/invitees/rsvp-response-summary?type=${selecteduserRoleType}`,
      EVENT_RSVP_LIST_TOTAL_RECORDS_REQUEST,
      EVENT_RSVP_LIST_TOTAL_RECORDS_SUCCESS,
      EVENT_RSVP_LIST_TOTAL_RECORDS_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  const loadNextPage = (
    eventId: string,
    rsvpListPageCount: number,
    rsvpListPageSize: number,
    response: string
  ) => {
    apiClient(
      `/api/v1/events/${eventId}/invitees?type=PLAYER&page=${rsvpListPageCount}&size=${rsvpListPageSize}&rsvpResponse=${response}`,
      EVENT_RSVP_LIST_MORE_REQUEST,
      EVENT_RSVP_LIST_MORE_SUCCESS,
      EVENT_RSVP_LIST_MORE_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  return { getRSVData, loadNextPage } as const;
};
