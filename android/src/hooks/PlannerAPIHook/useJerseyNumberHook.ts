import { FOOTBALL_SERVICE } from '../../constants/services';
import useApiPromiseArray from '../useApiPromiseArray';

export const useJerseyNumberHook = () => {
  const [getJerseyDetails] = useApiPromiseArray();

  const fetchJerseyData = async (
    sportsProfileIds: string[],
    teamId: string
  ) => {
    const chunkOfChatMemberList = [];
    const chunkSize = 50;
    for (let i = 0; i < sportsProfileIds?.length; i += chunkSize) {
      const chunk = sportsProfileIds?.slice(i, i + chunkSize);
      chunkOfChatMemberList.push(chunk);
    }

    const personalChatMemberInfoUrls = [];
    for (let index = 0; index < chunkOfChatMemberList.length; index++) {
      const element = chunkOfChatMemberList[index];
      personalChatMemberInfoUrls.push(
        `/api/v1/teams/${teamId}/players/jersey-numbers?profileIds=${element}&page=1&size=50`
      );
    }

    try {
      const values: any = await getJerseyDetails(
        personalChatMemberInfoUrls,
        'GET',
        FOOTBALL_SERVICE
      );
      const memberInfoList = [
        ...values?.flatMap((res: any) => res?.data?.data),
      ].filter(memberInfo => memberInfo);

      return memberInfoList;
    } catch (error) {
      return null;
    }
  };
  return [fetchJerseyData] as const;
};
