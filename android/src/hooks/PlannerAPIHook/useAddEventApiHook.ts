import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import {
  EVENT_ADD_FAIL,
  EVENT_ADD_REQUEST,
  EVENT_ADD_SUCCESS,
  EVENT_DELETE_FAIL,
  EVENT_DELETE_REQUEST,
  EVENT_DELETE_SUCCESS,
  FETCH_EVENT_RELATED_DATA_FAIL,
  FETCH_EVENT_RELATED_DATA_REQUEST,
  FETCH_EVENT_RELATED_DATA_SUCCESS,
} from '../../store/actionTypes/Event/EventActionTypes';
import {
  EVENT_OPPONENT_DETAILS_FAILED,
  EVENT_OPPONENT_DETAILS_REQUEST,
  EVENT_OPPONENT_DETAILS_SUCCESS,
  EVENT_RSVP_FETCH_FAILED,
  EVENT_RSVP_FETCH_REQUEST,
  EVENT_RSVP_FETCH_SUCCESS,
  EVENT_RSVP_SAVE_FAILED,
  EVENT_RSVP_SAVE_REQUEST,
  EVENT_RSVP_SAVE_SUCCESS,
  EVENT_SEASON_DETAILS_FAILED,
  EVENT_SEASON_DETAILS_REQUEST,
  EVENT_SEASON_DETAILS_SUCCESS,
  EVENT_TOURNAMENT_DETAILS_FAILED,
  EVENT_TOURNAMENT_DETAILS_REQUEST,
  EVENT_TOURNAMENT_DETAILS_SUCCESS,
} from '../../store/actionTypes/Planner/PlannerAction';
import {
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import useApi from '../useApi';
import useApiPromise from '../useApiPromise';

export const useAddEventApiHook = () => {
  const [fetchTeamData] = useApi();
  const [fetchEventData] = useApi();
  const [addEventData] = useApi();
  const [deleteEvent] = useApi();
  const [addNewDataPromise] = useApiPromise();

  const getTeamDetails = (userData: any) => {
    fetchTeamData(
      `/api/v1/teams?coachId=${userData?.id}&page=1&size=100`,
      TEAM_REQUEST,
      TEAM_SUCCESS,
      TEAM_FAIL,
      null,
      '',
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  const getEventRelatedDetails = (
    type: string,
    searchKey: string,
    page: number
  ) => {
    fetchEventData(
      `/api/v1/${type}?name=${searchKey}&page=${page}&size=20`,
      FETCH_EVENT_RELATED_DATA_REQUEST,
      FETCH_EVENT_RELATED_DATA_SUCCESS,
      FETCH_EVENT_RELATED_DATA_FAIL,
      null,
      '',
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  const handleEvent = (isEdit: boolean, createEventData: any, event: any) => {
    let updatedCreteEventData;
    if (isEdit && event?.fileUploads) {
      updatedCreteEventData = {
        ...event,
        ...createEventData,
      };
    } else {
      updatedCreteEventData = {
        ...createEventData,
      };
    }

    addEventData(
      `/api/v1/events`,
      EVENT_ADD_REQUEST,
      EVENT_ADD_SUCCESS,
      EVENT_ADD_FAIL,
      updatedCreteEventData,
      '',
      isEdit ? 'PUT' : 'POST',
      false,
      EVENT_SERVICE
    );
  };

  const handleDeleteEvent = (event: any) => {
    deleteEvent(
      `/api/v1/events?eventIds=${event._id}`,
      EVENT_DELETE_REQUEST,
      EVENT_DELETE_SUCCESS,
      EVENT_DELETE_FAIL,
      null,
      '',
      'DELETE',
      false,
      EVENT_SERVICE
    );
  };

  const createEventRelatedListDataPromise = (type: string, name: string) => {
    return addNewDataPromise(
      `/api/v1/${type}?name=${name}`,
      '-',
      '-',
      '-',
      {
        name,
      },
      '',
      'PUT',
      false,
      EVENT_SERVICE
    );
  };

  const getLocation = async (location: any, GOOGLE_API_KEY: string) => {
    return fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?place_id=${location.place_id}&key=${GOOGLE_API_KEY}`
    );
  };

  return {
    getTeamDetails,
    getEventRelatedDetails,
    handleEvent,
    handleDeleteEvent,
    createEventRelatedListDataPromise,
    getLocation,
  } as const;
};

export const useEventViewApiHook = () => {
  const [apiClient] = useApi();
  const [fetchTournamentData] = useApi();
  const [fetchOpponentData] = useApi();
  const [fetchSeasonData] = useApi();
  const [fetchEventData] = useApiPromise();

  const getEventDetail = async (eventId: string) => {
    const { data } = await fetchEventData(
      `/api/v1/events?eventIds=${[eventId]}`,
      '',
      '',
      '',
      undefined,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );

    return data.data[0];
  };

  const saveUserRsvpResponse = (eventId: string, requestBody: any) => {
    apiClient(
      `/api/v1/events/${eventId}/invitees`,
      EVENT_RSVP_SAVE_REQUEST,
      EVENT_RSVP_SAVE_SUCCESS,
      EVENT_RSVP_SAVE_FAILED,
      requestBody,
      null,
      'PUT',
      false,
      EVENT_SERVICE
    );
  };

  const getTournamentData = (tournamentId: string) => {
    fetchTournamentData(
      `/api/v1/tournaments?tournamentIds=${tournamentId}&page=1&size=1`,
      EVENT_TOURNAMENT_DETAILS_REQUEST,
      EVENT_TOURNAMENT_DETAILS_SUCCESS,
      EVENT_TOURNAMENT_DETAILS_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };
  const getOpponentData = (opponentId: string) => {
    fetchOpponentData(
      `/api/v1/opponents?opponentIds=${opponentId}&page=1&size=1`,
      EVENT_OPPONENT_DETAILS_REQUEST,
      EVENT_OPPONENT_DETAILS_SUCCESS,
      EVENT_OPPONENT_DETAILS_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };
  const getSeasonData = (seasonId: string) => {
    fetchSeasonData(
      `/api/v1/seasons?seasonIds=${seasonId}&page=1&size=1`,
      EVENT_SEASON_DETAILS_REQUEST,
      EVENT_SEASON_DETAILS_SUCCESS,
      EVENT_SEASON_DETAILS_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };
  const getSelectedUserData = (eventId: string, playerId: string) => {
    apiClient(
      `/api/v1/events/${eventId}/invitees?userIds=${playerId}`,
      EVENT_RSVP_FETCH_REQUEST,
      EVENT_RSVP_FETCH_SUCCESS,
      EVENT_RSVP_FETCH_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  return {
    saveUserRsvpResponse,
    getTournamentData,
    getOpponentData,
    getSeasonData,
    getSelectedUserData,
    getEventDetail,
  } as const;
};
