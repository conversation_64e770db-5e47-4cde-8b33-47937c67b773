import { useEffect, useState } from 'react';

import useApi from './useApi';

import { useSelector } from 'react-redux';
import {
  FETCH_S3_BUCKET_LOCATION_FAIL,
  FETCH_S3_BUCKET_LOCATION_REQUEST,
  FETCH_S3_BUCKET_LOCATION_SUCCESS,
} from '../store/actionTypes/common/commonActionTypes';
import { RootStore } from '../store/store';
const useS3bucketLocation = () => {
  const [s3FilePathAndService, setS3FilePathAndService] = useState<{
    path: string;
    service: string;
  } | null>(null);
  const { s3BucketLocation } = useSelector((state: RootStore) => state.common);

  const [apiCall] = useApi();

  useEffect(() => {
    s3FilePathAndService?.path &&
      apiCall(
        `/api/v1/cloud-storage/path/${s3FilePathAndService?.path || ''}`,
        FETCH_S3_BUCKET_LOCATION_REQUEST,
        FETCH_S3_BUCKET_LOCATION_SUCCESS,
        FETCH_S3_BUCKET_LOCATION_FAIL,
        {},
        '',
        'GET',
        false,
        s3FilePathAndService?.service || ''
      );
  }, [s3FilePathAndService]);

  return [setS3FilePathAndService, s3BucketLocation] as const;
};

export default useS3bucketLocation;
