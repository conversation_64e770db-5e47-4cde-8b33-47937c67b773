import { Auth, Signer } from 'aws-amplify';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { w3cwebsocket as W3CWebSocket } from 'websocket';
import {
  INITIATE_WS,
  TRY_WS_RE_CONNECT,
  WS_IS_CONNECT,
  SET_REFRESH_COUNT,
} from '../store/actionTypes/common/commonActionTypes';
import { RootStore } from '../store/store';

const useWSMessages = (customWss: string, wsType: string) => {
  //WEBSOCKET CONNECTION TO AWS
  const { user, userData, configurationData } = useSelector(
    (state: RootStore) => state.auth
  );

  const { wsClient, isWsReconnect, refreshCount, refreshCountLimit } =
    useSelector((state: RootStore) => state.common);

  const customRefreshCount = refreshCount?.[wsType];

  const customWsClient = wsClient?.[wsType];

  const [newMessage, setNewMessage] = useState<any>(null);

  const dispatch = useDispatch();

  const [credentials, setCredentials] = useState<any>(null);

  const setRefreshCount = (count: any) => {
    dispatch({
      type: SET_REFRESH_COUNT,
      payload: count,
      customInput: wsType,
    });
  };

  const handleTriggerConnection = (
    credentials: any,
    configurationData: any
  ) => {
    const { Auth } = configurationData || {};
    const { wss } = Auth || {};
    const signedUrl = Signer.signUrl(customWss, credentials);
    const ws = new W3CWebSocket(signedUrl);
    dispatch({ type: INITIATE_WS, payload: ws, customInput: wsType });
  };
  const getAccessCredentials = async () => {
    const credentials = await Auth.currentCredentials();

    const accessInfo = {
      access_key: credentials.accessKeyId,
      secret_key: credentials.secretAccessKey,
      session_token: credentials.sessionToken,
    };
    setCredentials(accessInfo);
  };

  useEffect(() => {
    credentials &&
      configurationData &&
      handleTriggerConnection(credentials, configurationData);
  }, [credentials]);

  const wsSetup = useCallback(() => {
    user?.username && getAccessCredentials();
  }, [user]);

  const reconnectWebSocket = (value: boolean) => {
    dispatch({
      type: TRY_WS_RE_CONNECT,
      payload: { data: value },
      customInput: wsType,
    });
  };

  useEffect(() => {
    if (customWsClient) {
      customWsClient.onerror = e => {
        console.error('Error opening websocket connection', e);
      };
      customWsClient.onopen = () => {
        console.log('WebSocket connection established successfully');
        reconnectWebSocket(false);
        dispatch({
          type: WS_IS_CONNECT,
          payload: { data: true },
          customInput: wsType,
        });

        setRefreshCount(0);
      };

      customWsClient.onclose = () => {
        console.log('The websocket connection has been closed successfully.');
        dispatch({
          type: WS_IS_CONNECT,
          payload: { data: false },
          customInput: wsType,
        });

        dispatch({ type: INITIATE_WS, payload: null, customInput: wsType });
        if (customRefreshCount < refreshCountLimit) {
          setRefreshCount(+customRefreshCount + 1);
          setTimeout(() => {
            getAccessCredentials();
          }, 2500);
        }
      };
      if (customRefreshCount >= refreshCountLimit) {
        reconnectWebSocket(false);
      }
    }
  }, [customWsClient, customRefreshCount]);

  useEffect(() => {
    return () => {
      customWsClient?.close();
    };
  }, [customWsClient]);

  useEffect(() => {
    if (isWsReconnect?.[wsType] && !customWsClient) {
      getAccessCredentials();
    }
  }, [customWsClient, isWsReconnect?.[wsType]]);

  const initiateWS = () => {
    wsSetup();
  };

  const onCloseWs = () => {
    customWsClient?.close();
  };

  return [initiateWS, customWsClient, newMessage, onCloseWs] as const;
};

export default useWSMessages;
