import { useDispatch, useSelector } from 'react-redux';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import useApi from './useApi';
import {
  FOOTBALL_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../constants/services';

import {
  FETCH_IMAGE_URL_REQUEST,
  FETCH_IMAGE_URL_SUCCESS,
  FETCH_IMAGE_URL_FAIL,
} from '../store/actionTypes/common/commonActionTypes';

const useGenerateImageUrls = () => {
  const [s3FileObject, setS3FileObject] = useState();
  const { imageUrl } = useSelector(state => state?.common);
  const { id, ...s3objectFiltered } = s3FileObject?.s3Object || {};
  const [apiCall] = useApi();

  useEffect(() => {
    if (s3FileObject?.s3Object) {
      apiCall(
        '/api/v1/cloud-storage/presigned-url',
        FETCH_IMAGE_URL_REQUEST,
        FETCH_IMAGE_URL_SUCCESS,
        FETCH_IMAGE_URL_FAIL,
        { ...s3objectFiltered.s3OBucketObject },
        '',
        'POST',
        null,
        USER_MANAGEMENT_SERVICE,
        { id }
      );
    }
  }, [JSON.stringify(s3FileObject)]);

  const getFileObject = s3Object => {
    s3Object &&
      setS3FileObject({ s3Object, timestamp: new Date().toISOString() });
  };
  return [getFileObject, imageUrl[id] || null];
};

export default useGenerateImageUrls;
