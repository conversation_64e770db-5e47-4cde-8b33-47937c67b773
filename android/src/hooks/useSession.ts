import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { userRoleType } from '../constants/constants';
import { EVENT_SERVICE } from '../constants/services';
import { dateTimeConversion, dateTimeUTCConversion } from '../helpers';
import { RootStore } from '../store/store';
import useApiPromise from './useApiPromise';

const useSession = () => {
  const { userData } = useSelector((state: RootStore) => state?.auth);
  const { PlayerInfoData } = useSelector(
    (state: RootStore) => state?.playerInfo
  );

  const [mode, setMode] = useState('date');
  const [isDatePikerEnable, setIsDatePikerEnable] = useState(false);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [addNumberOfSessions, setAddNumberOfSessions] = useState<any>();
  const [addExpireDate, setAddExpireDate] = useState<any>(null);
  const [expireDateFormatted, setExpireDateFormatted] = useState<any>(null);
  const [errorMassageOnSubmit, setErrorMassageOnSubmit] = useState<any>('');

  const [userSessionUpdates, setUserSessionUpdates] = useState<any>([]);

  const [currentPage, setCurrentPage] = useState(1);

  const isParent = userRoleType.PLAYER === userData?.type;
  const isPlayer = userRoleType.PARENT === userData?.type;
  const isCoach = userRoleType.COACH === userData?.type;

  const isPlayerOrParent = isParent || isPlayer || isCoach;

  const [submitSessions] = useApiPromise();
  const [getSessionsSummery] = useApiPromise();
  const [getUserLastSessions] = useApiPromise();

  const pageIncrement = useCallback(() => {
    const newPageNumber = currentPage + 1;

    setCurrentPage(newPageNumber);
  }, [currentPage]);

  useEffect(() => {
    PlayerInfoData && getSessionUpdate(1);
  }, []);

  const clearModal = () => {
    setAddNumberOfSessions(null);
    setErrorMassageOnSubmit('');
    setAddExpireDate(null);
    setExpireDateFormatted(null);
  };

  const [userSessionData, setUserSessionData] = useState<{
    count: number | null;
    expiryDate: string | null;
    remaining: any | null;
  }>({
    count: null,
    expiryDate: null,
    remaining: null,
  });

  const getSessionExpiryDate = (date: Date) => {
    if (!date) {
      setAddExpireDate(null);
      setExpireDateFormatted(null);
      return;
    }

    const { dateNumberString, monthNumberString, year } =
      dateTimeConversion(date);
    const inputDate = new Date(date);
    inputDate.setHours(23, 59, 59);
    const utcDate = inputDate.toISOString();

    setAddExpireDate(utcDate);
    setExpireDateFormatted(`${dateNumberString}/${monthNumberString}/${year}`);
  };
  const getSessionUpdate = useCallback(
    newPageNumber => {
      submitSessions(
        `/api/v1/sessions?userId=${PlayerInfoData.userId}&page=${newPageNumber}&size=40`,
        '-',
        '-',
        '-',
        null,
        '',
        'GET',
        false,
        EVENT_SERVICE
      ).then(data => {
        const listOfSession: any[] = data.data.data.map((item: any) => {
          const {
            _id,
            userId,
            count,
            addedAt,
            expiryDate,
            usedCount,
            userSubscriptionId,
          } = item;

          const addedAtDate = dateTimeConversion(addedAt);

          const expiryDateGet = dateTimeUTCConversion(expiryDate);

          return {
            _id,
            userId,
            count: count === null ? 'N/A' : count,
            usedCount: usedCount === null ? 'N/A' : usedCount,
            addedAt: addedAt
              ? `${addedAtDate.dateNumberString}/${addedAtDate.monthNumberString}/${addedAtDate.year}`
              : 'N/A',
            expiryDate: expiryDate
              ? `${expiryDateGet.dateNumberString}/${expiryDateGet.monthNumberString}/${expiryDateGet.year}`
              : 'N/A',
            userSubscriptionId,
          };
        });

        listOfSession.length &&
          setUserSessionUpdates((items: any) => [...items, ...listOfSession]);
      });
    },
    [currentPage, PlayerInfoData]
  );

  const getSessionInfo = useCallback(() => {
    getSessionsSummery(
      `/api/v1/sessions/summaries?userIds=${PlayerInfoData.userId}&page=1&size=4`,
      '-',
      '-',
      '-',
      null,
      '',
      'GET',
      false,
      EVENT_SERVICE
    ).then(data => {
      const {
        expiryDate,
        latestSessionCount,
        numberOfSessionsAvailable,
        allSessionsExpired,
      } = data.data.data[0];

      const { dateNumberString, monthNumberString, year } =
      dateTimeUTCConversion(expiryDate);
      setUserSessionData({
        count: latestSessionCount === null ? 'N/A' : latestSessionCount,
        expiryDate: allSessionsExpired
          ? 'Expired'
          : expiryDate
          ? `${dateNumberString}/${monthNumberString}/${year}`
          : 'N/A',
        remaining:
          numberOfSessionsAvailable === null
            ? 'N/A'
            : numberOfSessionsAvailable,
      });
    });
  }, [PlayerInfoData]);

  const isSubmitFormValidated = useCallback(
    (addNumberOfSessions, addExpireDate) => {
      if (!addNumberOfSessions && !addExpireDate) {
        setErrorMassageOnSubmit(
          'Please fill in at least one field to continue.'
        );
        return false;
      }

      if (addNumberOfSessions !== '' && Number(addNumberOfSessions) === 0) {
        setErrorMassageOnSubmit(
          'Invalid Input: The value you entered cannot be zero.'
        );
        return false;
      }

      return true;
    },
    []
  );

  const updateSessionUpdate = useCallback(() => {
    if (!isSubmitFormValidated(addNumberOfSessions, addExpireDate)) {
      return;
    }

    clearModal();

    submitSessions(
      `/api/v1/sessions`,
      '-',
      '-',
      '-',
      {
        userId: PlayerInfoData.userId,
        count: addNumberOfSessions,
        expiryDate: addExpireDate,
      },
      '',
      'POST',
      false,
      EVENT_SERVICE
    ).then(res => {
      clearModal();
      setUserSessionUpdates([]);
      getSessionInfo();
      getSessionUpdate(1);
      setIsEditModalOpen(false);
    });
  }, [addNumberOfSessions, addExpireDate, PlayerInfoData, currentPage]);

  const updateSessionBulk = useCallback(
    (userIds: string[]) => {
      if (!isSubmitFormValidated(addNumberOfSessions, addExpireDate)) {
        return;
      }

      clearModal();

      const mappedUserList = userIds.map((userId: string) => {
        return {
          userId: userId,
          count: addNumberOfSessions,
          expiryDate: addExpireDate,
        };
      });

      return submitSessions(
        `/api/v1/sessions/bulk`,
        '-',
        '-',
        '-',
        [...mappedUserList],
        '',
        'POST',
        false,
        EVENT_SERVICE
      ).then(res => {
        setIsEditModalOpen(false);
        return res;
      });
    },
    [addNumberOfSessions, addExpireDate]
  );

  const openSubmitModal = (state: boolean) => {
    setIsEditModalOpen(state);
    clearModal();
  };

  const getUserLastSessionInfo = useCallback(async () => {
    return await getUserLastSessions(
      `/api/v1/users/${PlayerInfoData.userId}/user-subscriptions?lastPayment=true`,
      '-',
      '-',
      '-',
      null,
      '',
      'GET',
      false,
      EVENT_SERVICE
    ).then(data => {
      const latestSession = data?.data?.data?.[0];
      const { expireAt, price, subscriptionName } = latestSession || {};
      const { dateNumberString, monthNumberString, year } =
        dateTimeUTCConversion(expireAt);

      return latestSession
        ? {
            price,
            date: `${dateNumberString}/${monthNumberString}/${year}`,
            subscriptionName,
          }
        : null;
    });
  }, [PlayerInfoData]);

  return {
    count: userSessionData.count,
    expiryDate: userSessionData.expiryDate,
    remaining: userSessionData.remaining,
    isEditModalOpen,
    setIsEditModalOpen,
    addNumberOfSessions,
    setAddNumberOfSessions,
    mode,
    setMode,
    isDatePikerEnable,
    setIsDatePikerEnable,
    updateSessionUpdate,
    getSessionExpiryDate,
    expireDateFormatted,
    getSessionInfo,
    getSessionUpdate,
    userSessionUpdates,
    errorMassageOnSubmit,
    openSubmitModal,
    isPlayerOrParent,
    pageIncrement,
    updateSessionBulk,
    getUserLastSessionInfo,
  };
};

export default useSession;
