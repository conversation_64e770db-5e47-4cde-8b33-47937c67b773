import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { USER_MANAGEMENT_SERVICE } from '../constants/services';
import useApi from './useApi';

import uuid from 'react-native-uuid';
import {
  FETCH_FILE_URL_FAIL,
  FETCH_FILE_URL_REQUEST,
  FETCH_FILE_URL_SUCCESS,
} from '../store/actionTypes/common/commonActionTypes';
const useGeneratedFileUrl = () => {
  const [s3FileObject, setS3FileObject] = useState();
  const { downloadableFileUrl, isDownloading } = useSelector(
    state => state?.common
  );
  const [apiCall] = useApi();
  const { v4: uuidv4 } = uuid;
  const uniqueID = useMemo(() => uuidv4(), []);
  useEffect(() => {
    if (s3FileObject?.s3Object) {
      apiCall(
        '/api/v1/cloud-storage/presigned-url',
        FETCH_FILE_URL_REQUEST,
        FETCH_FILE_URL_SUCCESS,
        FETCH_FILE_URL_FAIL,
        { ...s3FileObject.s3Object },
        '',
        'POST',
        null,
        USER_MANAGEMENT_SERVICE,
        { uniqueID }
      );
    }
  }, [JSON.stringify(s3FileObject)]);

  const getFileObject = s3Object => {
    s3Object &&
      setS3FileObject({ s3Object, timestamp: new Date().toISOString() });
  };
  return [
    getFileObject,
    downloadableFileUrl?.[uniqueID]?.preSignedUrl || null,
    isDownloading?.[uniqueID],
  ];
};

export default useGeneratedFileUrl;
