import { API } from 'aws-amplify';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { deleteRequest, get, post, put } from '../api/commonApi';
import { LOGOUT_SUCCESS } from '../store/actionTypes/auth';
import { RootStore } from '../store/store';

function useApi(redirectPath?: string) {
  const dispatch = useDispatch();
  const [actions, setActions] = useState<any>();
  const [baseUrl, setBaseUrl] = useState<any>(null);
  const [postValues, setPostValues] = useState({});
  const [history, setRedirect] = useState<any>();
  const [method, setMethod] = useState('POST');
  const [isKeyPropertyNoAvailable, setIsKeyPropertyNoAvailable] =
    useState(false);
  const [service, setService] = useState('');
  const [customInput, setCustomInput] = useState(null);
  const { services } = useSelector((state: RootStore) => state?.auth);

  const valueRef = useRef<any>();
  const processPromise = () => {
    const promise = callAPI();
    valueRef.current = promise;
    return promise.then(res => res).catch(error => error.response);
  };

  const processPromiseAPI = (
    promiseMethod: any,
    promiseService: any,
    promiseBaseUrl: any,
    promisePostValues: any
  ) => {
    switch (promiseMethod) {
      case 'POST':
        return post(
          promiseBaseUrl,
          promisePostValues,
          false,
          services[promiseService]
        )
          .then(res => res)
          .catch(error => error.response);
      case 'GET':
        return get(promiseBaseUrl, services[promiseService])
          .then(res => res)
          .catch(error => error.response);
      case 'PUT':
        return put(
          promiseBaseUrl,
          promisePostValues,
          false,
          services[promiseService]
        )
          .then(res => res)
          .catch(error => error.response);
      case 'DELETE':
        return deleteRequest(
          promiseBaseUrl,
          promisePostValues,
          services[promiseService]
        )
          .then(res => res)
          .catch(error => error.response);
      default:
        return get(promiseBaseUrl, services[promiseService])
          .then(res => res)
          .catch(error => error.response);
    }
  };

  const callAPI = () => {
    switch (method) {
      case 'POST':
        return post(baseUrl, postValues, isKeyPropertyNoAvailable, service);

      case 'GET':
        return get(baseUrl, service);

      case 'PUT':
        return put(baseUrl, postValues, isKeyPropertyNoAvailable, service);

      case 'DELETE':
        return deleteRequest(baseUrl, postValues, service);

      default:
        return get(baseUrl, service);
    }
  };

  useEffect(() => {
    if (baseUrl) {
      const requestApiCall = async () => {
        dispatch({ type: actions.request });
        if (customInput) {
          dispatch({
            type: `${actions.request}_CUSTOM_CONTENT`,
            payload: { customInput },
          });
        }

        try {
          const result = await processPromise();

          switch (result.status) {
            case 200:
            case 201:
              dispatch({
                type: actions.success,
                payload: result.data,
              });

              if (customInput) {
                dispatch({
                  type: `${actions.success}_CUSTOM_CONTENT`,
                  payload: { customInput, data: result.data },
                });
              }

              if (redirectPath && Object.values(history).length) {
                history.push(redirectPath);
              }
              break;

            case 204:
              dispatch({
                type: actions.success,
                payload: null,
              });

              if (customInput) {
                dispatch({
                  type: `${actions.success}_CUSTOM_CONTENT`,
                  payload: { customInput },
                });
              }

              break;

            case 403: {
              dispatch({
                type: LOGOUT_SUCCESS,
              });
              break;
            }
            case 500: {
              dispatch({
                type: actions.fail,
                payload:
                  result.data &&
                  (result.data.message || 'Something went wrong'),
              });
              break;
            }
            default:
              dispatch({
                type: actions.fail,
                payload: result.data && (result.data.message || result.data),
              });

              if (customInput) {
                dispatch({
                  type: `${actions.fail}_CUSTOM_CONTENT`,
                  payload: { customInput },
                });
              }
              break;
          }
        } catch (error) {
          dispatch({
            type: actions.fail,
            payload: error,
          });

          if (customInput) {
            dispatch({
              type: `${actions.fail}_CUSTOM_CONTENT`,
              payload: { customInput },
            });
          }
        }
        setBaseUrl(null);
      };

      requestApiCall();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [baseUrl]);

  const submitForm = (
    url: string,
    request: string,
    success: string,
    fail: string,
    postValues: any,
    history: any,
    method: string,
    isKeyPropertyNoAvailable = false,
    service: any,
    customInput?: any
  ) => {
    const newActions = { request, success, fail };
    const mergedActions = { ...actions, ...newActions };

    if (services) {
      setMethod(method);
      setActions(mergedActions);
      setPostValues(postValues);
      setRedirect(history);
      setIsKeyPropertyNoAvailable(isKeyPropertyNoAvailable);
      setService(services[service]);
      setCustomInput(customInput);
      setBaseUrl(url);
    }
  };

  const cancel = () => {
    API.cancel(valueRef.current, 'Cancel API Call');
  };

  return [submitForm, cancel, processPromiseAPI] as const;
}

export default useApi;
