import { USER_MANAGEMENT_SERVICE } from "../constants/services";
import {
  GET_CERTIFICATIONS_REQUEST,
  GET_CERTIFICATIONS_SUCCESS,
  GET_CERTIFICATIONS_FAIL,
  POST_CERTIFICATE_FAIL,
  POST_CERTIFICATE_REQUEST,
  POST_CERTIFICATE_SUCCESS
} from "../store/actionTypes/User/User";
import useApi from "./useApi";



export const useAddUser = () => {

  const [fetchCertification] = useApi();
  const [createCertification] = useApi();

  const getCertifications = () => {
    fetchCertification(
      `/api/v1/certifications?userType=COACH&page=1&size=100`,
      GET_CERTIFICATIONS_REQUEST,
      GET_CERTIFICATIONS_SUCCESS,
      GET_CERTIFICATIONS_FAIL,
      null,
      '',
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    );
  };

  const createCertifications = (payload: string) => {
    createCertification(
      `/api/v1/certifications`,
      POST_CERTIFICATE_REQUEST,
      POST_CERTIFICATE_SUCCESS,
      POST_CERTIFICATE_FAIL,
      payload,
      '',
      'POST',
      false,
      USER_MANAGEMENT_SERVICE
    );
  }
  return { getCertifications, createCertifications } as const;
}

