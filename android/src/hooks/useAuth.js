import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Auth } from 'aws-amplify';

import {
  LOGIN_REQUEST,
  LOGIN_SUCCESS,
  LOGIN_FAILED,
  SET_CURRENT_USER,
} from '../store/actionTypes/auth';

const useAuth = () => {
  const dispatch = useDispatch();
  const [username, setUsername] = useState(null);
  const [password, setPassword] = useState(null);

  const signIn = (username, password) => {
    dispatch({ type: LOGIN_REQUEST });
    setUsername(username);
    setPassword(password);
  };

  useEffect(() => {
    const login = () => {
      Auth.signIn(username, password)
        .then(user => {
          Auth.currentCredentials().then(credential => {
            const { accessKeyId, secretAccessKey, sessionToken } = credential;
            dispatch({
              type: LOGIN_SUCCESS,
              payload: {
                accessKeyId,
                secretAccessKey,
                sessionToken,
                refreshToken: user
                  .getSignInUserSession()
                  .getRefreshToken()
                  .getToken(),
              },
            });
          });

          dispatch({
            type: SET_CURRENT_USER,
            payload: {
              username: user.getUsername(),
              email: user.attributes?.email,
              roles: user.getSignInUserSession().getIdToken().payload[
                'cognito:groups'
              ],
            },
          });
          setUsername(null);
        })
        .catch(error => {
          setUsername(null);
          dispatch({
            type: LOGIN_FAILED,
            payload: error,
          });
        });
    };
    if (username && password) {
      login();
    }
  }, [username, password]);

  return signIn;
};

export default useAuth;
