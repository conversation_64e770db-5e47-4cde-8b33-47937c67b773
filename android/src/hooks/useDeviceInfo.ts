import { DeviceType, getDeviceTypeAsync } from 'expo-device';
import React, { useEffect, useState } from 'react';

const useDeviceInfo = () => {
  const deviceTypeMap = {
    [DeviceType.UNKNOWN]: 'unknown',
    [DeviceType.PHONE]: 'phone',
    [DeviceType.TABLET]: 'tablet',
    [DeviceType.DESKTOP]: 'desktop',
    [DeviceType.TV]: 'tv',
  };
  const [deviceType, setDeviceType] = useState<string | null>(null);

  useEffect(() => {
    getDeviceTypeAsync().then(deviceType => {
      setDeviceType(deviceTypeMap[deviceType]);
    });
  }, []);

  return [{ deviceType }];
};

export default useDeviceInfo;
