import useApi from '../useApi';
import {
  TERM_AND_CONDITION_DATA_FETCH_FAIL,
  TERM_AND_CONDITION_DATA_FETCH_REQUEST,
  TERM_AND_CONDITION_DATA_FETCH_SUCCESS,
  POST_TERM_AND_CONDITION_DATA_REQUEST,
  POST_TERM_AND_CONDITION_DATA_SUCCESS,
  POST_TERM_AND_CONDITION_DATA_FAIL,
} from '../../store/actionTypes/TermAndCondition/termAndCondtionAction';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';
export const useTermAndConditionHook = () => {
  const [fetchTermAndConditionDetails] = useApi();
  const [fetchTermAndCondition] = useApi();

  const getTermAndConditionDetails = (userId: string) => {
    fetchTermAndConditionDetails(
      `/api/v1/users/${userId}/term`,
      TERM_AND_CONDITION_DATA_FETCH_REQUEST,
      TERM_AND_CONDITION_DATA_FETCH_SUCCESS,
      TERM_AND_CONDITION_DATA_FETCH_FAIL,
      null,
      '',
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    );
  };

  const submitTermAndCondition = (payload: string, userId: string) => {
    fetchTermAndCondition(
      `/api/v1/users/${userId}/term`,
      POST_TERM_AND_CONDITION_DATA_REQUEST,
      POST_TERM_AND_CONDITION_DATA_SUCCESS,
      POST_TERM_AND_CONDITION_DATA_FAIL,
      payload,
      '',
      'POST',
      false,
      USER_MANAGEMENT_SERVICE
    );
  };

  return { getTermAndConditionDetails, submitTermAndCondition } as const;
};
