import { API } from 'aws-amplify';
import { useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { deleteRequest, get, post, put } from '../api/commonApi';
import { LOGOUT_SUCCESS } from '../store/actionTypes/auth';
import { RootStore } from '../store/store';

function useApiPromise() {
  const dispatch = useDispatch();
  const { services } = useSelector((state: RootStore) => state?.auth);
  const valueRef = useRef<any>();
  /**
   * Adding refer for Cancel the API
   * Calling redux function
   */
  const processPromise = async (
    method: any,
    service: any,
    baseUrl: any,
    postValues: any,
    reduxActions: any,
    customInput: any,
    hasHeader?: boolean
  ) => {
    const promise = await callAPI(
      method,
      service,
      baseUrl,
      postValues,
      reduxActions,
      customInput,
      hasHeader
    );
    reduxDispatches(promise, reduxActions, customInput);
    valueRef.current = promise;
    return promise;
  };

  const callAPI = (
    promiseMethod: any,
    promiseService: any,
    promiseBaseUrl: any,
    promisePostValues: any,
    reduxActions: any,
    customInput: any,
    hasHeader?: boolean
  ) => {
    dispatch({ type: reduxActions.request });
    switch (promiseMethod) {
      case 'POST':
        return post(
          promiseBaseUrl,
          promisePostValues,
          false,
          services[promiseService]
        );
      case 'PUT':
        return put(
          promiseBaseUrl,
          promisePostValues,
          false,
          services[promiseService]
        );
      case 'DELETE':
        return deleteRequest(
          promiseBaseUrl,
          promisePostValues,
          services[promiseService],
          hasHeader
        );
      case 'GET':
      default:
        return get(promiseBaseUrl, services[promiseService]);
    }
  };

  /**
   * Adding to redux
   */
  const reduxDispatches = (result: any, actions: any, customInput: any) => {
    dispatch({ type: actions.request });
    try {
      switch (result.status) {
        case 200:
        case 201:
          dispatch({
            type: actions.success,
            payload: result.data,
          });

          if (customInput) {
            dispatch({
              type: `${actions.success}_CUSTOM_CONTENT`,
              payload: { customInput, data: result.data },
            });
          }

          break;
        case 204:
          dispatch({
            type: actions.success,
            payload: null,
          });
          if (customInput) {
            dispatch({
              type: `${actions.success}_CUSTOM_CONTENT`,
              payload: { customInput },
            });
          }
          break;
        case 403: {
          dispatch({
            type: LOGOUT_SUCCESS,
          });
          break;
        }
        case 500: {
          dispatch({
            type: actions.fail,
            payload:
              result.data && (result.data.message || 'Something went wrong'),
          });
          break;
        }
        default:
          dispatch({
            type: actions.fail,
            payload: result.data && (result.data.message || result.data),
          });
          if (customInput) {
            dispatch({
              type: `${actions.fail}_CUSTOM_CONTENT`,
              payload: { customInput },
            });
          }
          break;
      }
    } catch (error) {
      dispatch({
        type: actions.fail,
        payload: error,
      });
      if (customInput) {
        dispatch({
          type: `${actions.fail}_CUSTOM_CONTENT`,
          payload: { customInput },
        });
      }
    }
  };

  /**
   * Return Function
   * @totdo need to implenet the logic history isKeyPropertyNoAvailable customInput ,
   * @info will implement later
   */
  const submitAPI = (
    url: string,
    request: string,
    success: string,
    fail: string,
    postValues: any,
    history: any,
    method: string,
    isKeyPropertyNoAvailable = false,
    service: any,
    customInput?: any,
    hasHeader?: boolean
  ) => {
    const reduxActions = { request, success, fail };
    return processPromise(
      method,
      service,
      url,
      postValues,
      reduxActions,
      customInput,
      hasHeader
    );
  };

  /**
   * Cancel the API
   */
  const cancel = () => {
    API.cancel(valueRef.current, 'Cancel API Call');
  };

  return [submitAPI, cancel] as const;
}

export default useApiPromise;
