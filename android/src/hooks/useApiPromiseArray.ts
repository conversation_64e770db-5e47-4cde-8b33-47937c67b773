import useApiPromise from './useApiPromise';

const useApiPromiseArray = () => {
  const [fetchApis] = useApiPromise();

  const submitApi = async (urls: string[], method: string, service: string) => {
    const urlPromises = urls?.map((url: string) =>
      fetchApis(url, '_', '_', '_', null, '', method, false, service)
    );

    try {
      return await Promise.all(urlPromises);
    } catch (error) {
      console.log(error);
    }
  };
  return [submitApi] as const;
};

export default useApiPromiseArray;
