import { Storage } from 'aws-amplify';
import { useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootStore } from '../store/store';

interface S3FileObject {
  fileKey: string;
  bucketName: string;
}

interface UploadOptions {
  file: File;
  path: string;
  bucket: string;
}

const useFileUploadPromise = () => {
  const { configurationData } = useSelector((state: RootStore) => state.auth);
  const [s3FileObject, setS3FileObject] = useState<S3FileObject | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const uploadFile = useCallback(async ({ file, path, bucket }: UploadOptions): Promise<S3FileObject | null> => {
    if (!file || !path || !bucket) {
      return null;
    }

    if (!configurationData?.Auth?.region) {
      return null;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setS3FileObject(null);

    const { region } = configurationData.Auth;

    const trimmedPath = path.startsWith('/') ? path.slice(1) : path;
    const sanitizedFileName = file.name
      .split('.')
      .slice(0, -1)
      .join('_')
      .replace(/[^\w-]/g, '_');
    const fileExtension = file.name.split('.').pop() || '';

    try {
      Storage.configure({
        region,
        bucket,
        level: 'public',
        customPrefix: { public: '' },
      });

      const key = `${trimmedPath}${sanitizedFileName}-${Date.now()}.${fileExtension}`;

      const result = await Storage.put(
        key,
        file,
        {
          contentType: file.type,
          progressCallback(progress: { loaded: number; total: number }) {
            const percentageProgress = Math.floor((progress.loaded / progress.total) * 100);
            setUploadProgress(percentageProgress);
          },
        }
      );
      if (result?.key) {
        const newS3FileObject = { fileKey: result.key, bucketName: bucket };
        setS3FileObject(newS3FileObject);
        return newS3FileObject;
      }
      return null;
    } catch (err) {
      return null;
    } finally {
      setIsUploading(false);
    }
  }, [configurationData]);

  return { uploadFile, s3FileObject, uploadProgress, isUploading };
};

export default useFileUploadPromise;