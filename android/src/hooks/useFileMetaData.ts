import { useEffect, useState } from 'react';
import * as mime from 'react-native-mime-types';

const useFileMetaData = () => {
  const [fileUri, setFileUri] = useState<string | null>(null);
  const [fileMetaData, setFileMeatData] = useState<any>(null);

  const fileFetch = async (fileUri: string) => {
    const readFile: any = await fetch(fileUri);
    return await readFile.blob();
  };

  useEffect(() => {
    if (fileUri) {
      const fileName = fileUri.split('/').pop() || '';
      const contentMime = mime.contentType(fileName);
      const fileType = contentMime.split('/')[0];
      const fileBlob: any = fileFetch(fileUri);
      const file = new File([fileBlob], `${fileName}`);

      setFileMeatData({ fileName, fileType, contentMime, file });
    }
  }, [fileUri]);

  const getFileInfo = (fileUri: string) => {
    setFileUri(fileUri);
  };

  return [getFileInfo, fileMetaData] as const;
};

export default useFileMetaData;
