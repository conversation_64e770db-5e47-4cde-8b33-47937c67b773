import axios from 'axios';
import { useDispatch } from 'react-redux';
import { SET_API_INFORMATION } from '../store/actionTypes/auth';

export const useGetAmplifyConfiguration = () => {
  const dispatch = useDispatch();
  const fetchAmplifyConfigurationData = async (clubId: string) => {
    const results = await axios.get(
      `https://bfrtgne4x7.execute-api.us-east-1.amazonaws.com/url?clubId=${clubId.toUpperCase()}`
    );
    const { Auth, API, theme, services, preferences } = results?.data || {};
    if (Auth && API && services && preferences) {
      dispatch({
        type: SET_API_INFORMATION,
        payload: {
          configurationData: { Auth, API },
          services,
          preferences,
        },
      });
    }
    return results;
  };

  return [fetchAmplifyConfigurationData] as const;
};
