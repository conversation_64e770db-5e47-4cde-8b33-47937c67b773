import { useState } from 'react';
import useApiPromise from './useApiPromise';
import {
  FETCH_S3_BUCKET_LOCATION_FAIL,
  FETCH_S3_BUCKET_LOCATION_REQUEST,
  FETCH_S3_BUCKET_LOCATION_SUCCESS,
} from '../store/actionTypes/common/commonActionTypes';

interface S3FilePathAndService {
  path: string;
  service: string;
}

interface BucketLocationResult {
  bucketName: string;
  region: string;
  filePath: string;
}

const useS3bucketLocationPromise = () => {
  const [bucketLocation, setBucketLocation] = useState<BucketLocationResult | null>(null);
  const [apiCall] = useApiPromise();

  const getBucketLocation = async ({ path, service }: S3FilePathAndService): Promise<BucketLocationResult | null> => {

    if (!path) {
      console.error('Path is required to fetch S3 bucket location');
      return null;
    }

    try {
      const result = await apiCall(
        `/api/v1/cloud-storage/path/${path}`,
        FETCH_S3_BUCKET_LOCATION_REQUEST,
        FETCH_S3_BUCKET_LOCATION_SUCCESS,
        FETCH_S3_BUCKET_LOCATION_FAIL,
        {},
        '',
        'GET',
        false,
        service
      );

      setBucketLocation(result?.data);
      return result?.data;
    } catch (error) {
      console.error('Failed to fetch S3 bucket location:', error);
      setBucketLocation(null);
      return null;
    }
  };

  return { getBucketLocation, bucketLocation };
};

export default useS3bucketLocationPromise;