import { Storage } from 'aws-amplify';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootStore } from '../store/store';
const useFileUpload = () => {
  const { configurationData } = useSelector((state: RootStore) => state.auth);
  const [s3FileObject, setS3FileObject] = useState<any>();
  const [uploadingFile, setUploadingFile] = useState<any>(null);
  const [s3path, setS3path] = useState<string>('');
  const [s3Bucket, setS3Bucket] = useState<string>('');
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  useEffect(() => {
    if (
      uploadingFile &&
      s3Bucket !== '' &&
      s3path !== '' &&
      configurationData
    ) {
      const { Auth } = configurationData || {};
      const { region } = Auth || {};

      const trimmedPath = s3path.charAt(0) === '/' ? s3path.substring(1) : s3path;
      const sanitizedFileName = uploadingFile?.name
        .split('.')
        .slice(0, -1)
        .join('_')
        .replace(/\W/g, '');
      const fileExtension = uploadingFile?.name.split('.').pop();
      Storage.configure({
        region,
        bucket: s3Bucket,
        level: 'public',
        customPrefix: {
          public: '',
        },
      });

      Storage.put(
        `${trimmedPath}${sanitizedFileName}-${Date.now()}.${fileExtension}`,
        uploadingFile,
        {
          contentType: uploadingFile.type,
          progressCallback(progress: any) {
            const { loaded, total } = progress;

            setIsUploading(true);

            const percentageProgress = Math.floor((loaded / total) * 100);
            setUploadProgress(percentageProgress);
          },
        }
      )
        .then((result: any) => {
          result?.key &&
            setS3FileObject({ fileKey: result.key, bucketName: s3Bucket });
          setIsUploading(false);
        })
        .catch(err => {
          console.log(err);
        });

      setUploadingFile(null);
    }
  }, [uploadingFile, s3path, s3Bucket, configurationData]);

  const submitForm = (file: any, path: string, bucket: string) => {
    setUploadingFile(file);
    setS3Bucket(bucket);
    setS3path(path);
    setUploadProgress(0);
    setS3FileObject(null);
  };

  return [submitForm, s3FileObject, uploadProgress, isUploading];
};

export default useFileUpload;
