import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { userRoleType } from '../constants/constants';
import { USER_MANAGEMENT_SERVICE } from '../constants/services';
import {
  GET_CHILD_DATA_FAIL,
  GET_CHILD_DATA_REQUEST,
  GET_CHILD_DATA_SUCCESS,
  GET_UPDATED_CHILD_ID_FAIL,
  GET_UPDATED_CHILD_ID_REQUEST,
  GET_UPDATED_CHILD_ID_SUCCESS,
} from '../store/actionTypes/common/commonActionTypes';
import { RootStore } from '../store/store';
import useApi from './useApi';
const getChildInformation = () => {
  const { userRole, userData } = useSelector((state: RootStore) => state?.auth);
  const { updatedChildrenIds, isChildrenDataUpdated } = useSelector(
    (state: RootStore) => state?.common
  );
  const [initialChildInfo, setInitialFetch] = useState<boolean>(false);
  const isParent = userRole === userRoleType.PARENT;
  const [fetchUpdateLogInUserData] = useApi();
  const [fetchChildren] = useApi();

  const getUpdatedLogInUserData = (emailId: string) => {
    console.log('called child');
    fetchUpdateLogInUserData(
      `/api/v1/users?email=${emailId}`,
      GET_UPDATED_CHILD_ID_REQUEST,
      GET_UPDATED_CHILD_ID_SUCCESS,
      GET_UPDATED_CHILD_ID_FAIL,
      null,
      '',
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    );
  };

  const getChildData = (userId: string[]) => {
    fetchChildren(
      `/api/v1/users?userIds=${userId}&page=1&size=${userId.length}`,
      GET_CHILD_DATA_REQUEST,
      GET_CHILD_DATA_SUCCESS,
      GET_CHILD_DATA_FAIL,
      null,
      '',
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    );
  };
  useEffect(() => {
    if (isParent && userData?.emailId) {
      getUpdatedLogInUserData(userData?.emailId);
    }
  }, [initialChildInfo]);

  useEffect(() => {
    if (isParent && updatedChildrenIds?.length) {
      isChildrenDataUpdated && getChildData(updatedChildrenIds);
    }
  }, [updatedChildrenIds, isChildrenDataUpdated]);
  const getChildInfo = () => {
    setInitialFetch(true);
  };
  return [getChildInfo];
};
export default getChildInformation;
