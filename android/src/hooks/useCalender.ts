import * as Calendar from 'expo-calendar';
import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { CALENDER, userRoleType } from '../constants/constants';
import { EVENT_SERVICE } from '../constants/services';
import { dateTimeConversion, getNextDateByDuration } from '../helpers';
import {
  RESET_LAST_SYNC_DATE_TIME,
  SET_CALENDER_SYNC_STATUS,
  SET_CALENDER_SYNC_TIME_DURATION,
  SET_USER_CALENDER,
  UPDATE_LAST_SYNC_DATE_TIME,
} from '../store/actionTypes/auth';

import {
  FETCH_ALL_EVENTS_SYNC_FAIL,
  FETCH_ALL_EVENTS_SYNC_REQUEST,
  FETCH_ALL_EVENTS_SYNC_SUCCESS,
  SET_DEVICE_CALENDER_LIST,
} from '../store/actionTypes/Planner/PlannerAction';
import { RootStore } from '../store/store';
import useApi from './useApiPromise';

const useCalender = () => {
  const [isHookStated, setIsHookStated] = useState(false);

  const { updatedChildrenIds } = useSelector(
    (state: RootStore) => state?.common
  );
  const { deviceCalendarList } = useSelector(
    (state: RootStore) => state?.planner
  );

  const {
    userData,
    selectedCalenderID,
    lastCalenderSyncDateTime,
    isCalenderSyncEnabled,
    calenderSyncTimeDuration,
  } = useSelector((state: RootStore) => state?.auth);

  const [fetchData] = useApi();
  const dispatch = useDispatch();

  useEffect(() => {
    (async () => {
      if (isHookStated) {
        const { status } = await Calendar.requestCalendarPermissionsAsync();
        if (status === CALENDER.calendarPermissions) {
          const calendars = await Calendar.getCalendarsAsync(
            Calendar.EntityTypes.EVENT
          );
          let filteredCalender: any[] = [];
          if (Platform.OS !== CALENDER.ios) {
            filteredCalender = calendars?.filter(
              calendar => calendar?.accessLevel !== CALENDER.accessLevelRead
            );
          } else {
            filteredCalender = calendars?.filter(
              calendar => calendar?.allowsModifications
            );
          }

          dispatch({
            type: SET_DEVICE_CALENDER_LIST,
            payload: filteredCalender,
          });
        }

        const {
          year: currentYear,
          monthNumberString: currentMonth,
          dateNumberString: currentDate,
        } = dateTimeConversion(new Date());

        const { year, monthNumberString, dateNumberString } =
          getNextDateByDuration([currentYear, currentMonth, currentDate], 90);

        const nextDate = new Date(
          `${year}-${monthNumberString}-${dateNumberString}`
        ).toISOString();
        const currentDateString = new Date(
          `${currentYear}-${currentMonth}-${currentDate}`
        ).toISOString();

        dispatch({
          type: SET_CALENDER_SYNC_TIME_DURATION,
          payload: { start: currentDateString, end: nextDate },
        });
      }
    })();
  }, [isHookStated]);

  const init = () => {
    setIsHookStated(true);
  };

  const [initialSync, setInitialSync] = useState(false);

  const setCalender = (id: string | null) => {
    dispatch({
      type: SET_USER_CALENDER,
      payload: id,
    });
    setInitialSync(true);
  };

  useEffect(() => {
    initialSync && selectedCalenderID && syncCalender();
  }, [selectedCalenderID, initialSync]);

  const getColanderEventList = async (
    timeDuration: any,
    selectedCalenderID: any
  ) => {
    const events = await Calendar.getEventsAsync(
      [selectedCalenderID || ''],
      new Date(timeDuration.start),
      new Date(timeDuration.end)
    );

    return events.filter(event => {
      const userID = userData?.id;

      const mainNote = event?.notes || '';

      if (mainNote.includes(CALENDER.uniqueKeyString)) {
        const note = event.notes.split(CALENDER.uniqueKeyString);
        if (
          note?.length &&
          note[1] &&
          JSON.parse(note[1])?.appID === CALENDER.appID &&
          JSON.parse(note[1])?.[CALENDER.userIDKey] === userID
        ) {
          return event;
        }
      }
    });
  };

  const createCalenderEvent = async (data: any) => {
    const event = {
      calendarId: selectedCalenderID,
      title: `${CALENDER.appID} - ${data.type}`,
      status: Calendar.EventStatus.CONFIRMED,
      startDate: new Date(data.startTime),
      endDate: new Date(data.endTime),
      location: data?.location?.name || '',
      notes: `${data.note} ${CALENDER.uniqueKeyString}{"appID":"${CALENDER.appID}","${CALENDER.eventIDKey}":"${data._id}","${CALENDER.userIDKey}":"${userData?.id}","${CALENDER.emailID}":"${userData?.emailId}"}`,
      accessLevel: Calendar.EventAccessLevel.CONFIDENTIAL,
    };
    await Calendar.createEventAsync(selectedCalenderID || '', {
      ...event,
    });
  };

  const deleteCalenderEvent = async (eventId: any) => {
    await Calendar.deleteEventAsync(eventId);
  };
  const getCalenderEventsAndSync = async (
    calendarSync: any[],
    isAddNewEvent: boolean
  ) => {
    if (calendarSync?.length) {
      const userCalenderEventList = await getColanderEventList(
        calenderSyncTimeDuration,
        selectedCalenderID
      );

      userCalenderEventList?.length &&
        userCalenderEventList.forEach((event: any) => {
          const { id } = event;
          deleteCalenderEvent(id);
        });
      if (isAddNewEvent) {
        calendarSync?.length &&
          calendarSync?.forEach(event => {
            createCalenderEvent(event);
          });
        dispatch({
          type: UPDATE_LAST_SYNC_DATE_TIME,
        });
      } else {
        dispatch({
          type: RESET_LAST_SYNC_DATE_TIME,
        });
        setCalender(null);
      }
    }
  };

  const requestUrl = (userData: any) => {
    const childIds = updatedChildrenIds || userData?.childrenIds;

    switch (userData?.type) {
      case userRoleType.HEAD_COACH:
        return `/api/v1/events?startDate=${calenderSyncTimeDuration?.start}&endDate=${calenderSyncTimeDuration?.end}&page=1&size=10000`;
      case userRoleType.PARENT:
        return `/api/v1/events?participantIds=${childIds?.toString()}&startDate=${
          calenderSyncTimeDuration?.start
        }&endDate=${calenderSyncTimeDuration?.end}&page=1&size=10000`;
      default:
        return `/api/v1/events?participantIds=${userData?.id}&startDate=${calenderSyncTimeDuration?.start}&endDate=${calenderSyncTimeDuration?.end}&page=1&size=10000`;
    }
  };
  const fetchCalenderData = (
    userData: any,
    isAddEventEnableWhileSyncing: boolean
  ) => {
    fetchData(
      requestUrl(userData),
      FETCH_ALL_EVENTS_SYNC_REQUEST,
      FETCH_ALL_EVENTS_SYNC_SUCCESS,
      FETCH_ALL_EVENTS_SYNC_FAIL,
      null,
      '',
      'GET',
      false,
      EVENT_SERVICE,
      { isAddEventEnableWhileSyncing }
    ).then(res => {
      const { data } = res;

      getCalenderEventsAndSync(data?.data, isAddEventEnableWhileSyncing);
    });

    setInitialSync(false);
  };

  const syncCalender = useCallback(async () => {
    if (isCalenderSyncEnabled && userData && calenderSyncTimeDuration) {
      const isAddEventEnableWhileSyncing = true;

      fetchCalenderData(userData, isAddEventEnableWhileSyncing);
    }
  }, [
    userData,
    calenderSyncTimeDuration,
    isCalenderSyncEnabled,
    updatedChildrenIds,
    selectedCalenderID,
  ]);

  const unSyncCalender = useCallback(async () => {
    if (isCalenderSyncEnabled && userData && calenderSyncTimeDuration) {
      const isAddEventEnableWhileSyncing = false;

      fetchCalenderData(userData, isAddEventEnableWhileSyncing);
    }
  }, [
    userData,
    calenderSyncTimeDuration,
    isCalenderSyncEnabled,
    updatedChildrenIds,
    selectedCalenderID,
  ]);

  const getLastSyncDateTime = () => {
    if (!lastCalenderSyncDateTime) {
      return null;
    }
    const {
      year,
      monthNumberString,
      dateNumberString,
      hours24String,
      minutesString,
      amPm,
    } = dateTimeConversion(lastCalenderSyncDateTime);

    return `${dateNumberString}/${monthNumberString}/${year} ${hours24String}:${minutesString}${amPm}`;
  };

  const updateSyncStatus = () => {
    dispatch({
      type: SET_CALENDER_SYNC_STATUS,
    });
  };

  const automaticSync = useCallback(() => {
    if (isCalenderSyncEnabled && selectedCalenderID) {
      syncCalender();
    }
  }, [isCalenderSyncEnabled, selectedCalenderID, userData, updatedChildrenIds]);

  return [
    {
      init,
      deviceCalendarList,
      setCalender,
      syncCalender,
      getLastSyncDateTime,
      updateSyncStatus,
      isCalenderSyncEnabled,
      selectedCalenderID,
      automaticSync,
      unSyncCalender,
    },
  ] as const;
};

export default useCalender;
