import useApiPromise from '../useApiPromise';
import { useState } from 'react';
import {
  EVENT_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import { applyAsyncAwait } from '../../helpers/applyAsyncAwait';

interface IUserSession {
  latestSessionCount: number;
  numberOfSessionsAvailable: number;
  userId: string;
  expiryDate: string | null;
  allSessionsExpired: boolean;
}
interface IUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  nickName: string | null;
  emailId: string;
  parentContact: string | null;
  emergencyContact: string | null;
  height: number | null;
  joinedDate: string | null;
  dateOfBirth: string | null;
  sportsProfileId: string;
  playerId: string | null;
  profileImage: {
    bucketName: string;
    fileKey: string;
  } | null;
  profileImageUrl: string;
  expoPushNotificationTokens: string[] | null;
  contact: {
    phone: string | null;
    address: {
      line1: string | null;
      line2: string | null;
      city: string | null;
      state: string | null;
      zip: string | null;
      country: string | null;
    } | null;
  } | null;
}

export interface IUserMappedSession {
  userId: string;
  latestSessionCount: number | null;
  numberOfSessionsAvailable: number | null;
  expiryDate: string | null;
  profileImageUrl: string;
  firstName: string | null;
  lastName: string | null;
  isChecked: boolean;
  allSessionsExpired: boolean;
}

export const useSessionBulkHook = () => {
  const [currentPageNo, setCurrentPageNo] = useState<number>(1);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [searchBarText, setSearchBarText] = useState<string>('');

  const [userSessionList, setUserSessionList] = useState<IUserMappedSession[]>(
    []
  );
  const [noUser, setNoUser] = useState<boolean>(false);

  const [getUsers] = useApiPromise();
  const [getUserSession] = useApiPromise();

  const mapUserData = (
    sessionData: IUserSession[],
    userData: IUser[]
  ): IUserMappedSession[] => {
    return userData.map((user: IUser) => {
      const session = sessionData.find(
        (session: IUserSession) => session.userId === user.id
      );
      return {
        userId: user.id,
        latestSessionCount: session ? session.latestSessionCount : null,
        numberOfSessionsAvailable: session
          ? session.numberOfSessionsAvailable
          : null,
        expiryDate: session ? session.expiryDate : null,
        allSessionsExpired: (session && session.allSessionsExpired) || false,
        profileImageUrl: user.profileImageUrl,
        firstName: user.firstName,
        lastName: user.lastName,
        isChecked: false,
      };
    });
  };

  const onLoadUser = async (
    teamId: string,
    pageNo: number,
    searchBarText?: string
  ) => {
    const tmpUserSessionList: IUserMappedSession[] =
      pageNo == 1 ? [] : userSessionList;

    setIsLoading(true);
    setCurrentPageNo(pageNo);
    setNoUser(false);
    let requestUrl = `/api/v1/users?page=${pageNo}&size=${12}&type=PLAYER`;
    requestUrl = `${requestUrl}&teamId=${teamId}`;
    if (searchBarText) {
      requestUrl = `${requestUrl}&search=${searchBarText}`;
    }

    const [userListResponse, userListErr] = await applyAsyncAwait(
      getUsers(
        requestUrl,
        '',
        '',
        '',
        null,
        '',
        'GET',
        undefined,
        USER_MANAGEMENT_SERVICE
      )
    );

    if (userListErr) {
      setNoUser(true);
      setUserSessionList([]);
      setIsLoading(false);
      return;
    }

    if (userListResponse) {
      if (userListResponse.status == 204 && tmpUserSessionList.length == 0) {
        setIsLoading(false);
        setNoUser(true);
        setUserSessionList([]);
        return;
      } else {
        setTotalRecords(userListResponse?.data.totalRecords);
      }
    }

    const mappedUserIds: string[] = userListResponse?.data?.data.map(
      (user: IUser) => user.id
    );

    if (mappedUserIds.length) {
      const [userSessionListResponse, userSessionListErr] =
        await applyAsyncAwait(
          getUserSession(
            `/api/v1/sessions/summaries?userIds=${mappedUserIds.toString()}&page=1&size=${
              mappedUserIds.length
            }`,
            '',
            '',
            '',
            null,
            '',
            'GET',
            undefined,
            EVENT_SERVICE
          )
        );
      if (userSessionListErr) {
        setIsLoading(false);
      }

      if (userSessionListResponse) {
        const value = mapUserData(
          userSessionListResponse?.data?.data,
          userListResponse?.data?.data
        );

        setIsLoading(false);
        setUserSessionList([...tmpUserSessionList, ...value]);
      }
    }
  };

  const getUserSessionList = async (mappedUserIds: string[]) => {
    if (mappedUserIds.length) {
      const [userSessionListResponse, userSessionListErr] =
        await applyAsyncAwait(
          getUserSession(
            `/api/v1/sessions/summaries?userIds=${mappedUserIds.toString()}&page=1&size=${
              mappedUserIds.length
            }`,
            '',
            '',
            '',
            null,
            '',
            'GET',
            undefined,
            EVENT_SERVICE
          )
        );

      if (userSessionListErr) {
        return [];
      }

      if (userSessionListResponse) {
        return userSessionListResponse?.data?.data;
      }
    }
  };

  const onUserChecked = (index: number, hasChecked: boolean) => {
    const tmpUserSessionList = [...userSessionList];
    tmpUserSessionList[index].isChecked = hasChecked;
    setUserSessionList([...tmpUserSessionList]);
  };

  return {
    onLoadUser: onLoadUser,
    onUserChecked: onUserChecked,
    setSearchBarText: setSearchBarText,
    getUserSessionList: getUserSessionList,
    searchBarText: searchBarText,
    userSessionList: userSessionList,
    noUser: noUser,
    currentPageNo: currentPageNo,
    totalRecords: totalRecords,
    isLoading: isLoading,
  };
};
