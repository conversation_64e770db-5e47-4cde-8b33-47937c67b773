import { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  INITIAL_DATA_LIST_SETUP,
  LIST_DATA_FAILED,
  LIST_DATA_REQUEST,
  LIST_DATA_RESET,
  LIST_DATA_SUCCESS,
} from '../store/actionTypes/common/commonActionTypes';
import { RootStore } from '../store/store';
import useApi from './useApi';
import uuid from 'react-native-uuid';

interface apiRequestListSingleItem {
  index: number;
  data?: any;
  request?: boolean;
  success?: boolean;
  failed?: boolean;
}

type RequestStatus = {
  status: apiRequestListSingleItem[];
  isAllRequestSuccess: boolean;
};

const useApiRequestInLoop = () => {
  const [api] = useApi();
  const { v4: uuidv4 } = uuid;
  const [url, setUrl] = useState<string[] | string | null>(null);
  const [service, setService] = useState<string | null>(null);
  const [method, setMethod] = useState<string | null>(null);
  const [dataList, setDataList] = useState<any[] | null>(null);
  const [apiRequestStatus, setApiRequestStatus] =
    useState<RequestStatus | null>(null);

  const uniqueID: any = useMemo(() => uuidv4(), []);

  const { apiRequestList } = useSelector((state: RootStore) => state.common);

  const dispatch = useDispatch();

  const [currentApiRequestIndex, setCurrentApiRequestIndex] =
    useState<number>(-1);

  useEffect(() => {
    if (url && service && method && currentApiRequestIndex >= 0) {
      setTimeout(() => {
        if (method === 'POST') {
          api(
            url[0],
            LIST_DATA_REQUEST,
            LIST_DATA_SUCCESS,
            LIST_DATA_FAILED,
            dataList?.[currentApiRequestIndex],
            {},
            method,
            false,
            service,
            { uniqueID, index: currentApiRequestIndex }
          );
        } else {
          api(
            url[currentApiRequestIndex],
            LIST_DATA_REQUEST,
            LIST_DATA_SUCCESS,
            LIST_DATA_FAILED,
            {},
            {},
            method,
            false,
            service,
            { uniqueID, index: currentApiRequestIndex }
          );
        }
      }, 5000);
    }
  }, [currentApiRequestIndex]);

  useEffect(() => {
    if ((dataList?.length || url?.length) && service && method) {
      if (method === 'POST') {
        dispatch({
          type: INITIAL_DATA_LIST_SETUP,
          payload: {
            dataWrapper: { data: { uniqueID, length: dataList?.length || 0 } },
          },
        });
      } else {
        dispatch({
          type: INITIAL_DATA_LIST_SETUP,
          payload: {
            dataWrapper: { data: { uniqueID, length: url?.length || 0 } },
          },
        });
      }
    }
  }, [dataList, url, service, method]);

  useEffect(() => {
    if (
      service &&
      url &&
      method &&
      (dataList?.length || url?.length) &&
      apiRequestList
    ) {
      const list = apiRequestList?.[uniqueID];

      list?.every(({ request, success, failed, index }: any) => {
        if (!request && (!success || !failed)) {
          setCurrentApiRequestIndex(index);
          return false;
        } else {
          return true;
        }
      });

      const completedApiRequest = list?.filter(
        ({ request, success, failed }: any) => request && (success || failed)
      );

      if (method === 'POST') {
        if (completedApiRequest?.length === dataList?.length) {
          const isAllRequestSuccess = completedApiRequest.every(
            ({ success }: any) => success
          );

          setApiRequestStatus({ status: list, isAllRequestSuccess });
        }
      } else {
        if (completedApiRequest?.length === url?.length) {
          const isAllRequestSuccess = completedApiRequest.every(
            ({ success }: any) => success
          );

          setApiRequestStatus({ status: list, isAllRequestSuccess });
        }
      }
    }
  }, [url, service, method, dataList, JSON.stringify(apiRequestList)]);

  const resetApiRequestList = () => {
    dispatch({
      type: LIST_DATA_RESET,
      payload: {
        dataWrapper: { data: { uniqueID } },
      },
    });
  };

  const submitForm = (
    url: string[] | string,
    serviceType: string,
    method: string,
    dataList: any[]
  ) => {
    setUrl(url);
    setService(serviceType);
    setMethod(method);
    setDataList(dataList);
    resetApiRequestList();
    setApiRequestStatus(null);
  };

  return [submitForm, apiRequestStatus] as const;
};

export default useApiRequestInLoop;
