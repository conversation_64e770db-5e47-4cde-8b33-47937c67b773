import { Auth } from 'aws-amplify';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { userRoleType } from '../constants/constants';
import { USER_MANAGEMENT_SERVICE } from '../constants/services';
import { LOGOUT_SUCCESS } from '../store/actionTypes/auth';
import { MATCH_LOG_RESET_DATA } from '../store/actionTypes/MatchLog/MatchLogActions';
import { RESET_TEAMS } from '../store/actionTypes/Team/TeamAction';
import {
  USER_TYPE_REQUEST,
  USER_TYPE_SUCCESS,
  USER_TYPE_FAILED,
} from '../store/actionTypes/userType/userType';
import { RootStore } from '../store/store';
import useApi from './useApi';

const useLogout = () => {
  const { userData, expoPushNotificationToken } = useSelector(
    (state: RootStore) => state?.auth
  );
  const [playerLogoutInitiated, setPlayerLogoutInitiated] = useState(false);
  const [fetchData] = useApi();
  const dispatch = useDispatch();

  const logoutUser = async () => {
    try {
      await Auth.signOut();
      dispatch({
        type: LOGOUT_SUCCESS,
      });
    } catch (error) {
      console.log('logout error', error);
    }
  };

  const handleLogout = async () => {
    const isPlayer = userRoleType.PLAYER === userData?.type;
    if (isPlayer && expoPushNotificationToken) {
      fetchData(
        `/api/v1/users?email=${userData.emailId}`,
        USER_TYPE_REQUEST,
        USER_TYPE_SUCCESS,
        USER_TYPE_FAILED,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
      setPlayerLogoutInitiated(true);

      //exit logout method until push notification tokens are updated for player
      return;
    }
    logoutUser();
  };

  return [handleLogout] as const;
};

export default useLogout;
