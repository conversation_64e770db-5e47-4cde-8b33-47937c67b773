import { useEffect } from 'react';

import { EVENT_SERVICE } from '../constants/services';
import useApi from './useApi';
import {
  EVENT_DELETE_REQUEST,
  EVENT_DELETE_SUCCESS,
  EVENT_DELETE_FAIL,
} from '../store/actionTypes/Event/EventActionTypes';
import useApiPromise from './useApiPromise';
function useRecurringEventDelete() {
  const [getFutureEventCount] = useApiPromise();
  const [apiCall] = useApi();

  const deleteEventData = (selectedOption: string, eventId: any) => {
    if (selectedOption) {
      apiCall(
        `/api/v1/events?eventId=${eventId}&deletionType=${selectedOption}`,
        EVENT_DELETE_REQUEST,
        EVENT_DELETE_SUCCESS,
        EVENT_DELETE_FAIL,
        null,
        '',
        'DELETE',
        false,
        EVENT_SERVICE
      );
    }
  };

  const getEventCount = async (selectedOption: string, eventId: any) => {
    let futureEventData: any = 0;

    try {
      futureEventData = await getFutureEventCount(
        `/api/v1/events/repeat-event-delete-count?deletionType=${
          selectedOption === 'SELECTED_AND_FOLLOWING_EVENTS'
            ? 'SELECTED_AND_FOLLOWING_EVENTS'
            : 'ALL_EVENTS'
        }&eventId=${eventId}`,
        '',
        '',
        '',
        null,
        '',
        'GET',
        false,
        EVENT_SERVICE
      );
    } catch (error) {}

    return futureEventData?.data || 0;
  };

  return [deleteEventData, getEventCount] as const;
}
export default useRecurringEventDelete;
