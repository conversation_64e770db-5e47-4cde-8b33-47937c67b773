import { singleMessageContentTypes } from '../store/reducers/Message/MessageReducer';

export const avoidedDuplicationData = (
  currentData: any[] | null,
  newData: any[] | undefined,
  key: string
) => {
  if (!newData) {
    return currentData;
  }

  const existingValues = currentData?.map(item => item[key]);

  const filteredData = newData.filter(
    item => !existingValues?.includes(item[key])
  );

  return [...(currentData || []), ...filteredData];
};

export const messageDataMapping = (user: any, data: any) => {
  const { id } = user;

  const mapsData =
    data?.map((item: singleMessageContentTypes) => {
      item.isUserOwnMessage = id === item.senderUserId;
      return item;
    }) || null;

  return mapsData;
};

export const getArrayIndexUsingKey = (list: any[], key: string, value: any) => {
  return list?.findIndex(element => element[key] === value);
};
export const filterListByKey = (
  list: any[] | null,
  key: string,
  value: any | null
) => {
  if (!list) {
    return [];
  }
  return list.filter(item => item[key] === value);
};

export const convertCamelCaseToStandardString = (title: any) => {
  return title
    ?.split(' ')
    ?.map((item: string) => item?.[0]?.toUpperCase() + item?.slice(1))
    ?.map((item: string) => item?.replace(/([a-z0-9])([A-Z])/g, '$1 $2'))
    ?.join(' ');
};

export const removeDuplicatesFromArray = <T>(arr: T[]): T[] => {
  const uniqueObjects: { [key: string]: T } = {};
  arr.forEach(obj => {
    const key = JSON.stringify(obj);
    uniqueObjects[key] = obj;
  });
  return Object.values(uniqueObjects);
};
