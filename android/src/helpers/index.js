import { Linking, Platform } from 'react-native';
import { days, EventDateType, months } from '../constants/constants';

export const addLeadingZeros = value => {
  return value < 10 ? '0' + value : value;
};

/**
 * return formatted date object
 *
 * @param {Date} dateTime
 *
 */
export const dateTimeConversion = dateTime => {
  const convertDateTime = new Date(dateTime);
  const dateTimeObject = {};
  const month = convertDateTime.getMonth() + 1;
  dateTimeObject.year = convertDateTime.getFullYear();

  dateTimeObject.month = month;
  dateTimeObject.monthNumberString = `${addLeadingZeros(month)}`;

  dateTimeObject.monthString = months[convertDateTime.getMonth()];
  dateTimeObject.date = convertDateTime.getDate();
  dateTimeObject.dateString = days[convertDateTime.getDay()];
  dateTimeObject.dateReadable = `${getOrdinalNum(convertDateTime.getDate())}`;
  dateTimeObject.dateNumberString = `${addLeadingZeros(
    convertDateTime.getDate()
  )}`;

  dateTimeObject.yearLastTwoDigit = Number(
    convertDateTime.getFullYear().toString().substring(-2)
  );
  dateTimeObject.hours24 = convertDateTime.getHours();
  dateTimeObject.hours12 = convertDateTime.getHours() % 12 || 12;
  dateTimeObject.minutes = convertDateTime.getMinutes();

  dateTimeObject.hours24String = `${addLeadingZeros(
    convertDateTime.getHours()
  )}`;
  dateTimeObject.hours12String = `${addLeadingZeros(
    convertDateTime.getHours() % 12 || 12
  )}`;
  dateTimeObject.minutesString = `${addLeadingZeros(
    convertDateTime.getMinutes()
  )}`;

  dateTimeObject.secondsString = `${addLeadingZeros(
    convertDateTime.getSeconds()
  )}`;

  dateTimeObject.amPm = convertDateTime.getHours() >= 12 ? 'pm' : 'am';

  dateTimeObject.yearMonthDateString = `${convertDateTime.getFullYear()}-${addLeadingZeros(
    month
  )}-${addLeadingZeros(convertDateTime.getDate())}`;
  dateTimeObject.dateDefaultFormat = convertDateTime;

  dateTimeObject.timestamp = convertDateTime.getTime();
  dateTimeObject.dateAndTime24AsNumber = Number(
    `${dateTimeObject.year}${dateTimeObject.monthNumberString}${dateTimeObject.dateNumberString}${dateTimeObject.hours24String}${dateTimeObject.minutesString}`
  );

  dateTimeObject.dateAsNumber = Number(
    `${dateTimeObject.year}${dateTimeObject.monthNumberString}${dateTimeObject.dateNumberString}`
  );

  return dateTimeObject;
};

export const dateTimeUTCConversion = dateTime => {
  const convertDateTime = new Date(dateTime);
  const dateTimeObject = {};
  const month = convertDateTime.getUTCMonth() + 1;
  dateTimeObject.year = convertDateTime.getUTCFullYear();

  dateTimeObject.month = month;
  dateTimeObject.monthNumberString = `${addLeadingZeros(month)}`;

  dateTimeObject.monthString = months[convertDateTime.getUTCMonth()];
  dateTimeObject.date = convertDateTime.getUTCDate();
  dateTimeObject.dateString = days[convertDateTime.getUTCDay()];
  dateTimeObject.dateReadable = `${getOrdinalNum(
    convertDateTime.getUTCDate()
  )}`;
  dateTimeObject.dateNumberString = `${addLeadingZeros(
    convertDateTime.getUTCDate()
  )}`;

  dateTimeObject.yearLastTwoDigit = Number(
    convertDateTime.getUTCFullYear().toString().substring(-2)
  );
  dateTimeObject.hours24 = convertDateTime.getHours();
  dateTimeObject.hours12 = convertDateTime.getHours() % 12 || 12;
  dateTimeObject.minutes = convertDateTime.getMinutes();

  dateTimeObject.hours24String = `${addLeadingZeros(
    convertDateTime.getHours()
  )}`;
  dateTimeObject.hours12String = `${addLeadingZeros(
    convertDateTime.getHours() % 12 || 12
  )}`;
  dateTimeObject.minutesString = `${addLeadingZeros(
    convertDateTime.getMinutes()
  )}`;

  dateTimeObject.amPm = convertDateTime.getHours() >= 12 ? 'pm' : 'am';

  dateTimeObject.yearMonthDateString = `${convertDateTime.getUTCFullYear()}-${addLeadingZeros(
    month
  )}-${addLeadingZeros(convertDateTime.getUTCDate())}`;
  dateTimeObject.dateDefaultFormat = convertDateTime;

  dateTimeObject.timestamp = convertDateTime.getTime();
  dateTimeObject.timestampUTC = Number(
    new Date(
      `${convertDateTime.getUTCFullYear()}-${addLeadingZeros(
        month
      )}-${addLeadingZeros(convertDateTime.getUTCDate())}`
    )
  );

  dateTimeObject.utc_timestamp = Date.UTC(
    convertDateTime.getUTCFullYear(),
    convertDateTime.getUTCMonth(),
    convertDateTime.getUTCDate(),
    12,
    0,
    0,
    0
  );

  return dateTimeObject;
};

/**
 * return month  as a array of objects
 *
 * @param {Date} date
 *
 */
const getDaysInMonth = (year, month) => {
  // month parameter is 1-based, but Date constructor expects 0-based month
  return new Date(year, month, 0).getDate();
};

export const calendarDays = userDate => {
  let { year, month, date } = dateTimeUTCConversion(userDate);
  let calendar = [];
  let currentWeek = 0;
  let week = 0;

  // Get the day of the week for the first day of the month (0-6)
  let firstDayOfMonth = new Date(year, month - 1, 1).getDay();

  // Add empty dates for days before the 1st of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    calendar[0] = calendar[0] || { week: 0, dates: [] };
    calendar[0].dates.push({ date: null, index: null });
  }

  const totalCount = getDaysInMonth(year, month)

  for (let index = 1; index <= totalCount; index++) {
    let dayStr = `${year}-${addLeadingZeros(month)}-${addLeadingZeros(index)}`;
    if (!isNaN(new Date(dayStr).getDay())) {
      let day = new Date(dayStr).getDay();
      let dateObject = { date: new Date(dayStr), index };

      if (new Date().getDate() === index && new Date().getMonth() + 1 === month) {
        currentWeek = week;
      }

      calendar[week] = calendar[week] || { week: week, dates: [] };
      calendar[week].dates.push(dateObject);

      if (day === 6) {  // Saturday is the last day of the week
        week++;
      }
    }
  }

  // Fill in any remaining days in the last week
  while (calendar[calendar.length - 1].dates.length < 7) {
    calendar[calendar.length - 1].dates.push({ date: null, index: null });
  }

  return { calendar, currentWeek };
};

/**
 * return array of months and year
 *
 * @param {int} count
 *
 */
export const calendarFutureMonths = count => {
  let now = new Date();
  let monthsArray = [];
  for (let index = 0; index < count; index++) {
    let future = new Date(now.getFullYear(), now.getMonth() -1 + index, 1);
    let monthName = months[future.getMonth()];
    let month = future.getMonth() + 1;
    let year = future.getFullYear();
    monthsArray = [...monthsArray, { year, monthName, month, index }];
  }

  return monthsArray;
};

/**
 * search event by Date
 *
 * @param {*} events
 * @param {*} date
 * @param {*} teamData
 *
 */
export const eventSearchByDate = (events, date, teamData) => {
  let array = [];

  events?.map(item => {
    const scheduleDateString = dateTimeConversion(item.startTime);

    const teamInfo = teamData.data.filter(team => team._id == item.teamId);

    if (date === scheduleDateString.yearMonthDateString) {
      array = [
        ...array,
        {
          ...item,
          team: teamInfo?.length
            ? { ...teamInfo[0] }
            : item?.team
              ? { ...item.team, teamName: item.team?.name }
              : {},
        },
      ];
    }
  });
  return array;
};

/**
 * Generate next date
 *
 * @param {*} param0
 *
 */
export const getNextDate = ([year, month, date]) => {
  const yearFormat = { current: Number(year), next: Number(year) + 1 };
  const monthFormat = { current: Number(month), next: Number(month) + 1 };
  const dateFormat = { current: Number(date), next: Number(date) + 1 };

  const nextDay = addLeadingZeros(dateFormat.next);

  const nextMonth = addLeadingZeros(monthFormat.next);

  const nextYear = yearFormat.next;

  if (!isNaN(dateTimeConversion(`${year}-${month}-${nextDay}`).year)) {
    return dateTimeConversion(`${year}-${month}-${nextDay}`);
  }

  if (!isNaN(dateTimeConversion(`${year}-${nextMonth}-01`).year)) {
    return dateTimeConversion(`${year}-${nextMonth}-01`);
  }

  return dateTimeConversion(`${nextYear}-01-01`);
};

/**
 * Generate time for duration
 *
 * @param {*} param0
 *
 */
export const getNextDateByDuration = ([year, month, date], duration) => {
  const { timestamp } = dateTimeConversion(`${year}-${month}-${date}`);
  const nextDate = new Date(timestamp + duration * 24 * 60 * 60 * 1000);
  return dateTimeConversion(nextDate);
};

/**
 * Generate next month
 *
 * @param {*} param0
 *
 */
export const getNextMonth = ([year, month, date]) => {
  const yearFormat = { current: Number(year), next: Number(year) + 1 };
  const monthFormat = { current: Number(month), next: Number(month) + 1 };

  const nextMonth = addLeadingZeros(monthFormat.next);

  const nextYear = yearFormat.next;

  if (!isNaN(dateTimeConversion(`${year}-${nextMonth}-01`).year)) {
    return dateTimeConversion(`${year}-${nextMonth}-01`);
  }

  return dateTimeConversion(`${nextYear}-01-01`);
};

/**
 * Generate next week same date
 *
 * @param {*} param0
 *
 */
export const getNextWeekDate = ([year, month, date]) => {
  const yearFormat = { current: Number(year), next: Number(year) + 1 };
  const monthFormat = { current: Number(month), next: Number(month) + 1 };
  const dateFormat = { current: Number(date), next: Number(date) + 7 };

  const nextDay = addLeadingZeros(dateFormat.next);

  const nextMonth = addLeadingZeros(monthFormat.next);

  const nextYear = yearFormat.next;

  if (!isNaN(dateTimeConversion(`${year}-${month}-${nextDay}`).year)) {
    return dateTimeConversion(`${year}-${month}-${nextDay}`);
  }

  if (!isNaN(dateTimeConversion(`${year}-${nextMonth}-01`).year)) {
    return dateTimeConversion(`${year}-${nextMonth}-01`);
  }

  return dateTimeConversion(`${nextYear}-01-01`);
};



export const getCurrentMonthRange = ([year, month]) => {
 // Create date strings in ISO format to ensure consistent timezone handling
 const firstDay = `${year}-${addLeadingZeros(month)}-01T00:00:00.000Z`;
  
 // Calculate last day by getting the 0th day of next month
 const lastDay = new Date(Date.UTC(Number(year), Number(month), 0));
 const lastDayStr = lastDay.toISOString();
 
 // Calculate previous month's last day
 const prevMonthLastDay = new Date(Date.UTC(Number(year), Number(month) - 1, 0));
 const prevMonthLastDayStr = prevMonthLastDay.toISOString();
 
 // Calculate next month's first day
 const nextMonthFirstDay = new Date(Date.UTC(Number(year), Number(month), 1));
 const nextMonthFirstDayStr = nextMonthFirstDay.toISOString();
 
 return {
   start: prevMonthLastDayStr,
   end: nextMonthFirstDayStr,
   monthRange: {
     first: dateTimeConversion(firstDay),
     last: dateTimeConversion(lastDayStr)
   }
 };
};


/**
 * Generate prev  month
 *
 * @param {*} param0
 *
 */
export const getPrevMonth = ([year, month, date]) => {
  const yearFormat = { current: Number(year), prev: Number(year) - 1 };
  const monthFormat = { current: Number(month), prev: Number(month) - 1 };

  const prevMonth = addLeadingZeros(monthFormat.prev);

  const prevYear = yearFormat.prev;

  if (!isNaN(dateTimeConversion(`${year}-${prevMonth}-01`).year)) {
    return dateTimeConversion(`${year}-${prevMonth}-01`);
  }

  return dateTimeConversion(`${prevYear}-12-01`);
};

/**
 * return 7 days on selected day belong
 *
 * @param {*} current
 *
 */
export const daysOnWeek = current => {
  let calendar = calendarDays(current);

  let week = {};
  calendar.calendar.map((item, index) => {
    if (
      item.dates.filter(day => day.index == new Date(current).getDate()).length
    ) {
      week = item.dates;
    }
  });

  return week;
};

/**
 * return  all event belong to 7 days
 *
 * @param {*} events
 * @param {*} date
 * @param {*} teamData \
 *
 */
export const eventSearchForWeek = (events, date, teamData) => {
  const weekDates = daysOnWeek(date);
  let allEvents = [];

  if (weekDates.length) {
    weekDates.forEach(element => {
      const { yearMonthDateString } = dateTimeConversion(element.date);
      const singleDateEvents = eventSearchByDate(
        events,
        yearMonthDateString,
        teamData
      );
      allEvents = [...allEvents, ...singleDateEvents];
    });
  }
  return allEvents;
};

/**
 * convert  string  to defined  length
 *
 * @param {string} string string
 * @param {number} length
 */
export const stringLength = (string, length) => {
  if (string?.length > length) {
    return string.substring(0, length) + '...';
  }
  return string;
};

/**
 * Convert string in to title case
 *
 * @param {string} str
 */
export const stringToTitleCase = str => {
  return str.replace(/\w\S*/g, function (txt) {
    return txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase();
  });
};

/**
 * to calculateEventDate
 *
 * @param {Date} date
 *
 */
export const calculateEventDate = (monthCount, type) => {
  if (type === EventDateType.END) {
    return new Date(
      new Date().setMonth(new Date().getMonth() + monthCount)
    ).toISOString();
  } else if (type === EventDateType.START) {
    return new Date(
      new Date().setMonth(new Date().getMonth() - monthCount)
    ).toISOString();
  }
};

export const openMap = location => {
  const { latitude, longitude, name } = location || {};
  if (latitude && longitude && name) {
    const url = Platform.select({
      ios: 'maps:' + latitude + ',' + longitude + '?q=' + name,
      android: 'geo:' + latitude + ',' + longitude + '?q=' + name,
    });
    Linking.openURL(url);
  }
};

const getOrdinalNum = n => {
  return (
    n +
    (n > 0
      ? ['th', 'st', 'nd', 'rd'][(n > 3 && n < 21) || n % 10 > 3 ? 0 : n % 10]
      : '')
  );
};

export const checkIsPastDate = selectedDate => {
  const {
    dateNumberString: currentDate,
    year: currentYear,
    monthNumberString: currentMonth,
  } = dateTimeUTCConversion(Date.now());

  const { dateNumberString, year, monthNumberString } =
    dateTimeUTCConversion(selectedDate);

  return (
    Number(`${year}${monthNumberString}${dateNumberString}`) <
    Number(`${currentYear}${currentMonth}${currentDate}`)
  );
};

export const getFirstDateOfMonth = inputDate => {
  // Create a new Date object based on the input date
  const date = new Date(inputDate);

  // Set the date to the first day of the month
  date.setDate(1);

  // Return the updated date
  return date;
};

export const dateToYMD = date => {
  var d = date.getDate();
  var m = date.getMonth() + 1; //Month from 0 to 11
  var y = date.getFullYear();
  return '' + y + '-' + (m <= 9 ? '0' + m : m) + '-' + (d <= 9 ? '0' + d : d);
};

/**
 * Get file extension
 *
 * @param {string} url
 * @returns string
 */
export const get_url_extension = url => {
  return url.split(/[#?]/)[0].split('.').pop().trim();
};

//replace the date in a dateString (output eg: '2022-06-01')
export const replaceDate = (dateString, date = '01') => {
  const customDateArray = [...dateString.split('-').splice(0, 2), date];
  const customDateString = customDateArray.join('-');
  return customDateString;
};

export const avoidedDuplicationData = (
  currentData,
  newData,
  key,
  pageCount
) => {
  if (pageCount === 1) {
    return newData;
  }
  if (!newData) {
    return currentData;
  }

  const existingValues = currentData?.map(item => item[key]);

  const filteredData = newData.filter(
    item => !existingValues?.includes(item[key])
  );

  return [...(currentData || []), ...filteredData];
};

export const convertS3UriToObject = imageUrl => {
  const items = imageUrl.split('/');
  const bucketName = imageUrl.split('/', 1)[0];
  const removeBucketName = items.filter(item => item !== bucketName);
  const fileKey = removeBucketName.join('/');

  return {
    bucketName,
    fileKey,
  };
};

export const renderDefaultValue = (items, playerData) => {
  return [items?.find(item => item?.label === playerData)?.value];
};

export const capitalize = string => {
  return string?.[0] + string?.slice(1)?.toLowerCase();
};
export const generateNewDateWithTimeReset = () => {
  let newDate = new Date();
  newDate.setHours(0);
  newDate.setMinutes(0);
  newDate.setSeconds(0);
  return newDate;
};

export const getDateObjectFromISOString = dateIsoString => {
  return {
    date: new Date(dateIsoString).getDate(),
    month: new Date(dateIsoString).getMonth() + 1,
    year: new Date(dateIsoString).getFullYear(),
  };
};

export const convertDateObjectToJSDate = dateObj => {
  const { date, month, year } = dateObj || {};

  if (!date || !month || !year) return new Date();
  return new Date(Date.UTC(year, month - 1, date));
};
