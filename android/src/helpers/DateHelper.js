import { dateTimeConversion, addLeadingZeros } from './index';
export const dateToString = date => {
  const addLeadingZero = number => {
    return number < 10 ? `0${number}` : number;
  };

  return (
    date.getFullYear() +
    '-' +
    addLeadingZero(Number(date.getMonth() + 1)) +
    '-' +
    addLeadingZero(date.getDate())
  );
};

export function getTimeHourMin(date) {
  if (date) {
    const time = date.split(' ');
    return time[0];
  } else {
    return '';
  }
}

export function getTimeHourMinWithoutSeconds(date) {
  if (date) {
    const time = date.split(' ');
    const timeArray = time[0].split(':');
    const timeWithoutSeconds = timeArray[0] + ':' + timeArray[1];
    return timeWithoutSeconds;
  } else {
    return '';
  }
}
/**
 * Get age from date of birth
 */
export function getAge(dob) {
  const today = new Date();
  const birthDate = new Date(dob);
  const month = today.getMonth() - birthDate.getMonth();

  let age = today.getFullYear() - birthDate.getFullYear();

  // If the month is equal or greater than the current month.
  if (month < 0 || (month === 0 && today.getDate() < birthDate.getDate())) {
    age = age - 1;
  }

  return age;
}

/**
 * convert Date to dd/mm/yyyy
 */
export function dateFormatConvert(dateTime) {
  let { year, month, date } = dateTimeConversion(dateTime);

  return `${addLeadingZeros(date)}/${addLeadingZeros(month)}/${year}`;
}

export function dateFormatConvertByString(year, month, date, format) {
  let dateformatString = format;

  dateformatString = dateformatString.replace('YYYY', `${year}`);
  dateformatString = dateformatString.replace('MM', `${month}`);
  dateformatString = dateformatString.replace('DD', `${date}`);

  return dateformatString;
}

export const getLocalDateTimeZone = () => {
  const offset = new Date().getTimezoneOffset();
  return new Date(new Date().getTime() - offset * 60 * 1000);
};

export const getRecentFutureDateKey = startTimes => {
  let recentFutureDate;
  const { dateAndTime24AsNumber: localTime } = dateTimeConversion(new Date());

  const recentFutureDates = startTimes.filter(dateTime => {
    const { dateAndTime24AsNumber: startTime } = dateTimeConversion(dateTime);
    return startTime >= localTime;
  });

  if (recentFutureDates?.length) {
    const { monthString, dateReadable, dateString } = dateTimeConversion(
      recentFutureDates[0]
    );

    return `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
  }
  return;
};

export const getDateKey = startTimes => {
  const { dateAndTime24AsNumber: localTime } = dateTimeConversion(new Date());
  const currentDate = new Date();

  const todayStart = startTimes.find(dateTime => {
    const dateTimeDate = dateTime.split('T')[0];
    const currentDateDate = currentDate.toISOString().split('T')[0];
    return dateTimeDate === currentDateDate;
  });

  if (todayStart) {
    const { monthString, dateReadable, dateString } =
      dateTimeConversion(currentDate);

    return `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
  }

  const futureDates = startTimes.filter(dateTime => {
    const { dateAndTime24AsNumber: startTime } = dateTimeConversion(dateTime);
    return startTime >= localTime;
  });

  if (futureDates?.length) {
    const { monthString, dateReadable, dateString } = dateTimeConversion(
      futureDates[0]
    );
    return `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
  }

  const { monthString, dateReadable, dateString } = dateTimeConversion(
    startTimes[startTimes.length - 1]
  );

  return `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
};

export const getDateRangeInLocalTimezone = (startDate, endDate) => {
  // Create dates in local timezone (Sri Lanka)
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // Convert to UTC for API
  const utcStart = new Date(start.getTime() - (start.getTimezoneOffset() * 60000))
  const utcEnd = new Date(end.getTime() - (end.getTimezoneOffset() * 60000))
    .setHours(23, 59, 59, 999);
  
  return {
    startDate: utcStart,
    endDate: new Date(utcEnd)
  };
}

export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return {
    month: date.toLocaleString('default', { month: 'long' }),
    date: date.getDate(), // Get the day of the month
    time: date.toLocaleString('default', { 
      hour: 'numeric', 
      minute: 'numeric', 
      hour12: true 
    }).toLowerCase()
  };
};