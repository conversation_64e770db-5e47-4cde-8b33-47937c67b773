export const sendDataIntoUseFileUploadHook = (
  s3BucketInfo: {
    [name: string]: {
      fileKey: string;
      bucketName: string;
    };
  } | null,
  uploadFileKey: string | number,
  upload: (
    data: any,
    fileKey: string | undefined,
    bucketName: string | undefined
  ) => void,
  data: any
) => {
  const { fileKey, bucketName } = { ...s3BucketInfo?.[uploadFileKey] };

  if (data) {
    upload(data, fileKey, bucketName);
  }
};
