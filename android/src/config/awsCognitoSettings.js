const awsConfiguration = {
  Auth: {
    identityPoolId: 'ap-south-1:65317e1f-b6ea-45c1-ab0f-2333082b0db9',
    region: 'ap-south-1',
    identityPoolRegion: 'ap-south-1',
    userPoolId: 'ap-south-1_2Leuihgze',
    userPoolWebClientId: '3mukoqd0cdqsmbidi4njj80vho',
    oauth: {
      domain: 'https://dev-aktive.auth.ap-south-1.amazoncognito.com',
      scope: ['openid'],
      redirectSignIn: 'http://localhost:19006',
      redirectSignOut: '',
      responseType: 'code',
    },
  },
  Storage: {
    AWSS3: {
      region: 'us-east-1',
    },
  },
  API: {
    endpoints: [
      {
        name: 'execute-api',
        endpoint: 'https://u9gle2jcj5.execute-api.ap-south-1.amazonaws.com',
        region: 'ap-south-1',
      },
    ],
  },
};

export default awsConfiguration;
