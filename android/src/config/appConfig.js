//Google API Key
export const GOOGLE_API_KEY = 'AIzaSyCfX8OFrpDZtCLw9Ra0AGsCSsJXiJdfHTE';

//Check Device Tab or Phone
import { Dimensions } from 'react-native';
import { PixelRatio } from 'react-native';

export const isTabDevice = () => {
  let pixelDensity = PixelRatio.get();
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;
  const adjustedWidth = screenWidth * pixelDensity;
  const adjustedHeight = screenHeight * pixelDensity;
  if (screenWidth < 750) {
    return false;
  } else if (screenWidth > 900) {
    return true;
  } else if (
    pixelDensity < 2 &&
    (adjustedWidth >= 1000 || adjustedHeight >= 1000)
  ) {
    return true;
  } else
    return (
      pixelDensity === 2 && (adjustedWidth >= 1920 || adjustedHeight >= 1920)
    );
};
