import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerDropdownStyles = (colors: any) => ({
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        height: wp('45%'),
        paddingTop: hp('5%'),
        paddingRight: hp('5%'),
        width: wp('80%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('70%'),
        height: hp('90%'),
        paddingTop: wp('6%'),
        paddingRight: wp('6%'),
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        marginTop: 15,
        width: wp('15%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        marginTop: 20,
        width: wp('35%'),
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      },
  textStyle: {
    color: colors.black,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: isTabDevice()
    ? {
        marginBottom: hp('2%'),
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
      }
    : {
        marginBottom: hp('2%'),
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
      },
  playerListContainer: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        height: wp('30%'),
      }
    : {
        width: wp('57%'),
      },
  playerListDivider: isTabDevice()
    ? {
        width: wp('0.1%'),
        backgroundColor: colors.lightBlue,
        height: wp('30%'),
      }
    : {
        width: wp('60%'),
        backgroundColor: colors.lightBlue,
        height: wp('0.5%'),
        marginTop: wp('2%'),
        marginBottom: wp('2%'),
      },
  playerListHeading: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'left',
        width: '100%',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'left',
        width: '100%',
      },
  closeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        position: 'absolute',
        right: -6,
        top: -7,
      },
  addView: isTabDevice()
    ? {
        position: 'absolute',
        bottom: -wp('5%'),
        right: wp('3%'),
      }
    : {
        marginRight: hp('3%'),
      },
  AddTouchableOpacity: isTabDevice()
    ? {
        height: wp('7%'),
        width: wp('7%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('11%'),
        width: wp('11%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: wp('2%'),
      },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('-0.5'),
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('4%'),
        top: wp('4%'),
        zIndex: 9,
      },

  // player list
  listItem: isTabDevice()
    ? {
        backgroundColor: colors.midDarkBlue,
        borderRadius: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 5,
        paddingRight: 5,
        marginBottom: wp('1%'),
        width: '100%',
      }
    : {
        backgroundColor: colors.midDarkBlue,
        borderRadius: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 5,
        paddingRight: 5,
        marginBottom: wp('1%'),
        width: '100%',
      },
  listItemSelected: isTabDevice()
    ? {
        backgroundColor: colors.lightGrey,
        borderRadius: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 5,
        paddingRight: 5,
        marginBottom: wp('1%'),
        width: '100%',
      }
    : {
        backgroundColor: colors.lightGrey,
        borderRadius: 10,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 5,
        paddingRight: 5,
        marginBottom: wp('1%'),
        width: '100%',
      },
  listItemText: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: wp('1%'),
        marginRight: wp('4%'),
        fontSize: wp('1.6%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        paddingLeft: wp('3%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  listContainer: isTabDevice()
    ? {
        width: '30%',
      }
    : {
        width: '100%',
      },
  listTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        marginBottom: 15,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
        marginBottom: 15,
      },
  list: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 15,
        padding: wp('1%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 15,
        padding: wp('2%'),
        height: hp('18%'),
        width: wp('60%'),
      },
});
export default PlayerDropdownStyles;
