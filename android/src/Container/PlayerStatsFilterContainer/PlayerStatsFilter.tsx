import { AntDesign } from '@expo/vector-icons';
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Text,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import PlayerStatsList from '../../components/PlayerStatsList/PlayerStatsList';
import useStyles from '../../hooks/useStyles';
import CustomPlayerDropdownStyles from './PlayerStatsFilterStyles';
import {
  seasonsType,
  tournamentsType,
  teamType,
  opponentType,
} from '../../store/reducers/PlayerStats/PlayerStatsReducer';
import { filterType } from '../../constants/data';
import PlayerStateDateRange from '../../components/PlayerStatsDateRange/PlayerStateDateRange';

interface IPlayerStatsFliter {
  modalVisible: boolean;
  onHandleFilter: () => void;
  seasonsData?: seasonsType[];
  tournamentsData?: tournamentsType[];
  teamData?: teamType[];
  setSelectedSeasons: Function;
  setSelectedTournaments: Function;
  setSelectedTeams: Function;
  selectedTournaments: tournamentsType[];
  selectedTeams: teamType[];
  selectedSeasons: seasonsType[];
  teamID: string;
  selectedStatCategory: string;
  selectedDateRange: Date;
  setSelectedDateRange: Function;
  isEndDateError: boolean;
  opponentData: opponentType[];
  onClose: Function;
}

const PlayerStatsFliter: React.FC<IPlayerStatsFliter> = ({
  modalVisible,
  onHandleFilter,
  seasonsData,
  tournamentsData,
  teamData,
  setSelectedSeasons,
  setSelectedTournaments,
  setSelectedTeams,
  selectedTournaments,
  selectedTeams,
  selectedSeasons,
  teamID,
  selectedStatCategory,
  selectedDateRange,
  setSelectedDateRange,
  isEndDateError,
  isStartDateError,
  opponentData,
  selectedOpponent,
  setSelectedOpponent,
  selectedTempDateRange,
  setSelectedTempDateRange,
  onClose,
}: any) => {
  const PlayerDropdownStyles = useStyles(CustomPlayerDropdownStyles);

  const initialDateRange = {
    startDate: '',
    endDate: '',
  };

  const [selectedTempSeason, setSelectedTempSeason] = useState([]);
  const [selectedTempTournament, setSelectedTempTournament] = useState([]);
  const [selectedTempTeam, setSelectedTempTeam] = useState([]);
  const [selectedTempOpponent, setSelectedTempOpponent] = useState([]);

  const customTeamData = () => {
    return teamData?.map(({ _id, name }: teamType) => ({
      name,
      _id,
    }));
  };
  const selectedMatchWise = selectedStatCategory === '3';

  useEffect(() => {
    if (isEndDateError || isStartDateError) {
      setSelectedDateRange(initialDateRange);
    }
  }, [isEndDateError, isStartDateError]);

  useEffect(() => {
    if (modalVisible) {
      setSelectedTempOpponent(selectedOpponent);
      setSelectedTempSeason(selectedSeasons);
      setSelectedTempTournament(selectedTournaments);
      setSelectedTempTeam(selectedTeams);
      setSelectedTempDateRange(selectedDateRange);
    }
  }, [modalVisible]);

  const onFilterHandler = () => {
    onHandleFilter();
    if (
      (selectedStatCategory === '3' &&
        selectedTempDateRange.startDate &&
        !selectedTempDateRange.endDate) ||
      (selectedStatCategory === '3' &&
        !selectedTempDateRange.startDate &&
        selectedTempDateRange.endDate)
    ) {
      return;
    } else {
      setSelectedSeasons(selectedTempSeason);
      setSelectedTournaments(selectedTempTournament);
      setSelectedTeams(selectedTempTeam);
      setSelectedOpponent(selectedTempOpponent);
      setSelectedDateRange(selectedTempDateRange);
    }
  };
  const onHandleClose = () => {
    setSelectedTempDateRange(initialDateRange);
    setSelectedTempOpponent([]);
    setSelectedTempSeason([]);
    setSelectedTempTournament([]);
    setSelectedTempTeam([]);
    onClose();
  };

  return (
    <View style={PlayerDropdownStyles.container}>
      {modalVisible && (
        <Modal animationType="slide" transparent={true}>
          <View style={PlayerDropdownStyles.centeredView}>
            <View style={PlayerDropdownStyles.overlay}></View>
            <View style={PlayerDropdownStyles.modalView}>
              <TouchableOpacity
                style={PlayerDropdownStyles.closeButton}
                onPress={onHandleClose}
              >
                <View>
                  <AntDesign
                    name="close"
                    size={20}
                    color="#FFFFFF"
                    style={PlayerDropdownStyles.closeText}
                  />
                </View>
              </TouchableOpacity>
              <Text style={PlayerDropdownStyles.playerListHeading}>Filter</Text>
              <View style={PlayerDropdownStyles.playerListContainer}>
                {selectedStatCategory !== '3' ? (
                  <PlayerStatsList
                    data={seasonsData}
                    title={filterType.SEASON}
                    isMultiple={true}
                    selectedItems={selectedTempSeason}
                    setSelectedItems={setSelectedTempSeason}
                  />
                ) : (
                  <View style={PlayerDropdownStyles.listContainer}>
                    <PlayerStateDateRange
                      title="Date Range"
                      selectedDateRange={selectedTempDateRange}
                      setSelectedDateRange={setSelectedTempDateRange}
                      isEndDateError={isEndDateError}
                      isStartDateError={isStartDateError}
                    />
                  </View>
                )}
                <View style={PlayerDropdownStyles.playerListDivider}></View>
                <PlayerStatsList
                  data={selectedMatchWise ? opponentData : tournamentsData}
                  title={
                    selectedMatchWise
                      ? filterType.OPPONENT
                      : filterType.TOURNAMENT
                  }
                  isMultiple={true}
                  selectedItems={
                    selectedMatchWise
                      ? selectedTempOpponent
                      : selectedTempTournament
                  }
                  setSelectedItems={
                    selectedMatchWise
                      ? setSelectedTempOpponent
                      : setSelectedTempTournament
                  }
                />
                <View style={PlayerDropdownStyles.playerListDivider}></View>
                <PlayerStatsList
                  data={customTeamData()}
                  title={filterType.TEAM}
                  isMultiple={true}
                  selectedItems={selectedTempTeam}
                  setSelectedItems={setSelectedTempTeam}
                  teamID={teamID}
                />
              </View>
              <View>
                <TouchableHighlight
                  onPress={onFilterHandler}
                  style={PlayerDropdownStyles.button}
                >
                  <Text style={PlayerDropdownStyles.buttonText}>Filter</Text>
                </TouchableHighlight>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </View>
  );
};

export default PlayerStatsFliter;
