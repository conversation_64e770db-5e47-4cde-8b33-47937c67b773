import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoActivityStyles = colors => ({
  PlayerInfoWrapper: isTabDevice()
    ? {
        width: wp('67%'),
        marginBottom: hp('5%'),
      }
    : {
        width: wp('67%'),
      },
  container: isTabDevice()
    ? {
        width: '100%',
        height: hp('60%'),
        marginBottom: hp('5%'),
      }
    : {
        width: '100%',
        height: hp('60%'),
      },
  dataRow: {},
});
export default PlayerInfoActivityStyles;
