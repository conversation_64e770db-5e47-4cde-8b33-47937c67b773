import { Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const addUserStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('58%'),
        height: hp('85%'),
        flexDirection: 'column',
        // marginBottom: wp('27%'),
      }
    : {
        width: wp('96%'),
        height: hp('90%'),
        marginBottom: hp('30%'),
      },
  loaderWrapper: {
    position: 'absolute',
    top: wp('30%'),
    left: wp('30%'),
  },
  topRowLeft: {},
  topRowRight: {},
  topRow: {
    width: wp('100%'),
    paddingBottom: wp('2%'),
  },
  bottomRow: isTabDevice()
    ? {
        width: '100%',
      }
    : {
        width: wp('100%'),
      },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarText: isTabDevice()
    ? {
        fontSize: hp('2.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        width: wp('16%'),
        marginLeft: wp('2%'),
      }
    : {
        fontSize: wp('4.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        width: wp('30%'),
        marginLeft: wp('2%'),
      },
  TouchablePressed: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('10%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        margin: hp('1%'),
      }
    : {
        backgroundColor: colors.green,
        height: wp('33%'),
        width: wp('33%'),
        borderRadius: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        marginTop: wp('1%'),
      },
  addUserImg: isTabDevice()
    ? {
        height: wp('7%'),
        width: wp('7%'),
      }
    : {
        height: wp('15%'),
        width: wp('15%'),
      },
  userImg: isTabDevice()
    ? {
        height: wp('10%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        position: 'absolute',
      }
    : {
        height: wp('33%'),
        width: wp('33%'),
        borderRadius: wp('2%'),
        position: 'absolute',
      },
  datePicker: {},
  datePickerContainer: {},
  datePickerSelector: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'absolute',
        left: wp('27%'),
        top: wp('25%'),
      }
    : {
        width: wp('95%'),
        position: 'absolute',
        left: 0,
        height: wp('110%'),
        top: wp('50%'),
      },
  datePickerWrapper: isTabDevice()
    ? {
        // zIndex: 3,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      }
    : {
        position: 'absolute',
        borderRadius: wp('3%'),
        top: 0,
        left: 0,
        // zIndex: 3,
        width: '100%',
        borderWidth: 10,
        borderColor: colors.semiDarkBlue,
        backgroundColor: colors.white,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },

  textInput: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        width: wp('24%'),
        padding: hp('1.5%'),
        height: hp('6%'),
      }
    : {
        height: hp('5%'),
        paddingLeft: hp('1%'),
        color: colors.white,
        fontSize: hp('2%'),
        width: '100%',
      },
  commentInput: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        width: wp('70%'),
        padding: hp('1.5%'),
        height: hp('13%'),
      }
    : {
        height: hp('5%'),
        paddingLeft: hp('1%'),
        color: colors.white,
        fontSize: hp('2%'),
        width: '100%',
      },
  placeholderStyle: {
    color: colors.lightGrey,
    // fontSize: wp('1.5%')
    fontSize: 20,
  },
  scrollContainer: isTabDevice()
    ? {
        width: wp('70%'),
        height: hp('100%'),
      }
    : {
        height: hp('55%'),
      },
  scrollContainerParent: isTabDevice()
    ? {}
    : {
        height: hp('35%'),
      },
  dropdownList: {
    backgroundColor: colors.borderBlue,
    borderColor: colors.borderBlue,
    marginTop: wp('-1%'),
    borderRadius: wp('100%'),
    position: 'absolute',
    // zIndex: 100,
    borderBottomLeftRadius: wp('1.5%'),
    borderBottomRightRadius: wp('1.5%'),
  },
  rowView: isTabDevice()
    ? {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
        // zIndex: 10,
      }
    : {
        flexDirection: 'column',
        width: wp('96%'),
        justifyContent: 'space-between',
        // marginBottom: wp('2%'),
        // zIndex: 10,
      },
  rowSelectionView: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('20%'),
        marginTop: wp('1%'),
        height: wp('5%'),
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('96%'),
        marginTop: wp('1%'),
      },
  rowSelectionView2: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('20%'),
        marginTop: wp('1%'),
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('96%'),
        marginTop: wp('2%'),
      },
  rowLastView: isTabDevice()
    ? {
        width: wp('60%'),
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginTop: wp('3%'),
      }
    : {
        width: wp('97%'),
        flexDirection: 'row',
        justifyContent: 'center',
      },
  buttonWrapper: isTabDevice()
    ? {
        width: wp('40%'),
        justifyContent: 'center',
        alignItems: 'flex-end',
      }
    : {
        width: wp('95%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  teamList: isTabDevice()
    ? {
        maxHeight: wp('15%'),
      }
    : {
        maxHeight: wp('40%'),
      },
  inputView: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.7%'),
        // padding: wp('1.5%'),
        paddingRight: wp('1.5%'),
        // width: wp('25%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
        height: hp('6%'),
        padding: hp('1.5'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.7%'),
        paddingRight: wp('1.5%'),
        // width: wp('25%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
      },
  selectedLabel: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: 10,
        padding: wp('1%'),
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        padding: wp('2%'),
        // paddingRight: wp('3%'),
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginRight: wp('1.5%'),
        marginBottom: wp('1.5%'),
      },
  labelTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  dateTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
      }
    : {
        height: hp('5%'),
        color: colors.white,
        fontSize: hp('2%'),
      },
  dateInput: isTabDevice()
    ? {
        padding: hp('1.5%'),
        width: '100%',
      }
    : {
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        width: '100%',
      },
  // createView: {
  //   flexDirection: 'column',
  //   alignItems: 'flex-end',
  //   paddingRight: 135,
  // },
  createView: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.green,
        height: wp('10%'),
        width: wp('45%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  disableButton: {
    backgroundColor: colors.grey,
  },
  changePasswordButton: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: colors.semiDarkBlue,
        marginLeft: wp('2%'),
        borderRadius: wp('1%'),
        padding: wp('1%'),
        width: wp('16%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: colors.semiDarkBlue,
        marginLeft: wp('2%'),
        borderRadius: wp('2%'),
        padding: wp('2%'),
        width: wp('38%'),
      },
  changePasswordText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  createTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  icon: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        // width: wp('6%'),
        marginLeft: wp('3%'),
      },
  dropdownView: isTabDevice()
    ? {
        width: wp('25.3%'),
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.5%'),
        height: hp('6%'),
      }
    : {
        width: wp('96%'),
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.5%'),
      },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        height: wp('4.8%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('8%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderColor: colors.tileBackground,
        marginTop: wp('1%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        // zIndex: 11,
        // width: wp('80%'),
        marginLeft: wp('4%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderColor: colors.tileBackground,
        marginTop: wp('-3%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        // zIndex: 11,
        // width: wp('80%'),
        marginLeft: wp('4%'),
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: hp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
      },
  dropdownTopArea: {
    ...(Platform.OS === 'android'
      ? {
          backgroundColor: colors.semiDarkBlue,
          borderColor: colors.transparent,
        }
      : {
          backgroundColor: colors.aqua,
          borderColor: colors.transparent,
          position: 'absolute',
          // zIndex: 5,
        }),
  },
  mandatoryField: {
    color: 'red',
    fontSize: 25,
  },
  requiredAstric: isTabDevice()
    ? {
        color: colors.red,
        fontWeight: 'bold',
        fontSize: hp('0.7%'),
      }
    : {
        color: colors.red,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
      },
  inputFieldWrapper: isTabDevice()
    ? {
        width: '45%',
      }
    : {
        width: '100%',
        marginBottom: wp('2%'),
      },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: hp('2.3%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      },
  inputField: {
    width: '100%',
    height: hp('5%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  commentFieldWrapper: isTabDevice()
    ? {
        width: '100%',
      }
    : {
        width: '100%',
        marginBottom: wp('2%'),
      },
  commentField: {
    width: '100%',
    height: hp('12%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
   
  },
  scroll: isTabDevice()
    ? {
        height: wp('40%'),
      }
    : {
        height: '100%',
        // backgroundColor: colors.red,
        marginBottom: wp('80%'),
      },
  keyboardScroll: isTabDevice()
    ? {}
    : {
        height: hp('120%'),
      },
  autoCompleteInput: isTabDevice()
    ? {
        color: colors.white,
        height: hp('6%'),
        width: wp('25.5%'),
        borderWidth: 0,
        paddingLeft: 10,
        position: 'relative',
        // zIndex: 100,
        fontSize: hp('2%'),
        // marginTop: 5,
      }
    : {
        color: colors.white,
        height: wp('10%'),
        width: wp('100%'),
        borderWidth: 0,
        paddingLeft: 10,
        position: 'relative',
        fontSize: wp('4%'),
      },
  autoCompleteInputContainer: isTabDevice()
    ? {
        borderWidth: 0,
        backgroundColor: colors.tileBackground,
        paddingLeft: wp('0.8%'),
        borderRadius: wp('0.7%'),
        width: wp('25%'),
        height: hp('6%'),
      }
    : {
        // zIndex: 10,
        borderWidth: 0,
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.7%'),
        width: wp('96%'),
        height: wp('10%'),
      },
  autoCompletelistContainerStyle: isTabDevice()
    ? {
        borderRadius: wp('1.5%'),
        height: wp('15%'),
        width: wp('25%'),
        top: 0,
        position: 'relative',
        left: 0,
        // zIndex: 2,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        height: wp('10%'),
        width: '100%',
        position: 'absolute',
        left: 0,
        // zIndex: 2,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        fontSize: wp('3.5%'),
      },
  autoCompleteList: isTabDevice()
    ? {
        borderWidth: 0,
        backgroundColor: colors.borderBlue,
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 20,
        left: wp('-1%'),
        top: 0,
        maxHeight: wp('30%'),
        minHeight: wp('5%'),
        width: '100%',
        padding: 15,
      }
    : {
        borderWidth: 0,
        padding: wp('0.5%'),
        backgroundColor: colors.borderBlue,
        borderBottomLeftRadius: wp('2.5%'),
        borderBottomRightRadius: wp('2.5%'),
        position: 'absolute',
        zIndex: 20,
        left: wp('-2.5%'),
        top: 0,
        maxHeight: wp('30%'),
        minHeight: wp('5%'),
        width: '100%',
        padding: 10,
      },
  autoCompleteListItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        height: hp('5%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        height: wp('10%'),
      },
  autoCompleteText: isTabDevice()
    ? {
        height: hp('5%'),
        color: colors.white,
        fontSize: hp('2%'),
      }
    : {
        height: wp('7%'),
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  inputField: isTabDevice()
    ? {
        width: '50%',
        height: hp('6%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'absolute',
      }
    : {
        width: '100%',
        height: hp('5%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },
  loader: isTabDevice()
    ? {
        paddingLeft: 10,
        paddingRight: 10,
      }
    : {
        paddingLeft: 5,
        paddingRight: 5,
      },
  errorText: isTabDevice()
    ? {
        color: colors.red,
        marginBottom: wp('1%'),
        // width: wp('30%'),
        fontSize: hp('1.5%'),
        textAlign: 'right',
      }
    : {
        color: colors.red,
        marginBottom: wp('1%'),
        // width: wp('100%'),
        fontSize: wp('3%'),
        textAlign: 'center',
      },
  errorTextWrapper: {
    width: '50%',
  },
  errorMessage2Wrapper: isTabDevice()
    ? {
        marginLeft: wp('2%'),
      }
    : {
        marginLeft: wp('2%'),
      },
  errorText2: isTabDevice()
    ? {
        color: colors.red,
        marginTop: wp('1%'),
        width: wp('30%'),
        fontSize: wp('1.2%'),
        textAlign: 'left',
      }
    : {
        color: colors.red,
        marginTop: wp('1%'),
        width: wp('100%'),
        fontSize: wp('2%'),
      },
  createButton: {
    display: 'flex',
    alignItems: !isTabDevice() ? 'center' : 'flex-end',
  },
  createButtonWrapper: isTabDevice()
    ? {
        // zIndex:20,
        marginBottom:wp('3%'),
        width: wp('20'),
        position: 'relative',
        left: wp('50'),
      }
    : {
        // marginTop: wp('10%'),
        // zIndex:5,
        width: wp('20'),
        position: 'relative',
        left: wp('36.5'),
      },
  picker: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        color: 'white',
      }
    : {
        backgroundColor: colors.borderBlue,
        color: 'white',
        height: wp('30%'),
      },
  selectedTeams: isTabDevice()
    ? {
        maxHeight: wp('10%'),
      }
    : {
        maxHeight: wp('25%'),
      },
  selectedTeams2: isTabDevice()
    ? {
        maxHeight: wp('10%'),
      }
    : {
        maxHeight: wp('25%'),
      },
  checkBoxSection: isTabDevice()
    ? {
        flexDirection: 'row',
        marginBottom: wp('2%'),
      }
    : {
        flexDirection: 'row',
        marginLeft: wp('2%'),
        marginBottom: wp('4%'),
        marginTop: wp('2%'),
      },
  checkBox: isTabDevice()
    ? {
        borderRadius: 5,
        width: wp('2%'),
        height: wp('2%'),
      }
    : {
        borderRadius: 3,
        width: wp('3%'),
        height: wp('3%'),
      },
  checkBoxText: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginLeft: wp('1%'),
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginLeft: wp('2%'),
        marginTop: wp('-0.5%'),
      },
});
export default addUserStyle;
