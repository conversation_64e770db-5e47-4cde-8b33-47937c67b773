import { AntDesign, Ionicons } from '@expo/vector-icons';
import React, {
  Fragment,
  useCallback,
  useEffect,
  useState,
  useRef,
} from 'react';
import { FlatList, ScrollView, Text, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import uuid from 'react-native-uuid';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import MatchPlanNotReadyModal from '../../components/modal/MatchPlanNotReadyModal/MatchPlanNotReadyModal';
import ModalWrapper from '../../components/modal/ModalWrapper/ModalWrapper';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import { isTabDevice } from '../../config/appConfig';
import {
  MatchLogActions,
  MatchLogStages,
  MatchLogText,
} from '../../constants/constants';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import { addLeadingZeros, dateTimeConversion } from '../../helpers';
import { getRecentFutureDateKey } from '../../helpers/DateHelper';
import useApi from '../../hooks/useApi';
import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';
import {
  GO_TO_LATEST_ONGOING_MATCH,
  MATCH_LOG_ACTIVITIES_FAILED,
  MATCH_LOG_ACTIVITIES_REQUEST,
  MATCH_LOG_ACTIVITIES_SUCCESS,
  MATCH_LOG_ADD_NEW_ACTION,
  MATCH_LOG_CONCLUDED_EVENTS_FAILED,
  MATCH_LOG_CONCLUDED_EVENTS_REQUEST,
  MATCH_LOG_CONCLUDED_EVENTS_SUCCESS,
  MATCH_LOG_EVENTS_FAILED,
  MATCH_LOG_EVENTS_REQUEST,
  MATCH_LOG_EVENTS_SUCCESS,
  MATCH_LOG_GET_SCORE_FAILED,
  MATCH_LOG_GET_SCORE_REQUEST,
  MATCH_LOG_GET_SCORE_SUCCESS,
  MATCH_LOG_MORE_ACTIVITIES_FAILED,
  MATCH_LOG_MORE_ACTIVITIES_REQUEST,
  MATCH_LOG_MORE_ACTIVITIES_SUCCESS,
  MATCH_LOG_MORE_EVENTS_FAILED,
  MATCH_LOG_MORE_EVENTS_REQUEST,
  MATCH_LOG_MORE_EVENTS_SUCCESS,
  MATCH_LOG_OPPONENT_DETAILS_FAILED,
  MATCH_LOG_OPPONENT_DETAILS_REQUEST,
  MATCH_LOG_OPPONENT_DETAILS_SUCCESS,
  MATCH_LOG_SET_SELECTED_DATE,
  MATCH_LOG_SET_SELECTED_EVENT,
  MATCH_LOG_SET_SELECTED_TEAM,
  SET_ONGOING_MATCH,
  RESET_SELECTED_DATE_MATCH_LOG,
  FETCH_ONGOING_MATCH_SUCCESS,
  CLEAR_MATCH_SOCKET_MESSAGES,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import { REMOVE_SELECTED_MATCH_PLAN } from '../../store/actionTypes/MatchPlan/MatchPlanActions';
import AttendanceContainer from '../AttendanceContainer/AttendanceContainer';
import PlayerRating from '../PlayerRatingContainer/PlayerRating';
import PlayerSummeryContainer from '../PlayerSummeryContainer/PlayerSummeryContainer';
import SocketMatchLogContainer from '../SocketMatchContainers/SocketMatchLogContainer';
import TeamContainer from '../TeamMatchesContainer/TeamContainer';
import MatchLogAPIHooks from './MatchLogAPIHook';
import customMatchLogContainerStyles from './MatchLogContainerStyles';
import {
  IS_USER_IN_MATCHES,
  IS_USER_IN_MATCH_LOG,
} from '../../store/actionTypes/common/commonActionTypes';

const MatchLogContainer = ({
  seconds,
  minutes,
  isRunning,
  startTimer,
  pauseTimer,
  resumeTimer,
  resetTimer,
  setTime,
  setGameStatus,
  setGameStage,
  saveLog,
  netInfo,
  halfTimeDurationMillis,
  refreshLoading,
  disableAction,
  getMatchActivities,
  hideLoading,
}) => {
  const { v4: uuidv4 } = uuid;
  const MatchLogContainerStyles = useStyles(customMatchLogContainerStyles);
  const colors = useColors();
  const [fetchData] = useApi();
  const [fetchActivities] = useApi();
  const [fetchEvents] = useApi();
  const [fetchConcludedEvents] = useApi();
  const [fetchScore] = useApi();
  const dispatch = useDispatch();
  const {
    matches = {},
    matchesLoading,
    matchesCurrentPage,
    moreMatchesLoading,
    selectedEvent,
    selectedEventIndex,
    selectedDate,
    activitiesPage,
    activitiesLoading,
    activities = [],
    eventLogs = {},
    selectedTeamId,
    gotoLatestOngoingMatch,
    latestOngoingMatch,
    isLoadingMatchActivity,
  } = useSelector(state => state?.matchLog);
  const { connectionIdMatchWs } = useSelector(state => state?.common);
  const {
    isGameOngoing,
    score,
    opponentDetails,
    matchPlan = {},
    currentStage,
    matchPlanLoading,
    playerList,
  } = eventLogs[selectedTeamId]?.[selectedEventIndex] || {};
  const { selectedTeam } = useSelector(state => state?.team);
  const { selectedMatchDetails } = useSelector(state => state.matchPlan);

  const today = new Date();
  const endDate = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate() + 14
  );
  const rangeStartDate = new Date(
    new Date().setDate(new Date().getDate() - 14)
  );

  const eventId = matchPlan?.eventId;
  const formationId = matchPlan?.formationId;

  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [showSummeryModal, setShowSummeryModal] = useState(false);

  const [showMatchPlanNotReady, setShowMatchPlanNotReady] = useState(false);
  const [isShowModal, setIsShowModal] = useState(false);

  const [groupedEvents, setGroupedEvents] = useState({});
  const scrollIndexRef = useRef(null);
  const [lengthOfChip, setLengthOfChip] = useState(0);
  const [playersSportProfileIds, setPlayersSportProfileIds] = useState([]);
  const { getAllMatchPlayers, getFormationData } = MatchLogAPIHooks();

  useEffect(() => {
    eventId && getAllMatchPlayers(eventId);
  }, [eventId]);

  useEffect(() => {
    formationId && getFormationData(formationId);
  }, [formationId]);

  useEffect(() => {
    getMatchActivity();
  }, []);
  const getMatchActivity = () => {
    !activitiesLoading &&
      fetchActivities(
        `/api/v1/match-activities?page=1&size=50`,
        MATCH_LOG_ACTIVITIES_REQUEST,
        MATCH_LOG_ACTIVITIES_SUCCESS,
        MATCH_LOG_ACTIVITIES_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
  };

  useEffect(() => {
    if (refreshLoading) {
      getMatchScore(selectedEvent);
      getMatchActivity();
    }
  }, [refreshLoading]);
  useEffect(() => {
    if (hideLoading) {
      getMatchScore(selectedEvent);
    }
  }, [hideLoading]);

  useEffect(() => {
    if (eventLogs) {
      const spotProfileIds = Object.keys(playerList || {});
      setPlayersSportProfileIds(spotProfileIds);
    }
  }, [eventLogs]);

  useEffect(() => {
    if (disableAction) {
      setShowAttendanceModal(false);
      setShowSummeryModal(false);
      setShowMatchPlanNotReady(false);
      setIsShowModal(false);
    }
  }, [disableAction]);

  useEffect(() => {
    activitiesPage &&
      !activitiesLoading &&
      fetchActivities(
        `/api/v1/match-activities?page=${activitiesPage + 1}&size=50`,
        MATCH_LOG_MORE_ACTIVITIES_REQUEST,
        MATCH_LOG_MORE_ACTIVITIES_SUCCESS,
        MATCH_LOG_MORE_ACTIVITIES_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
  }, [activitiesPage]);
  const clearSelectDate = () => {
    dispatch({
      type: RESET_SELECTED_DATE_MATCH_LOG,
    });
  };

  useEffect(() => {
    if (selectedTeam?._id) {
      clearSelectDate();
      setGroupedEvents({});
      dispatch({
        type: MATCH_LOG_SET_SELECTED_TEAM,
        payload: selectedTeam._id,
      });
      selectedTeamEventsFetch();
    }
  }, [selectedTeam]);

  const selectedTeamEventsFetch = () => {
    fetchEvents(
      `/api/v1/events?teamId=${
        selectedTeam._id
      }&type=match&startDate=${rangeStartDate.toISOString()}&endDate=${endDate.toISOString()}&page=1&size=31`,
      MATCH_LOG_EVENTS_REQUEST,
      MATCH_LOG_EVENTS_SUCCESS,
      MATCH_LOG_EVENTS_FAILED,
      null,
      '',
      'GET',
      null,
      EVENT_SERVICE
    );

    fetchConcludedEvents(
      `/api/v1/matches?teamId=${
        selectedTeam._id
      }&concludeStartDate=${rangeStartDate.toISOString()}&concludeEndDate=${today.toISOString()}&page=1&size=31`,
      MATCH_LOG_CONCLUDED_EVENTS_REQUEST,
      MATCH_LOG_CONCLUDED_EVENTS_SUCCESS,
      MATCH_LOG_CONCLUDED_EVENTS_FAILED,
      null,
      null,
      'GET',
      null,
      EVENT_SERVICE,
      selectedTeam._id
    );
  };

  useEffect(() => {
    if (
      Object.keys(groupedEvents)?.length &&
      selectedDate &&
      !matchPlanLoading &&
      !matchesLoading
    ) {
      const length = Object.keys(groupedEvents)?.length;
      const index = Object.keys(groupedEvents)?.indexOf(selectedDate);
      scrollIndexRef && scrollToIndex(index, length);
    }
  }, [
    JSON.stringify(groupedEvents),
    lengthOfChip,
    matchPlanLoading,
    matchesLoading,
  ]);

  const scrollToIndex = (index, length) => {
    scrollIndexRef?.current?.scrollToIndex({
      index: index > length - 1 || index < 0 ? 0 : index || 0,
    });
  };
  useEffect(() => {
    return () => {
      clearSelectDate();
      setGroupedEvents({});
    };
  }, []);

  const loadMoreEvents = () => {
    if (selectedTeam?._id && !moreMatchesLoading) {
      const nextPage = Number(matchesCurrentPage) + 1;
      fetchData(
        `/api/v1/events?teamId=${
          selectedTeam._id
        }&type=match&startDate=${rangeStartDate.toISOString()}&endDate=${endDate.toISOString()}&page=${nextPage}&size=31`,
        MATCH_LOG_MORE_EVENTS_REQUEST,
        MATCH_LOG_MORE_EVENTS_SUCCESS,
        MATCH_LOG_MORE_EVENTS_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  };

  const setSelectedDate = date => {
    dispatch({
      type: MATCH_LOG_SET_SELECTED_DATE,
      payload: date,
    });
  };

  useEffect(() => {
    if (matches[selectedTeamId]) {
      let groupedMatches = {};
      matches[selectedTeamId].forEach(match => {
        const { monthString, dateReadable, dateString } = dateTimeConversion(
          match?.startTime
        );
        const key = `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
        if (groupedMatches[key]) {
          groupedMatches[key].push(match);
        } else {
          groupedMatches[key] = [match];
        }
      });
      setGroupedEvents(groupedMatches);

      if (Object.keys(groupedMatches)?.length && !matchesLoading) {
        const recentFutureDate = getRecentFutureDateKey(
          matches?.[selectedTeamId]?.map(({ startTime }) => startTime)
        );
        const isSameDate = Object.keys(groupedMatches)[0] === selectedDate;

        if (selectedMatchDetails) {
          const selectedEventObj = matches[selectedTeamId].find(
            event => selectedMatchDetails._id == event._id
          );

          if (selectedEventObj) {
            const { monthString, dateReadable, dateString } =
              dateTimeConversion(selectedEventObj?.startTime);
            const date = `${monthString.slice(
              0,
              3
            )} ${dateReadable}, ${dateString}`;
            setSelectedDate(date);
            return;
          }
        }

        if (gotoLatestOngoingMatch && latestOngoingMatch) {
          const { monthString, dateReadable, dateString } = dateTimeConversion(
            latestOngoingMatch?.startTime
          );
          const date = `${monthString.slice(
            0,
            3
          )} ${dateReadable}, ${dateString}`;
          if (Object.keys(groupedMatches).includes(date)) {
            setSelectedDate(date);
          } else {
            !isSameDate &&
              setSelectedDate(
                recentFutureDate || Object.keys(groupedMatches)[0]
              );
          }
        } else if (Object.keys(groupedMatches).includes(selectedDate)) {
          setSelectedDate(selectedDate);
        } else {
          !isSameDate &&
            setSelectedDate(recentFutureDate || Object.keys(groupedMatches)[0]);
        }
      }
    }
  }, [matches]);

  useEffect(() => {
    if (selectedEvent?.concluded) {
      setGameStage(MatchLogStages.GAME_CONCLUDED);
    }

    if (selectedEvent?.opponentId) {
      fetchData(
        `/api/v1/opponents?opponentIds=${selectedEvent.opponentId}&page=1&size=1`,
        MATCH_LOG_OPPONENT_DETAILS_REQUEST,
        MATCH_LOG_OPPONENT_DETAILS_SUCCESS,
        MATCH_LOG_OPPONENT_DETAILS_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    }
    getMatchScore(selectedEvent);
  }, [selectedEvent]);

  const getMatchScore = selectedEvent => {
    selectedEvent?._id &&
      fetchScore(
        `/api/v1/matches/${selectedEvent._id}/match-scorecard`,
        MATCH_LOG_GET_SCORE_REQUEST,
        MATCH_LOG_GET_SCORE_SUCCESS,
        MATCH_LOG_GET_SCORE_FAILED,
        null,
        null,
        'GET',
        null,
        EVENT_SERVICE
      );
  };

  const isGameCanbeStarted = () => {
    if (!selectedEvent?.startTime) {
      return false;
    }
    const now = new Date();
    const start = new Date(selectedEvent.startTime);

    //if game scheduled time has already passed
    if (now - start >= 0) {
      return true;
    }
    //if game is scheduled within next 30 minutes
    if (start - now <= 1000 * 60 * 30) {
      return true;
    }
    return false;
  };

  const selectEvent = event => {
    dispatch({
      type: MATCH_LOG_SET_SELECTED_EVENT,
      payload: event,
    });
  };
  useEffect(() => {
    const updatedEventDetails =
      groupedEvents?.[selectedDate]?.find(
        event => selectedEvent?._id === event?._id
      ) || null;
    updatedEventDetails && selectEvent(updatedEventDetails);
  }, [JSON.stringify(groupedEvents), refreshLoading]);

  const getAction = action => {
    const activity = activities.find(activity => activity.code === action);
    return activity;
  };

  const addToLog = (data, msgObject = null) => {
    dispatch({
      type: MATCH_LOG_ADD_NEW_ACTION,
      payload: data,
    });
    if (!msgObject) {
      const customDataWithConnectionId = {
        performedByConnectionId: connectionIdMatchWs,
        ...data,
      };
      saveLog(customDataWithConnectionId);
    } else {
      dispatch({
        type: CLEAR_MATCH_SOCKET_MESSAGES,
      });
    }
  };

  const getElapsedTime = () => {
    return `${addLeadingZeros(minutes)}:${addLeadingZeros(seconds)} min`;
  };

  const millisecToMinutesAndSeconds = millis => {
    var minutes = Math.floor(millis / 60000);
    var seconds = ((millis % 60000) / 1000).toFixed(0);
    return `${addLeadingZeros(minutes)}:${addLeadingZeros(seconds)} min`;
  };

  const onActionButtonClicked = (
    code,
    addTimeDiff = false,
    msgObject = null
  ) => {
    if (!matchPlan?.isReady) {
      setShowMatchPlanNotReady(true);
      return;
    }

    const action = getAction(code);
    const timeStamp = new Date().toISOString();

    switch (code) {
      case MatchLogActions.START_GAME:
        // disabled the game start time validation for the demo
        // if (!isGameCanbeStarted()) {
        //   return;
        // }
        handleOngoingMatch();
        setTime(0);
        startTimer();
        setGameStatus(true);
        setGameStage(MatchLogStages.GAME_STARTED);
        break;
      case MatchLogActions.END_GAME:
        pauseTimer();
        setGameStatus(false);
        setGameStage(MatchLogStages.GAME_ENDED);
        !netInfo?.isInternetReachable &&
          dispatch({
            type: FETCH_ONGOING_MATCH_SUCCESS,
            payload: null,
          });
        break;
      case MatchLogActions.FIRST_HALF_END:
        pauseTimer();
        setGameStage(MatchLogStages.FIRST_HALF_ENDED);
        break;
      case MatchLogActions.SECOND_HALF_START:
        resumeTimer();
        setGameStage(MatchLogStages.SECOND_HALF_STARTED);
        break;
      default:
        break;
    }

    const isSecondHalfStarted = code === MatchLogActions.SECOND_HALF_START;

    const computedElapsedTime = `${
      isSecondHalfStarted
        ? millisecToMinutesAndSeconds(halfTimeDurationMillis)
        : getElapsedTime()
    } ${getTimeDifference(code)}`;

    let addData = {
      activityId: action?._id,
      code: code,
      comment: MatchLogText[code],
      timeStamp,
      elapsedTime: computedElapsedTime,
      type: action?.type,
      performedByOpponent: false,
      uuid: uuidv4(),
    };
    if (msgObject) {
      addData = {
        _id: msgObject._id,
        ...addData,
      };
    }

    addToLog(addData, msgObject);
  };

  const getTimeDifference = code => {
    const matchDuration = matchPlan?.duration;

    const elapsedTimeInSeconds = minutes * 60 + seconds;
    const matchDurationInSeconds = matchDuration * 60;
    const halfTimeInSeconds = halfTimeDurationMillis / 1000;

    if (!matchDuration) return '';
    let additionalTime = 0;
    if (
      code === MatchLogActions.FIRST_HALF_END &&
      elapsedTimeInSeconds > halfTimeInSeconds
    ) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - halfTimeInSeconds) / 60
      );
    } else if (
      code === MatchLogActions.END_GAME &&
      elapsedTimeInSeconds > matchDurationInSeconds
    ) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - matchDurationInSeconds) / 60
      );
    }
    return additionalTime > 0 ? ` ( +${additionalTime} min )` : '';
  };

  const onRemovedReducerSelectedMatchDetails = () => {
    dispatch({
      type: REMOVE_SELECTED_MATCH_PLAN,
    });
  };

  // const updateScore = (isOurs, isAdd) => {
  //   const code = MatchLogActions.ANONYMOUS_GOAL_SCORED;
  //   const codeOpponent = MatchLogActions.ANONYMOUS_GOAL_SCORED_OPPONENT;
  //   const action = getAction(code);
  //   const timestamp = new Date().toISOString();
  //   dispatch({
  //     type: MATCH_LOG_UPDATE_SCORE,
  //     payload: {
  //       isOurs,
  //       isAdd,
  //     },
  //   });

  //   addToLog({
  //     activityId: action?._id,
  //     code: code,
  //     comment: isOurs ? MatchLogText[code] : MatchLogText[codeOpponent],
  //     time: timestamp,
  //     elapsedTime: getElapsedTime(),
  //     type: action?.type,
  //     performedByOpponent: !isOurs,
  //   });
  // };

  // const Arrow = ({ isOurs, isAdd }) => {
  //   return (
  //     <TouchableOpacity
  //       style={[
  //         MatchLogContainerStyles.arrow,
  //         { opacity: isGameOngoing ? 1 : 0.5 },
  //       ]}
  //       onPress={() => updateScore(isOurs, isAdd)}
  //       disabled={!isGameOngoing}
  //     >
  //       <MaterialIcons
  //         name={`arrow-drop-${isAdd ? 'up' : 'down'}`}
  //         size={40}
  //         color={colors.white}
  //       />
  //     </TouchableOpacity>
  //   );
  // };

  useEffect(() => {
    if (
      selectedDate &&
      groupedEvents &&
      groupedEvents[selectedDate] &&
      !matchesLoading
    ) {
      if (selectedMatchDetails) {
        const selectedEventObj = matches[selectedTeamId]?.find(
          event => selectedMatchDetails._id == event._id
        );
        if (selectedEventObj) {
          selectEvent(selectedEventObj);
          return;
        }
      }
      if (
        gotoLatestOngoingMatch &&
        latestOngoingMatch &&
        selectedTeamId === latestOngoingMatch?.team?._id
      ) {
        if (
          groupedEvents[selectedDate]
            .map(e => e?._id)
            .includes(latestOngoingMatch?._id)
        ) {
          selectEvent(latestOngoingMatch);
          dispatch({
            type: GO_TO_LATEST_ONGOING_MATCH,
            payload: false,
          });
        } else {
          selectEvent(groupedEvents[selectedDate][0]);
        }
      } else if (
        !selectedEvent ||
        !groupedEvents[selectedDate]
          .map(e => e?._id)
          .includes(selectedEvent?._id)
      ) {
        selectEvent(groupedEvents[selectedDate][0]);
      }
    }
  }, [selectedDate, selectedTeamId, groupedEvents]);

  const dateItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        onLayout={event => {
          const { width } = event.nativeEvent.layout;
          !lengthOfChip && setLengthOfChip(width);
        }}
        disabled={matchesLoading || matchPlanLoading || disableAction}
        onPress={() => [
          setSelectedDate(item),
          onRemovedReducerSelectedMatchDetails(),
        ]}
        style={[
          selectedDate === item
            ? MatchLogContainerStyles.matchCompMatchDate
            : MatchLogContainerStyles.matchCompMatchDateNextDay,
        ]}
      >
        <Text style={MatchLogContainerStyles.matchCompMatchDateText}>
          {item}
        </Text>
      </TouchableOpacity>
    );
  };

  const eventItem = ({ item }) => {
    const {
      hours12: eventHours,
      minutes: eventMinutes,
      amPm,
    } = dateTimeConversion(item?.startTime);
    return (
      <TouchableOpacity
        style={MatchLogContainerStyles.matchEventItemTouchable}
        onPress={() => selectEvent(item)}
        disabled={disableAction}
      >
        {selectedEvent?._id === item?._id && (
          <ColoredText color={colors.grey}> | </ColoredText>
        )}
        <ColoredText
          color={selectedEvent?._id === item?._id ? colors.green : colors.grey}
        >
          {eventHours}:{addLeadingZeros(eventMinutes)}
          {amPm}
        </ColoredText>
        {selectedEvent?._id === item?._id && (
          <ColoredText color={colors.white}>
            {opponentDetails?.name && `vs ${opponentDetails?.name}`}
          </ColoredText>
        )}
        {selectedEvent?._id === item?._id && (
          <ColoredText color={colors.grey}> | </ColoredText>
        )}
      </TouchableOpacity>
    );
  };

  const ColoredText = ({ children, color }) => {
    return (
      <Text style={[MatchLogContainerStyles.coloredText, { color: color }]}>
        {children}
      </Text>
    );
  };

  const ColoredButton = useCallback(
    ({ children, color, onPress, disabled, ...rest }) => {
      return (
        <TouchableOpacity
          style={{
            ...MatchLogContainerStyles.matchCompGameButton,
            backgroundColor: color,
            opacity: disabled ? 0.5 : 1,
          }}
          onPress={onPress}
          disabled={disabled}
          {...rest}
        >
          <Text style={MatchLogContainerStyles.matchCompGameButtonText}>
            {children}
          </Text>
        </TouchableOpacity>
      );
    },
    [isGameOngoing, currentStage, disableAction, isLoadingMatchActivity]
  );

  const handleOngoingMatch = () => {
    dispatch({
      type: SET_ONGOING_MATCH,
    });
  };
  const handleSocketMessage = (msgObject, code) => {
    onActionButtonClicked(code, false, msgObject);
  };
  return (
    <View style={MatchLogContainerStyles.container}>
      <SocketMatchLogContainer
        onMainActionClicked={handleSocketMessage}
        matchId={selectedEvent?._id}
        onRefreshHandler={getMatchActivities}
      />

      {isTabDevice() && (
        <View style={MatchLogContainerStyles.leftView}>
          <ScrollView nestedScrollEnabled>
            <TeamContainer />
          </ScrollView>
        </View>
      )}
      <View style={MatchLogContainerStyles.rightView}>
        <View style={MatchLogContainerStyles.rightViewWrapper}>
          <Fragment>
            <View
              style={MatchLogContainerStyles.matchCompMatchDateFlatListWrapper}
            >
              <FlatList
                ref={scrollIndexRef}
                data={Object.keys(groupedEvents)}
                renderItem={dateItem}
                keyExtractor={(item, index) => index.toString()}
                getItemLayout={(data, index) => ({
                  length: lengthOfChip,
                  offset: lengthOfChip * index,
                  index,
                })}
                horizontal
                onEndReached={loadMoreEvents}
                style={MatchLogContainerStyles.matchCompMatchDateWrapper}
              />
            </View>
          </Fragment>
          {matchesLoading || matchPlanLoading ? (
            <ActivitySpinner isMatchLog />
          ) : matches[selectedTeamId]?.length ? (
            <Fragment>
              <View style={MatchLogContainerStyles.matchTimeDate}>
                {selectedEvent && (
                  <ColoredText color={colors.aquaBlue}>Time</ColoredText>
                )}
                {selectedEvent && (
                  <FlatList
                    data={groupedEvents[selectedDate]}
                    renderItem={eventItem}
                    horizontal
                  />
                )}
              </View>

              <View style={MatchLogContainerStyles.matchCompContainer}>
                <View style={MatchLogContainerStyles.matchCompScores}>
                  <View style={MatchLogContainerStyles.matchCompScoreRow}>
                    <View
                      style={{
                        ...MatchLogContainerStyles.matchCompScore,
                        marginLeft: 0,
                      }}
                    >
                      <Text
                        style={{
                          ...MatchLogContainerStyles.matchCompTeamName,
                          alignSelf: 'flex-end',
                          textAlign: 'center',
                        }}
                      >
                        {selectedTeam?.teamName || selectedTeam?.name}
                      </Text>
                      <View style={MatchLogContainerStyles.matchCompCounter}>
                        <View
                          style={MatchLogContainerStyles.matchCompCounterArrows}
                        >
                          {/* <Arrow isAdd isOurs />
                      <Arrow isAdd={false} isOurs /> */}
                        </View>
                        <View
                          style={
                            MatchLogContainerStyles.matchCompCounterScoreWrapper
                          }
                        >
                          <Text
                            style={
                              MatchLogContainerStyles.matchCompCounterScore
                            }
                          >
                            {score?.our}
                          </Text>
                        </View>
                      </View>
                    </View>

                    <View style={MatchLogContainerStyles.matchCompScoreCenter}>
                      <Text style={MatchLogContainerStyles.matchCompScoreVS}>
                        VS
                      </Text>
                    </View>

                    <View style={MatchLogContainerStyles.matchCompScore}>
                      <Text
                        style={{
                          ...MatchLogContainerStyles.matchCompTeamName,
                          alignSelf: 'flex-start',
                          textAlign: 'center',
                        }}
                      >
                        {opponentDetails?.name}
                      </Text>
                      <View style={MatchLogContainerStyles.matchCompCounter}>
                        <View
                          style={
                            MatchLogContainerStyles.matchCompCounterScoreWrapper
                          }
                        >
                          <Text
                            style={
                              MatchLogContainerStyles.matchCompCounterScore
                            }
                          >
                            {score?.opponent}
                          </Text>
                        </View>
                        <View
                          style={MatchLogContainerStyles.matchCompCounterArrows}
                        >
                          {/* <Arrow isOurs={false} isAdd />
                      <Arrow isOurs={false} isAdd={false} /> */}
                        </View>
                      </View>
                    </View>
                  </View>
                  <View style={MatchLogContainerStyles.matchCompTimer}>
                    <Text style={MatchLogContainerStyles.matchCompTimerText}>
                      {addLeadingZeros(minutes)}:{addLeadingZeros(seconds)}
                    </Text>
                  </View>
                </View>
                <View style={MatchLogContainerStyles.matchCompGameButtons}>
                  <ColoredButton
                    color={colors.green}
                    onPress={() =>
                      onActionButtonClicked(MatchLogActions.START_GAME)
                    }
                    disabled={
                      isGameOngoing ||
                      currentStage !== MatchLogStages.NOT_STARTED ||
                      disableAction ||
                      isLoadingMatchActivity
                    }
                  >
                    Start Game
                  </ColoredButton>
                  <ColoredButton
                    color={colors.darkBlue}
                    onPress={() =>
                      onActionButtonClicked(
                        MatchLogActions.FIRST_HALF_END,
                        true
                      )
                    }
                    disabled={
                      !isGameOngoing ||
                      currentStage !== MatchLogStages.GAME_STARTED ||
                      disableAction ||
                      isLoadingMatchActivity
                    }
                  >
                    1st Half End
                  </ColoredButton>
                  <ColoredButton
                    color={colors.darkBlue}
                    onPress={() =>
                      onActionButtonClicked(MatchLogActions.SECOND_HALF_START)
                    }
                    disabled={
                      !isGameOngoing ||
                      currentStage !== MatchLogStages.FIRST_HALF_ENDED ||
                      disableAction ||
                      isLoadingMatchActivity
                    }
                  >
                    2nd Half Start
                  </ColoredButton>
                  <ColoredButton
                    color={colors.red}
                    onPress={() =>
                      onActionButtonClicked(MatchLogActions.END_GAME, true)
                    }
                    disabled={
                      !isGameOngoing ||
                      currentStage !== MatchLogStages.SECOND_HALF_STARTED ||
                      disableAction ||
                      isLoadingMatchActivity
                    }
                  >
                    End Game
                  </ColoredButton>
                </View>
              </View>
              <View style={MatchLogContainerStyles.matchCompOptionsButtons}>
                <TouchableOpacity
                  onPress={() =>
                    matchPlan?.isReady
                      ? setShowSummeryModal(true)
                      : setShowMatchPlanNotReady(true)
                  }
                  disabled={disableAction}
                  style={{
                    ...MatchLogContainerStyles.matchCompOptionsButton,
                    backgroundColor: colors.tileBackground,
                    opacity: disableAction ? 0.5 : 1,
                  }}
                >
                  <Text
                    style={MatchLogContainerStyles.matchCompOptionsButtonText}
                  >
                    Summary
                  </Text>
                  <Ionicons
                    name="clipboard"
                    size={25}
                    color={colors.white}
                    style={MatchLogContainerStyles.matchCompOptionsButtonIcon}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() =>
                    matchPlan?.isReady
                      ? setShowAttendanceModal(true)
                      : setShowMatchPlanNotReady(true)
                  }
                  disabled={disableAction}
                  style={{
                    ...MatchLogContainerStyles.matchCompOptionsButton,
                    backgroundColor: colors.aquaBlue,
                    opacity: disableAction ? 0.5 : 1,
                  }}
                >
                  <Text
                    style={MatchLogContainerStyles.matchCompOptionsButtonText}
                  >
                    Attendance
                  </Text>
                  <Ionicons
                    name="people"
                    size={25}
                    color={colors.white}
                    style={MatchLogContainerStyles.matchCompOptionsButtonIcon}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    ...MatchLogContainerStyles.matchCompOptionsButton,
                    backgroundColor: colors.green,
                    opacity: disableAction ? 0.5 : 1,
                  }}
                  disabled={disableAction}
                  onPress={() =>
                    matchPlan?.isReady
                      ? setIsShowModal(true)
                      : setShowMatchPlanNotReady(true)
                  }
                >
                  <Text
                    style={MatchLogContainerStyles.matchCompOptionsButtonText}
                  >
                    Player Ratings
                  </Text>
                  <AntDesign
                    name="barchart"
                    size={25}
                    color={colors.white}
                    style={MatchLogContainerStyles.matchCompOptionsButtonIcon}
                  />
                </TouchableOpacity>
              </View>
              <View style={MatchLogContainerStyles.scrollOver}>
                <Text style={MatchLogContainerStyles.scrollOverText}>
                  Scroll down
                </Text>
                <AntDesign
                  name="arrowdown"
                  size={24}
                  color="black"
                  style={MatchLogContainerStyles.scrollOverArrow}
                />
              </View>
            </Fragment>
          ) : (
            <NoContentMessage message="No Content" />
          )}
        </View>
      </View>

      {showAttendanceModal && (
        <ModalWrapper visible>
          <AttendanceContainer
            selectedEvent={selectedEvent}
            setShowAttendanceModal={setShowAttendanceModal}
          />
        </ModalWrapper>
      )}
      {showSummeryModal && (
        <ModalWrapper visible>
          <PlayerSummeryContainer
            selectedEvent={selectedEvent}
            setShowSummeryModal={setShowSummeryModal}
          />
        </ModalWrapper>
      )}
      <MatchPlanNotReadyModal
        showModal={showMatchPlanNotReady}
        closeModal={() => setShowMatchPlanNotReady(false)}
      />
      {isShowModal && selectedEvent?._id && (
        <PlayerRating
          setIsShowModal={setIsShowModal}
          matchId={selectedEvent?._id}
        />
      )}
    </View>
  );
};

export default MatchLogContainer;
