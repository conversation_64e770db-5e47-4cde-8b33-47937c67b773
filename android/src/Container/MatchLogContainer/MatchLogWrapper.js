import { useNetInfo } from '@react-native-community/netinfo';
import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTimer } from 'react-compound-timer';
import { Text, View } from 'react-native';
import Swiper from 'react-native-swiper/src';
import { useDispatch, useSelector } from 'react-redux';
import ForceModal from '../../components/modal/ForceModal/ForceModal';
import {
  forcedModalType,
  MatchLogActions,
  onlineBarStatus,
} from '../../constants/constants';
import { EVENT_SERVICE } from '../../constants/services';
import { addLeadingZeros } from '../../helpers';
import useApi from '../../hooks/useApi';
import useApiPromise from '../../hooks/useApiPromise';
import useStyles from '../../hooks/useStyles';
import {
  MATCH_LOG_BULK_DELETE_FAILED,
  MATCH_LOG_BULK_DELETE_REQUEST,
  MATCH_LOG_BULK_DELETE_SUCCESS,
  MATCH_LOG_BULK_SAVE_FAILED,
  MATCH_LOG_BULK_SAVE_REQUEST,
  MATCH_LOG_BULK_SAVE_SUCCESS,
  MATCH_LOG_DELETE_ACTIVITY_FAILED,
  MATCH_LOG_DELETE_ACTIVITY_REQUEST,
  MATCH_LOG_DELETE_ACTIVITY_SUCCESS,
  MATCH_LOG_GET_LOG_FAILED,
  MATCH_LOG_GET_LOG_REQUEST,
  MATCH_LOG_GET_LOG_SUCCESS,
  MATCH_LOG_SAVE_ACTIVITY_FAILED,
  MATCH_LOG_SAVE_ACTIVITY_REQUEST,
  MATCH_LOG_SAVE_ACTIVITY_SUCCESS,
  MATCH_LOG_SET_CURRENT_STAGE,
  MATCH_LOG_SET_GAME_STATUS,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import HandleMatchWs from '../SocketMatchContainers/HandleMatchWs';
import HandleMatchFormation from '../SocketMatchContainers/HandleMatchFormation';
import MatchLogActionsContainer from './MatchLogActionsContainer';
import MatchLogAPIHooks from './MatchLogAPIHook';
import MatchLogContainer from './MatchLogContainer';
import customMatchLogContainerStyles from './MatchLogContainerStyles';
import { useJerseyNumberHook } from '../../hooks/PlannerAPIHook/useJerseyNumberHook';

const MatchLogWrapper = () => {
  const netInfo = useNetInfo();
  const navigation = useNavigation();
  const MatchLogOnlineStyle = useStyles(customMatchLogContainerStyles);
  const {
    value,
    controls: {
      start: timerStart,
      stop: timerStop,
      reset: timerReset,
      resume: timerResume,
      pause: timerPause,
      setTime,
      getTimerState,
      setTimeToUpdate,
    },
  } = useTimer({
    initialTime: 0,
    direction: 'forward',
    startImmediately: false,
    lastUnit: 'm',
    timeToUpdate: 1000,
    formatValue: value => addLeadingZeros(value),
  });

  const isInternetReachable = netInfo?.isInternetReachable || false;

  const isRunning = useMemo(
    () => getTimerState() === 'PLAYING',
    [getTimerState(), refreshLoading]
  );
  const dispatch = useDispatch();
  const [saveData] = useApiPromise();
  const [deleteData] = useApi();
  const [fetchData] = useApi();
  const [isOfflineAlert, setIsOfflineAlert] = useState(false);
  const [disableActions, setDisableActions] = useState(false);
  const [onlineBar, setOnlineBar] = useState(false);
  const {
    selectedEvent,
    eventLogs = [],
    selectedEventIndex,
    selectedDate,
    activities = [],
    selectedTeamId,
  } = useSelector(state => state.matchLog);
  const { isAppOnActive } = useSelector(state => state.common);
  const {
    unsyncedLogs = [],
    logsLoaded,
    matchLog,
    unsyncedDeletedLogs = [],
    matchPlan = {},
    matchLogActivitySaveFailed,
    matchLogActivitySaveFailedCount,
    matchLogActivityDeleteFailed,
    matchLogActivityDeleteFailedCount,
    logsLoading,
  } = eventLogs[selectedTeamId]?.[selectedEventIndex] || {};
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [jerseyNumbers, setJerseyNumbers] = useState([]);

  const { getAllMatchPlayers } = MatchLogAPIHooks();
  const [fetchJerseyNumbers] = useJerseyNumberHook();

  useEffect(() => {
    if (matchPlan?.formationId) {
      const matchPlanPlayersSportsProfileId =
        matchPlan?.playerCoordinates?.map(
          data => data?.playerData?.sportsProfileId
        ) || [];
      const matchPlanSubstitutesSportsProfileId =
        matchPlan?.substitutePlayers?.map(data => data?.sportsProfileId) || [];
      const allSportsProfileIds = [
        ...matchPlanPlayersSportsProfileId,
        ...matchPlanSubstitutesSportsProfileId,
      ].filter(sportsProfileId => !!sportsProfileId);

      if (allSportsProfileIds?.length) {
        setAllJerseyNumbers(allSportsProfileIds);
      }
    }
  }, [matchPlan?.formationId]);

  const setAllJerseyNumbers = async allSportsProfileIds => {
    const allJerseyNumbers = await fetchJerseyNumbers(
      allSportsProfileIds,
      selectedTeamId
    );
    setJerseyNumbers(allJerseyNumbers);
  };

  useEffect(() => {
    if (netInfo.isInternetReachable) {
      disableActions && setOnlineBar(true);
      setIsOfflineAlert(false);
      setDisableActions(false);
      syncAllData();
    } else {
      if (netInfo.isInternetReachable !== null) {
        setDisableActions(true);
        setTimeout(() => {
          setIsOfflineAlert(true);
        }, 100);
      }
    }
  }, [netInfo]);

  useEffect(() => {
    onlineBar &&
      setTimeout(() => {
        setOnlineBar(false);
      }, 3000);
  }, [onlineBar]);

  useEffect(() => {
    if (
      netInfo.isInternetReachable &&
      matchLogActivitySaveFailed &&
      matchLogActivitySaveFailedCount < 4
    ) {
      syncLogData();
    }
  }, [matchLogActivitySaveFailed, matchLogActivitySaveFailedCount]);
  useEffect(() => {
    if (
      netInfo.isInternetReachable &&
      matchLogActivityDeleteFailed &&
      matchLogActivityDeleteFailedCount < 4
    ) {
      //todo in offline feature
      // syncDeletedLogData();
    }
  }, [matchLogActivityDeleteFailed, matchLogActivityDeleteFailedCount]);

  const syncAllData = useCallback(() => {
    //todo in offline feature
    // syncLogData();
    // syncDeletedLogData();
  }, []);
  const getAction = action => {
    const activity = activities.find(activity => activity.code === action);
    return activity;
  };
  const getActionById = actionId => {
    const activity = activities.find(activity => activity._id === actionId);
    return activity;
  };

  const minToMiliSec = min => {
    return min * 60 * 1000;
  };

  const secToMiliSec = sec => {
    return sec * 1000;
  };

  const getLogItemFromActivityId = activityId => {
    return matchLog.find(l => l.activityId === activityId);
  };

  const halfTimeDurationMillis = useMemo(() => {
    const duration = matchPlan?.duration || 0;
    const halftimeInMinutes = duration / 2;

    return halftimeInMinutes * 60 * 1000;
  }, [matchPlan]);

  useEffect(() => {
    if (logsLoaded && matchLog?.length && activities?.length) {
      const codes = matchLog.map(i => getActionById(i.activityId)?.code);

      const actionEnd = getAction(MatchLogActions.END_GAME);
      const action2nd = getAction(MatchLogActions.SECOND_HALF_START);
      const action1st = getAction(MatchLogActions.FIRST_HALF_END);
      const actionStart = getAction(MatchLogActions.START_GAME);
      const actionSystemEnd = getAction(MatchLogActions.SYSTEM_END_GAME);
      if (codes.includes(MatchLogActions.END_GAME)) {
        const matchEnd = getLogItemFromActivityId(actionEnd._id);
        const secondHalf = getLogItemFromActivityId(action2nd._id);
        const firstHalf = getLogItemFromActivityId(action1st._id);
        const start = getLogItemFromActivityId(actionStart._id);

        const duration = matchPlan?.duration * 60 * 1000;

        let timerValue = 0;
        if (matchEnd && secondHalf && firstHalf && start) {
          const minNdSec = matchEnd?.elapsedTime.split(' ')[0].split(':');
          timerValue = minToMiliSec(minNdSec[0]) + secToMiliSec(minNdSec[1]);
        }
        timerValue = Math.min(timerValue, duration);
        setTime(timerValue);
      } else if (codes.includes(MatchLogActions.SYSTEM_END_GAME)) {
        const systemEnd = getLogItemFromActivityId(actionSystemEnd._id);
        const duration = matchPlan?.duration * 60 * 1000;
        let timerValue = 0;
        if (systemEnd) {
          const minNdSec = systemEnd?.elapsedTime.split(' ')[0].split(':');
          timerValue = minToMiliSec(minNdSec[0]) + secToMiliSec(minNdSec[1]);
        }
        timerValue = Math.min(timerValue, duration);
        setTime(timerValue);
      } else if (codes.includes(MatchLogActions.SECOND_HALF_START)) {
        const secondHalf = getLogItemFromActivityId(action2nd._id);
        const firstHalf = getLogItemFromActivityId(action1st._id);
        const start = getLogItemFromActivityId(actionStart._id);

        let timerValue = 0;
        if (secondHalf && firstHalf && start) {
          timerValue =
            Date.now() -
            new Date(secondHalf.timeStamp) +
            halfTimeDurationMillis;
        }
        setTime(timerValue);
      } else if (codes.includes(MatchLogActions.FIRST_HALF_END)) {
        setTime(halfTimeDurationMillis);
      } else if (codes.includes(MatchLogActions.START_GAME)) {
        const start = getLogItemFromActivityId(actionStart._id);
        let timerValue = 0;
        if (start) {
          timerValue = Date.now() - new Date(start.timeStamp);
        }
        setTime(timerValue);
      }
    } else if (logsLoaded) {
      setTime(0);
      timerPause();
    }
  }, [
    logsLoaded,
    matchLog,
    activities,
    halfTimeDurationMillis,
    refreshLoading,
  ]);

  const setGameStatus = useCallback(value => {
    dispatch({
      type: MATCH_LOG_SET_GAME_STATUS,
      payload: value,
    });
  }, []);

  const setGameStage = useCallback(stage => {
    dispatch({
      type: MATCH_LOG_SET_CURRENT_STAGE,
      payload: stage,
    });
  }, []);

  const saveLog = useCallback(
    async data => {
      const matchId = selectedEvent?._id;
      matchId &&
        (await saveData(
          `/api/v1/matches/${matchId}/match-activities`,
          MATCH_LOG_SAVE_ACTIVITY_REQUEST,
          MATCH_LOG_SAVE_ACTIVITY_SUCCESS,
          MATCH_LOG_SAVE_ACTIVITY_FAILED,
          data,
          null,
          'POST',
          false,
          EVENT_SERVICE,
          data
        ));
      if (data?.code == MatchLogActions.START_GAME) {
        await getAllMatchPlayers(matchId);
      }
    },
    [selectedEvent]
  );

  const saveBulkData = useCallback(
    data => {
      const matchId = selectedEvent?._id;
      if (data?.length && matchId) {
        saveData(
          `/api/v1/matches/${matchId}/match-activities/bulk`,
          MATCH_LOG_BULK_SAVE_REQUEST,
          MATCH_LOG_BULK_SAVE_SUCCESS,
          MATCH_LOG_BULK_SAVE_FAILED,
          data,
          null,
          'POST',
          false,
          EVENT_SERVICE,
          data
        );
      }
    },
    [selectedEvent]
  );

  const syncLogData = () => {
    //todo in offline feature
    //   saveBulkData(unsyncedLogs);
    // }
  };
  const syncDeletedLogData = () => {
    const matchId = selectedEvent?._id;
    if (unsyncedDeletedLogs?.length && matchId) {
      const deletedIds = unsyncedDeletedLogs.map(l => l._id);
      deleteData(
        `/api/v1/matches/${matchId}/match-activities/bulk`,
        MATCH_LOG_BULK_DELETE_REQUEST,
        MATCH_LOG_BULK_DELETE_SUCCESS,
        MATCH_LOG_BULK_DELETE_FAILED,
        deletedIds,
        null,
        'DELETE',
        false,
        EVENT_SERVICE,
        unsyncedDeletedLogs
      );
    }
  };

  const deleteActivity = useCallback(
    matchActivityId => {
      const matchId = selectedEvent?._id;
      matchId &&
        matchActivityId &&
        saveData(
          `/api/v1/matches/${matchId}/match-activities/${matchActivityId}`,
          MATCH_LOG_DELETE_ACTIVITY_REQUEST,
          MATCH_LOG_DELETE_ACTIVITY_SUCCESS,
          MATCH_LOG_DELETE_ACTIVITY_FAILED,
          null,
          null,
          'DELETE',
          false,
          EVENT_SERVICE,
          matchActivityId
        );
    },
    [selectedEvent]
  );

  const [hideLoading, setHideLoading] = useState(false);

  const fetchMatchActivitySummary = () => {
    if (selectedEvent?._id && selectedDate) {
      fetchMatchActivities(selectedEvent, selectedDate);
    }
  };

  const getMatchActivities = () => {
    setHideLoading(true);
    fetchMatchActivitySummary();
  };

  useEffect(() => {
    fetchMatchActivitySummary();
  }, [selectedEvent, selectedDate]);

  useEffect(() => {
    !logsLoading && setHideLoading(false);
  }, [logsLoading]);

  useEffect(() => {
    if (refreshLoading) {
      fetchMatchActivitySummary();
      setTimeout(() => {
        setRefreshLoading(false);
      }, 1000);
    }
  }, [refreshLoading]);

  useEffect(() => {
    isAppOnActive && setRefreshLoading(true);
  }, [isAppOnActive]);

  useEffect(() => {
    isInternetReachable && setRefreshLoading(true);
  }, [isInternetReachable]);

  const fetchMatchActivities = (selectedEvent, selectedDate) => {
    fetchData(
      `/api/v1/matches/${selectedEvent?._id}/match-activities?page=1&size=200`,
      MATCH_LOG_GET_LOG_REQUEST,
      MATCH_LOG_GET_LOG_SUCCESS,
      MATCH_LOG_GET_LOG_FAILED,
      null,
      null,
      'GET',
      null,
      EVENT_SERVICE,
      selectedEvent?._id
    );
  };
  useEffect(
    () =>
      navigation.addListener('beforeRemove', e => {
        if (netInfo.isInternetReachable) {
          return;
        }
        // when internet connection lost
        // Prevent default behavior of leaving the screen
        e.preventDefault();
      }),
    [navigation, netInfo]
  );

  return (
    <>
      <HandleMatchFormation />
      <HandleMatchWs matchId={selectedEvent?._id || ''} />
      {onlineBar && (
        <View style={MatchLogOnlineStyle.onlineBar}>
          <Text style={MatchLogOnlineStyle.onlineBarText}>
            {onlineBarStatus.ONLINE}
          </Text>
        </View>
      )}

      {isOfflineAlert && (
        <ForceModal
          type={forcedModalType.MATCH_PLANER_OFFLINE}
          onClose={() => {
            setIsOfflineAlert(false);
          }}
          goBack={() => setIsOfflineAlert(false)}
        />
      )}
      <Swiper
        showsButtons={false}
        showsPagination={false}
        horizontal={false}
        loop={false}
      >
        <MatchLogContainer
          seconds={value.s}
          minutes={value.m}
          isRunning={isRunning}
          startTimer={timerStart}
          pauseTimer={timerPause}
          resumeTimer={timerResume}
          resetTimer={timerReset}
          setGameStatus={setGameStatus}
          setGameStage={setGameStage}
          saveLog={saveLog}
          setTime={setTime}
          netInfo={netInfo}
          halfTimeDurationMillis={halfTimeDurationMillis}
          disableAction={disableActions}
          refreshLoading={refreshLoading}
          getMatchActivities={getMatchActivities}
          hideLoading={hideLoading}
        />
        <MatchLogActionsContainer
          seconds={value.s}
          minutes={value.m}
          isRunning={isRunning}
          startTimer={timerStart}
          pauseTimer={timerPause}
          resumeTimer={timerResume}
          resetTimer={timerReset}
          setGameStatus={setGameStatus}
          setGameStage={setGameStage}
          saveLog={saveLog}
          deleteActivity={deleteActivity}
          setTime={setTime}
          netInfo={netInfo}
          halfTimeDurationMillis={halfTimeDurationMillis}
          syncAllData={syncAllData}
          saveBulkData={saveBulkData}
          disableAction={disableActions}
          refreshLoading={refreshLoading}
          setRefreshLoading={setRefreshLoading}
          hideLoading={hideLoading}
          jerseyNumbers={jerseyNumbers}
        />
      </Swiper>
    </>
  );
};

export default MatchLogWrapper;
