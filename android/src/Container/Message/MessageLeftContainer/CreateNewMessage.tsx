import React, { FC } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Text,
  FlatList,
  Image,
} from 'react-native';
import { messageTypes, singleMessageTypes } from '../../../constants/constants';
import SingleUserMessageBox from '../../../components/Message/MeessageLeftComponents/SingleUserMessageBox';
import { createNewMessageUsersType } from '../../../store/reducers/Message/MessageReducer';
import customMessageStyles from '../MessageLeftContainer/MessageStyles';
import useStyles from '../../../hooks/useStyles';

type CreateNewMessageProps = {
  messageClicked: Function;
  users: createNewMessageUsersType[] | null;
  loadMoreUsers: Function;
};

const CreateNewMessage: FC<CreateNewMessageProps> = ({
  messageClicked,
  users,
  loadMoreUsers,
}) => {
  const renderItem = ({ item, index }: any) => (
    <SingleUserMessageBox
      item={{ ...item, img: item.profileImageUrl }}
      isSelectedPersonal={false}
      messageClicked={messageClicked}
    />
  );

  const MessageStyles = useStyles(customMessageStyles);

  return (
    <View style={MessageStyles.newMessageList}>
      <FlatList
        data={users}
        // data={[
        //   {
        //     name: 'john smith',
        //     img: '',
        //     type: singleMessageTypes.USER_CREATE_MESSAGE,
        //     lastMessage: 'you:kdshflkjsdfkj ldskjflsdjfljsd dfsdfsdfsdf',
        //     unReadMessage: 0,
        //   },
        //   {
        //     name: 'john pool',
        //     img: '',
        //     type: singleMessageTypes.USER_CREATE_MESSAGE,
        //     lastMessage: 'you:kdshflkjsdfkj ldskjflsdjfljsd dfsdfsdfsdf',
        //     unReadMessage: 0,
        //   },
        //   {
        //     name: 'camerron smith',
        //     img: '',
        //     type: singleMessageTypes.USER_CREATE_MESSAGE,
        //     lastMessage: 'you:kdshflkjsdfkj ldskjflsdjfljsd dfsdfsdfsdf',
        //     unReadMessage: 4,
        //   },
        // ]}
        // style={styles.scroll}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        onEndReached={() => loadMoreUsers()}
        // scrollEnabled={visibleItemCount < itemCount}
        // contentContainerStyle={{ ...styles.scrollContainer }}
      />
    </View>
  );
};

export default CreateNewMessage;
