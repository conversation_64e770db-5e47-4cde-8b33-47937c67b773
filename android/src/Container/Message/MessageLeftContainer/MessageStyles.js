import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MessageStyles = colors => ({
  leftViewContainer: {
    paddingRight: wp('2.5%'),
    height: hp('75%'),
  },
  createSelectButton: isTabDevice()
    ? {
        backgroundColor: colors.green,
        color: colors.white,
        textAlign: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        borderRadius: 10,
        fontSize: wp('1.5%'),
      }
    : {
        backgroundColor: colors.green,
        color: colors.white,
        textAlign: 'center',
        alignItems: 'center',
        padding: wp('3%'),
        borderRadius: 10,
        fontSize: wp('4%'),
      },
  messageList: isTabDevice()
    ? {
        height: hp('68%'),
        marginBottom: wp('2%'),
      }
    : {
        height: hp('60%'),
      },
  messageListPersonal: isTabDevice()
    ? {
        height: hp('57%'),
        marginBottom: wp('2%'),
      }
    : {
        height: hp('60%'),
      },
  newMessageList: {
    marginTop: wp('1%'),
    height: hp('58%'),
  },
  messageListWrapper: isTabDevice()
    ? {}
    : {
        height: hp('67%'),
      },
});

export default MessageStyles;
