import React, { FC, useEffect, useMemo, useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import CreateNewMessage from './CreateNewMessage';
import MessageList from './MessageList';
import MessageSearchAndTab from '../../../components/Message/MeessageLeftComponents/MessageSearchAndTab';
import customMessageStyles from '../MessageLeftContainer/MessageStyles';
import { useNavigation, useRoute } from '@react-navigation/native';
import useStyles from '../../../hooks/useStyles';
import useDeviceInfo from '../../../hooks/useDeviceInfo';
import {
  SET_SELECTED_MESSAGE_TYPE,
  GET_CREATE_NEW_MESSAGE_USERS_REQUEST,
  GET_CREATE_NEW_MESSAGE_USERS_SUCCESS,
  GET_CREATE_NEW_MESSAGE_USERS_FAIL,
  SELECTED_USER_FOR_MESSAGE,
  CHECK_CHAT_INITIATED_REQUEST,
  CHECK_CHAT_INITIATED_SUCCESS,
  CHECK_CHAT_INITIATED_FAIL,
  CREATE_CHAT_ROOM_ID_REQUEST,
  CREATE_CHAT_ROOM_ID_SUCCESS,
  CREATE_CHAT_ROOM_ID_FAIL,
  GET_PAST_MESSAGE_CHAT_INFO_REQUEST,
  GET_PAST_MESSAGE_CHAT_INFO_SUCCESS,
  GET_PAST_MESSAGE_CHAT_INFO_FAIL,
  GET_FAST_MESSAGE_CHAT_MEMBER_INFO_SUCCESS,
  GET_FAST_MESSAGE_CHAT_MEMBER_INFO_FAIL,
  GET_FAST_CHAT_LATEST_MESSAGE_REQUEST,
  GET_FAST_CHAT_LATEST_MESSAGE_SUCCESS,
  GET_FAST_CHAT_LATEST_MESSAGE_FAIL,
  SET_CHAT_ROOM_ID_FOR_SELECTED_MESSAGE,
  RESET_MESSAGE_TYPES,
  RESET_TO_INITIAL_STATE_MESSAGE,
  GET_UNREAD_MESSAGE_COUNT_REQUEST,
  GET_UNREAD_MESSAGE_COUNT_SUCCESS,
  GET_UNREAD_MESSAGE_COUNT_FAIL,
  CHECK_VALIDITY_OF_PERSONAL_CHAT_REQUEST,
  CHECK_VALIDITY_OF_PERSONAL_CHAT_SUCCESS,
  CHECK_VALIDITY_OF_PERSONAL_CHAT_FAIL,
  GET_PERSONAL_TAB_COUNT_REQUEST,
  GET_PERSONAL_TAB_COUNT_SUCCESS,
  GET_PERSONAL_TAB_COUNT_FAIL,
  GET_TEAMS_TAB_COUNT_REQUEST,
  GET_TEAMS_TAB_COUNT_SUCCESS,
  GET_TEAMS_TAB_COUNT_FAIL,
  SET_PREV_CHAT_STATE,
  SET_CURRENT_CHAT_STATE,
  RESET_SELECTED_NOTIFICATION,
  GET_TEAMS_CHAT_MEMBER_INFO_SUCCESS,
  PAST_MESSAGE_CHAT_MEMBER_INFO_RESET,
} from '../../../store/actionTypes/Message/MessageAction';
import { CREATE_NEW_MESSAGE_CLICK } from '../../../store/actionTypes/common/commonActionTypes';
import { RootStore } from '../../../store/store';
import useApi from '../../../hooks/useApi';
import {
  MESSAGING_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../../constants/services';
import useDebounce from '../../../hooks/useDebounce';
import { createNewMessageUsersType } from '../../../store/reducers/Message/MessageReducer';
import {
  messageTypes,
  ROUTE_PATH,
  WebSocketParams,
} from '../../../constants/constants';
import { ActivityIndicator } from 'react-native-paper';
import useApiPromiseArray from '../../../hooks/useApiPromiseArray';
import { isTabDevice } from '../../../config/appConfig';
type MessageLeftContainerProps = {};

const MessageLeft: FC<MessageLeftContainerProps> = () => {
  const MessageStyles = useStyles(customMessageStyles);

  const {
    selectedMessageType,
    createNewMessageUsers,
    createNewMessageUsersPage,
    createNewMessageUsersTotalRecords,
    createNewMessageUsersLoading,
    selectedMessageChatId,
    selectedMessageChatIdLoading,
    selectedMessageChatIdTotalRecords,
    selectedUsersForMessages,
    pastMessageChatInfo,
    pastMessageChatMemberInfo,
    pastMessageChatInfoTotalRecords,
    pastMessageChatInfoPage,
    pastChatLatestMessage,
    pastChatLatestMessageLoading,
    pastMessageChatMemberInfoLoading,
    pastMessageChatInfoLoading,
    unreadMessageCount,
    isScreenActive,
    tabCountPagePersonal,
    tabCountPageTeams,
    personalMessageCount,
    teamMessageCount,
    isChatValid,
    prevSelectedMessageChatId,
    prevSelectedUsersForMessages,
    prevIsChatValid,
    isNotificationSelected,
    notificationSelectedFormatedMessages,
  } = useSelector((state: RootStore) => state?.message);
  const { userData } = useSelector((state: RootStore) => state?.auth);
  const [searchKey, setSearchKey] = useState<string>('');
  const debouncedSearchText = useDebounce(searchKey, 500);
  const [disableFetchChatInfo, setDisableFetchChatInfo] =
    useState<boolean>(false);
  const dispatch = useDispatch();
  const [getCreateUserData] = useApi();
  const [checkChatInitiated] = useApi();
  const [createChatIdForMessage] = useApi();
  const [getPastMsgChatInfo] = useApi();
  const [getPastMsgChatMemberInfo] = useApiPromiseArray();
  const [getTabMessageCountPersonal] = useApi();
  const [getTabMessageCountTeams] = useApi();

  const [getPastChatLatestMessage] = useApiPromiseArray();
  const [loadUnreadMessageCount] = useApiPromiseArray();
  const [checkValidityOfPersonalMessage] = useApi();

  const [getTeamChatMemberInfo] = useApiPromiseArray();

  const [deviceInfo] = useDeviceInfo();

  const [isInitUnReadMessage, setIsInitUnReadMessage] = useState(true);
  const [isMobileDevice, setIsMobileDevice] = useState(false);
  const navigation = useNavigation();
  const [pastChatIdList, setPastChatIdList] = useState<string[] | null>();
  const [chatMemberIdList, setChatMemberIdList] = useState<any | null>();

  const route = useRoute();
  const params = route.params as any;


  useEffect(() => {
    if (params && params?.playerData) {
      setTimeout(() => {
        dispatch({
          type: SELECTED_USER_FOR_MESSAGE,
          payload: {
            customInput: {
              ...params?.playerData,
              img: params?.playerData?.profileImageUrl,
            },
          },
        });
        !isTabDevice() && navigation.navigate('MessagingChat');
      }, 200);
    }
  }, [params]);

  const { wsClient, wsIsConnect, isCreateNewMessage, currentRoute } =
    useSelector((state: RootStore) => state.common);

  const messageWsClient = wsClient?.[WebSocketParams.MESSAGE_WS];
  const fetchCreateUserData = (page: number) => {
    userData?.id &&
      getCreateUserData(
        `/api/v1/users/${userData?.id}/personal-chat-candidates?page=${page}&size=15&searchKey=${debouncedSearchText}`,
        GET_CREATE_NEW_MESSAGE_USERS_REQUEST,
        GET_CREATE_NEW_MESSAGE_USERS_SUCCESS,
        GET_CREATE_NEW_MESSAGE_USERS_FAIL,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
  };

  const fetchTabMessageCountsPersonal = (size: number) => {
    userData?.id &&
      getTabMessageCountPersonal(
        `/api/v1/chats?userId=${userData?.id}&type=${messageTypes.PERSONAL}&isUnread=true&page=1&size=${size}`,
        GET_PERSONAL_TAB_COUNT_REQUEST,
        GET_PERSONAL_TAB_COUNT_SUCCESS,
        GET_PERSONAL_TAB_COUNT_FAIL,
        null,
        '',
        'GET',
        false,
        MESSAGING_SERVICE
      );
  };
  const fetchTabMessageCountsTeams = (size: number) => {
    userData?.id &&
      getTabMessageCountTeams(
        `/api/v1/chats?userId=${userData?.id}&type=${messageTypes.TEAMS}&isUnread=true&page=1&size=${size}`,
        GET_TEAMS_TAB_COUNT_REQUEST,
        GET_TEAMS_TAB_COUNT_SUCCESS,
        GET_TEAMS_TAB_COUNT_FAIL,
        null,
        '',
        'GET',
        false,
        MESSAGING_SERVICE
      );
  };

  const getChatValidity = (opponentId: string) => {
    userData?.id &&
      checkValidityOfPersonalMessage(
        `/api/v1/users/${userData?.id}/personal-chat-candidates?generateImageUrl=false&oppositeCandidateId=${opponentId}`,
        CHECK_VALIDITY_OF_PERSONAL_CHAT_REQUEST,
        CHECK_VALIDITY_OF_PERSONAL_CHAT_SUCCESS,
        CHECK_VALIDITY_OF_PERSONAL_CHAT_FAIL,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
  };

  const isChatInitiatedForSelectedUsers = () => {
    checkChatInitiated(
      `/api/v1/chats?type=PERSONAL&membersUserIds=${userData?.id},${selectedUsersForMessages?.[0]?.id}`,
      CHECK_CHAT_INITIATED_REQUEST,
      CHECK_CHAT_INITIATED_SUCCESS,
      CHECK_CHAT_INITIATED_FAIL,
      null,
      '',
      'GET',
      false,
      MESSAGING_SERVICE,
      { id: selectedUsersForMessages?.[0]?.id }
    );
  };

  const onLoadUnreadMessageCount = async (
    chatIds: (string | null | undefined)[]
  ) => {
    if (chatIds?.length) {
      const chunkOfChatIdList = [];
      const chunkSize = 50;
      for (let i = 0; i < chatIds?.length; i += chunkSize) {
        const chunk = chatIds?.slice(i, i + chunkSize);
        chunkOfChatIdList.push(chunk);
      }
      const msgStatsUrls = [];
      for (let index = 0; index < chunkOfChatIdList.length; index++) {
        const element = chunkOfChatIdList[index];
        msgStatsUrls.push(
          `/api/v1/message-stats?userId=${userData?.id}&chatIds=${element}&type=UNREAD&size=${element.length}&page=1`
        );
      }
      try {
        dispatch({
          type: GET_UNREAD_MESSAGE_COUNT_REQUEST,
        });
        const values: any = await loadUnreadMessageCount(
          msgStatsUrls,
          'GET',
          MESSAGING_SERVICE
        );
        const messageStatsList = [
          ...values?.flatMap((res: any) => res?.data?.data),
        ]?.filter(memberInfo => memberInfo);
        messageStatsList?.length &&
          dispatch({
            type: GET_UNREAD_MESSAGE_COUNT_SUCCESS,
            payload: { data: messageStatsList?.length && messageStatsList },
          });
      } catch (error) {
        dispatch({
          type: GET_UNREAD_MESSAGE_COUNT_FAIL,
        });
      }
    }
  };

  const postChatIdForMessage = () => {
    if (userData?.id && selectedUsersForMessages?.[0]?.id) {
      const createData = {
        type: messageTypes.PERSONAL,
        memberUserIds: [userData.id, selectedUsersForMessages[0].id],
        creatorUserId: userData.id,
      };
      createChatIdForMessage(
        `/api/v1/chats`,
        CREATE_CHAT_ROOM_ID_REQUEST,
        CREATE_CHAT_ROOM_ID_SUCCESS,
        CREATE_CHAT_ROOM_ID_FAIL,
        createData,
        '',
        'POST',
        false,
        MESSAGING_SERVICE,
        { id: selectedUsersForMessages?.[0]?.id }
      );
    }
  };

  const fetchPastMsgChatInfo = (page: number) => {
    userData?.id &&
      getPastMsgChatInfo(
        `/api/v1/chats?page=${page}&size=10&type=${selectedMessageType}&userId=${userData?.id}`,
        GET_PAST_MESSAGE_CHAT_INFO_REQUEST,
        GET_PAST_MESSAGE_CHAT_INFO_SUCCESS,
        GET_PAST_MESSAGE_CHAT_INFO_FAIL,
        null,
        '',
        'GET',
        false,
        MESSAGING_SERVICE,
        { userId: userData?.id }
      );
  };

  const fetchPastMsgChatMemberInfo = async () => {
    if (chatMemberIdList?.length > 0) {
      const chunkOfChatMemberList = [];
      const chunkSize = 50;
      for (let i = 0; i < chatMemberIdList?.length; i += chunkSize) {
        const chunk = chatMemberIdList?.slice(i, i + chunkSize);
        chunkOfChatMemberList.push(chunk);
      }

      const personalChatMemberInfoUrls = [];
      for (let index = 0; index < chunkOfChatMemberList.length; index++) {
        const element = chunkOfChatMemberList[index];
        personalChatMemberInfoUrls.push(
          `/api/v1/users?page=1&size=${element?.length}&userIds=${element}`
        );
      }

      try {
        const values: any = await getPastMsgChatMemberInfo(
          personalChatMemberInfoUrls,
          'GET',
          USER_MANAGEMENT_SERVICE
        );
        const memberInfoList = [
          ...values?.flatMap((res: any) => res?.data?.data),
        ].filter(memberInfo => memberInfo);
        dispatch({
          type: GET_FAST_MESSAGE_CHAT_MEMBER_INFO_SUCCESS,
          payload: { data: memberInfoList?.length && memberInfoList },
        });
      } catch (error) {
        dispatch({
          type: GET_FAST_MESSAGE_CHAT_MEMBER_INFO_FAIL,
        });
      }
    }
  };

  const fetchPastChatLatestMessage = async () => {
    if (pastChatIdList?.length) {
      const chunkOfChatIdList = [];
      const chunkSize = 50;
      for (let i = 0; i < pastChatIdList?.length; i += chunkSize) {
        const chunk = pastChatIdList?.slice(i, i + chunkSize);
        chunkOfChatIdList.push(chunk);
      }

      const PastChatLatestMessageUrls = [];
      for (let index = 0; index < chunkOfChatIdList.length; index++) {
        const element = chunkOfChatIdList[index];
        PastChatLatestMessageUrls.push(
          `/api/v1/messages?page=1&size=${element?.length}&chatIds=${element}&latest=true`
        );
      }

      try {
        dispatch({
          type: GET_FAST_CHAT_LATEST_MESSAGE_REQUEST,
        });
        const values: any = await getPastChatLatestMessage(
          PastChatLatestMessageUrls,
          'GET',
          MESSAGING_SERVICE
        );
        const pastChatMessageInfoList = [
          ...values?.flatMap((res: any) => res?.data?.data),
        ]?.filter(messageInfo => messageInfo);
        pastChatMessageInfoList?.length &&
          dispatch({
            type: GET_FAST_CHAT_LATEST_MESSAGE_SUCCESS,
            payload: {
              data: pastChatMessageInfoList?.length && pastChatMessageInfoList,
            },
          });
      } catch (error) {
        dispatch({
          type: GET_FAST_CHAT_LATEST_MESSAGE_FAIL,
        });
      }
    }
  };

  const fetchTeamChatMemberInfo = async (
    chatId: any,
    memberListSize: number
  ) => {
    const PAGE_SIZE = 50;
    const numberOfPage =
      memberListSize > 0 ? Math.ceil(memberListSize / PAGE_SIZE) : 0;
    if (numberOfPage > 0) {
      const TeamChatMemberInfoUrls = [];
      for (let index = 0; index < numberOfPage; index++) {
        TeamChatMemberInfoUrls.push(
          `/api/v1/team-chats/${chatId}/members?page=${
            index + 1
          }&size=${PAGE_SIZE}`
        );
      }
      try {
        const values: any = await getTeamChatMemberInfo(
          TeamChatMemberInfoUrls,
          'GET',
          MESSAGING_SERVICE
        );
        const memberInfoList = [
          ...values?.flatMap((res: any) => res?.data?.data),
        ]?.filter(memberInfo => memberInfo);
        memberInfoList?.length &&
          dispatch({
            type: GET_TEAMS_CHAT_MEMBER_INFO_SUCCESS,
            payload: { data: memberInfoList?.length && memberInfoList },
          });
      } catch (error) {
        dispatch({
          type: GET_FAST_MESSAGE_CHAT_MEMBER_INFO_FAIL,
        });
      }
    }
  };

  const resetPastMessageChatMemberInfoList = () => {
    dispatch({
      type: PAST_MESSAGE_CHAT_MEMBER_INFO_RESET,
    });
  };

  const setSelectedUserForMessage = (value: any) => {
    dispatch({
      type: SELECTED_USER_FOR_MESSAGE,
      payload: { customInput: value },
    });
  };

  useEffect(() => {
    if (isCreateNewMessage) {
      setSelectedUserForMessage(null);
      !createNewMessageUsers?.length && fetchCreateUserData(1);
    }
  }, [isCreateNewMessage]);

  useEffect(() => {
    isCreateNewMessage && fetchCreateUserData(1);
  }, [debouncedSearchText]);

  useEffect(() => {
    if (selectedUsersForMessages?.length) {
      const currentSelectedUserChatDetails =
        pastMessageChatInfo?.find(
          data =>
            data?.opponentUserIds?.[0] === selectedUsersForMessages?.[0]?.id
        ) || {};

      getChatValidity(selectedUsersForMessages?.[0]?.id);
      !Object?.keys(currentSelectedUserChatDetails)?.length
        ? isChatInitiatedForSelectedUsers()
        : dispatch({
            type: SET_CHAT_ROOM_ID_FOR_SELECTED_MESSAGE,
            payload: {
              customInput: {
                id: selectedUsersForMessages?.[0]?.id,
                value: currentSelectedUserChatDetails,
              },
            },
          });
    }
  }, [JSON.stringify(selectedUsersForMessages)]);

  useEffect(() => {
    const selectedUserId = selectedUsersForMessages?.[0]?.id;

    selectedMessageChatId &&
      selectedUserId &&
      Object.keys(selectedMessageChatId || {})?.[0] === selectedUserId &&
      !selectedMessageChatIdTotalRecords &&
      postChatIdForMessage();
  }, [JSON.stringify(selectedMessageChatId)]);

  useEffect(() => {
    setIsMobileDevice(deviceInfo?.deviceType === 'phone');
  }, [JSON.stringify(deviceInfo)]);

  const customWsIsConnect = wsIsConnect?.[WebSocketParams.MESSAGE_WS];

  useEffect(() => {
    if (
      customWsIsConnect &&
      messageWsClient?.readyState === messageWsClient?.OPEN
    ) {
      messageWsClient?.send(
        JSON.stringify({
          action: 'getConnectionId',
        })
      );
    }
  }, [messageWsClient, customWsIsConnect]);

  useEffect(() => {
    fetchTabMessageCountsPersonal(tabCountPagePersonal);
  }, [tabCountPagePersonal]);
  useEffect(() => {
    fetchTabMessageCountsTeams(tabCountPageTeams);
  }, [tabCountPageTeams]);

  useEffect(() => {
    return () => {
      dispatch({ type: RESET_TO_INITIAL_STATE_MESSAGE });
    };
  }, []);

  const replaceCurrentChatWithPreviousChatState = () => {
    dispatch({
      type: SET_CURRENT_CHAT_STATE,
      payload: {
        prevSelectedMessageChatId,
        prevSelectedUsersForMessages,
        prevIsChatValid,
      },
    });
  };
  const replacePreviousChatWithCurrentChatState = () => {
    dispatch({
      type: SET_PREV_CHAT_STATE,
      payload: {
        selectedMessageChatId,
        selectedUsersForMessages,
        isChatValid,
      },
    });
  };

  useEffect(() => {
    if (isScreenActive) {
      if (prevIsChatValid) {
        setDisableFetchChatInfo(false);
        fetchPastMsgChatInfo(1);
        replaceCurrentChatWithPreviousChatState();
      }
    } else {
      replacePreviousChatWithCurrentChatState();
    }
  }, [isScreenActive]);

  useEffect(() => {
    dispatch({ type: RESET_MESSAGE_TYPES });
    setDisableFetchChatInfo(false);
    fetchPastMsgChatInfo(1);
  }, [selectedMessageType]);

  useEffect(() => {
    if (isNotificationSelected && notificationSelectedFormatedMessages) {
      if (selectedMessageType === messageTypes.PERSONAL) {
        setSelectedUserForMessage(notificationSelectedFormatedMessages);
      }
      if (selectedMessageType === messageTypes.TEAMS) {
        handleSelectedNavigation(notificationSelectedFormatedMessages);
      }
      dispatch({
        type: RESET_SELECTED_NOTIFICATION,
      });
      navigation.navigate('MessagingChat');
    }
  }, [selectedMessageType]);

  useEffect(() => {
    const isMobileValild =
      currentRoute == ROUTE_PATH.MESSAGING_CHAT ||
      currentRoute == ROUTE_PATH.MESSAGING;

    const isVaildRoute = isMobileDevice
      ? isMobileValild
      : currentRoute == ROUTE_PATH.MESSAGING;

    if (
      isVaildRoute &&
      isNotificationSelected &&
      notificationSelectedFormatedMessages
    ) {
      dispatch({ type: RESET_MESSAGE_TYPES });
      setDisableFetchChatInfo(false);
      fetchPastMsgChatInfo(1);
      if (selectedMessageType === messageTypes.PERSONAL) {
        setSelectedUserForMessage(notificationSelectedFormatedMessages);
      }
      if (selectedMessageType === messageTypes.TEAMS) {
        handleSelectedNavigation(notificationSelectedFormatedMessages);
      }
      dispatch({
        type: RESET_SELECTED_NOTIFICATION,
      });
      navigation.navigate('MessagingChat');
    }
  }, [
    isNotificationSelected,
    notificationSelectedFormatedMessages,
    currentRoute,
  ]);

  const handleSelectedNavigation = (selectedFormatedMessages: any) => {
    dispatch({
      type: SET_CHAT_ROOM_ID_FOR_SELECTED_MESSAGE,
      payload: {
        customInput: {
          id: selectedFormatedMessages?.id,
          value: selectedFormatedMessages?.value,
        },
      },
    });
  };

  useEffect(() => {
    if (isScreenActive) {
      // const tmpUnreadMessageCount = unreadMessageCount
      //   ? Object?.keys(unreadMessageCount)
      //   : [];
      const formatedChatIds =
        pastChatLatestMessage?.map(pastMessage => pastMessage?.chatId) || [];

      // const filterArrId =
      //   formatedChatIds?.filter(
      //     id => id && tmpUnreadMessageCount.indexOf(id) == -1
      //   ) || [];
      if (formatedChatIds?.length) {
        onLoadUnreadMessageCount(formatedChatIds);
      }
    }
  }, [JSON.stringify(pastChatLatestMessage), isScreenActive]);

  useEffect(() => {
    if (!disableFetchChatInfo && pastMessageChatInfo?.length) {
      const chatIds = pastMessageChatInfo?.map(data => data._id);
      const memberIds = pastMessageChatInfo
        ?.filter(data => data?.opponentUserIds !== null)
        ?.map(data => data?.opponentUserIds)
        ?.flat();
      const uniqueMemberIds = Array.from(new Set(memberIds));
      const uniqueChatIds = Array.from(new Set(chatIds));
      setPastChatIdList(uniqueChatIds);
      setChatMemberIdList(uniqueMemberIds);
      setDisableFetchChatInfo(true);
    }
  }, [JSON.stringify(pastMessageChatInfo)]);

  useEffect(() => {
    if (selectedMessageType === messageTypes.PERSONAL) {
      fetchPastMsgChatMemberInfo();
    }
  }, [chatMemberIdList]);

  useEffect(() => {
    fetchPastChatLatestMessage();
  }, [pastChatIdList]);

  const isLoading = useMemo(() => {
    return (
      (isCreateNewMessage
        ? !createNewMessageUsers?.length
        : !pastChatLatestMessage?.length) &&
      (pastChatLatestMessageLoading ||
        pastMessageChatMemberInfoLoading ||
        pastMessageChatInfoLoading ||
        createNewMessageUsersLoading)
    );
  }, [
    isCreateNewMessage,
    pastChatLatestMessage,
    pastChatLatestMessageLoading,
    pastMessageChatMemberInfoLoading,
    pastMessageChatInfoLoading,
    createNewMessageUsersLoading,
  ]);

  return isLoading ? (
    <ActivityIndicator color="#36d982" />
  ) : (
    <View style={MessageStyles.leftViewContainer}>
      <MessageSearchAndTab
        searchKey={searchKey}
        personalMessageCount={personalMessageCount}
        teamsMessageCount={teamMessageCount}
        setSearchKey={setSearchKey}
        selectedMessageType={selectedMessageType}
        isCreateNewMessage={isCreateNewMessage}
        setSelectedMessageType={(messageType: string) => {
          dispatch({
            type: SET_SELECTED_MESSAGE_TYPE,
            payload: { customInput: messageType },
          });
          resetPastMessageChatMemberInfoList();
        }}
      />
      {isCreateNewMessage ? (
        <>
          <Text style={{ color: '#36d982' }}>Select Contact</Text>
          <CreateNewMessage
            users={createNewMessageUsers}
            messageClicked={(item: createNewMessageUsersType) => {
              setSelectedUserForMessage(item);
              dispatch({
                type: CREATE_NEW_MESSAGE_CLICK,
                payload: { data: false },
              });
              isMobileDevice && navigation.navigate('MessagingChat');
            }}
            loadMoreUsers={() =>
              (createNewMessageUsers?.length || 0) <
                createNewMessageUsersTotalRecords &&
              fetchCreateUserData(createNewMessageUsersPage + 1)
            }
          />
        </>
      ) : (
        <View style={MessageStyles.messageListWrapper}>
          <MessageList
            messageClicked={(item: any) => {
              //need to check
              if (selectedMessageType === messageTypes.PERSONAL) {
                if (item.id !== selectedUsersForMessages?.[0]?.id) {
                  const currentSelectedUserDetails =
                    pastMessageChatMemberInfo?.find(
                      data => data?.id === item?.opponentUserIds?.[0]
                    );
                  setSelectedUserForMessage(currentSelectedUserDetails);
                }
              } else {
                if (item.id !== Object.keys(selectedMessageChatId || {})?.[0]) {
                  const currentSelectedChatDetails = pastMessageChatInfo?.find(
                    data => data?._id === item?._id
                  );
                  dispatch({
                    type: SET_CHAT_ROOM_ID_FOR_SELECTED_MESSAGE,
                    payload: {
                      customInput: {
                        id: item?._id,
                        value: currentSelectedChatDetails,
                      },
                    },
                  });
                }
                resetPastMessageChatMemberInfoList();
                fetchTeamChatMemberInfo(item?.id, item?.memberUserIds?.length);
              }

              isMobileDevice && navigation.navigate('MessagingChat');
            }}
            onEndReached={() => {
              if (
                pastMessageChatInfoTotalRecords >
                (pastMessageChatInfo?.length || 0)
              ) {
                setDisableFetchChatInfo(false);
                fetchPastMsgChatInfo(pastMessageChatInfoPage + 1);
              }
            }}
          />
          {selectedMessageType === messageTypes.PERSONAL && (
            <TouchableOpacity
              onPress={() => {
                dispatch({
                  type: CREATE_NEW_MESSAGE_CLICK,
                  payload: { data: true },
                });
              }}
            >
              <Text style={MessageStyles.createSelectButton}>
                Create New Message
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

export default MessageLeft;
