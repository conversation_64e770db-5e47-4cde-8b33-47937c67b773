import React, { FC, useState, useEffect } from 'react';
import uuid from 'react-native-uuid';
import { useDispatch, useSelector } from 'react-redux';
import MessageFooterComponent from '../../../../components/Message/MeessageRightComponents/MessageFooterComponent/MessageFooterComponent';
import { messageDataMapping } from '../../../../helpers/common';
import {
  IMPORT_LAST_MESSAGE,
  IMPORT_MESSAGES,
  IMPORT_RELEVANT_DETAILS_TO_INITIALIZE_CHAT,
  MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
} from '../../../../store/actionTypes/Message/MessageAction';
import {
  messageContentType,
  messageTypes,
  userRoleType,
  WebSocketParams,
} from '../../../../constants/constants';
import { singleMessageContentTypes } from '../../../../store/reducers/Message/MessageReducer';
import { RootStore } from '../../../../store/store';
import useGeneratedFileUrl from '../../../../hooks/useGeneratedFileUrl';

type MessageFooterProps = {
  isReplyMode: boolean;
  setIsReplyMode: Function;
  selectedMsgContent: any;
  setSelectedMsgContent: Function;
  renderGetUserSelectedMessage: Function;
  setIsAttached: Function;
};
const MessageFooterContainer: FC<MessageFooterProps> = props => {
  const {
    isReplyMode,
    selectedMsgContent,
    setIsReplyMode,
    setSelectedMsgContent,
    setIsAttached,
  } = props;
  const { wsClient } = useSelector((state: RootStore) => state.common);
  const { userData } = useSelector((state: RootStore) => state.auth);
  const {
    selectedMessageChatId,
    selectedUsersForMessages,
    selectedUsersMessageContent,
    selectedMessageType,
    pastMessageChatMemberInfo,
    isChatValid,
  } = useSelector((state: RootStore) => state.message);
  const messageWsClient = wsClient?.[WebSocketParams.MESSAGE_WS];

  const dispatch = useDispatch();
  const [msgContent, setMsgContent] = useState<string>('');
  const [isDisableSendMessage, setIsDisableSendMessage] =
    useState<boolean>(false);

  const [msgFile, setMsgFile] = useState<any>(null);
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();

  const [messageContent, setMessageContent] = useState<string>(
    messageContentType.TEXT
  );

  useEffect(() => {
    if (msgFile) {
      setIsAttached(true);
    } else {
      setIsAttached(false);
    }
  }, [msgFile]);

  useEffect(() => {
    if (msgFile?.fileType) {
      setMessageContent(msgFile?.fileType.toUpperCase());

      s3FileObject({
        fileKey: msgFile?.s3fileObject.fileKey,
        bucketName: msgFile?.s3fileObject.bucketName,
      });
    }
  }, [msgFile, msgContent]);

  const [mainSelectedChatId, setMainSelectedChatId] = useState<string | null>(
    null
  );

  useEffect(() => {
    let selectedChatId: string | null = null;
    let selectedMessageUniqueId: string | null = null;
    if (selectedMessageType === messageTypes.PERSONAL) {
      selectedMessageUniqueId = selectedUsersForMessages?.[0]?.id || null;
      if (selectedMessageUniqueId) {
        selectedChatId =
          selectedMessageChatId?.[selectedMessageUniqueId]?._id || null;
      }
    } else {
      selectedChatId = Object.keys(selectedMessageChatId || {})?.[0];
    }

    setMainSelectedChatId(selectedChatId);
  }, [selectedMessageType, selectedUsersForMessages, selectedMessageChatId]);

  const sendWebsocketMessage = () => {
    if (messageWsClient?.readyState === messageWsClient?.OPEN) {
      let selectedChatId: string | null = null;
      let selectedMessageUniqueId: string | null = null;
      if (selectedMessageType === messageTypes.PERSONAL) {
        selectedMessageUniqueId = selectedUsersForMessages?.[0]?.id || null;
        if (selectedMessageUniqueId) {
          selectedChatId =
            selectedMessageChatId?.[selectedMessageUniqueId]?._id || null;
        }
      } else {
        selectedChatId = Object.keys(selectedMessageChatId || {})?.[0];
        selectedMessageUniqueId = Object.keys(selectedMessageChatId || {})?.[0];
        dispatch({
          type: MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
          payload: {
            data: selectedChatId,
          },
        });
      }

      if (selectedMessageUniqueId) {
        const { v4: uuidv4 } = uuid;

        let msg: singleMessageContentTypes | null = {
          _id: uuidv4().toString(),
          chatId: selectedChatId,
          senderUserId: userData.id,
          type: messageContentType.TEXT,
          content: msgContent || '',
          sentDate: new Date(),
          isDeleted: false,
          isBroadcast: false,
          isForwarded: false,
          repliedForMessageId: isReplyMode ? selectedMsgContent?._id : '',
          chatType: '',
        };

        dispatch({
          type: IMPORT_LAST_MESSAGE,
          payload: {
            data: msg,
          },
        });

        if (msg && msgFile?.fileType) {
          const { s3fileObject = {}, fileType = '' } = msgFile;
          msg.type = messageContent;

          msg = { ...msg, [fileType]: { ...s3fileObject } };
        }

        //if user messageing for first time we need to add user details and chat details to reducer
        //if initials pastMessageChatMemberInfo doesn't contain send user details, We need to add user details and chat details to reducer
        if (
          selectedUsersMessageContent?.[selectedMessageUniqueId] === null ||
          checkInPastMessageChatMemberInfo(selectedMessageUniqueId)
        ) {
          dispatch({
            type: IMPORT_RELEVANT_DETAILS_TO_INITIALIZE_CHAT,
            payload: {
              data: {
                userDetails: selectedUsersForMessages?.[0],
                chatInfo: selectedMessageChatId?.[selectedMessageUniqueId],
              },
              customInput: userData.id,
            },
          });
        }

        dispatch({
          type: IMPORT_MESSAGES,
          payload: {
            data: messageDataMapping(userData, [msg]),
            customInput: selectedMessageUniqueId,
          },
        });

        const defaultMsg = {
          MessageGroupId: uuidv4(),
          action: 'send',
        };

        const repliedForMessageId = isReplyMode
          ? { repliedForMessageId: msg?.repliedForMessageId }
          : {};
        if (msg) {
          switch (messageContent) {
            case messageContentType.TEXT:
              if (msgContent.length) {
                const {
                  sentDate,
                  isDeleted,
                  isBroadcast,
                  isForwarded,
                  chatType,
                  ...rest
                } = msg;
                messageWsClient?.send(
                  JSON.stringify({
                    ...rest,
                    ...defaultMsg,
                    ...repliedForMessageId,
                  })
                );
              }
              setSelectedMsgContent({});
              break;
            case messageContentType.IMAGE:
            case messageContentType.VIDEO:
            case messageContentType.FILE:
              if (msgFile) {
                const { s3fileObject = {}, fileType = '' } = msgFile;
                const {
                  sentDate,
                  isDeleted,
                  isBroadcast,
                  isForwarded,
                  chatType,
                  ...rest
                } = msg;

                messageWsClient?.send(
                  JSON.stringify({
                    ...rest,

                    type: fileType.toUpperCase(),

                    ...defaultMsg,
                    ...repliedForMessageId,
                  })
                );
              }
              setSelectedMsgContent({});
              break;

            default:
              break;
          }
        }
        setMsgContent('');
        setMsgFile(null);
        setMessageContent(messageContentType.TEXT);
      }
    }
    setIsReplyMode(false);
  };

  useEffect(() => {
    if (selectedUsersForMessages?.length) {
      if (
        userData?.type === userRoleType.PLAYER &&
        selectedUsersForMessages[0]?.type === userRoleType.PLAYER
      ) {
        setIsDisableSendMessage(true);
      } else {
        setIsDisableSendMessage(false);
      }
    }
  }, [JSON.stringify(userData), JSON.stringify(selectedUsersForMessages)]);

  const checkInPastMessageChatMemberInfo = (selectedMessageUniqueId: any) => {
    if (selectedMessageType === messageTypes.PERSONAL) {
      const memberInfo = pastMessageChatMemberInfo?.find(
        data => data.id === selectedMessageUniqueId
      );
      return !memberInfo ? true : false;
    } else {
      return false;
    }
  };

  return (
    <>
      <MessageFooterComponent
        sendMsg={sendWebsocketMessage}
        isChatValid={
          selectedMessageType === messageTypes.PERSONAL ? isChatValid : true
        }
        setMsgContent={setMsgContent}
        msgContent={msgContent}
        disableSendMessage={isDisableSendMessage}
        pastMessageChatMemberInfo={pastMessageChatMemberInfo}
        setMsgFile={setMsgFile}
        msgFile={msgFile}
        mainSelectedChatId={mainSelectedChatId}
        {...props}
      />
    </>
  );
};

export default MessageFooterContainer;
