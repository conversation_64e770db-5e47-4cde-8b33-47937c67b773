import React, { FC, useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import MessageTopBarComponent from '../../../../components/Message/MeessageRightComponents/MessageTopBarComponent/MessageTopBarComponent';
import { messageTypes, userRoleType } from '../../../../constants/constants';
import { MESSAGING_SERVICE } from '../../../../constants/services';
import useApi from '../../../../hooks/useApi';
import {
  SET_TEAM_PROFILE_PICTURE_FAIL,
  SET_TEAM_PROFILE_PICTURE_REQUEST,
  SET_TEAM_PROFILE_PICTURE_SUCCESS,
} from '../../../../store/actionTypes/Message/MessageAction';
import { RootStore } from '../../../../store/store';
import ChatProfileModal from './../../../../components/modal/ChatProfileModal/ChatProfileModal';

type MessageTopBarProps = {};

const MessageTopBarContainer: FC<MessageTopBarProps> = ({}) => {
  const {
    selectedUsersForMessages,
    selectedMessageType,
    selectedMessageChatId,
    pastMessageChatMemberInfo,
  } = useSelector((state: RootStore) => state?.message);
  const { uploadPofileImageData, uploadPofileImageLoading } = useSelector(
    (state: RootStore) => state.addUser
  );
  const { userData } = useSelector((state: RootStore) => state?.auth);
  const isAllowtoClick =
    userRoleType.HEAD_COACH === userData?.type ||
    userRoleType.COACH === userData?.type;

  const [isImageUplading, setImageUploading] = useState<boolean>(false);

  const [chatProfileModalOpen, setChatProfileModalOpen] =
    useState<boolean>(false);

  const [setImageUpload] = useApi();

  const selectedTeamProfile = useMemo(() => {
    return selectedMessageChatId?.[
      Object.keys(selectedMessageChatId || {})?.[0]
    ];
  }, [JSON.stringify(selectedMessageChatId)]);

  useEffect(() => {
    if (!uploadPofileImageLoading && isImageUplading) {
      setImageUploading(false);
      let tmpUploadPofileImageData: any = uploadPofileImageData;
      let payload = {
        _id: Object.keys(selectedMessageChatId || {})?.[0],
        type: 'TEAM',
        image: {
          fileName: `Team Profile image`,
          ...tmpUploadPofileImageData,
        },
      };
      setImageUpload(
        '/api/v1/chats',
        SET_TEAM_PROFILE_PICTURE_REQUEST,
        SET_TEAM_PROFILE_PICTURE_SUCCESS,
        SET_TEAM_PROFILE_PICTURE_FAIL,
        payload,
        '',
        'PUT',
        undefined,
        MESSAGING_SERVICE
      );
    }
  }, [uploadPofileImageLoading]);

  return (
    <View>
      {chatProfileModalOpen && (
        <ChatProfileModal
          setChatProfileModalOpen={setChatProfileModalOpen}
          memberList={pastMessageChatMemberInfo || []}
          selectedTeamProfile={selectedTeamProfile}
          setImageUploading={setImageUploading}
          isImageUplading={isImageUplading}
          isAllowtoClick={isAllowtoClick}
          selectedTeamProfileId={selectedTeamProfile?._id || ''}
        />
      )}
      <MessageTopBarComponent
        data={
          selectedMessageType === messageTypes.PERSONAL
            ? selectedUsersForMessages?.[0] || null
            : selectedMessageChatId?.[
                Object.keys(selectedMessageChatId || {})?.[0]
              ]
        }
        setChatProfileModalOpen={setChatProfileModalOpen}
        selectedMessageType={selectedMessageType}
        selectedTeamProfile={selectedTeamProfile}
      />
    </View>
  );
};

export default MessageTopBarContainer;
