import React, { FC, useEffect, useMemo, useState } from 'react';
import { View, Text } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useDispatch, useSelector } from 'react-redux';
import NoContentMessage from '../../../components/NoContents/NoContentMessage';
import { isTabDevice } from '../../../config/appConfig';
import { messageTypes } from '../../../constants/constants';
import { MESSAGING_SERVICE } from '../../../constants/services';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';
import {
  FETCH_MESSAGE_FOR_USERS_FAIL,
  FETCH_MESSAGE_FOR_USERS_REQUEST,
  FETCH_MESSAGE_FOR_USERS_SUCCESS,
  SET_LAST_SEEN_MESSAGE_FAIL,
  SET_LAST_SEEN_MESSAGE_REQUEST,
  SET_LAST_SEEN_MESSAGE_SUCCESS,
  SET_UNREAD_MESSAGE_COUNT_SELECTED_RESET,
  FETCH_REPLIED_CONTENT_REQUEST,
  FETCH_REPLIED_CONTENT_SUCCESS,
  FETCH_REPLIED_CONTENT_FAIL,
  SET_RESET_SELECTED_MESSAGE_CHAT_ID,
  FETCH_MORE_MESSAGE_FOR_USERS_REQUEST,
  FETCH_MORE_MESSAGE_FOR_USERS_SUCCESS,
  FETCH_MORE_MESSAGE_FOR_USERS_FAIL,
} from '../../../store/actionTypes/Message/MessageAction';
import { RootStore } from '../../../store/store';
import customMessageStyles from '../MessageRightContainer/MessageRightStyles';
import MessageFooterContainer from './MessageFooterContainer/MessageFooter';
import MessageTopBarContainer from './MessageTopBarContainer/MessageTopBar';
import SingleMessageContainer from './SingleMessageContainer/SingleMessage';
import useApiPromiseArray from '../../../hooks/useApiPromiseArray';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { singleMessageContentTypes } from '../../../store/reducers/Message/MessageReducer';

type MessageRightContainerProps = {};

interface ILastSeenMessagePayload {
  chatId: string | null | undefined;
  userId: any;
  messageId: string;
}

const MessageRight: FC<MessageRightContainerProps> = ({}) => {
  const {
    selectedMessageChatId,
    selectedUsersForMessages,
    selectedMessageType,
    selectedUsersMessageContent,
    selectedUsersMessageContentPage,
    isScreenActive,
    pastMessageChatInfoLoading,
  } = useSelector((state: RootStore) => state?.message);

  const dispatch = useDispatch();
  const { userData } = useSelector((state: RootStore) => state.auth);
  const [fetchUserSelectedMessage] = useApi();
  const [getCreateUserData] = useApi();
  const [getRepliedContent] = useApiPromiseArray();
  const [setLastSeenMessage] = useApi();
  const [isUserSelected, setIsUserSelected] = useState(true);
  const [selectedMsgType, setSelectedMsgType] = useState(messageTypes.PERSONAL);
  const [isReplyMode, setIsReplyMode] = useState<boolean>(false);
  const [isAttached, setIsAttached] = useState<boolean>(false);
  const [selectedMsgContent, setSelectedMsgContent] = useState<Object>({});

  const [finalKeyboardHeight, setFinalKeyboardHeight] = useState(0);

  const replyMessageProps = {
    isReplyMode,
    setIsReplyMode,
    selectedMsgContent,
    setSelectedMsgContent,
    isAttached,
    setIsAttached,
  };

  const getUserSelectedMessage = (chatId: string) => {
    fetchUserSelectedMessage(
      `/api/v1/messages?chatIds=${chatId}&page=1&size=15`,
      FETCH_MESSAGE_FOR_USERS_REQUEST,
      FETCH_MESSAGE_FOR_USERS_SUCCESS,
      FETCH_MESSAGE_FOR_USERS_FAIL,
      null,
      '',
      'GET',
      false,
      MESSAGING_SERVICE,
      {
        id:
          selectedMessageType === messageTypes.PERSONAL
            ? selectedUsersForMessages?.[0]?.id
            : Object?.keys(selectedMessageChatId || {})?.[0],
        logInUser: userData,
      }
    );
  };

  const [mainChatId, setMainChatId] = useState<string | null>();

  const onEndReached = () => {
    console.log('onEndReached');

    fetchUserSelectedMessage(
      `/api/v1/messages?chatIds=${mainChatId}&page=${
        selectedUsersMessageContentPage + 1
      }&size=15`,
      FETCH_MORE_MESSAGE_FOR_USERS_REQUEST,
      FETCH_MORE_MESSAGE_FOR_USERS_SUCCESS,
      FETCH_MORE_MESSAGE_FOR_USERS_FAIL,
      null,
      '',
      'GET',
      false,
      MESSAGING_SERVICE,
      {
        id:
          selectedMessageType === messageTypes.PERSONAL
            ? selectedUsersForMessages?.[0]?.id
            : Object?.keys(selectedMessageChatId || {})?.[0],
        logInUser: userData,
      }
    );
  };

  const renderGetUserSelectedMessage = () => {
    if (selectedMessageType === messageTypes.PERSONAL) {
      const selectedUserId = selectedUsersForMessages?.[0]?.id;
      if (
        selectedUserId &&
        Object?.keys(selectedMessageChatId?.[selectedUserId] || {})?.length
      ) {
        const chatId = selectedMessageChatId?.[selectedUserId]?._id;
        if (chatId) {
          getUserSelectedMessage(chatId);
          setMainChatId(chatId);
        }
      }
    } else {
      const chatId = Object?.keys(selectedMessageChatId || {})?.[0];
      if (chatId) {
        getUserSelectedMessage(chatId);
        setMainChatId(chatId);
      }
    }
  };

  useEffect(() => {
    if (isScreenActive && !pastMessageChatInfoLoading) {
      renderGetUserSelectedMessage();
    }
  }, [
    JSON.stringify(selectedMessageChatId),
    isScreenActive,
    pastMessageChatInfoLoading,
  ]);

  const formatedMessageList = useMemo(() => {
    const selectedUserId = selectedUsersForMessages?.[0]?.id;
    let selectedchatId: string | undefined = Object?.keys(
      selectedMessageChatId || {}
    )?.[0];
    if (selectedMessageType === messageTypes.PERSONAL) {
      selectedchatId = selectedUserId
        ? selectedMessageChatId?.[selectedUserId]?._id
        : undefined;
    }
    let tmpMessageList = selectedchatId
      ? selectedUsersMessageContent?.[selectedchatId]
      : [];
    if (selectedMessageType === messageTypes.PERSONAL) {
      tmpMessageList = selectedUserId
        ? selectedUsersMessageContent?.[selectedUserId]
        : [];
    }
    return tmpMessageList;
  }, [JSON.stringify(selectedUsersMessageContent), selectedUsersForMessages]);

  useEffect(() => {
    formatedMessageList?.[0]?.chatId &&
      dispatch({
        type: SET_UNREAD_MESSAGE_COUNT_SELECTED_RESET,
        payload: {
          data: {
            chatId: formatedMessageList?.[0]?.chatId,
          },
        },
      });

    if (formatedMessageList?.length) {
      const lastMessage = formatedMessageList?.[0];
      if (lastMessage.senderUserId !== userData?.id) {
        let payload: ILastSeenMessagePayload = {
          chatId: formatedMessageList?.[0]?.chatId,
          userId: userData?.id,
          messageId: formatedMessageList?.[0]?._id,
        };
        onSetLastSeenMessage(payload);
      }
    }
  }, [JSON.stringify(formatedMessageList)]);

  useEffect(() => {
    return () => {
      dispatch({
        type: SET_RESET_SELECTED_MESSAGE_CHAT_ID,
      });
    };
  }, []);

  const onSetLastSeenMessage = (payload: ILastSeenMessagePayload) => {
    setLastSeenMessage(
      `/api/v1/last-seen-messages`,
      SET_LAST_SEEN_MESSAGE_REQUEST,
      SET_LAST_SEEN_MESSAGE_SUCCESS,
      SET_LAST_SEEN_MESSAGE_FAIL,
      payload,
      '',
      'PUT',
      false,
      MESSAGING_SERVICE,
      { ...payload }
    );
  };

  const fetchRepliedContent = async (messageIds: string[]) => {
    if (messageIds?.length) {
      const chunkOfMessageIds = [];
      const chunkSize = 50;
      for (let i = 0; i < messageIds?.length; i += chunkSize) {
        const chunk = messageIds?.slice(i, i + chunkSize);
        chunkOfMessageIds.push(chunk);
      }
      const repliedContentUrls = [];
      for (let index = 0; index < chunkOfMessageIds.length; index++) {
        const element = chunkOfMessageIds[index];
        repliedContentUrls.push(
          `/api/v1/messages?messageIds=${element}&page=1&size=${element?.length}`
        );
      }

      try {
        dispatch({
          type: FETCH_REPLIED_CONTENT_REQUEST,
        });
        const values: any = await getRepliedContent(
          repliedContentUrls,
          'GET',
          MESSAGING_SERVICE
        );
        const replyMsgList = [
          ...values?.flatMap((res: any) => res?.data?.data),
        ]?.filter(messageInfo => messageInfo);
        replyMsgList?.length &&
          dispatch({
            type: FETCH_REPLIED_CONTENT_SUCCESS,
            payload: { data: replyMsgList?.length && replyMsgList },
          });
      } catch (error) {
        dispatch({
          type: FETCH_REPLIED_CONTENT_FAIL,
        });
      }
    }
  };

  //filter selected chat messages which has been replied and call API to get replied Messages
  useEffect(() => {
    if (Object.keys(selectedUsersMessageContent || {})?.length) {
      const selectedChatMessages =
        selectedUsersMessageContent?.[
          Object.keys(selectedUsersMessageContent)[0]
        ];

      const filterRepliedMessages = selectedChatMessages
        ?.filter((item: singleMessageContentTypes) =>
          item.hasOwnProperty('repliedForMessageId')
        )
        .map((item: singleMessageContentTypes) => item?.repliedForMessageId)
        ?.filter((id: string) => !!id);

      filterRepliedMessages?.length &&
        fetchRepliedContent(filterRepliedMessages);
    }
  }, [selectedUsersMessageContent]);

  const MessageRightStyles = useStyles(customMessageStyles);

  useEffect(() => {
    setIsUserSelected(
      !!(
        selectedUsersForMessages?.length ||
        Object.keys(selectedMessageChatId || {})?.length
      )
    );
  }, [selectedUsersForMessages, selectedMessageChatId]);

  //reset reply mode every time a user selects another chat
  useEffect(() => {
    setIsReplyMode(false);
  }, [JSON.stringify(selectedMessageChatId)]);

  return (
    <View
      style={MessageRightStyles.rightMainContainer}
      onLayout={event => {
        var { height } = event.nativeEvent.layout;

        setFinalKeyboardHeight(height);
      }}
    >
      {isUserSelected ? (
        <>
          <MessageTopBarContainer />
          <View>
            <SingleMessageContainer
              selectedMsgType={selectedMsgType}
              setSelectedMsgType={setSelectedMsgType}
              getUserSelectedMessage={getUserSelectedMessage}
              onEndReached={onEndReached}
              renderGetUserSelectedMessage={renderGetUserSelectedMessage}
              {...replyMessageProps}
            />
          </View>
          <MessageFooterContainer
            {...replyMessageProps}
            renderGetUserSelectedMessage={renderGetUserSelectedMessage}
          />
        </>
      ) : (
        <NoContentMessage
          message={'No Message'}
          customWrapperStyle={MessageRightStyles.noMessage}
        />
      )}
    </View>
  );
};

export default MessageRight;
