import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const UserTypeStyles = colors => ({
  container: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('100%'),
      },
  TouchablePressed: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('8%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('4%'),
        width: wp('45.5%'),
        height: wp('13%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      },
  TouchableOpacity: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: wp('8%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('4%'),
        width: wp('45.5%'),
        height: wp('13%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      },
  TouchablePressedParent: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('8%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('4%'),
        width: wp('95%'),
        height: wp('13%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      },
  TouchableOpacityParent: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: wp('8%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('4%'),
        width: wp('95%'),
        height: wp('13%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      },
  Text: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.4%'),
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Medium',
      },
});
export default UserTypeStyles;
