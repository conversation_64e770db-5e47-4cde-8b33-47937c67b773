import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import customUserTypeStyles from './userTypeStyle';
import useStyles from '../../hooks/useStyles';

const UserType = ({ userCreationMode, handleUserCreation, isEditMode }) => {
  const UserTypeStyles = useStyles(customUserTypeStyles);
  const userTypes = ['Coach', 'Player', 'Parent'];

  const singleUserTypeStyle = userType => {
    if (userType === 'Parent') {
      return userType.toUpperCase() == userCreationMode
        ? UserTypeStyles.TouchablePressedParent
        : UserTypeStyles.TouchableOpacityParent;
    } else {
      return userType.toUpperCase() == userCreationMode
        ? UserTypeStyles.TouchablePressed
        : UserTypeStyles.TouchableOpacity;
    }
  };

  return (
    <View style={UserTypeStyles.container}>
      {userTypes.map((userType, index) => (
        <TouchableOpacity
          key={index}
          style={{ ...singleUserTypeStyle(userType) }}
          onPress={() => handleUserCreation(userType.toUpperCase())}
          disabled={isEditMode}
        >
          <Text key={index} style={UserTypeStyles.Text}>
            {userType}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default UserType;
