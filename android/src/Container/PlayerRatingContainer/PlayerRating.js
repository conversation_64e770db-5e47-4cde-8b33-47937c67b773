import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import PlayerRatingStyles from './PlayerRatingStyles';
import PlayerRatingModal from '../../components/modal/PlayerRatingModal/PlayerRatingModal';
import useApi from '../../hooks/useApi';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import useApiPromiseArray from '../../hooks/useApiPromiseArray';
import {
  MATCH_PLAYER_RATINGS_FAILED,
  MATCH_PLAYER_RATINGS_REQUEST,
  MATCH_PLAYER_RATINGS_RESET,
  MATCH_PLAYER_RATINGS_SUCCESS,
  SAVE_PLAYER_RATINGS_FAILED,
  SAVE_PLAYER_RATINGS_REQUEST,
  SAVE_PLAYER_RATINGS_RESET,
  SAVE_PLAYER_RATINGS_SUCCESS,
  GET_INITIAL_MATCH_PLAN_REQUEST,
  GET_INITIAL_MATCH_PLAN_SUCCESS,
  GET_INITIAL_MATCH_PLAN_FAILED,
  GET_LATEST_MATCH_PLAN_REQUEST,
  GET_LATEST_MATCH_PLAN_SUCCESS,
  GET_LATEST_MATCH_PLAN_FAILED,
  GET_INITIAL_RATINGS_REQUEST,
  GET_INITIAL_RATINGS_SUCCESS,
  GET_INITIAL_RATINGS_FAILED,
} from '../../store/actionTypes/MatchPlan/MatchPlanActions';

const PlayerRating = ({ setIsShowModal, matchId }) => {
  const [isError, setIsError] = useState(false);
  const [isIntializeRatings, setIsIntializeRatings] = useState(false);
  const [playersSportProfileIds, setPlayersSportProfileIds] = useState([]);
  const [getUsersForPlayerRatings] = useApiPromiseArray();
  const [fetchPlayerRatings] = useApiPromiseArray();
  const [savePlayerRatingsApi] = useApi();
  const [getInitialsMatchPlanApi] = useApi();
  const [getLatestMatchPlanApi] = useApi();

  const [getInitialPlayerRatingApi] = useApi();
  const [isInitialsPlayerRating, setIsInitialsPlayerRating] = useState(false);

  const dispatch = useDispatch();
  const {
    matchPlayerRatingsFetching,
    matchPlayerRatings,
    savePlayerRatingsSuccess,
    savePlayerRatingsLoading,
    savePlayerRatingsError,
    initialMatchPlan,
    matchPlayerRatingsTotalsRecord,
    isMatchPlanLoad,
    latestMatchPlan,
    initialsPlayerRating,
    isInitialsPlayerRatingLoad,
  } = useSelector(state => state.matchPlan);

  const PLAYER_RATING_SIZE = 20;

  useEffect(() => {
    getInitialsMatchPlan();
    return () => {
      dispatch({ type: MATCH_PLAYER_RATINGS_RESET });
    };
  }, []);

  useEffect(() => {
    if (playersSportProfileIds?.length) {
      getInitialPlayerRating();
    }
  }, [playersSportProfileIds]);

  useEffect(() => {
    if (initialsPlayerRating?.length) {
      matchPlayerRatingsTotalsRecord &&
        getPlayerRating(matchPlayerRatingsTotalsRecord);
    } else if (isInitialsPlayerRatingLoad) {
      setIsIntializeRatings(true);
      playersSportProfileIds?.length && getInvitees(playersSportProfileIds);
    }
  }, [initialsPlayerRating, isInitialsPlayerRatingLoad]);

  useEffect(() => {
    if (savePlayerRatingsSuccess) {
      setIsShowModal(false);
      setIsError(false);
      dispatch({
        type: SAVE_PLAYER_RATINGS_RESET,
      });
    }
  }, [savePlayerRatingsSuccess]);

  useEffect(() => {
    if (savePlayerRatingsError) {
      setIsError(true);
      dispatch({
        type: SAVE_PLAYER_RATINGS_RESET,
      });
    }
  }, [savePlayerRatingsError]);

  useEffect(() => {
    if (isMatchPlanLoad) {
      if (initialMatchPlan) {
        generateSportProfileIds(initialMatchPlan);
      } else {
        getLatestMatchPlan();
      }
    }
  }, [initialMatchPlan, isMatchPlanLoad]);

  useEffect(() => {
    if (latestMatchPlan) {
      latestMatchPlan && generateSportProfileIds(latestMatchPlan);
    }
  }, [latestMatchPlan]);

  const generateSportProfileIds = initialMatchPlan => {
    const sportProfileIdList = [];
    const substitutesSportProfileIds = initialMatchPlan.substitutePlayers
      ?.map(data => data.sportsProfileId)
      .filter(data => data);
    const playerSportProfileIds = initialMatchPlan.playerCoordinates
      ?.map(data => !!data.playerData && data.playerData.sportsProfileId)
      .filter(data => data);

    substitutesSportProfileIds?.length &&
      sportProfileIdList.push(...substitutesSportProfileIds);
    playerSportProfileIds?.length &&
      sportProfileIdList.push(...playerSportProfileIds);
    sportProfileIdList?.length && setPlayersSportProfileIds(sportProfileIdList);
  };

  const getInitialsMatchPlan = () => {
    getInitialsMatchPlanApi(
      `/api/v1/matches/${matchId}/match-plan?isCurrent=false`,
      GET_INITIAL_MATCH_PLAN_REQUEST,
      GET_INITIAL_MATCH_PLAN_SUCCESS,
      GET_INITIAL_MATCH_PLAN_FAILED,
      null,
      '',
      'GET',
      null,
      EVENT_SERVICE
    );
  };

  const getLatestMatchPlan = () => {
    getLatestMatchPlanApi(
      `/api/v1/matches/${matchId}/match-plan`,
      GET_LATEST_MATCH_PLAN_REQUEST,
      GET_LATEST_MATCH_PLAN_SUCCESS,
      GET_LATEST_MATCH_PLAN_FAILED,
      null,
      '',
      'GET',
      null,
      EVENT_SERVICE
    );
  };

  const savePlayerRatings = ratings => {
    savePlayerRatingsApi(
      `/api/v1/matches/${matchId}/player-rating`,
      SAVE_PLAYER_RATINGS_REQUEST,
      SAVE_PLAYER_RATINGS_SUCCESS,
      SAVE_PLAYER_RATINGS_FAILED,
      ratings,
      '',
      'PUT',
      null,
      EVENT_SERVICE
    );
  };

  const updatePlayerRating = userCurrentInputRatings => {
    if (isIntializeRatings) {
      if (userCurrentInputRatings?.length) {
        const updatePlayerRatings = matchPlayerRatings?.map(
          ({ userId, rating }) => {
            const findAddedRatingPlayer = userCurrentInputRatings.find(
              updatedRating => updatedRating?.userId === userId
            );
            return {
              rating: findAddedRatingPlayer?.rating || rating || null,
              userId,
            };
          }
        );
        savePlayerRatings(updatePlayerRatings);
      } else {
        const allPlayerRatings = matchPlayerRatings?.map(
          ({ userId, rating }) => ({
            rating: rating || null,
            userId,
          })
        );
        savePlayerRatings(allPlayerRatings);
      }
    } else {
      if (userCurrentInputRatings?.length) {
        savePlayerRatings(userCurrentInputRatings);
      }
    }
  };

  const getInitialPlayerRating = () => {
    setIsInitialsPlayerRating(true);
    getInitialPlayerRatingApi(
      `/api/v1/events/${matchId}/player-rating?page=1&size=${PLAYER_RATING_SIZE}`,
      GET_INITIAL_RATINGS_REQUEST,
      GET_INITIAL_RATINGS_SUCCESS,
      GET_INITIAL_RATINGS_FAILED,
      null,
      '',
      'GET',
      null,
      EVENT_SERVICE
    );
  };

  const getPlayerRating = async playerListSize => {
    setIsInitialsPlayerRating(false);
    const numberOfPage = playerListSize
      ? Math.ceil(playerListSize / PLAYER_RATING_SIZE)
      : 0;
    if (numberOfPage) {
      const playerRatingUrls = [];
      for (let index = 0; index < numberOfPage; index++) {
        playerRatingUrls.push(
          `/api/v1/events/${matchId}/player-rating?page=${
            index + 1
          }&size=${PLAYER_RATING_SIZE}`
        );
      }
      try {
        const values = await fetchPlayerRatings(
          playerRatingUrls,
          'GET',
          EVENT_SERVICE
        );
        const playerList = [...values?.flatMap(res => res?.data?.data)].filter(
          memberInfo => memberInfo
        );
        dispatch({
          type: MATCH_PLAYER_RATINGS_SUCCESS,
          payload: { data: playerList?.length && playerList },
        });
      } catch (error) {
        dispatch({
          type: MATCH_PLAYER_RATINGS_FAILED,
        });
      }
    }
  };

  const getInvitees = async sportProfileIdsList => {
    if (sportProfileIdsList?.length) {
      const chunkOfSportProfileIdsList = [];
      const chunkSize = 10;
      for (let i = 0; i < sportProfileIdsList?.length; i += chunkSize) {
        const chunk = sportProfileIdsList?.slice(i, i + chunkSize);
        chunkOfSportProfileIdsList.push(chunk);
      }

      const playerInfoUrls = [];
      for (let index = 0; index < chunkOfSportProfileIdsList.length; index++) {
        const sportProfileIdArrayList = chunkOfSportProfileIdsList[index];
        playerInfoUrls.push(
          `/api/v1/sport-profiles?page=1&size=${sportProfileIdArrayList?.length}&sportsProfileIds=${sportProfileIdArrayList}`
        );
      }
      try {
        const values = await getUsersForPlayerRatings(
          playerInfoUrls,
          'GET',
          FOOTBALL_SERVICE
        );
        const playerInfoList = [
          ...values?.flatMap(res => res?.data?.data),
        ].filter(memberInfo => memberInfo);
        dispatch({
          type: MATCH_PLAYER_RATINGS_SUCCESS,
          payload: { data: playerInfoList?.length && playerInfoList },
        });
      } catch (error) {
        dispatch({
          type: MATCH_PLAYER_RATINGS_FAILED,
        });
      }
    }
  };

  return (
    // <View style={LayoutStyles.contentWrapper}>
    //   <View style={PlayerRatingStyles.container}>
    <PlayerRatingModal
      isLoading={matchPlayerRatingsFetching || savePlayerRatingsLoading}
      showModal={true}
      playerData={matchPlayerRatings || []}
      saveUpdatedValues={updatePlayerRating}
      setIsShowModal={setIsShowModal}
      isError={isError}
    />
    //   </View>
    // </View>
  );
};

export default PlayerRating;
