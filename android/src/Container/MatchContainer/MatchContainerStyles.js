import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const matchContainerStyles = colors => ({
  contianerWrapper: isTabDevice()
    ? {
        marginRight: wp('2%'),
        width: '46%',
      }
    : {
        width: '98%',
      },
  wrapper: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('1.5%'),
        marginBottom: wp('2%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
        borderRadius: wp('2%'),
        width: '100%',
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        marginBottom: wp('2%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
        borderRadius: wp('2%'),
        width: '100%',
      },
  leftCol: isTabDevice()
    ? {}
    : {
        width: wp('90%'),
        marginBottom: wp('2%'),
      },
  loaderWrapper: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        width: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: wp('1%'),
        justifyContent: 'center',
        shadowColor: colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('5%'),
        width: wp('40%'),
        flexDirection: 'row',
        alignItems: 'center',
        // padding: wp('1%'),
        shadowColor: colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  greenTick: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'contain',
        marginRight: wp('1%'),
      }
    : {
        width: wp('6%'),
        height: wp('6%'),
        resizeMode: 'contain',
        marginRight: wp('2%'),
      },
  rightColBlue: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        width: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: wp('1%'),
        justifyContent: 'center',
        shadowColor: colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('5%'),
        width: wp('40%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  rightColGreen: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('2%'),
        width: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: wp('1%'),
        justifyContent: 'center',
        shadowColor: colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('5%'),
        width: wp('40%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: colors.black,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  rightColText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.1%'),
        padding: wp('0.7%'),
        paddingLeft: 0,
        paddingRight: 0,
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        padding: wp('3%'),
        paddingLeft: 0,
        paddingRight: 0,
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      },
  text: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  text2: isTabDevice()
    ? {
        fontSize: wp('0.8%'),
        color: colors.green,
        marginBottom: wp('0.7%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.7%'),
        color: colors.green,
        marginBottom: wp('0.7%'),
        fontFamily: 'Poppins-Medium',
      },
  text3: isTabDevice()
    ? {
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginBottom: wp('0.1%'),
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginBottom: wp('0.5%'),
      },
  rightColTextMatchNotReady: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.1%'),
        padding: wp('1%'),
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
        padding: wp('3%'),
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      },
});
export default matchContainerStyles;
