import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TrainingContainerStyles = colors => ({
  container: isTabDevice()
    ? {
      width: wp('100%'),
      flexDirection: 'row',
    }
    : {
      width: wp('100%'),
      flexDirection: 'column',
    },
  leftView: isTabDevice()
    ? {
      width: wp('26%'),
      paddingLeft: wp('2%'),
      display: 'flex',
      justifyContent: 'space-between',
      paddingRight: wp('2%'),
      marginTop: hp('-2%'),
    }
    : {
      display: 'flex',
      justifyContent: 'space-between',
      width: wp('100%'),
      paddingLeft: wp('2%'),
      marginTop: hp('1%'),
      marginBottom: hp('1%'),
      height: hp('20%'),
    },
  rightView: isTabDevice()
    ? {
      width: wp('74%'),
      height: hp('100%'),
      paddingLeft: wp('3%'),
      borderLeftWidth: 1,
      borderLeftColor: colors.borderBlue,
    }
    : {
      width: wp('100%'),
      height: hp('10%'),
      paddingLeft: wp('2%'),
    },
  rightViewWrapper: isTabDevice()
    ? {
      flexDirection: 'column',
      alignItems: 'flex-end',
    }
    : {
      flexDirection: 'column',
      justifyContent: 'flex-start',
    },
  dateWrapper: isTabDevice()
    ? {
      height: hp('5%'),
      width: '100%',
    }
    : {
      height: hp('5%'),
    },
  matchCompMatchDateWrapper: isTabDevice()
    ? {
      width: '100%',
      flexDirection: 'row',
    }
    : {
      width: '100%',
      height: 20,
      flexDirection: 'row',
      paddingTop: wp('1%'),
    },
  matchTimeDate: isTabDevice()
    ? {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.veryDarkBlue,
      paddingLeft: wp('1%'),
      paddingRight: wp('1%'),
      marginTop: wp('1%'),
      marginBottom: wp('1%'),
      width: '100%',
      height: hp('5%'),
    }
    : {
      flexDirection: 'row',
      backgroundColor: colors.veryDarkBlue,
      padding: wp('2%'),
      marginTop: wp('2%'),
      marginBottom: wp('1%'),
      width: '100%',
    },
  matchCompMatchDate: isTabDevice()
    ? {
      backgroundColor: colors.aquaBlue,
      borderRadius: wp('5%'),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('2%'),
      paddingLeft: wp('1%'),
      paddingRight: wp('1%'),
      height: hp('5%'),
    }
    : {
      backgroundColor: colors.aquaBlue,
      borderRadius: wp('5%'),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('2%'),
      padding: wp('2%'),
      height: hp('4%'),
    },
  matchCompMatchDateNextDay: isTabDevice()
    ? {
      borderRadius: wp('5%'),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('2%'),
      paddingLeft: wp('1%'),
      paddingRight: wp('1%'),
      height: hp('5%'),
    }
    : {
      borderRadius: wp('5%'),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('2%'),
      padding: wp('2%'),
      height: hp('4%'),
    },
  matchCompMatchDateText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.2%'),
      fontFamily: 'Poppins-Medium',
    }
    : {
      color: colors.white,
      fontSize: hp('1.5%'),
      fontFamily: 'Poppins-Bold',
    },
  coloredText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.2%'),
      fontFamily: 'Poppins-Bold',
      marginRight: wp('1%'),
    }
    : {
      color: colors.white,
      fontSize: wp('2.8%'),
      fontFamily: 'Poppins-Bold',
      marginRight: wp('1%'),
    },

  matchCompCounterArrows: {
    justifyContent: 'space-evenly',
  },

  matchCompTimerText: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
    }
    : {
      fontSize: wp('4%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
    },
  matchCompGameButtons: isTabDevice()
    ? {
      justifyContent: 'space-evenly',
      width: '30%',
      paddingLeft: wp('1.5%'),
    }
    : {
      display: 'none',
    },
  matchCompGameButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1.5%'),
    margin: 5,
    borderRadius: 15,
  },
  matchCompGameButtonText: {
    fontSize: wp('1.5%'),
    color: colors.white,
  },
  matchCompOptionsButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  matchCompOptionsButton: isTabDevice()
    ? {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: wp('1.5%'),
      paddingBottom: wp('1.5%'),
      paddingLeft: wp('3%'),
      paddingRight: wp('3%'),
      // margin: 5,
      borderRadius: wp('1%'),
    }
    : {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: wp('1.5%'),
      paddingBottom: wp('1.5%'),
      paddingLeft: wp('3%'),
      paddingRight: wp('3%'),
      // margin: 5,
      borderRadius: wp('1%'),
      width: '30%',
    },
  matchCompOptionsButtonText: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
      marginRight: wp('1%'),
    }
    : {
      fontSize: wp('2.7%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
      marginRight: wp('1%'),
    },
  matchCompOptionsButtonIcon: isTabDevice()
    ? {}
    : {
      fontSize: wp('5%'),
    },
  matchEventItemTouchable: {
    flexDirection: 'row',
  },

  /* ------ Match Action Screen ------ */

  matchActionContainer: isTabDevice()
    ? {
      width: wp('100%'),
      height: hp('100%'),
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
    }
    : {
      width: wp('100%'),
      height: hp('100%'),
      flexDirection: 'column',
      justifyContent: 'space-between',
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
    },
  matchActionColumn: isTabDevice()
    ? {
      width: '32%',
      height: '70%',
      borderRadius: wp('2%'),
    }
    : {
      width: '100%',
      borderRadius: wp('2%'),
    },
  matchActionColumnMobile: isTabDevice()
    ? {}
    : {
      flexDirection: 'column',
      alignItems: 'center',
      height: '50%',
      backgroundColor: colors.trasparent,
    },
  matchActionColumnMobile2: isTabDevice()
    ? {}
    : {
      height: '20%',
      marginTop: wp('5%'),
    },
  matchActionColumnHide: isTabDevice()
    ? {}
    : {
      display: 'none',
    },
  matchActionColumnBottom: isTabDevice()
    ? {
      width: '66%',
      flexDirection: 'row',
      justifyContent: 'space-between',
    }
    : {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
  matchActionFormationTitle: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.5%'),
      textAlign: 'center',
      marginBottom: wp('1%'),
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
      textAlign: 'center',
      marginBottom: wp('1%'),
    },
  matchActionFormation: isTabDevice()
    ? {
      color: colors.green,
      fontSize: wp('1.2%'),
      textAlign: 'center',
    }
    : {
      color: colors.green,
      fontSize: wp('3%'),
      textAlign: 'center',
    },
  matchActionFieldRow1: {
    marginBottom: wp('2%'),
  },
  matchActionFieldRow2: isTabDevice()
    ? {
      width: '100%',
      height: '60%',
      marginTop: wp('3%'),
      marginBottom: wp('3%'),
    }
    : {
      width: '90%',
      height: '70%',
      marginTop: wp('3%'),
      marginBottom: wp('3%'),
    },
  substituteTitle: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.5%'),
      textAlign: 'center',
      marginBottom: wp('1%'),
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
      textAlign: 'center',
      marginBottom: wp('1%'),
    },
  substituteProfileImage: isTabDevice()
    ? {
      width: wp('4%'),
      height: hp('4%'),
      borderRadius: wp('100%'),
      marginRight: wp('1%'),
    }
    : {
      width: wp('8%'),
      height: hp('8%'),
      borderRadius: wp('100%'),
      marginRight: wp('1%'),
    },
  matchActionCommentContainer: isTabDevice()
    ? {
      width: wp('25%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.lightBlue,
      paddingBottom: wp('1%'),
      marginBottom: wp('1%'),
    }
    : {
      width: wp('90%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.lightBlue,
      paddingBottom: wp('1%'),
      marginBottom: wp('1%'),
    },
  matchActionCommentClose: {
    position: 'absolute',
    right: 0,
    zIndex: 1,
  },
  matchActionCommentTime: isTabDevice()
    ? {
      color: colors.green,
      fontSize: wp('1.3%'),
      fontFamily: 'Poppins-Bold',
      marginBottom: wp('0.5%'),
    }
    : {
      color: colors.green,
      fontSize: wp('3.3%'),
      fontFamily: 'Poppins-Bold',
      marginBottom: wp('0.5%'),
    },
  matchActionComment: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.3%'),
      fontFamily: 'Poppins-Bold',
    }
    : {
      color: colors.white,
      fontSize: wp('4.3%'),
      fontFamily: 'Poppins-Bold',
    },
  matchActionScoreBoard: {
    backgroundColor: colors.tileBackground,
    padding: wp('1%'),
    borderRadius: wp('1.5%'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: wp('2%'),
  },
  matchActionScoreTeam: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '30%',
  },
  matchActionScoreTeamName: {
    color: colors.white,
    fontSize: wp('1%'),
    fontFamily: 'Poppins-Bold',
    textAlign: 'center',
  },
  matchActionScoreTeamScore: {
    color: colors.white,
    fontSize: wp('5%'),
    fontFamily: 'Poppins-Bold',
  },
  matchActionScoreTimerTile: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: '30%',
  },
  matchActionScoreVsText: {
    color: colors.white,
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
  },
  matchActionScoreTimerText: {
    color: colors.white,
    fontSize: wp('1.7%'),
    fontFamily: 'Poppins-Bold',
  },
  matchActionButtonColumn: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  coloredButton: {
    width: '47%',
    backgroundColor: colors.tileBackground,
    marginBottom: wp('2%'),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: wp('1.5%'),
    padding: wp('1.5%'),
  },
  coloredButtonText: {
    color: colors.white,
    fontSize: wp('1.5%'),
    fontFamily: 'Poppins-Medium',
  },
  matchActionBelowButton: {
    width: '48%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: wp('1.5%'),
    padding: wp('1.5%'),
    marginTop: wp('2%'),
  },
  matchActionBelowButtonText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.5%'),
      fontFamily: 'Poppins-Medium',
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
      fontFamily: 'Poppins-Medium',
    },
  close: isTabDevice()
    ? {}
    : {
      fontSize: wp('6%'),
    },
  scrollOver: isTabDevice()
    ? {
      display: 'none',
    }
    : {
      display: 'none',
      width: '100%',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },
  scrollOverText: isTabDevice()
    ? {
      display: 'none',
    }
    : {
      color: colors.white,
      fontSize: wp('2.5%'),
      fontFamily: 'Poppins-Medium',
    },
  scrollOverArrow: isTabDevice()
    ? {
      display: 'none',
    }
    : {
      color: colors.green,
      fontSize: wp('4%'),
      fontFamily: 'Poppins-Bold',
    },
  errorView: {
    justifyContent: 'flex-end',
    marginTop: wp('1%'),
  },
  errorText: {
    color: colors.red,
    marginRight: wp('7%'),
    fontSize: wp('1.2%'),
  },
  selectedTraining: isTabDevice()
    ? {
      backgroundColor: 'transparent',
      height: hp('5%'),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('5%'),
      borderBottomColor: colors.green,
      borderBottomWidth: wp('0.5%'),
    }
    : {
      backgroundColor: 'transparent',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('10%'),
      borderBottomColor: colors.green,
      borderBottomWidth: hp('0.5%'),
      fontSize: hp('2%'),
    },
  notSelectedTraining: isTabDevice()
    ? {
      height: hp('5%'),
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('5%'),
      borderBottomWidth: wp('0.5%'),
      borderBottomColor: 'transparent',
    }
    : {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: wp('10%'),
      borderBottomWidth: hp('0.5%'),
      fontSize: hp('2%'),
      borderBottomColor: 'transparent',
    },
  Button: isTabDevice()
    ? {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: hp('1.5%'),
      paddingBottom: hp('2%'),
      paddingLeft: wp('3%'),
      paddingRight: wp('3%'),
      marginRight: wp('1.5%'),
      borderRadius: wp('1%'),
      height: hp('7%'),
    }
    : {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: wp('1.5%'),
      paddingBottom: wp('1.5%'),
      paddingLeft: hp('1%'),
      paddingRight: hp('1%'),
      marginBottom: wp('2%'),
      // margin: 5,
      borderRadius: hp('1%'),
      width: '48%',
    },
  addDrilText: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
      color: colors.white,
      marginTop: wp('1%'),
    }
    : {
      fontSize: hp('1.5%'),
      color: colors.white,
      marginTop: hp('1%'),
    },
  NoDrillsText: isTabDevice()
    ? {
      fontSize: wp('1.2%'),
      color: colors.white,
    }
    : {
      fontSize: hp('2%'),
      color: colors.white,
    },
  buttonText: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
      marginRight: wp('1%'),
      textAlign: 'center',
    }
    : {
      fontSize: hp('1.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
      marginRight: wp('1%'),
    },
  buttonIcon: isTabDevice()
    ? {}
    : {
      fontSize: wp('5%'),
    },
  trainingListWrapper: isTabDevice()
    ? {
      width: '100%',
      height: hp('15%'),
      flexDirection: 'row',
      marginLeft: wp('20%'),
      marginRight: wp('20%'),
    }
    : {
      width: '70%',
      height: hp('4%'),
      flexDirection: 'row',
      paddingTop: wp('1%'),
      marginLeft: wp('15%'),
      marginRight: wp('20%'),
    },
  teamWrapper: isTabDevice()
    ? {
      height: hp('75%'),
    }
    : {
      height: hp('8%'),
    },
  childWrapper: {
    marginBottom: wp('2%'),
  },
  trainingDrilWrapper: isTabDevice()
    ? {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      width: '100%',
      height: hp('71%'),
    }
    : {
      display: 'flex',
      alignItems: 'flex-end',
      width: '100%',
      height: hp('51%'),
    },
  drillwrapper: isTabDevice()
    ? {
      width: '100%',
      height: hp('10%'),
    }
    : {
      width: '100%',
      height: hp('33%'),
    },
  addDrillWrapper: isTabDevice()
    ? {
      width: wp('30%'),
      height: hp('20%'),
      backgroundColor: colors.borderBlue,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: wp('1.5%'),
      marginTop: wp('2%'),
    }
    : {
      width: wp('50%'),
      height: hp('15%'),
      backgroundColor: colors.borderBlue,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 10,
      marginTop: 20,
    },
  drillButtonWrapper: isTabDevice()
    ? {
      display: 'flex',
      flexDirection: 'row',
      height: hp('5%'),
    }
    : {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      width: '100%',
      justifyContent: 'space-between',
    },
  editIcon: isTabDevice()
    ? {
      width: wp('1.5%'),
      height: wp('1.5%'),
      resizeMode: 'contain',
    }
    : {
      width: hp('1.5%'),
      height: hp('1.5%'),
    },
  drillButton: isTabDevice()
    ? {
      width: wp('21%'),
      height: hp('7%'),
    }
    : {
      width: wp('47%'),
      height: hp('4%'),
    },
  rightViewTeamName: {
    color: colors.white,
    fontSize: wp('4%'),
    fontFamily: 'Poppins-Bold',
    paddingTop: wp('2%'),
    paddingBottom: wp('2%'),
  },
  noScheduledWrapper: isTabDevice()
    ? {
      width: wp('40%'),
      margin: 'auto',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    }
    : {
      width: wp('70%'),
      margin: 'auto',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
  noScheduledWrapperTextMain: isTabDevice() ? {
    color: colors.white,
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
    textAlign: 'center'
  } : {
    color: colors.white,
    fontSize: wp('4%'),
    fontFamily: 'Poppins-Bold',
    textAlign: 'center'

  },
  noScheduledWrapperTextSub: isTabDevice() ? {
    color: colors.white,
    fontSize: wp('1.5%'),
    fontFamily: 'Poppins-Regular',
    marginTop: wp("2%"),
    textAlign: 'center'

  } : {
    color: colors.white,
    fontSize: wp('3%'),
    fontFamily: 'Poppins-Regular',
    marginTop: wp("2%"),
    textAlign: 'center'
  },
  noScheduledWrapperButton: isTabDevice() ? {
    backgroundColor: colors.green,
    marginTop: wp("5%"),
    padding: wp('1.5%'),
    margin: 5,
    borderRadius: 8,
    width: "40%",
    color: colors.white,
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
  } : {
    backgroundColor: colors.green,
    marginTop: wp("5%"),
    padding: wp('1.5%'),
    margin: 5,
    borderRadius: 8,
    width: "40%",
    color: colors.white,
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
  },
  noScheduledWrapperButtonText: isTabDevice() ? {
    color: colors.white,
    fontSize: wp('1.5%'),
    fontFamily: 'Poppins-Regular',
    textAlign: 'center'

  } : {
    color: colors.white,
    fontSize: wp('3%'),
    fontFamily: 'Poppins-Regular',
    textAlign: 'center'
  },
});

export default TrainingContainerStyles;
