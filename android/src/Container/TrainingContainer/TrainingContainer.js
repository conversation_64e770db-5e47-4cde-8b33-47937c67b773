import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { useDispatch, useSelector } from 'react-redux';
import editIcon from '../../../assets/buttons/edit-icon-training.png';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import ChildLabel from '../../components/ChildLabel/ChildLabel';
import ModalWrapper from '../../components/modal/ModalWrapper/ModalWrapper';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import TrainingFormReport from '../../components/PlayerInfoUpdateForm/TrainingFormReport';
import { isTabDevice } from '../../config/appConfig';
import { userRoleType } from '../../constants/constants';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import { addLeadingZeros, dateTimeConversion } from '../../helpers';
import { getDateKey } from '../../helpers/DateHelper';
import useApi from '../../hooks/useApi';
import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';
import {
  SET_SELECTED_CHILD,
  IS_USER_IN_MATCHES,
} from '../../store/actionTypes/common/commonActionTypes';
import useGetChildInformation from '../../hooks/useGetChildInformation';
import { useNavigation } from '@react-navigation/native';

import {
  GET_CHILD_TEAM_DATA_FAILED,
  GET_CHILD_TEAM_DATA_REQUEST,
  GET_CHILD_TEAM_DATA_SUCCESS,
  RESET_TEAMS,
} from '../../store/actionTypes/Team/TeamAction';
import {
  MATCH_TRAINING_REPORT_SAVE_FAILED,
  MATCH_TRAINING_REPORT_SAVE_REQUEST,
  MATCH_TRAINING_REPORT_SAVE_SUCCESS,
  RESET_SELECTED_DATE_TRAININGS,
  TRAINING_EVENTS_FAILED,
  TRAINING_EVENTS_REQUEST,
  TRAINING_EVENTS_SUCCESS,
  TRAINING_MORE_EVENTS_FAILED,
  TRAINING_MORE_EVENTS_REQUEST,
  TRAINING_MORE_EVENTS_SUCCESS,
  TRAINING_SET_SELECTED_DATE,
  TRAINING_SET_SELECTED_EVENT,
  TRAINING_SET_SELECTED_TEAM,
} from '../../store/actionTypes/Training/TrainingActions';
import {
  REPORT_DELETE,
  REPORT_RESET,
  UPLOAD_REPORT_UPDATE,
} from '../../store/actionTypes/UploadReport/uploadreportAction';
import AttendanceContainer from '../AttendanceContainer/AttendanceContainer';
import TeamContainer from '../TeamMatchesContainer/TeamContainer';
import customTrainingContainerStyles from './TrainingContainerStyle';
const TrainingContainer = () => {
  const TrainingContainerStyles = useStyles(customTrainingContainerStyles);
  const colors = useColors();
  const [fetchData] = useApi();
  const [updateData] = useApi();
  const [fetchEvents] = useApi();
  const dispatch = useDispatch();

  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedTraining, setSelectedTraining] = useState('Warm Up');
  const scrollIndexRef = useRef(null);
  const [fetchChildInformation] = useGetChildInformation();

  const {
    trainings = {},
    trainingsLoading,
    trainingsCurrentPage,
    moreTrainingsLoading,
    selectedEvent,
    selectedEventIndex,
    selectedDate,
    eventLogs = {},
    selectedTeamId,
    gotoLatestOngoingMatch,
    latestOngoingMatch,
    loadingTrainingTrigger,
  } = useSelector(state => state?.trainings);
  const { userData, userRole } = useSelector(state => state.auth);
  const { matchPlanLoading } =
    eventLogs[selectedTeamId]?.[selectedEventIndex] || {};
  const { selectedTeam } = useSelector(state => state?.team);
  const [fetchChildTeam] = useApi();
  const [lengthOfChip, setLengthOfChip] = useState(0);

  const { playerUploadedReport, playerUploadedReportLoading } = useSelector(
    state => state?.player
  );
  const today = new Date();
  const endDate = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate() + 14
  );
  const rangeStartDate = new Date(
    new Date().setDate(new Date().getDate() - 14)
  );

  const [showTeamData, setShowTeamData] = useState(false);

  const { children, selectedChild, isUserInMatches } = useSelector(
    state => state?.common
  );
  const isParent = userRole === userRoleType.PARENT;

  const setSelectedChild = payload => {
    dispatch({
      type: SET_SELECTED_CHILD,
      payload,
    });
  };

  const [groupedEvents, setGroupedEvents] = useState({});

  const userTypes = [userRoleType.PLAYER];
  const isPlayer = userTypes.includes(userData?.type);
  const isCoach = userRole === userRoleType.COACH;
  const isHeadCoach = userRole === userRoleType.HEAD_COACH;
  const navigation = useNavigation();

  const getChildTeamData = userId => {
    fetchChildTeam(
      `/api/v1/teams?page=1&size=100&playerIds=${userId}`,
      GET_CHILD_TEAM_DATA_REQUEST,
      GET_CHILD_TEAM_DATA_SUCCESS,
      GET_CHILD_TEAM_DATA_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    if (isParent && selectedChild) {
      getChildTeamData(selectedChild?.id);
    }
  }, [selectedChild]);
  useEffect(() => {
    if (loadingTrainingTrigger && selectedTeam?._id) {
      dispatch({
        type: TRAINING_SET_SELECTED_TEAM,
        payload: selectedTeam._id,
      });
      fetchEvents(
        `/api/v1/events?teamId=${
          selectedTeam._id
        }&type=training&startDate=${rangeStartDate.toISOString()}&endDate=${endDate.toISOString()}&page=1&size=31`,
        TRAINING_EVENTS_REQUEST,
        TRAINING_EVENTS_SUCCESS,
        TRAINING_EVENTS_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  }, [loadingTrainingTrigger]);

  useEffect(() => {
    if (Object.keys(groupedEvents)?.length && selectedDate) {
      const length = Object.keys(groupedEvents)?.length;
      const index = Object.keys(groupedEvents)?.indexOf(selectedDate);
      setTimeout(() => {
        scrollIndexRef &&
          scrollIndexRef?.current?.scrollToIndex({
            index: index > length - 1 || index < 0 ? 0 : index || 0,
          });
      }, 20);
    }
  }, [groupedEvents, lengthOfChip]);

  const clearSelectDate = () => {
    dispatch({
      type: RESET_SELECTED_DATE_TRAININGS,
    });
  };

  useEffect(() => {
    fetchChildInformation();

    return () => {
      setGroupedEvents({});
      clearSelectDate();
      resetRedux()
    };
  }, []);

  useEffect(() => {
    if (selectedTeam?._id) {
      clearSelectDate();
      setGroupedEvents({});
      dispatch({
        type: TRAINING_SET_SELECTED_TEAM,
        payload: selectedTeam._id,
      });

      fetchEvents(
        `/api/v1/events?teamId=${
          selectedTeam._id
        }&type=training&startDate=${rangeStartDate.toISOString()}&endDate=${endDate.toISOString()}&page=1&size=31`,
        TRAINING_EVENTS_REQUEST,
        TRAINING_EVENTS_SUCCESS,
        TRAINING_EVENTS_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  }, [selectedTeam]);

  const loadMoreEvents = () => {
    if (selectedTeam?._id && !moreTrainingsLoading) {
      const nextPage = Number(trainingsCurrentPage) + 1;
      fetchData(
        `/api/v1/events?teamId=${
          selectedTeam._id
        }&type=training&startDate=${rangeStartDate.toISOString()}&endDate=${endDate.toISOString()}&page=${nextPage}&size=31`,
        TRAINING_MORE_EVENTS_REQUEST,
        TRAINING_MORE_EVENTS_SUCCESS,
        TRAINING_MORE_EVENTS_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  };

  const setSelectedDate = date => {
    dispatch({
      type: TRAINING_SET_SELECTED_DATE,
      payload: date,
    });
  };

  useEffect(() => {
    if (trainings[selectedTeamId]) {
      let groupedTrainings = {};
      trainings[selectedTeamId].forEach(match => {
        const { monthString, dateReadable, dateString } = dateTimeConversion(
          match?.startTime
        );
        const key = `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
        if (groupedTrainings[key]) {
          groupedTrainings[key].push(match);
        } else {
          groupedTrainings[key] = [match];
        }
      });
      setGroupedEvents(groupedTrainings);

      if (Object.keys(groupedTrainings)?.length && !trainingsLoading) {
        const currentDateKey = getDateKey(
          trainings?.[selectedTeamId]?.map(({ startTime }) => startTime)
        );

        const isSameDate = Object.keys(groupedTrainings)[0] === selectedDate;
        if (gotoLatestOngoingMatch && latestOngoingMatch) {
          const { monthString, dateReadable, dateString } = dateTimeConversion(
            latestOngoingMatch?.startTime
          );
          const date = `${monthString.slice(
            0,
            3
          )} ${dateReadable}, ${dateString}`;
          if (Object.keys(groupedTrainings).includes(date)) {
            setSelectedDate(date);
          } else {
            !isSameDate &&
              setSelectedDate(
                currentDateKey || Object.keys(groupedTrainings)[0]
              );
          }
        } else if (Object.keys(groupedTrainings).includes(selectedDate)) {
          setSelectedDate(selectedDate);
        } else {
          !isSameDate &&
            setSelectedDate(currentDateKey || Object.keys(groupedTrainings)[0]);
        }
      }
    }
  }, [trainings, selectedDate]);

  const selectEvent = event => {
    dispatch({
      type: REPORT_RESET,
    });
    dispatch({
      type: UPLOAD_REPORT_UPDATE,
      payload: event?.fileUploads || [],
    });
    dispatch({
      type: TRAINING_SET_SELECTED_EVENT,
      payload: event,
    });
  };

  useEffect(() => {
    if (
      selectedDate &&
      groupedEvents &&
      groupedEvents?.[selectedDate] &&
      !trainingsLoading
    ) {
      if (
        gotoLatestOngoingMatch &&
        latestOngoingMatch &&
        selectedTeamId === latestOngoingMatch?.team?._id
      ) {
        if (
          groupedEvents?.[selectedDate]
            .map(e => e?._id)
            .includes(latestOngoingMatch?._id)
        ) {
          selectEvent(latestOngoingMatch);
        } else {
          selectEvent(groupedEvents?.[selectedDate][0]);
        }
      } else if (
        !selectedEvent ||
        !groupedEvents?.[selectedDate]
          .map(e => e?._id)
          .includes(selectedEvent?._id)
      ) {
        groupedEvents?.[selectedDate]?.length &&
          selectEvent(groupedEvents?.[selectedDate][0]);
      }
    }
  }, [selectedDate, selectedTeamId, groupedEvents]);

  const dateItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        onLayout={event => {
          const { width } = event.nativeEvent.layout;
          !lengthOfChip && setLengthOfChip(width);
        }}
        onPress={() => setSelectedDate(item)}
        style={[
          selectedDate === item
            ? TrainingContainerStyles.matchCompMatchDate
            : TrainingContainerStyles.matchCompMatchDateNextDay,
        ]}
      >
        <Text style={TrainingContainerStyles.matchCompMatchDateText}>
          {item}
        </Text>
      </TouchableOpacity>
    );
  };

  const trainginItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => setSelectedTraining(item)}
        style={[
          selectedTraining === item
            ? TrainingContainerStyles.selectedTraining
            : TrainingContainerStyles.notSelectedTraining,
        ]}
      >
        <Text style={TrainingContainerStyles.matchCompMatchDateText}>
          {item}
        </Text>
      </TouchableOpacity>
    );
  };

  const eventItem = ({ item }) => {
    const {
      hours12: eventHours,
      minutes: eventMinutes,
      amPm,
    } = dateTimeConversion(item?.startTime);
    return (
      <TouchableOpacity
        style={TrainingContainerStyles.matchEventItemTouchable}
        onPress={() => selectEvent(item)}
      >
        {selectedEvent?._id === item?._id && (
          <ColoredText color={colors.grey}> | </ColoredText>
        )}
        <ColoredText
          color={selectedEvent?._id === item?._id ? colors.green : colors.grey}
        >
          {eventHours}:{addLeadingZeros(eventMinutes)}
          {amPm}
        </ColoredText>
      </TouchableOpacity>
    );
  };

  const ColoredText = ({ children, color }) => {
    return (
      <Text style={[TrainingContainerStyles.coloredText, { color: color }]}>
        {children}
      </Text>
    );
  };

  const addUpdate = fileUploads => {
    let postValues = { ...selectedEvent };

    if (fileUploads) {
      postValues = { ...postValues, ...fileUploads };
    }

    updateData(
      `/api/v1/events`,
      MATCH_TRAINING_REPORT_SAVE_REQUEST,
      MATCH_TRAINING_REPORT_SAVE_SUCCESS,
      MATCH_TRAINING_REPORT_SAVE_FAILED,
      postValues,
      '',
      'PUT',
      null,
      EVENT_SERVICE
    );

    dispatch({
      type: TRAINING_SET_SELECTED_EVENT,
      payload: {},
    });

    setShowReportModal(false);
  };

  const deleteReportAction = index => {
    dispatch({
      type: REPORT_DELETE,
      payload: { data: index },
    });
  };

  const setMatchScreen = response => {
    dispatch({
      type: IS_USER_IN_MATCHES,
      payload: response,
    });
  };

  useEffect(() => {
    if (!isUserInMatches) {
      setShowTeamData(false);
    }
  }, [isUserInMatches]);


  const resetRedux = () => {
    dispatch({ type: RESET_TEAMS });
  };

  return (
    <View style={TrainingContainerStyles.container}>
      {!isTabDevice() && showTeamData ? null : (
        <View style={TrainingContainerStyles.leftView}>
          <View style={TrainingContainerStyles.teamWrapper}>
            <View style={TrainingContainerStyles.childWrapper}>
              <ChildLabel
                data={children}
                setSelectedChild={data => {
                  resetRedux();
                  setSelectedChild(data);
                }}
                selectedChild={selectedChild}
              />
            </View>
            <TeamContainer
              setMatchScreen={setMatchScreen}
              setShowTeamData={setShowTeamData}
            />

            {/* Button is obsolete */}
            {/* <TouchableOpacity
              style={{
                ...TrainingContainerStyles.Button,
                ...TrainingContainerStyles.drillButton,
                backgroundColor: colors.aquaBlue,
                justifyContent: 'center',
                opacity: 0.3,
              }}
              disabled
            >
              <Text style={TrainingContainerStyles.buttonText}>Activities</Text>
            </TouchableOpacity> */}
          </View>
        </View>
      )}
      <View
        style={
          isTabDevice()
            ? TrainingContainerStyles.rightView
            : showTeamData
            ? TrainingContainerStyles.rightView
            : { ...TrainingContainerStyles.rightView, display: 'none' }
        }
      >
        <View style={TrainingContainerStyles.rightViewWrapper}>
          {trainingsLoading || matchPlanLoading ? (
            <ActivitySpinner isMatchLog />
          ) : trainings[selectedTeamId]?.length ? (
            <>
              {!isTabDevice() && (
                <Text style={TrainingContainerStyles.rightViewTeamName}>
                  {selectedTeam?.teamName}
                </Text>
              )}
              <View style={TrainingContainerStyles.dateWrapper}>
                <FlatList
                  ref={scrollIndexRef}
                  data={Object.keys(groupedEvents)}
                  renderItem={dateItem}
                  keyExtractor={(item, index) => index.toString()}
                  horizontal
                  onEndReached={loadMoreEvents}
                  style={TrainingContainerStyles.matchCompMatchDateWrapper}
                  getItemLayout={(data, index) => ({
                    length: lengthOfChip,
                    offset: lengthOfChip * index,
                    index,
                  })}
                />
              </View>

              <View style={TrainingContainerStyles.matchTimeDate}>
                {selectedEvent && (
                  <ColoredText color={colors.aquaBlue}>Time</ColoredText>
                )}
                {selectedEvent && (
                  <FlatList
                    data={groupedEvents[selectedDate]}
                    renderItem={eventItem}
                    horizontal
                  />
                )}
              </View>
              <View style={TrainingContainerStyles.trainingDrilWrapper}>
                <View style={TrainingContainerStyles.drillwrapper}>
                  {/* <FlatList
                    data={['Warm Up', 'Activities', 'Warm Down']}
                    renderItem={trainginItem}
                    keyExtractor={item => item}
                    horizontal
                    style={TrainingContainerStyles.trainingListWrapper}
                  /> */}
                  <View style={{ height: hp('5%') }}>
                                     <TrainingCard/>

                  </View>
                </View>
                <View style={TrainingContainerStyles.drillButtonWrapper}>
                  {selectedEvent && (
                    <TouchableOpacity
                      onPress={() => setShowReportModal(true)}
                      style={[
                        {
                          ...TrainingContainerStyles.Button,
                          backgroundColor: colors.aquaBlue,

                          opacity:
                            (isPlayer || isParent) &&
                            !selectedEvent?.fileUploads?.length
                              ? 0.5
                              : 1,
                        },
                        !isTabDevice() && { width: '48%' },
                      ]}
                      disabled={
                        (isPlayer || isParent) &&
                        !selectedEvent?.fileUploads?.length
                      }
                    >
                      <Text style={TrainingContainerStyles.buttonText}>
                        Session Plans
                      </Text>
                      <Ionicons
                        name="document"
                        size={25}
                        color={colors.white}
                        style={TrainingContainerStyles.buttonIcon}
                      />
                    </TouchableOpacity>
                  )}

                  {!isPlayer && !isParent && (
                    <TouchableOpacity
                      onPress={() => setShowAttendanceModal(true)}
                      style={[
                        {
                          ...TrainingContainerStyles.Button,
                          backgroundColor: colors.aquaBlue,
                        },
                        !isTabDevice() && { width: '48%' },
                      ]}
                    >
                      <Text style={TrainingContainerStyles.buttonText}>
                        Attendance
                      </Text>
                      <Ionicons
                        name="people"
                        size={25}
                        color={colors.white}
                        style={TrainingContainerStyles.buttonIcon}
                      />
                    </TouchableOpacity>
                  )}

                  {!isPlayer && !isParent && (
                    <TouchableOpacity
                      style={{
                        ...TrainingContainerStyles.Button,
                        backgroundColor: colors.tileBackground,
                        opacity: 0.3,
                      }}
                      disabled
                    >
                      <Text style={TrainingContainerStyles.buttonText}>
                        Edit
                      </Text>
                      <Image
                        source={editIcon}
                        style={TrainingContainerStyles.editIcon}
                      />
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity
                    style={{
                      ...TrainingContainerStyles.Button,
                      justifyContent: 'center',
                      backgroundColor: colors.green,
                      opacity: 0.3,
                    }}
                    disabled
                  >
                    <Text style={TrainingContainerStyles.buttonText}>
                      Download
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </>
          ) : (
            <View style={TrainingContainerStyles.noScheduledWrapper}>
              <Text style={TrainingContainerStyles.noScheduledWrapperTextMain}>You do not have any training sessions scheduled at the moment.</Text>

              {(isHeadCoach || isCoach) && (
                <>
                  <Text style={TrainingContainerStyles.noScheduledWrapperTextSub}>To schedule a Training session, please click on the 'Planner' button below.</Text>
                  <TouchableOpacity
                    style={TrainingContainerStyles.noScheduledWrapperButton}
                    onPress={() => [setMatchScreen(false), navigation.navigate('PlannerScreen')]}
                  >
                    <Text style={TrainingContainerStyles.noScheduledWrapperButtonText}>Planner</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          )}
        </View>
      </View>

      {showAttendanceModal && (
        <ModalWrapper visible>
          <AttendanceContainer
            selectedEvent={selectedEvent}
            setShowAttendanceModal={setShowAttendanceModal}
          />
        </ModalWrapper>
      )}

      {showReportModal && (
        <ModalWrapper visible>
          <TrainingFormReport
            selectedEvent={selectedEvent}
            playerUploadedReport={playerUploadedReport}
            modalAction={setShowReportModal}
            action={addUpdate}
            deleteReportAction={deleteReportAction}
            isPlayerOrParent={isPlayer || isParent}
            playerUploadedReportLoading={playerUploadedReportLoading}
          />
        </ModalWrapper>
      )}
    </View>
  );
};

export default TrainingContainer;
