import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PlayerInfoUpdateFormReport from '../../components/PlayerInfoUpdateForm/PlayerInfoUpdateFormReport';
import { S3_BUCKET_LOCATION } from '../../constants/constants';
import {
  REPORT_DELETE,
  REPORT_RESET,
} from '../../store/actionTypes/UploadReport/uploadreportAction';
const PlayerInfoMedicalInjuriesAddContainer = ({
  setModalVisible,
  addUpdate,
  isAddModal,
}) => {
  const { playerUploadedReport, playerUploadedReportLoading } = useSelector(
    state => state?.player
  );
  const dispatch = useDispatch();
  const deleteReportAction = index => {
    dispatch({
      type: REPORT_DELETE,
      payload: { data: index },
    });
  };
  const selectedUpdateData = {
    _id: '',
    message: '',
    createdDate: new Date().toISOString(),
  };

  useEffect(() => {
    isAddModal &&
      dispatch({
        type: REPORT_RESET,
      });
  }, [isAddModal]);

  return (
    <>
      <PlayerInfoUpdateFormReport
        selectedUpdateData={selectedUpdateData}
        modalAction={setModalVisible}
        action={addUpdate}
        playerUploadedReport={playerUploadedReport}
        deleteReportAction={index => deleteReportAction(index)}
        fileType={S3_BUCKET_LOCATION.injuryDocuments}
        playerUploadedReportLoading={playerUploadedReportLoading}
      />
    </>
  );
};
export default PlayerInfoMedicalInjuriesAddContainer;
