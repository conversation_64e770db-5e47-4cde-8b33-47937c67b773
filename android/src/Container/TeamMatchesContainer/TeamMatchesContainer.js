import React, { useState, useEffect } from 'react';
import { ScrollView, View, Text } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import customTeamMatchesContainerStyle from './TeamMatchesContainerStyle';
import ProfileImage from '../../Container/PlayerInfo/ProfileImage';

import { calendarFutureMonths } from '../../helpers/index';
import MonthSlider from '../../components/Matches/MonthSlider';
import MonthContainer from '../../Container/MonthContainer/MonthContainer';
import TeamContainer from '../../Container/TeamMatchesContainer/TeamContainer';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import { userRoleType } from '../../constants/constants';
import { MATCHES_RESET } from '../../store/actionTypes/MatchesInfo/MatchesAction';

import { RESET_TEAMS } from '../../store/actionTypes/Team/TeamAction';
import useStyles from '../../hooks/useStyles';
import ChildLabel from '../../components/ChildLabel/ChildLabel';

import {
  GET_CHILD_INFORMATION_FAILED,
  GET_CHILD_INFORMATION_REQUEST,
  GET_CHILD_INFORMATION_SUCCESS,
  IS_USER_IN_MATCHES,
} from '../../store/actionTypes/common/commonActionTypes';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import { isTabDevice } from '../../config/appConfig';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
const TeamMatchesContainer = () => {
  const TeamMatchesContainerStyle = useStyles(customTeamMatchesContainerStyle);
  const dispatch = useDispatch();
  const [fetchChildInformation] = useApi();

  const { selectedTeam, teamDataLoading } = useSelector(state => state?.team);
  const { userData, userRole } = useSelector(state => state?.auth);
  const [selectedMonth, setSelectedMonth] = useState({});
  const [futureMonths, setFutureMonths] = useState({});
  const [showTeamData, setShowTeamData] = useState(false);
  const [showMatchData, setMatchData] = useState(false);
  const { children, isUserInMatches, isUserInMatchLog } = useSelector(
    state => state?.common
  );

  const isParent = userRole === userRoleType.PARENT;

  const [selectedChild, setSelectedChild] = useState();

  useEffect(() => {
    let monthList = calendarFutureMonths(12);
    setSelectedMonth(monthList[1]);
    setFutureMonths(monthList);
  }, []);

  useEffect(() => {
    if (!isUserInMatches) {
      setShowTeamData(false);
    } else setShowTeamData(true);
  }, [isUserInMatches]);

  useEffect(() => {
    dispatch({ type: RESET_TEAMS });
    return () => {
      resetRedux();
    };
  }, []);

  const resetRedux = () => {
    dispatch({ type: RESET_TEAMS });
    dispatch({ type: MATCHES_RESET });
  };

  const setMatchScreen = response => {
    dispatch({
      type: IS_USER_IN_MATCHES,
      payload: response,
    });
  };

  const getChildInformation = playerId => {
    fetchChildInformation(
      `/api/v1/sport-profiles?userIds=${playerId}`,
      GET_CHILD_INFORMATION_REQUEST,
      GET_CHILD_INFORMATION_SUCCESS,
      GET_CHILD_INFORMATION_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    selectedChild?.id && getChildInformation(selectedChild.id);
  }, [selectedChild]);

  useEffect(() => {
    children?.length && setSelectedChild(children[0]);
  }, [children]);
  useEffect(() => {
    fetchChildInformation();
  }, []);

  return (
    <View style={TeamMatchesContainerStyle.container}>
      {!isTabDevice() && showTeamData ? null : (
        <ScrollView
          style={
            userData?.type === userRoleType.PLAYER
              ? TeamMatchesContainerStyle.leftView
              : TeamMatchesContainerStyle.leftViewPlayer
          }
        >
          {isParent && (
            <View
              style={
                isTabDevice()
                  ? { ...TeamMatchesContainerStyle.childWrapper }
                  : isUserInMatches
                  ? {
                      ...TeamMatchesContainerStyle.childWrapper,
                      display: 'none',
                    }
                  : { ...TeamMatchesContainerStyle.childWrapper }
              }
            >
              <ChildLabel
                data={children}
                setSelectedChild={data => {
                  resetRedux();
                  setSelectedChild(data);
                }}
                selectedChild={selectedChild}
              />
            </View>
          )}
          {isTabDevice() ? (
            <TeamContainer selectedChild={selectedChild} />
          ) : (
            !showTeamData && (
              <TeamContainer
                selectedChild={selectedChild}
                setShowTeamData={setShowTeamData}
                setMatchScreen={setMatchScreen}
              />
            )
          )}
        </ScrollView>
      )}
      <View style={TeamMatchesContainerStyle.rightView}>
        {!teamDataLoading && selectedTeam && selectedTeam._id ? (
          <View>
            {!isTabDevice() && (
              <View>
                <Text style={TeamMatchesContainerStyle.rightViewTeamName}>
                  {selectedTeam?.teamName}
                </Text>
              </View>
            )}

            <MonthSlider
              selectedMonth={selectedMonth}
              months={futureMonths}
              setSelectedMonth={setSelectedMonth}
            />
            <MonthContainer selectedMonth={selectedMonth} />
          </View>
        ) : teamDataLoading ? (
          <ActivitySpinner />
        ) : null}
      </View>
    </View>
  );
};

export default TeamMatchesContainer;
