import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const TeamMatchesContainer = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: wp('100%'),
        height: hp('100%'),
        flexDirection: 'column',
      },
  leftView: isTabDevice()
    ? {
        height: hp('80%'),
        width: wp('26%'),
        marginTop: wp('5%'),
        paddingLeft: wp('2%'),
      }
    : {
        width: wp('100%'),
        height: hp('77%'),
        // marginTop: wp('5%'),
        paddingLeft: wp('2%'),
      },
  leftViewPlayer: isTabDevice()
    ? {
        width: wp('26%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      }
    : {
        width: wp('100%'),
        paddingLeft: wp('2%'),
        marginTop: wp('5%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('100%'),
        paddingLeft: wp('3%'),
        borderLeftWidth: 1,
        borderLeftColor: colors.borderBlue,
      }
    : {
        width: wp('100%'),
        height: hp('10%'),
        marginTop: hp('2%'),
        paddingLeft: wp('3%'),
      },
  childWrapper: isTabDevice()
    ? {
        marginTop: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        marginBottom: wp('2%'),
      },
  ProfileLabelView: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  commonView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactsView: {
    flex: 1,
  },
  columnView: {
    flex: 1,
    flexDirection: 'column',
  },
  dateRange: {
    marginTop: hp('10%'),
    marginBottom: hp('1%'),
    fontSize: wp('1.5%'),
    color: colors.white,
    position: 'relative',
  },
  dateRangeSelector: {
    backgroundColor: colors.borderBlue,
    color: colors.white,
    padding: wp('1%'),
    width: wp('20%'),
  },
  dateRangeArrow: {
    width: wp('1%'),
    resizeMode: 'contain',
    position: 'absolute',
    bottom: wp('0.2%'),
    left: wp('18%'),
  },
  profileImage: isTabDevice()
    ? {
        height: hp('50%'),
      }
    : {
        height: wp('30%'),
        marginBottom: wp('15%'),
      },
  rightViewTeamName: {
    color: colors.white,
    fontSize: wp('4%'),
    fontFamily: 'Poppins-Bold',
    paddingTop: wp('2%'),
    paddingBottom: wp('2%'),
  },
});
export default TeamMatchesContainer;
