import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  FETCH_MATCH_REPORT_TEAMS_FAILED,
  FETCH_MATCH_REPORT_TEAMS_REQUEST,
  FETCH_MATCH_REPORT_TEAMS_SUCCESS,
  MATCH_REPORT_REDUCER_RESET,
  MATCH_REPORT_RESET,
  SET_MATCH_REPORT_PARAMS,
} from '../../../store/actionTypes/matchReport/matchReportActions';
import TeamLeftView from '../../../components/TeamLeftView/TeamLeftView';
import useApi from '../../../hooks/useApi';
import { FOOTBALL_SERVICE } from '../../../constants/services';
import { userRoleType } from '../../../constants/constants';
import { isTabDevice } from '../../../config/appConfig';

const ReportTeamsWrapper = () => {
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [fetchData] = useApi();
  const dispatch = useDispatch();
  const { userData } = useSelector(state => state?.auth);
  const { childInformation } = useSelector(state => state?.common);
  const { teams, teamsPageNo, stopFetchingTeams, teamLoading, filterParams } =
    useSelector(state => state?.matchReport);

  useEffect(() => {
    if (userData) {
      loadTeams();
    }

    return () => {
      dispatch({ type: MATCH_REPORT_REDUCER_RESET });
    };
  }, [userData]);

  useEffect(() => {
    if (teams?.length && teams[0]._id !== selectedTeam?._id) {
      setSelectedTeam(teams[0]);
    }
  }, [teams]);

  useEffect(() => {
    if (selectedTeam?._id) {
      dispatch({
        type: MATCH_REPORT_RESET,
      });
      dispatch({
        type: SET_MATCH_REPORT_PARAMS,
        payload: {
          ...filterParams,
          teamId: selectedTeam?._id,
        },
      });
    }
  }, [selectedTeam]);

  const filterUser = () => {
    switch (userData?.type) {
      case userRoleType.PLAYER:
        return `/api/v1/sport-profiles/${
          userData?.sportsProfileId
        }/teams?page=${teamsPageNo + 1}&size=10`;
      case userRoleType.PARENT:
        return `/api/v1/sport-profiles/${
          childInformation?.sportsProfileId
        }/teams?page=${teamsPageNo + 1}&size=10`;
      default:
        return `/api/v1/teams?coachId=${userData?.id}&page=${
          teamsPageNo + 1
        }&size=10`;
    }
  };

  const loadTeams = () => {
    fetchData(
      filterUser(),
      FETCH_MATCH_REPORT_TEAMS_REQUEST,
      FETCH_MATCH_REPORT_TEAMS_SUCCESS,
      FETCH_MATCH_REPORT_TEAMS_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  return (
    <TeamLeftView
      teamsData={teams}
      isHorizontal={isTabDevice() ? false : true}
      numberOfColumns={isTabDevice() ? 2 : false}
      isLoading={teamLoading}
      selectedTeam={selectedTeam}
      setSelectedTeam={setSelectedTeam}
      isStopOnEndReached={stopFetchingTeams}
      onEndReached={loadTeams}
    />
  );
};

export default ReportTeamsWrapper;
