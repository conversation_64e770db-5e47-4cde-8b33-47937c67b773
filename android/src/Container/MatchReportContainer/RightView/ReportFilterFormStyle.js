import { StyleSheet, Text, View, Dimensions, Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ReportFilterFormStyle = colors => ({
  filterRow: isTabDevice()
    ? {
        flexDirection: 'row',
        zIndex: 5,
      }
    : {
        flexDirection: 'row',
        zIndex: 5,
        marginTop: Platform.OS === 'android' ? wp('2%') : 0,
      },
  calendar: isTabDevice()
    ? {
        backgroundColor: colors.white,
        borderRadius: wp('2%'),
        width: wp('30%'),
        height: wp('27%'),
      }
    : {
        backgroundColor: colors.white,
        borderRadius: wp('2%'),
        width: wp('80%'),
        // height: wp('27%'),
      },
  search: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        width: wp('50%'),
        height: hp('7%'),
        marginRight: wp('2%'),
        borderRadius: wp('1%'),
        shadowOpacity: 0,
      }
    : {
        backgroundColor: colors.borderBlue,
        height: wp('9%'),
        width: wp('60%'),
        marginRight: wp('2%'),
        borderRadius: wp('1%'),
        shadowOpacity: 0,
      },
  filterTitle: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('2%'),
      },
  searchText: isTabDevice()
    ? {
        ...(Platform.OS === 'android'
          ? {
              color: colors.white,
              fontFamily: 'Poppins-Regular',
              fontSize: hp('1.8%'),
            }
          : {
              color: colors.white,
              fontFamily: 'Poppins-Regular',
              fontSize: wp('1.2%'),
            }),
      }
    : {
        ...(Platform.OS === 'android'
          ? {
              color: colors.white,
              fontFamily: 'Poppins-Regular',
              fontSize: wp('3.5%'),
            }
          : {
              color: colors.white,
              fontFamily: 'Poppins-Regular',
              fontSize: wp('3.5%'),
            }),
      },
  datePicker: {},
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  dateRangeWrapper: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        width: wp('17%'),
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        paddingTop: hp('1.9%'),
        paddingBottom: hp('1.9%'),
        height: hp('7%'),
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        width: wp('34%'),
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        height: wp('9%'),
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  dateRangeTextPlaceholderWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dateRangeText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: hp('1.7%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('2.5%'),
        marginTop: wp('1%'),
      },
  dateRangeTextPlaceholder: isTabDevice()
    ? {
        color: colors.grey,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('1.2%'),
        textAlign: 'left',
        width: '88%',
      }
    : {
        color: colors.grey,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('3.5%'),
        textAlign: 'left',
        width: '75%',
      },
  calendarIcon: isTabDevice()
    ? {
        width: wp('1.5%'),
        height: wp('1.5%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'contain',
        marginTop: Platform.OS === 'android' ? wp('0.6%') : 0,
      },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        // height: wp('100%'),
      },
  modalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
  },
  closeButton: isTabDevice()
    ? {
        width: wp('8%'),
        height: wp('4%'),
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1'),
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: wp('2%'),
      }
    : {
        width: wp('25%'),
        height: wp('10%'),
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: wp('2%'),
      },
  closeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Bold',
      },
});
export default ReportFilterFormStyle;
