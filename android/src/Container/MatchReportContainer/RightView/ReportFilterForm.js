import React, { useEffect, useState } from 'react';
import {
  Image,
  Keyboard,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { Searchbar } from 'react-native-paper';

import { useDispatch, useSelector } from 'react-redux';
import DateRangeModal from '../../../components/modal/DaterangeModal/DateRangeModal';
import { dateToYMD, getFirstDateOfMonth } from '../../../helpers';
import useStyles from '../../../hooks/useStyles';
import {
  MATCH_REPORT_RESET,
  SET_MATCH_REPORT_PARAMS,
} from '../../../store/actionTypes/matchReport/matchReportActions';
import customReportFilterFormStyle from './ReportFilterFormStyle';

const ReportFilterForm = () => {
  const ReportFilterFormStyle = useStyles(customReportFilterFormStyle);
  const [keyWord, setKeyWord] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [markedDates, setMarkedDates] = useState({});

  const { filterParams } = useSelector(state => state?.matchReport);
  const dispatch = useDispatch();

  const searchReport = () => {
    dispatch({
      type: MATCH_REPORT_RESET,
    });

    dispatch({
      type: SET_MATCH_REPORT_PARAMS,
      payload: {
        ...filterParams,
        keyWord: keyWord,
        startDate: startDate
          ? new Date(startDate).toISOString()
          : filterParams.startDate,
        endDate: endDate
          ? new Date(endDate)
              .toISOString()
              ?.replace('00:00:00.000Z', '23:59:00.000Z')
          : filterParams.endDate,
      },
    });
  };

  useEffect(() => {
    searchReport();
  }, [startDate, endDate]);

  useEffect(() => {
    if (!keyWord && filterParams?.keyWord) {
      searchReport();
    }
  }, [keyWord, filterParams]);

  const calculateDateRange = date => {
    if (date) {
      if (!startDate) {
        setStartDate(date);
      } else if (startDate.timestamp < date.timestamp) {
        setEndDate(date);
      } else if (startDate && endDate) {
        if (
          startDate.timestamp < date.timestamp &&
          endDate.timestamp > date.timestamp
        ) {
          setStartDate(date);
        } else {
          setMarkedDates({});
          setStartDate(date);
          setEndDate(null);
        }
      }
    }
  };
  return (
    <TouchableWithoutFeedback
      onPress={Keyboard.dismiss}
      touchSoundDisabled={true}
    >
      <View>
        <View style={ReportFilterFormStyle.filterRow}>
          <View style={ReportFilterFormStyle.filterLeft}>
            <Text style={ReportFilterFormStyle.filterTitle}>Search</Text>
            <Searchbar
              placeholder="Keyword"
              placeholderTextColor="#c3c5c8"
              onChangeText={text => setKeyWord(text)}
              value={keyWord}
              iconColor="#36d982"
              inputStyle={ReportFilterFormStyle.searchText}
              style={ReportFilterFormStyle.search}
              onSubmitEditing={() => searchReport()}
              onIconPress={() => searchReport()}
              theme={{ colors: { text: 'white', primary: 'white' } }}
            />
          </View>
          <View style={ReportFilterFormStyle.filterRight}>
            <Text style={ReportFilterFormStyle.filterTitle}>Date Range</Text>
            <TouchableOpacity
              onPress={() => setShowDatePicker(prevState => !prevState)}
              style={ReportFilterFormStyle.dateRangeWrapper}
            >
              {startDate || endDate ? (
                <Text style={ReportFilterFormStyle.dateRangeText}>{`${
                  dateToYMD(startDate) || ''
                } - ${dateToYMD(endDate)}`}</Text>
              ) : (
                <View
                  style={ReportFilterFormStyle.dateRangeTextPlaceholderWrapper}
                >
                  <Text style={ReportFilterFormStyle.dateRangeTextPlaceholder}>
                    Range
                  </Text>
                  <Image
                    style={ReportFilterFormStyle.calendarIcon}
                    source={require('../../../../assets/icons/greenCalendarIcon.png')}
                  />
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>
        <DateRangeModal
          modalVisible={showDatePicker}
          setModalVisible={setShowDatePicker}
          setModalResponse={({ from, to }) => {
            setStartDate(
              from ? new Date(dateToYMD(from)) : new Date(dateToYMD(to))
            );
            setEndDate(
              to ? new Date(dateToYMD(to)) : new Date(dateToYMD(from))
            );
          }}
          subTitle={'Select Date Range'}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ReportFilterForm;
