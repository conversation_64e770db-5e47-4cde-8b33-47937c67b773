import * as Localization from 'expo-localization';
import React, { useEffect, useState } from 'react';
import { FlatList, Linking, Platform, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';
import MatchReportTile from '../../../components/MatchReport/MatchReportTile';
import NoMatchReport from '../../../components/modal/NoMatchReportModal/NoMatchReport';
import NoContentMessage from '../../../components/NoContents/NoContentMessage';
import { isTabDevice } from '../../../config/appConfig';
import { EVENT_SERVICE } from '../../../constants/services';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';

import {
  DOWNLOADING_REPORT,
  FETCH_MATCH_REPORT_FAILED,
  FETCH_MATCH_REPORT_REQUEST,
  FETCH_MATCH_REPORT_SUCCESS,
  FETCH_MATCH_REPORT_URL_FAILED,
  FETCH_MATCH_REPORT_URL_REQUEST,
  FETCH_MATCH_REPORT_URL_SUCCESS,
  FETCH_MATCH_RE_GENERATE_REPORT_FAILED,
  FETCH_MATCH_RE_GENERATE_REPORT_REQUEST,
  FETCH_MATCH_RE_GENERATE_REPORT_SUCCESS,
} from '../../../store/actionTypes/matchReport/matchReportActions';
import customReportFileWrapperStyle from './ReportFileWrapperStyle';

const ReportFileWrapper = () => {
  const ReportFileWrapperStyle = useStyles(customReportFileWrapperStyle);
  const [showNoReportModal, setShowNoReportModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reportUrlFetching, setReportUrlFetching] = useState(false);
  const {
    filterParams,
    reportsLoading,
    reports,
    reportsCurrentPageNo,
    stopFetchingReports,
    matchReportDownloading,
    matchReportUrl,
    matchReportUrlFetching,
  } = useSelector(state => state?.matchReport);

  const dispatch = useDispatch();
  const [fetchReports] = useApi();
  const [fetchReportReGenerate] = useApi();

  useEffect(() => {
    if (filterParams) {
      loadReports(1);
    }
  }, [filterParams]);

  useEffect(() => {
    if (reportUrlFetching && !matchReportUrlFetching && !matchReportUrl) {
      setShowNoReportModal(true);
      setReportUrlFetching(false);
    }
    if (reportUrlFetching && !matchReportUrlFetching && matchReportUrl) {
      callDownloadReport(matchReportUrl);
      setReportUrlFetching(false);
    }
  }, [matchReportUrlFetching]);

  const callDownloadReport = reportUrl => {
    dispatch({
      type: DOWNLOADING_REPORT,
      payload: true,
    });

    downloadReport(reportUrl, selectedReport.name);
  };

  useEffect(() => {
    if (selectedReport) {
      if (
        selectedReport.reportUrl &&
        (Platform.OS === 'android' || Platform.OS === 'ios')
      ) {
        callDownloadReport(selectedReport.reportUrl);
      } else {
        setReportUrlFetching(true);
        fetchReports(
          `/api/v1/matches/${selectedReport._id}/report?preferredTimeZone=${Localization.timezone}`,
          FETCH_MATCH_REPORT_URL_REQUEST,
          FETCH_MATCH_REPORT_URL_SUCCESS,
          FETCH_MATCH_REPORT_URL_FAILED,
          null,
          '',
          'GET',
          null,
          EVENT_SERVICE
        );
      }
    }
  }, [selectedReport]);

  const downloadReport = async (url, fileName) => {
    Linking.openURL(url);
    dispatch({
      type: DOWNLOADING_REPORT,
      payload: false,
    });
  };

  const loadReports = pageNo => {
    if (filterParams?.teamId) {
      const { teamId, startDate, endDate, keyWord } = filterParams;
      let reportRequestUrl = `/api/v1/matches?teamId=${teamId}&page=${pageNo}&size=20`;
      if (startDate && endDate) {
        reportRequestUrl += `&concludeStartDate=${startDate}&concludeEndDate=${endDate}`;
      } else {
        reportRequestUrl += `&concludeStartDate=${new Date(
          null
        ).toISOString()}&concludeEndDate=${new Date().toISOString()}`;
      }
      if (keyWord) {
        reportRequestUrl += `&searchMatchNameLike=${keyWord}`;
      }
      fetchReports(
        reportRequestUrl,
        FETCH_MATCH_REPORT_REQUEST,
        FETCH_MATCH_REPORT_SUCCESS,
        FETCH_MATCH_REPORT_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE,
        {
          filterParams,
        }
      );
    }
  };

  const closeModalAction = () => {
    setShowNoReportModal(false);
    setSelectedReport(null);
  };

  const reportReGenerate = reportId => {
    fetchReportReGenerate(
      `/api/v1/matches/${
        reportId || selectedReport._id
      }/report?preferredTimeZone=${Localization.timezone}`,
      FETCH_MATCH_RE_GENERATE_REPORT_REQUEST,
      FETCH_MATCH_RE_GENERATE_REPORT_SUCCESS,
      FETCH_MATCH_RE_GENERATE_REPORT_FAILED,
      null,
      '',
      'POST',
      null,
      EVENT_SERVICE
    );
    if (!reportId) {
      closeModalAction();
    }
  };

  const renderItem = ({ item }, index) => (
    <MatchReportTile
      key={index}
      item={item}
      handleChange={report => setSelectedReport(report)}
      selected={selectedReport}
      isLoading={matchReportDownloading}
      reportReGenerate={() => reportReGenerate(item._id)}
    />
  );

  return (
    <View style={ReportFileWrapperStyle.fileWrapper}>
      {reportsLoading && reportsCurrentPageNo == 0 ? (
        <ActivitySpinner />
      ) : Array.isArray(reports) && !reports.length ? (
        <NoContentMessage message="No Reports Available" />
      ) : (
        <FlatList
          numColumns={isTabDevice() ? 5 : 3}
          data={reports || []}
          renderItem={renderItem}
          keyExtractor={item => item._id}
          onEndReached={() => {
            if (!stopFetchingReports) {
              loadReports(reportsCurrentPageNo + 1);
            }
          }}
          style={ReportFileWrapperStyle.list}
        />
      )}
      {showNoReportModal && (
        <NoMatchReport
          showModal={true}
          closeModal={() => closeModalAction()}
          reportReGenerate={reportReGenerate}
        />
      )}
    </View>
  );
};

export default ReportFileWrapper;
