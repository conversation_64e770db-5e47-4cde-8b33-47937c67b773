import React from 'react';
import { View, Text } from 'react-native';

import customMatchReportStyle from './MatchReportStyle';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import ReportTeamsWrapper from './LeftView/ReportTeamsWrapper';
import ReportFileWrapper from './RightView/ReportFileWrapper';
import ReportFilterForm from './RightView/ReportFilterForm';
import useStyles from '../../hooks/useStyles';

const MatchReport = () => {
  const MatchReportStyle = useStyles(customMatchReportStyle);
  return (
    <View style={MatchReportStyle.container}>
      <View style={MatchReportStyle.leftView}>
        <ReportTeamsWrapper />
      </View>
      <View style={MatchReportStyle.rightView}>
        <ReportFilterForm />
        <ReportFileWrapper />
      </View>
    </View>
  );
};

export default MatchReport;
