import { Platform, StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { colorPalette } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';
import { isRTL } from 'expo-localization';

const MatchPlanContainerStyles = colors => ({
  container: {
    display: 'flex',
    width: wp('100%'),
  },
  top: {
    flexDirection: 'row',
    width: '100%',
    height: hp('85%'),
    // flex: 1,
  },
  Wrapper: {
    flexDirection: 'column',
    width: '100%',
  },
  innerWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
      }
    : {
        flexDirection: 'column',
        justifyContent: 'space-between',
        height: '100%',
        paddingBottom: hp('10%'),
      },
  scroll: isTabDevice()
    ? {
        width: '100%',
      }
    : {},
  buttons: isTabDevice()
    ? {
        width: hp('30%'),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        width: wp('94%'),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: wp('3%'),
      },
  leftCol: isTabDevice()
    ? {
        width: wp('20%'),
        height: hp('75%'),
      }
    : {
        width: wp('100%'),
        marginBottom: wp('5%'),
      },
  field: isTabDevice()
    ? {
        width: wp('55%'),
        height: hp('75%'),
      }
    : {
        width: wp('100%'),
        height: hp('45%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        marginBottom: wp('5%'),
      },
  rightCol: isTabDevice()
    ? {
        width: wp('20%'),
        height: hp('50%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
      },
  leftColInner: isTabDevice()
    ? {
        paddingLeft: wp('3%'),
        paddingTop: hp('6%'),
      }
    : {
        paddingLeft: wp('3%'),
        // paddingTop: wp('6%'),
        width: '100%',
      },
  rightColInner: isTabDevice()
    ? {
        paddingRight: wp('3%'),
        paddingTop: hp('6%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
        // height: '100%',
      }
    : {
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
        width: '100%',
      },
  dropdown: {
    marginBottom: wp('3%'),
  },
  dropdownWrapper: {
    zIndex: 1,
  },
  dropdownLabel: isTabDevice()
    ? {
        fontSize: wp('1.1%'),
        fontFamily: 'Poppins-Medium',
        color: colors.green,
        // marginBottom: wp('1%'),
        width: hp('20%'),
      }
    : {
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.green,
        marginBottom: wp('1%'),
      },
  addMoreButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: 5,
        elevation: 2,
        width: hp('4%'),
        height: hp('4%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: wp('3.4%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: 5,
        padding: 0,
        elevation: 2,
        width: wp('4%'),
        height: wp('4%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: Platform.OS === 'android' ? wp('-2%') : -2,
        marginLeft: 5,
      },
  submitButton: isTabDevice()
    ? {
        borderRadius: wp('1%'),
        elevation: 2,
        width: hp('13%'),
        height: hp('7%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        borderRadius: wp('3%'),
        elevation: 2,
        width: wp('30%'),
        height: wp('10%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  addMoreText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Regular',
        textAlign: 'center',
        marginTop: wp('-0.3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginTop: wp('-0.3%'),
      },
  submitButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
        fontSize: wp('3.5%'),
      },
  substituteWrapper: {
    width: '100%',
    height: hp('55%'),
  },
  opponentName: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('2%'),
        marginLeft: wp('3%'),
        fontSize: wp('2.5%'),
        width: '100%',
        textAlign: 'left',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('2%'),
        marginLeft: wp('3%'),
        fontSize: wp('5.5%'),
        width: '100%',
        textAlign: 'left',
      },
  deleteIcon: isTabDevice()
    ? {
        width: wp('2.5%'),
        height: wp('2.5%'),
      }
    : {
        width: wp('8%'),
        height: wp('8%'),
      },
  deleteBtn: {},
  substituteSelection: {
    width: wp('20%'),
  },
  substituteSelectionWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('0.3%'),
        width: '100%',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('4%'),
        width: '100%',
      },
  subsituteListScroller: isTabDevice()
    ? {
        height: hp('50%'),
      }
    : {},
  subsituteListWrapper: isTabDevice()
    ? {
        flexGrow: 1,
        marginBottom: hp('10%'),
      }
    : { flexGrow: 1, marginBottom: hp('10%') },

  dropDownWrapper: isTabDevice()
    ? {
        width: '80%',
        marginBottom: 10,
      }
    : {
        width: '90%',
        marginBottom: 10,
      },
  subsProfileImage: isTabDevice()
    ? {
        height: hp('5%'),
        width: hp('5%'),
        borderRadius: wp('100%'),
        marginRight: wp('1%'),
      }
    : {
        height: wp('6%'),
        width: wp('6%'),
        borderRadius: wp('100%'),
        marginRight: wp('2%'),
        backgroundColor: colors.green,
      },
  selectedFormationWrapper: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        padding: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('3%'),
        padding: wp('3%'),
        marginBottom: wp('2%'),
        width: wp('95%'),
      },
  selectedFormation: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('3.5%'),
      },
  selectedMatchWrapper: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        padding: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('3%'),
        padding: wp('3%'),
        marginBottom: wp('2%'),
        width: wp('95%'),
      },
  selectedMatch: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('1.2%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('3.5%'),
        paddingTop: wp('0.5%'),
      },
  selectedSubs: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: wp('2%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: wp('4%'),
      },
  subsName: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
      },
  buttonSave: {
    backgroundColor: colors.aquaBlue,
  },
  buttonCancel: {
    backgroundColor: colors.lightBlue,
  },
  textInput: isTabDevice()
    ? {
      fontSize: wp('1.2%'),
      fontFamily: 'Poppins-Medium',
      color: colors.white,
      height: hp('5%'),
      width: '75%',
      textAlign: 'center',
    }
    : {
      fontSize: wp('3.5%'),
      fontFamily: 'Poppins-Medium',
      color: colors.white,
      textAlign: 'center',
      paddingTop: Platform.OS === 'android' ? wp('0.5%') : 0,
      width: '75%',
    },

  textContainer: isTabDevice()
    ? {
      backgroundColor: colors.semiDarkBlue,
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: wp('1%'),
      height: hp('6%'),
      marginBottom: wp('1%'),
      width: '100%',
    }
    : {
      backgroundColor: colors.tileBackground,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      borderRadius: wp('2%'),
      padding: wp('2%'),
      marginBottom: wp('2%'),
      width: '96%',
    },
  textDivider:
  {
    fontSize: wp('5%'),
    color: colors.white,
    marginBottom: wp('1%'),
  },
  minsText: isTabDevice() ?
    {
      color: colors.white,
      fontFamily: 'Poppins-Regular',
      fontSize: wp('1.2%'),
    } :
    {
      color: colors.white,
      fontFamily: 'Poppins-Regular',
      fontSize: wp('3.5%'),
      paddingTop: wp('0.5%'),
      width: '25%',
      textAlign: 'center'
    },
  errorMessage: isTabDevice()
    ? {
      color: colors.red,
      fontSize: wp('1.3%'),
    }
    : {
      color: colors.red,
      fontSize: wp('3%'),
    },
  dropdownExclamationIcon: isTabDevice()
    ? {
      width: hp('2%'),
      height: hp('2%'),
      position: 'absolute',
      top: 0,
      right: 0,
    }
    : {
      width: 20,
      height: 20,
      position: 'absolute',
      top: 0,
      right: 0,
    },
  playerExclamationIcon: {
    width: 20,
    height: 20,
    position: 'absolute',
    top: 0,
    right: 10,
  },
  subPlayerDropdwnImage: isTabDevice()
    ? {
      width: hp('4%'),
      height: hp('4%'),
      borderRadius: wp('100%'),
    }
    : {
      width: wp('10%'),
      height: wp('10%'),
      borderRadius: wp('100%'),
    },
  notificationToggle: isTabDevice()
    ? {
      flexDirection: 'row',
      alignContent: 'center',
      justifyContent: 'space-even',
      width: '100%',
    }
    : {
      flexDirection: 'row',
      alignContent: 'center',
      justifyContent: 'center',
    },
  notificationText2: isTabDevice()
    ? {
      color: colors.white,
      marginLeft: wp('0.7%'),
      fontSize: hp('2%'),
      marginTop: wp('0.9%'),
    }
    : {
      color: colors.white,
      marginLeft: wp('1%'),
      fontSize: hp('1.5%'),
      marginTop: 16,
    },
});
export default MatchPlanContainerStyles;
