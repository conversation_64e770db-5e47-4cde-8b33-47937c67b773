import { View, FlatList } from 'react-native';
import React, { useEffect } from 'react';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import useGenerateImageUrls from '../../hooks/useGeneratedImageUrls';
import { convertS3UriToObject } from '../../helpers/index';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';

const ProfileImageWrapper = ({ imageStyles, item, index, loading }) => {
  const [getFileObject, preSignedUrl] = useGenerateImageUrls();

  useEffect(() => {
    if (item?.profileImageUrl) {
      const { id } = item;

      const s3OBucketObject = convertS3UriToObject(item.profileImageUrl);

      getFileObject({ s3OBucketObject, id });
    }
  }, [item]);

  return (
    <View>
      {loading ? (
        <View style={imageStyles}>
          <ActivitySpinner />
        </View>
      ) : (
        <ProfileImage
          imageStyles={imageStyles}
          profileImageUrl={preSignedUrl}
        />
      )}
    </View>
  );
};

export default ProfileImageWrapper;
