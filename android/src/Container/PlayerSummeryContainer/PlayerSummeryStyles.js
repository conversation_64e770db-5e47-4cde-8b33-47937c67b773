import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { colorPalette } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';

const playerSummeryStyles = colors => ({
  container: {
    backgroundColor: colors.darkBlue,
    height: hp('100%'),
    width: wp('100%'),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  wrapper: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: hp('90%'),
        width: wp('95%'),
        borderRadius: wp('2%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        height: wp('90%'),
        width: hp('90%'),
        borderRadius: wp('2%'),
        transform: [{ rotate: '90deg' }],
      },
  flatList: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        height: wp('70%'),
      },
  tableHeaderRow: {
    backgroundColor: colors.green,
    paddingLeft: wp('2%'),
    paddingRight: wp('2%'),
    paddingTop: wp('1%'),
    paddingBottom: wp('1%'),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tableHeaderCell: isTabDevice()
    ? {
        width: '10%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        width: '10%',
        height: wp('6%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  tableHeaderText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.1%'),
        fontWeight: 'bold',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        textAlign: 'center',
        width: '100%',
      },
  tableBodyWrapper: isTabDevice()
    ? {
        height: hp('73%'),
      }
    : {},
  tableBodyRow: isTabDevice()
    ? {
        width: wp('95%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        width: hp('90%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: wp('2%'),
  },
  title: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('2%'),
        color: colors.white,
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('3.5%'),
        color: colors.white,
      },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  button: {
    backgroundColor: colors.aquaBlue,
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonTransparent: {
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonText: {
    color: colors.white,
    fontSize: wp('1.5%'),
    fontWeight: 'bold',
  },
  closeButton: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('2%'),
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('4%'),
      },
  buttonWord: {
    color: colors.white,
    fontSize: wp('1.5%'),
    fontWeight: 'bold',
  },
  playerWrapper: { position: 'relative' },
  icon: { position: 'absolute', right: 10, bottom: 10 },
  loaderContainer: isTabDevice()
    ? {
        width: '100%',
        height: '100%',
        paddingBottom: 70,
      }
    : {
        width: '100%',
        height: '100%',
      },
});
export default playerSummeryStyles;
