import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import customPlayerSummeryStyles from './PlayerSummeryStyles';
import useStyles from '../../hooks/useStyles';
import { isTabDevice } from '../../config/appConfig';

const SummerRow = ({ item, summeryTitleArray }) => {
  const playerSummeryStyles = useStyles(customPlayerSummeryStyles);
  return (
    <View style={playerSummeryStyles.tableBodyRow}>
      <View
        style={{
          ...playerSummeryStyles.tableHeaderCell,
          justifyContent: 'flex-start',
        }}
      >
        <Text style={playerSummeryStyles.tableHeaderText}>
          {item.firstName || ''} {item.lastName || ''}
        </Text>
      </View>

      {summeryTitleArray.map(activity => {
        let result = 0;
        let subInOut = { isSubIN: false, isSubOut: false };

        activity.ids &&
          activity.ids.forEach(element => {
            const singleActivity = item.activities.filter(
              item => item._id === element.id
            );

            if (singleActivity.length) {
              const activityTypeResult = singleActivity[0].result;
              result = activityTypeResult;

              if (element.key == 'SUB_IN' && activityTypeResult > 0) {
                subInOut = { ...subInOut, isSubIN: true };
              }

              if (element.key == 'SUB_OUT' && activityTypeResult > 0) {
                subInOut = { ...subInOut, isSubOut: true };
              }
            }
          });
        if (activity.key == 'SUB_IN_OUT') {
          return (
            <View
              style={playerSummeryStyles.tableHeaderCell}
              key={activity?.key}
            >
              {subInOut.isSubOut && (
                <AntDesign
                  color="red"
                  name="arrowdown"
                  size={isTabDevice() ? 30 : 20}
                />
              )}
              {subInOut.isSubIN && (
                <AntDesign
                  color="green"
                  name="arrowup"
                  size={isTabDevice() ? 30 : 20}
                />
              )}
            </View>
          );
        } else {
          return (
            <View
              style={playerSummeryStyles.tableHeaderCell}
              key={activity?.key}
            >
              <Text style={playerSummeryStyles.tableHeaderText}>{result}</Text>
            </View>
          );
        }
      })}
    </View>
  );
};

export default SummerRow;
