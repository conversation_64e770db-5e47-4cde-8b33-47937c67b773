import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

const MatchDayTimeViewStyle = colors => ({
  matchDaySet: isTabDevice()
    ? {
        width: wp('67.4%'),
      }
    : {
        width: wp('100%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  selectedMatchDay: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        borderRadius: wp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        borderRadius: wp('4%'),
      },
  matchDay: {
    padding: wp('1%'),
  },
  matchDayText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
  timeSlotContainer: isTabDevice()
    ? {
        marginTop: wp('1%'),
        backgroundColor: colors.black,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: wp('3%'),
      }
    : {
        marginTop: wp('2%'),
        backgroundColor: colors.black,
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: wp('3%'),
      },
  timeSlotWrapper: isTabDevice()
    ? {
        paddingTop: hp('0.8%'),
        paddingBottom: hp('0.8%'),
      }
    : {
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
      },
  timeSlot: {
    marginRight: wp('3'),
    marginLeft: wp('3'),
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeSlotText: isTabDevice()
    ? {
        fontSize: hp('1.7%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('1.2%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      },
  timeSlotTextDivider: isTabDevice()
    ? {
        marginLeft: wp('1%'),
        marginRight: wp('1%'),
        fontSize: wp('1.5%'),
        color: colors.lightBlue,
        fontFamily: 'Poppins-Regular',
      }
    : {
        marginLeft: wp('1%'),
        marginRight: wp('1%'),
        fontSize: wp('3.5%'),
        color: colors.lightBlue,
        fontFamily: 'Poppins-Regular',
      },
  timeSlotTextSelected: isTabDevice()
    ? {
        fontSize: hp('1.8%'),
        color: colors.green,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
        fontFamily: 'Poppins-Regular',
      },
  timeSlotTimeText: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3%'),
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Regular',
      },
});
export default MatchDayTimeViewStyle;
