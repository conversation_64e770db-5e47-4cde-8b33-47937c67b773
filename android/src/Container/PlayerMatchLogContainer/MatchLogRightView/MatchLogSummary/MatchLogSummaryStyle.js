import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

const MatchLogSummaryStyle = colors => ({
  summaryButton: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: wp('1.5%'),
        borderRadius: wp('1.5%'),
        flexDirection: 'row',
        width: wp('13%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: wp('3.5%'),
        borderRadius: wp('3.5%'),
        flexDirection: 'row',
        width: wp('30%'),
      },
  summaryButtonText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      },
  summaryButtonIcon: isTabDevice()
    ? {
        fontSize: wp('2%'),
      }
    : {
        fontSize: wp('4%'),
      },
});
export default MatchLogSummaryStyle;
