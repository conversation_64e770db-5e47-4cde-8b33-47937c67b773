import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import customMatchLogSummaryStyle from './MatchLogSummaryStyle';
import ModalWrapper from '../../../../components/modal/ModalWrapper/ModalWrapper';
import PlayerSummeryContainer from '../../../PlayerSummeryContainer/PlayerSummeryContainer';
import MatchPlanNotReadyModal from '../../../../components/modal/MatchPlanNotReadyModal/MatchPlanNotReadyModal';
import { useSelector } from 'react-redux';
import useStyles from '../../../../hooks/useStyles';
import useColors from '../../../../hooks/useColors';
import useApi from '../../../../hooks/useApi';
import {
  MATCH_LOG_ACTIVITIES_FAILED,
  MATCH_LOG_ACTIVITIES_REQUEST,
  MATCH_LOG_ACTIVITIES_SUCCESS,
  MATCH_LOG_MORE_ACTIVITIES_FAILED,
  MATCH_LOG_MORE_ACTIVITIES_REQUEST,
  MATCH_LOG_MORE_ACTIVITIES_SUCCESS,
} from '../../../../store/actionTypes/MatchLog/MatchLogActions';
import { FOOTBALL_SERVICE } from '../../../../constants/services';

const MatchLogSummary = () => {
  const MatchLogSummaryStyle = useStyles(customMatchLogSummaryStyle);
  const colors = useColors();
  const [showSummeryModal, setShowSummeryModal] = useState(false);
  const [showMatchPlanNotReady, setShowMatchPlanNotReady] = useState(false);
  const [fetchActivities] = useApi();

  const { activitiesPage, activitiesLoading } = useSelector(
    state => state?.matchLog
  );

  useEffect(() => {
    fetchActivities(
      `/api/v1/match-activities?page=1&size=50`,
      MATCH_LOG_ACTIVITIES_REQUEST,
      MATCH_LOG_ACTIVITIES_SUCCESS,
      MATCH_LOG_ACTIVITIES_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  }, []);

  useEffect(() => {
    activitiesPage &&
      !activitiesLoading &&
      fetchActivities(
        `/api/v1/match-activities?page=${activitiesPage + 1}&size=50`,
        MATCH_LOG_MORE_ACTIVITIES_REQUEST,
        MATCH_LOG_MORE_ACTIVITIES_SUCCESS,
        MATCH_LOG_MORE_ACTIVITIES_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
  }, [activitiesPage]);

  const { currentMatch, matchPlan } = useSelector(
    state => state?.playerMatchLog
  );

  return (
    <>
      <TouchableOpacity
        onPress={() =>
          matchPlan?.isReady
            ? setShowSummeryModal(true)
            : setShowMatchPlanNotReady(true)
        }
        style={MatchLogSummaryStyle.summaryButton}
      >
        <Text style={MatchLogSummaryStyle.summaryButtonText}>Summary</Text>
        <Ionicons
          name="clipboard"
          size={20}
          color={colors.white}
          style={MatchLogSummaryStyle.summaryButtonIcon}
        />
      </TouchableOpacity>
      {showSummeryModal && currentMatch?._id && (
        <ModalWrapper visible>
          <PlayerSummeryContainer
            selectedEvent={currentMatch}
            setShowSummeryModal={setShowSummeryModal}
          />
        </ModalWrapper>
      )}
      <MatchPlanNotReadyModal
        showModal={showMatchPlanNotReady}
        closeModal={() => setShowMatchPlanNotReady(false)}
      />
    </>
  );
};

export default MatchLogSummary;
