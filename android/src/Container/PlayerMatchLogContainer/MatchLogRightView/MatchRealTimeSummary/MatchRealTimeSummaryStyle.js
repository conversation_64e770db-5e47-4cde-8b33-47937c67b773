import { StyleSheet, Text, View, Dimensions } from 'react-native';
import { Platform } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

const MatchRealTimeSummaryStyle = colors => ({
  matchActionCommentContainer: isTabDevice()
    ? {
        width: '100%',
        borderBottomWidth: 1,
        borderBottomColor: colors.lightBlue,
        paddingBottom: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        borderRightWidth: 1,
        borderRightColor: colors.lightBlue,
        paddingRight: wp('1%'),
        marginRight: wp('1%'),
      },
  matchActionCommentTime: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.3%'),
        fontWeight: 'bold',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('2.3%'),
        fontWeight: 'bold',
        marginBottom: wp('0.5%'),
      },
  matchActionComment: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.3%'),
        fontWeight: 'bold',
      },
  matchCommentContainerWrapper: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: hp('3%'),
        width: '100%',
        height: hp('41.5%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        padding: wp('3%'),
        width: '100%',
        height: Platform.OS === 'android' ? wp('14%') : wp('12%'),
        marginBottom: wp('2%'),
        paddingBottom: Platform.OS === 'android' ? wp('2%') : wp('0'),
      },
});
export default MatchRealTimeSummaryStyle;
