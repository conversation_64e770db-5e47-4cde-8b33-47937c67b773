import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import ActivitySpinner from '../../../../components/ActivitySpinner/ActivitySpinner';
import { EVENT_SERVICE } from '../../../../constants/services';
import useApi from '../../../../hooks/useApi';
import {
  PLAYER_MATCH_LOG_SCORE_FAILED,
  PLAYER_MATCH_LOG_SCORE_REQUEST,
  PLAYER_MATCH_LOG_SCORE_SUCCESS,
} from '../../../../store/actionTypes/MatchLog/PlayerMatchLogActions';
import useStyles from '../../../../hooks/useStyles';

import customMatchScoreViewStyle from './MatchScoreViewStyle';

const MatchScoreView = () => {
  const MatchScoreViewStyle = useStyles(customMatchScoreViewStyle);
  const [fetchScoreData] = useApi();
  const [currentTimer, setCurrentTimer] = useState('00:00');
  const [pauseTimer, setPauseTimer] = useState(false);
  const { currentMatch, matchScore, matchActivity, matchScoreLoading } =
    useSelector(state => state?.playerMatchLog);

  useEffect(() => {
    if (currentMatch?._id) {
      setCurrentTimer('00:00');

      fetchScoreData(
        `/api/v1/matches/${currentMatch._id}/match-scorecard`,
        PLAYER_MATCH_LOG_SCORE_REQUEST,
        PLAYER_MATCH_LOG_SCORE_SUCCESS,
        PLAYER_MATCH_LOG_SCORE_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  }, [currentMatch]);

  useEffect(() => {
    let interval;

    if (!pauseTimer && currentTimer !== '00:00') {
      interval = setInterval(() => {
        let timeArray = currentTimer.split(':');
        let seconds = timeToSeconds(timeArray);
        seconds++;
        setCurrentTimer(secondsToTime(seconds));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [currentTimer]);

  useEffect(() => {
    if (matchActivity?.length && matchActivity[0].elapsedTime) {
      const activityCode = matchActivity[0].activity?.activityCode;
      // If match finished
      if (activityCode === 'END_GAME') {
        const elapsedTime = matchActivity[0].elapsedTime.split(' ')[0];
        setPauseTimer(true);
        setCurrentTimer(elapsedTime);
      } else if (activityCode === 'FIRST_HALF_END') {
        // If 1st half break
        const elapsedTime = matchActivity[0].elapsedTime.split(' ')[0];
        setCurrentTimer(elapsedTime);
        setPauseTimer(true);
      } else if (activityCode === 'SECOND_HALF_START') {
        // If 2nd half started
        const elapsedTime = matchActivity[0].elapsedTime.split(' ')[0];
        setCurrentTimer(elapsedTime);
        setPauseTimer(false);
      } else {
        let lastActivtityTime = new Date(matchActivity[0].timeStamp);
        let currentTime = new Date();

        const elapsedTime = matchActivity[0].elapsedTime.split(' ')[0];
        const elapsedMinutes = elapsedTime.split(':')[0];
        const elapsedSeconds = elapsedTime.split(':')[1];

        const totalElapsedTime =
          parseInt(elapsedMinutes) * 60 + parseInt(elapsedSeconds);

        let diffInSeconds =
          (currentTime.getTime() - lastActivtityTime.getTime()) / 1000;

        diffInSeconds = diffInSeconds + totalElapsedTime;

        setPauseTimer(false);
        setCurrentTimer(
          [diffInSeconds / 60, diffInSeconds % 60].map(Math.floor).join(':')
        );
      }
    }
    if (matchActivity?.length === 0) {
      setCurrentTimer('00:00');
    }
  }, [JSON.stringify(matchActivity)]);

  const timeToSeconds = timeArray => {
    let minutes = timeArray[0] * 1;
    let seconds = minutes * 60 + timeArray[1] * 1;

    return seconds;
  };

  const secondsToTime = secs => {
    // Caluculating hours
    let hours = Math.floor(secs / (60 * 60));
    hours = hours < 10 ? '0' + hours : hours;

    // Caluculating minutes
    let divisor_for_minutes = secs % (60 * 60);
    let minutes = Math.floor(divisor_for_minutes / 60);

    //add hours to minites
    minutes = minutes + hours * 60;
    minutes = minutes < 10 ? '0' + minutes : minutes;

    // Calculating seconds
    let divisor_for_seconds = divisor_for_minutes % 60;
    let seconds = Math.ceil(divisor_for_seconds);
    seconds = seconds < 10 ? '0' + seconds : seconds;

    return minutes + ':' + seconds;
  };

  return (
    <View style={MatchScoreViewStyle.wrapper}>
      {!matchScoreLoading ? (
        <View style={MatchScoreViewStyle.container}>
          <View style={MatchScoreViewStyle.TeamWrapper}>
            <Text style={MatchScoreViewStyle.TeamText}>
              {currentMatch?.team?.name}
            </Text>
            <View style={MatchScoreViewStyle.TeamScore}>
              <Text style={MatchScoreViewStyle.TeamScoreText}>
                {matchScore?.score || 0}
              </Text>
            </View>
          </View>
          <View style={MatchScoreViewStyle.matchTime}>
            <Text style={MatchScoreViewStyle.matchTimeText}>
              {currentTimer}
            </Text>
          </View>
          <View style={MatchScoreViewStyle.TeamWrapper}>
            <Text style={MatchScoreViewStyle.TeamText}>
              {currentMatch?.opponent?.name}
            </Text>
            <View style={MatchScoreViewStyle.TeamScore}>
              <Text style={MatchScoreViewStyle.TeamScoreText}>
                {matchScore?.opponentScore || 0}
              </Text>
            </View>
          </View>
        </View>
      ) : (
        <ActivitySpinner />
      )}
    </View>
  );
};

export default MatchScoreView;
