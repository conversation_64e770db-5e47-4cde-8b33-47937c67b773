import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

const MatchScoreViewStyle = colors => ({
  wrapper: {
    backgroundColor: colors.tileBackground,
    display: 'flex',
    flexDirection: 'row',
    borderRadius: wp('2%'),
    padding: hp('2%'),
  },
  container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  TeamWrapper: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '30%',
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '30%',
      },
  TeamScore: isTabDevice()
    ? {
        backgroundColor: colors.midDarkBlue,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: wp('7%'),
        height: hp('8%'),
        borderRadius: wp('1%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,

        elevation: 5,
      }
    : {
        backgroundColor: colors.midDarkBlue,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: wp('20%'),
        height: wp('20%'),
        borderRadius: wp('2%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,

        elevation: 5,
      },
  TeamScoreText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('5%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('15%'),
        fontFamily: 'Poppins-Bold',
      },
  TeamText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
        width: wp('10%'),
        textAlign: 'center',
        flexDirection: 'row',
        flexWrap: 'wrap',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        width: '100%',
        textAlign: 'center',
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginBottom: wp('2%'),
      },
  matchTime: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1%'),
        width: '30%',
        height: hp('8%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1%'),
        width: '20%',
        height: wp('6%'),
      },
  matchTimeText: isTabDevice()
    ? {
        fontSize: wp('1.9%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.7%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
export default MatchScoreViewStyle;
