import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

const MatchFormationViewStyle = colors => ({
  matchFormationTitleWrapper: {
    // marginBottom: wp('1%'),
  },

  matchFormationTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        textAlign: 'center',
        marginBottom: wp('2%'),
      },

  matchFormationText: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.2%'),
        textAlign: 'center',
      }
    : {
        color: colors.green,
        fontSize: wp('2.5%'),
        textAlign: 'center',
      },

  matchFormationField: isTabDevice()
    ? {
        width: '100%',
        height: hp('40%'),
        marginTop: hp('1%'),
        marginBottom: hp('1%'),
      }
    : {
        width: '100%',
        height: '60%',
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
      },
  matchFormationSubsWrapper: isTabDevice()
    ? {
        height: hp('12%'),
      }
    : {},
  matchsubstituteTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.8%'),
        textAlign: 'center',
        marginBottom: hp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        textAlign: 'center',
        marginBottom: wp('2%'),
      },

  substituteProfileImage: isTabDevice()
    ? {
        width: hp('7%'),
        height: hp('7%'),
        borderRadius: 25,
      }
    : {
        width: wp('12%'),
        height: wp('12%'),
        borderRadius: wp('100%'),
        marginLeft: wp('3%'),
      },

  matchFormationWrapper: isTabDevice()
    ? {
        width: '100%',
        height: hp('65%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.veryDarkBlue,
        padding: hp('1.5%'),
        marginTop: -hp('2%'),
      }
    : {
        width: '100%',
        height: hp('55%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.veryDarkBlue,
        padding: wp('2%'),
      },
  jerseyCircle: isTabDevice()
    ? {
        width: wp('1.7%'),
        height: wp('1.7%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.white,
        borderColor: colors.white,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 10,
        bottom: 0,
        zIndex: 10,
      }
    : {
        width: wp('4.0%'),
        height: wp('4.0%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.white,
        borderColor: colors.white,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 0,
        bottom: 0,
        zIndex: 10,
      },

  jerseyCircleMatchText: isTabDevice()
    ? {
        color: colors.black,
        fontSize: wp('0.9%'),
        fontFamily: 'Poppins-Thin',
      }
    : {
        color: colors.black,
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Thin',
      },
});
export default MatchFormationViewStyle;
