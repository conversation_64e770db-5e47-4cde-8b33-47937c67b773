import React, { useEffect, useState } from 'react';
import { View, Text, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import ActivitySpinner from '../../../../components/ActivitySpinner/ActivitySpinner';
import Field from '../../../../components/MatchPlan/Field';
import {
  EVENT_SERVICE,
  FOOTBALL_SERVICE,
} from '../../../../constants/services';
import useApi from '../../../../hooks/useApi';
import {
  PLAYER_MATCH_LOG_FORMATIONS_FAILED,
  PLAYER_MATCH_LOG_FORMATIONS_REQUEST,
  PLAYER_MATCH_LOG_FORMATIONS_SUCCESS,
  PLAYER_MATCH_LOG_MATCH_PLAN_FAILED,
  PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST,
  PLAYER_MATCH_LOG_MATCH_PLAN_RESET,
  PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS,
} from '../../../../store/actionTypes/MatchLog/PlayerMatchLogActions';
import { useJerseyNumberHook } from '../../../../hooks/PlannerAPIHook/useJerseyNumberHook';

import customMatchFormationViewStyle from './MatchFormationViewStyle';
import useStyles from '../../../../hooks/useStyles';
import ProfileImage from '../../../../components/ProfileImage/ProfileImage';
import { SET_CURRENT_MATCH_PLAN } from '../../../../store/actionTypes/MatchLog/MatchLogActions';

const MatchFormationView = () => {
  const MatchFormationViewStyle = useStyles(customMatchFormationViewStyle);
  const [fetchData] = useApi();
  const [fetchFormationData] = useApi();
  const dispatch = useDispatch();
  const [jerseyNumbers, setJerseyNumbers] = useState(null);
  const [fetchJerseyNumbers] = useJerseyNumberHook();

  const { matchPlan, matchPlanLoading, currentMatch, matchFormation } =
    useSelector(state => state?.playerMatchLog);

  const { currentMatchPlan } = useSelector(state => state.matchLog);

  useEffect(() => {
    dispatch({
      type: SET_CURRENT_MATCH_PLAN,
      payload: matchPlan,
    });
  }, [JSON.stringify(matchPlan)]);

  useEffect(() => {
    if (currentMatch?._id) {
      dispatch({
        type: PLAYER_MATCH_LOG_MATCH_PLAN_RESET,
      });

      fetchData(
        `/api/v1/matches/${currentMatch._id}/match-plan`,
        PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST,
        PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS,
        PLAYER_MATCH_LOG_MATCH_PLAN_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    }
  }, [currentMatch]);

  useEffect(() => {
    if (matchPlan?.formationId) {
      fetchFormationData(
        `/api/v1/formations/${matchPlan.formationId}`,
        PLAYER_MATCH_LOG_FORMATIONS_REQUEST,
        PLAYER_MATCH_LOG_FORMATIONS_SUCCESS,
        PLAYER_MATCH_LOG_FORMATIONS_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
    }
    const matchPlanPlayersSportsProfileId =
      matchPlan?.playerCoordinates?.map(
        data => data?.playerData?.sportsProfileId
      ) || [];
    const matchPlanSubstitutesSportsProfileId =
      matchPlan?.substitutePlayers?.map(data => data?.sportsProfileId) || [];
    const allSportsProfileIds = [
      ...matchPlanPlayersSportsProfileId,
      ...matchPlanSubstitutesSportsProfileId,
    ].filter(sportsProfileId => !!sportsProfileId);

    if (!matchPlanLoading && allSportsProfileIds?.length) {
      setAllJerseyNumbers(allSportsProfileIds);
    }
  }, [matchPlan]);

  const setAllJerseyNumbers = async allSportsProfileIds => {
    const allJerseyNumbers = await fetchJerseyNumbers(
      allSportsProfileIds,
      currentMatch?.teamId
    );
    setJerseyNumbers(allJerseyNumbers);
  };

  const renderSubstitutes = ({ item }) => {
    return (
      <>
        <View style={MatchFormationViewStyle.jerseyCircle}>
          <Text style={MatchFormationViewStyle.jerseyCircleMatchText}>
            {jerseyNumbers?.find(
              data => data?.sportsProfileId === item?.sportsProfileId
            )?.jersyNo || ''}
          </Text>
        </View>
        <ProfileImage
          style={MatchFormationViewStyle.substituteProfileImage}
          profileImageUrl={item.profileImageUrl}
          imageStyles={MatchFormationViewStyle.substituteProfileImage}
        />
      </>
    );
  };

  return (
    <View style={MatchFormationViewStyle.matchFormationWrapper}>
      <View style={MatchFormationViewStyle.matchFormationTitleWrapper}>
        <Text style={MatchFormationViewStyle.matchFormationTitle}>
          Match Formation
        </Text>
        <Text style={MatchFormationViewStyle.matchFormationText}>
          {matchFormation?.name}
        </Text>
      </View>
      {matchPlanLoading ? (
        <ActivitySpinner />
      ) : (
        <>
          <View style={MatchFormationViewStyle.matchFormationField}>
            <Field
              jerseyNumbers={jerseyNumbers}
              isMatchLogView
              matchPlan={currentMatchPlan || []}
              playerCoordinates={currentMatchPlan?.playerCoordinates || []}
              openCandidatePlayerModal={() => {}}
            />
          </View>
          <View style={MatchFormationViewStyle.matchFormationSubsWrapper}>
            <Text style={MatchFormationViewStyle.matchsubstituteTitle}>
              Substitutes
            </Text>
            <FlatList
              horizontal
              data={currentMatchPlan?.substitutePlayers || []}
              renderItem={renderSubstitutes}
              keyExtractor={item => item.sportsProfileId}
            />
          </View>
        </>
      )}
    </View>
  );
};

export default MatchFormationView;
