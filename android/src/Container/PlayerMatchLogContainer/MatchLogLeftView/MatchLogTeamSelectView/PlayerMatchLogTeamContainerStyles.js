import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

const PlayerMatchLogTeamContainerStyles = colors => ({
  teamWrapper: isTabDevice()
    ? {
        borderBottomColor: colors.tileBackground,
        borderBottomWidth: 1,
        marginTop: wp('1%'),
        marginBottom: wp('2%'),
      }
    : {
        borderBottomColor: colors.tileBackground,
        borderBottomWidth: 1,
        marginTop: wp('1%'),
        marginBottom: wp('2%'),
        width: wp('50%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  team: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('7%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('2%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('47%'),
        height: wp('15%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('2%'),
      },
  teamSelected: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('7%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('2%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        width: wp('47%'),
        height: wp('15%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('2%'),
      },
  teamText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
export default PlayerMatchLogTeamContainerStyles;
