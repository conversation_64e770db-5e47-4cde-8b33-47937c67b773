import React, { useEffect, useRef, useState } from 'react';
import { Text, View, TouchableOpacity, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import {
  EVENT_SERVICE,
  FOOTBALL_SERVICE,
} from '../../../../constants/services';
import useApi from '../../../../hooks/useApi';
import {
  PLAYER_MATCH_LOG_EVENTS_FAILED,
  PLAYER_MATCH_LOG_EVENTS_REQUEST,
  PLAYER_MATCH_LOG_EVENTS_SUCCESS,
  PLAYER_MATCH_LOG_RESET,
  FETCH_PLAYER_MATCH_LOG_TEAMS_REQUEST,
  FETCH_PLAYER_MATCH_LOG_TEAMS_SUCCESS,
  FETCH_PLAYER_MATCH_LOG_TEAMS_FAILED,
  PLAYER_MATCH_LOG_ALL_RESET,
} from '../../../../store/actionTypes/MatchLog/PlayerMatchLogActions';
import customPlayerMatchLogTeamContainerStyles from './PlayerMatchLogTeamContainerStyles';
import useStyles from '../../../../hooks/useStyles';
import { userRoleType } from '../../../../constants/constants';

const PlayerMatchLogTeamContainer = () => {
  const PlayerMatchLogTeamContainerStyles = useStyles(
    customPlayerMatchLogTeamContainerStyles
  );
  const scrollIndexRef = useRef(null);
  const [lengthOfChip, setLengthOfChip] = useState(0);
  const [selectedTeam, setSelectedTeam] = useState(null);
  const [selectedTeamIndex, setSelectedTeamIndex] = useState(0);
  const [fetchData] = useApi();
  const dispatch = useDispatch();
  const { userData, userRole } = useSelector(state => state?.auth);
  const {
    matchesLoading,
    teams,
    teamsPageNo,
    stopFetchingTeams,
    teamsTotalRecords,
  } = useSelector(state => state?.playerMatchLog);

  const { childInformation } = useSelector(state => state?.common);
  const { selectedMatchDetails } = useSelector(state => state.matchPlan);
  useEffect(() => {
    if (userData?.sportsProfileId || childInformation?.sportsProfileId) {
      loadTeams(1, 6);
    }

    return () => {
      dispatch({ type: PLAYER_MATCH_LOG_ALL_RESET });
    };
  }, [userData, childInformation]);

  useEffect(() => {
    if (teams?.length && teams[0]._id !== selectedTeam?._id) {
      const selectedIndex =
        (selectedMatchDetails?.team?._id &&
          teams.findIndex(
            team => selectedMatchDetails?.team?._id == team._id
          )) ||
        0;

      const selectedTeam = teams[selectedIndex];
      setSelectedTeamIndex(selectedIndex);
      setSelectedTeam(selectedTeam);
    }
  }, [teams]);

  useEffect(() => {
    if (selectedTeamIndex > 0 && lengthOfChip > 0) {
      teams && scrollIndexRef && scrollToIndex(selectedTeamIndex, teams.length);
    }
  }, [selectedTeamIndex, lengthOfChip]);

  const scrollToIndex = (index, length) => {
    scrollIndexRef?.current?.scrollToIndex({
      index: index,
    });
  };

  useEffect(() => {
    if (selectedTeam?._id) {
      dispatch({
        type: PLAYER_MATCH_LOG_RESET,
      });

      fetchData(
        `/api/v1/events?teamId=${
          selectedTeam._id
        }&type=MATCH&startDate=${new Date(
          new Date().setDate(new Date().getDate() - 1)
        ).toISOString()}&endDate=${new Date(
          new Date().setDate(new Date().getDate() + 30)
        ).toISOString()}&page=1&size=20`,
        PLAYER_MATCH_LOG_EVENTS_REQUEST,
        PLAYER_MATCH_LOG_EVENTS_SUCCESS,
        PLAYER_MATCH_LOG_EVENTS_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  }, [selectedTeam]);

  const loadTeams = (pageNo, pageSize) => {
    fetchData(
      `/api/v1/sport-profiles/${
        userRole === userRoleType.PARENT
          ? childInformation?.sportsProfileId
          : userData?.sportsProfileId
      }/teams?page=${pageNo}&size=${pageSize}`,
      FETCH_PLAYER_MATCH_LOG_TEAMS_REQUEST,
      FETCH_PLAYER_MATCH_LOG_TEAMS_SUCCESS,
      FETCH_PLAYER_MATCH_LOG_TEAMS_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const renderItem = ({ item }) => {
    return (
      <View style={PlayerMatchLogTeamContainerStyles.teamWrapper}>
        <TouchableOpacity
          onLayout={event => {
            const { width } = event.nativeEvent.layout;
            !lengthOfChip && setLengthOfChip(width);
          }}
          onPress={() => !matchesLoading && setSelectedTeam(item)}
        >
          <View
            style={
              selectedTeam?._id === item?._id
                ? PlayerMatchLogTeamContainerStyles.teamSelected
                : PlayerMatchLogTeamContainerStyles.team
            }
          >
            <Text style={PlayerMatchLogTeamContainerStyles.teamText}>
              {item?.teamName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <FlatList
      horizontal
      ref={scrollIndexRef}
      extraData={[selectedTeam, matchesLoading]}
      data={teams || []}
      renderItem={renderItem}
      keyExtractor={(item, index) => index.toString()}
      getItemLayout={(data, index) => ({
        length: lengthOfChip,
        offset: lengthOfChip * index,
        index,
      })}
      onEndReached={() => {
        if (teamsTotalRecords != teams?.length) {
          loadTeams(teamsPageNo + 1, 6);
        }
      }}
    />
  );
};

export default PlayerMatchLogTeamContainer;
