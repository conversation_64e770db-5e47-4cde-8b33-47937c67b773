import { EvilIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import { isTabDevice } from '../../config/appConfig';
import { MatchLogActions } from '../../constants/constants';
import { EVENT_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import { CLEAR_MATCH_SOCKET_MESSAGES } from '../../store/actionTypes/MatchLog/MatchLogActions';
import {
  PLAYER_MATCH_LOG_ACTIVITIES_FAILED,
  PLAYER_MATCH_LOG_ACTIVITIES_REQUEST,
  PLAYER_MATCH_LOG_ACTIVITIES_RESET,
  PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS,
  PLAYER_MATCH_LOG_MATCH_PLAN_FAILED,
  PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST,
  PLAYER_MATCH_LOG_MATCH_PLAN_RESET,
  PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS,
  PLAYER_MATCH_LOG_SCORE_FAILED,
  PLAYER_MATCH_LOG_SCORE_REQUEST,
  PLAYER_MATCH_LOG_SCORE_SUCCESS,
  PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_SUCCESS,
  PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_REVERSE,
  UPDATE_PLAYER_MATCH_SCORE_SOCKET,
} from '../../store/actionTypes/MatchLog/PlayerMatchLogActions';
import HandleMatchWs from '../SocketMatchContainers/HandleMatchWs';
import HandleMatchFormation from '../SocketMatchContainers/HandleMatchFormation';
import SocketMatchLogContainer from '../SocketMatchContainers/SocketMatchLogContainer';
import MatchFormationView from './MatchLogLeftView/MatchFormationView/MatchFormationView';
import PlayerMatchLogTeamContainer from './MatchLogLeftView/MatchLogTeamSelectView/PlayerMatchLogTeamContainer';
import MatchDayTimeView from './MatchLogRightView/MatchDayTimeView/MatchDayTimeView';
import MatchLogSummary from './MatchLogRightView/MatchLogSummary/MatchLogSummary';
import MatchRealTimeSummary from './MatchLogRightView/MatchRealTimeSummary/MatchRealTimeSummary';
import MatchScoreView from './MatchLogRightView/MatchScoreView/MatchScoreView';
import customPlayerMatchLogScreenStyles from './PlayerMatchLogScreenStyles';
import { useNetInfo } from '@react-native-community/netinfo';

const PlayerMatchLogScreen = () => {
  const PlayerMatchLogScreenStyles = useStyles(
    customPlayerMatchLogScreenStyles
  );
  const dispatch = useDispatch();
  const LayoutStyles = useStyles(customLayoutStyles);
  const {
    currentMatch,
    matchesLoading,
    matches,
    teams,
    teamsLoading,
    matchActivityLoading,
  } = useSelector(state => state?.playerMatchLog);
  const [fetchData] = useApi();
  const [fetchScoreData] = useApi();
  const [fetchLogs] = useApi();

  const { activities = [] } = useSelector(state => state?.matchLog);
  const { isAppOnActive } = useSelector(state => state.common);

  const netInfo = useNetInfo();
  const isInternetReachable = netInfo?.isInternetReachable || false;

  const updateScore = (isOurs, isAdd) => {
    dispatch({
      type: UPDATE_PLAYER_MATCH_SCORE_SOCKET,
      payload: {
        isOurs,
        isAdd,
      },
    });
  };

  const getActionById = actionId => {
    return activities?.find(activity => activity._id === actionId);
  };

  const onItemRemoveFromSocket = (msgObject, index) => {
    const action = getActionById(msgObject?.activityId);
    const { code } = action || {};

    if (code === MatchLogActions.GOAL_SCORED) {
      if (msgObject?.performedByOpponent) {
        updateScore(false, false);
      } else {
        updateScore(true, false);
      }
    }

    if (code === MatchLogActions.GOAL_CONCEDED) {
      if (msgObject?.performedByOpponent) {
        updateScore(true, false);
      } else {
        updateScore(false, false);
      }
    }
    dispatch({
      type: PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_REVERSE,
      payload: index,
    });
  };

  const onActionButtonClicked = (msgObject, code) => {
    if (code === MatchLogActions.GOAL_SCORED) {
      if (msgObject?.performedByOpponent) {
        updateScore(false, true);
      } else {
        updateScore(true, true);
      }
    } else if (code === MatchLogActions.GOAL_CONCEDED) {
      if (msgObject?.performedByOpponent) {
        updateScore(true, true);
      } else {
        updateScore(false, true);
      }
    }

    let addData = {
      _id: msgObject?._id,
      matchId: msgObject?.matchId,
      timeStamp: msgObject?.timeStamp,
      activityId: msgObject?.activityId,
      comment: msgObject?.comment,
      type: msgObject?.type,
      elapsedTime: msgObject?.elapsedTime,
      performedByOpponent: msgObject?.performedByOpponent,
      socketMessageType: msgObject?.socketMessageType,
    };

    dispatch({
      type: PLAYER_MATCH_LOG_SOCKET_ACTIVITIES_SUCCESS,
      payload: addData,
    });
  };

  const handleSocketMessage = (msgObject, code) => {
    onActionButtonClicked(msgObject, code);
  };

  const onLoadFormation = () => {
    if (currentMatch?._id) {
      dispatch({
        type: PLAYER_MATCH_LOG_MATCH_PLAN_RESET,
      });
      fetchData(
        `/api/v1/matches/${currentMatch._id}/match-plan`,
        PLAYER_MATCH_LOG_MATCH_PLAN_REQUEST,
        PLAYER_MATCH_LOG_MATCH_PLAN_SUCCESS,
        PLAYER_MATCH_LOG_MATCH_PLAN_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    }
  };

  const onLoadScore = () => {
    currentMatch?._id &&
      fetchScoreData(
        `/api/v1/matches/${currentMatch._id}/match-scorecard`,
        PLAYER_MATCH_LOG_SCORE_REQUEST,
        PLAYER_MATCH_LOG_SCORE_SUCCESS,
        PLAYER_MATCH_LOG_SCORE_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
  };

  const onLoadLogs = () => {
    dispatch({
      type: PLAYER_MATCH_LOG_ACTIVITIES_RESET,
    });
    currentMatch?._id &&
      fetchLogs(
        `/api/v1/matches/${currentMatch._id}/match-activities?page=1&size=200`,
        PLAYER_MATCH_LOG_ACTIVITIES_REQUEST,
        PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS,
        PLAYER_MATCH_LOG_ACTIVITIES_FAILED,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
  };

  const [hideLoading, setHideLoading] = useState(false);

  const handleRefresh = () => {
    onLoadFormation();
    onLoadScore();
    onLoadLogs();
  };

  const handleCustomRefresh = () => {
    setHideLoading(true);
    onLoadScore();
    onLoadLogs();
  };

  useEffect(() => {
    isAppOnActive && handleRefresh();
  }, [isAppOnActive]);

  useEffect(() => {
    isInternetReachable && handleRefresh();
  }, [isInternetReachable]);

  useEffect(() => {
    !matchActivityLoading && setHideLoading(false);
  }, [matchActivityLoading]);

  const renderRefreshBtn = () => (
    <TouchableOpacity
      onPress={handleRefresh}
      style={PlayerMatchLogScreenStyles.button}
    >
      <EvilIcons name="refresh" size={isTabDevice() ? 50 : 27} color="white" />
      <Text style={PlayerMatchLogScreenStyles.buttonText}>Refresh Now</Text>
    </TouchableOpacity>
  );

  return (
    <>
      <HandleMatchFormation />
      <HandleMatchWs matchId={currentMatch?._id || ''} />
      <SocketMatchLogContainer
        onPlayerActionClicked={handleSocketMessage}
        matchId={currentMatch?._id}
        deleteLogsForMatch={onItemRemoveFromSocket}
        onRefreshHandler={handleCustomRefresh}
      />
      {isTabDevice() ? (
        <ScreenWrapper>
          <View style={LayoutStyles.contentWrapper}>
            <View style={PlayerMatchLogScreenStyles.container}>
              <View style={PlayerMatchLogScreenStyles.leftView}>
                <View style={PlayerMatchLogScreenStyles.teamsContainer}>
                  <PlayerMatchLogTeamContainer />
                </View>
                {currentMatch && (
                  <View
                    style={PlayerMatchLogScreenStyles.matchFormationContainer}
                  >
                    <MatchFormationView />
                  </View>
                )}
              </View>
              {teamsLoading || (matchesLoading && !hideLoading) ? (
                <ActivitySpinner />
              ) : teams && matches ? (
                <View style={PlayerMatchLogScreenStyles.rightView}>
                  <View style={PlayerMatchLogScreenStyles.matchDayTimContainer}>
                    <MatchDayTimeView />
                  </View>
                  {currentMatch && (
                    <>
                      <View
                        style={PlayerMatchLogScreenStyles.matchScoreContainer}
                      >
                        <MatchScoreView hideLoading={hideLoading} />
                      </View>
                      <View
                        style={
                          PlayerMatchLogScreenStyles.matchRealTimeSummaryContainer
                        }
                      >
                        <MatchRealTimeSummary />
                      </View>
                      <View
                        style={
                          PlayerMatchLogScreenStyles.playerViewSummaryButton
                        }
                      >
                        {renderRefreshBtn()}
                        <MatchLogSummary />
                      </View>
                    </>
                  )}
                </View>
              ) : (
                <NoContentMessage message="No Matches Available" />
              )}
            </View>
          </View>
        </ScreenWrapper>
      ) : (
        <ScreenWrapper>
          <View style={LayoutStyles.contentWrapper}>
            <ScrollView style={PlayerMatchLogScreenStyles.mobileScroll}>
              <View style={PlayerMatchLogScreenStyles.container}>
                <View style={PlayerMatchLogScreenStyles.teamsContainer}>
                  <PlayerMatchLogTeamContainer />
                </View>
                {teamsLoading || matchesLoading ? (
                  <ActivitySpinner />
                ) : teams && matches ? (
                  <View style={PlayerMatchLogScreenStyles.rightView}>
                    <View
                      style={PlayerMatchLogScreenStyles.matchDayTimContainer}
                    >
                      <MatchDayTimeView />
                    </View>
                    {currentMatch && (
                      <>
                        <View
                          style={PlayerMatchLogScreenStyles.matchScoreContainer}
                        >
                          <MatchScoreView hideLoading={hideLoading} />
                        </View>
                        <View
                          style={
                            PlayerMatchLogScreenStyles.matchRealTimeSummaryContainer
                          }
                        >
                          <MatchRealTimeSummary />
                        </View>
                      </>
                    )}
                  </View>
                ) : (
                  <NoContentMessage message="No Matches Available" />
                )}
                <View style={PlayerMatchLogScreenStyles.leftView}>
                  {currentMatch && (
                    <View
                      style={PlayerMatchLogScreenStyles.matchFormationContainer}
                    >
                      <MatchFormationView />
                    </View>
                  )}
                </View>
                {currentMatch && (
                  <>
                    <View
                      style={PlayerMatchLogScreenStyles.playerViewSummaryButton}
                    >
                      {renderRefreshBtn()}
                      <MatchLogSummary />
                    </View>
                  </>
                )}
              </View>
            </ScrollView>
          </View>
        </ScreenWrapper>
      )}
    </>
  );
};

export default PlayerMatchLogScreen;
