import { useFocusEffect, useNavigation } from '@react-navigation/native';
import React, { useCallback } from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';
import { EventDateType, userRoleType } from '../../../constants/constants';
import { EVENT_SERVICE } from '../../../constants/services';
import { calculateEventDate, dateTimeConversion } from '../../../helpers/index';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';
import {
  UPCOMING_MATCHES_FAIL,
  UPCOMING_MATCHES_REQUEST,
  UPCOMING_MATCHES_SUCCESS,
} from '../../../store/actionTypes/SeasonUpdate/SeasonUpdateAction';
import customSeasonUpdateStyle from '../SeasonUpdateStyle';

const Item = ({
  location,
  time,
  navigator,
  matchID,
  opponent,
  team,
  SeasonUpdateStyle,
}) => {
  const dateConvert = dateTimeConversion(time);
  const { dateReadable, monthString } = dateTimeConversion(time);
  return (
    <TouchableOpacity
      onPress={() =>
        navigator.navigate('MatchPlan', {
          matchId: matchID,
          opponent: opponent?._id,
        })
      }
      style={SeasonUpdateStyle.itemView}
    >
      <Text style={SeasonUpdateStyle.match}>
        {team?.name} vs {opponent?.name}
      </Text>
      <View style={SeasonUpdateStyle.matchDetails}>
        <View style={SeasonUpdateStyle.leftCol}>
          <Image
            style={SeasonUpdateStyle.locationIcon}
            source={require('../../../../assets/icons/locationIconGray.png')}
          />
          <Text style={SeasonUpdateStyle.location}>{location?.name}</Text>
        </View>
        <View style={SeasonUpdateStyle.rightCol}>
          <Text style={SeasonUpdateStyle.date}>
            {dateReadable} {monthString}
          </Text>
          <Text style={SeasonUpdateStyle.seperator}> | </Text>
          <Text style={SeasonUpdateStyle.time}>
            {dateConvert?.hours12}:{dateConvert?.minutesString}
            {dateConvert?.amPm}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const UpcomingMatches = ({ selectedChild }) => {
  const SeasonUpdateStyle = useStyles(customSeasonUpdateStyle);
  const navigator = useNavigation();

  const [fetchData] = useApi();
  const { UpcomingMatchesData, UpcomingMatchesLoading } = useSelector(
    state => state?.SeasonUpdate
  );
  const { userData } = useSelector(state => state?.auth);

  const renderItem = ({ item }) => (
    <Item
      opponent={item.opponent}
      team={item.team}
      matchID={item._id}
      location={item.location}
      time={item.startTime}
      navigator={navigator}
      SeasonUpdateStyle={SeasonUpdateStyle}
    />
  );

  const getParticipantDetails = userData => {
    switch (userData?.type) {
      case userRoleType.COACH:
      case userRoleType.PLAYER:
        return `&participantIds=${userData?.id}`;
      case userRoleType.PARENT:
        return `&participantIds=${selectedChild?.id}`;
      default:
        return '';
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData(
        `/api/v1/events?startDate=${new Date().toISOString()}&endDate=${calculateEventDate(
          2,
          EventDateType.END
        )}&page=1&size=100&type=MATCH${getParticipantDetails(userData)}`,
        UPCOMING_MATCHES_REQUEST,
        UPCOMING_MATCHES_SUCCESS,
        UPCOMING_MATCHES_FAIL,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }, [selectedChild, userData])
  );

  return (
    <>
      {!UpcomingMatchesLoading ? (
        <>
          <Text style={SeasonUpdateStyle.title}>Upcoming matches</Text>
          <FlatList
            data={UpcomingMatchesData?.data || []}
            renderItem={renderItem}
            keyExtractor={item => item._id}
          />
        </>
      ) : (
        <ActivitySpinner />
      )}
    </>
  );
};

export default UpcomingMatches;
