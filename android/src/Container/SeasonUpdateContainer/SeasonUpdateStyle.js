import { Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const SeasonUpdateStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        height: hp('80%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        width: wp('100%'),
        height: hp('77%'),
        paddingLeft: wp('4%'),
        paddingRight: wp('4%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
        paddingTop: wp('3%'),
      },
  wrapper: isTabDevice()
    ? {
        width: '90%',
        height: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
      }
    : {
        width: '100%',
        height: '85%',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
      },
  wrapperTop: isTabDevice()
    ? {
        width: '90%',
        marginBottom: wp('2%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
      }
    : {
        width: '100%',
        height: '10%',
        marginBottom: wp('2%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      },
  column: isTabDevice()
    ? {
        width: '30%',
        height: '80%',
        padding: wp('2%'),
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
      }
    : {
        width: '100%',
        height: '31%',
        padding: wp('2%'),
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        marginBottom: wp('4%'),
      },
  scroll: isTabDevice()
    ? {}
    : {
        paddingBottom: wp('80'),
      },
  topRow: isTabDevice()
    ? {}
    : {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
  itemView: isTabDevice()
    ? {
        borderBottomWidth: 1,
        borderBottomColor: colors.lightBlue,
        paddingBottom: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        borderBottomWidth: 1,
        borderBottomColor: colors.lightBlue,
        paddingBottom: wp('2%'),
        marginBottom: wp('2%'),
      },
  matchItemView: isTabDevice()
    ? {
        borderRadius: wp('2%'),
        padding: wp('1.5%'),
        marginBottom: wp('2%'),
        flexDirection: 'row',
        width: wp('22%'),
        height: hp('17%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.32,
        shadowRadius: 5.46,
        elevation: 9,
        position: 'relative',
      }
    : {
        borderRadius: wp('2%'),
        padding: wp('3%'),
        marginBottom: wp('2%'),
        flexDirection: 'row',
        width: wp('88%'),
        height: hp('14%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.32,
        shadowRadius: 5.46,

        elevation: 9,
      },
  matchItemLeft: isTabDevice()
    ? {
        width: '70%',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
      }
    : {
        width: '80%',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'flex-start',
      },
  matchItemRight: isTabDevice()
    ? {
        width: '30%',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        width: '20%',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      },
  matchItemtitle: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  matchItemtournament: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        color: colors.white,
        marginBottom: wp('0.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3.2%'),
        color: colors.white,
        marginBottom: wp('0.5%'),
        fontFamily: 'Poppins-Medium',
      },
  matchItemDate: isTabDevice()
    ? {
        fontSize: wp('0.8%'),
        color: colors.black,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2%'),
        color: colors.black,
        fontFamily: 'Poppins-Medium',
      },
  score: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        padding: wp('0.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        padding: wp('1%'),
        fontFamily: 'Poppins-Medium',
      },
  matchItemseperator: isTabDevice()
    ? {
        width: '100%',
        height: wp('0.1%'),
        backgroundColor: colors.darkBlue,
        opacity: 0.1,
      }
    : {
        width: '100%',
        height: wp('0.5%'),
        backgroundColor: colors.darkBlue,
        opacity: 0.1,
      },
  title: isTabDevice()
    ? {
        color: colors.green,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2%'),
        marginBottom: wp('2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('5%'),
        marginBottom: wp('2%'),
        fontFamily: 'Poppins-Medium',
      },
  match: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1.5%'),
        marginBottom: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('3.5%'),
        marginBottom: wp('1%'),
        fontFamily: 'Poppins-Medium',
      },
  matchDetails: isTabDevice()
    ? {
        flexDirection: 'row',
        // alignItems: 'flex-start',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        width: '70%',
      },
  leftCol: {
    width: '35%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginRight: wp('1%'),
    paddingRight: wp('1%'),
    display: 'none',
  },
  rightCol: {
    width: '60%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: '100%',
    // paddingLeft: wp('1%'),
  },
  locationIcon: isTabDevice()
    ? {
        width: wp('1%'),
        height: wp('1%'),
        marginRight: wp('1%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('2.5%'),
        height: wp('2.5%'),
        marginRight: wp('1%'),
        resizeMode: 'contain',
      },
  location: isTabDevice()
    ? {
        color: colors.grey,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.grey,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
  date: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
  seperator: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1%'),
      }
    : {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.8%'),
      },
  time: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
  /*----------------- DropDown styles ----------------*/
  dropdownView: isTabDevice()
    ? {
        width: '95%',
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        // height: wp('5%'),
        marginBottom: wp('2%'),
        position: 'relative',
        zIndex: 10,
      }
    : {
        width: wp('40%'),
        borderRadius: wp('3%'),
        backgroundColor:
          Platform.OS === 'android' ? colors.transparent : colors.darkBlue,
        marginBottom: wp('2%'),
      },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        borderColor: colors.darkBlue,
        height: hp('5%'),
        width: '100%',
      }
    : {
        height: wp('10%'),
        // backgroundColor: colors.red,
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('-1%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,

        elevation: 10,
      }
    : {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('-2%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,

        elevation: 10,
      },

  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },

  picker: {
    backgroundColor: colors.borderBlue,
    // color: 'white',
  },
  overlayMatchPlayed: isTabDevice()
    ? {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        width: wp('22%'),
        height: hp('17%'),
        backgroundColor: colors.black,
        opacity: 0.8,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        marginBottom: wp('2%'),
        zIndex: 10,
      }
    : {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        width: wp('88%'),
        height: hp('14%'),
        backgroundColor: colors.black,
        opacity: 0.8,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        marginBottom: wp('2%'),
        zIndex: 10,
      },
  overlayMatchPlayedText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },

  /*----------------- DropDown styles End----------------*/
});
export default SeasonUpdateStyle;
