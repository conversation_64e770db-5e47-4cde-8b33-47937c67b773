import React, { useEffect, useState } from 'react';
import { View, ScrollView } from 'react-native';
import customSeasonUpdateStyle from './SeasonUpdateStyle';
import UpcomingMatches from './UpcomingMatches/UpcomingMatches';
import UpcomingTraining from './UpcomingTraining/UpcomingTraining';
import SeasonUpdates from './SeasonUpdate/SeasonUpdate';
import useStyles from '../../hooks/useStyles';
import { useSelector } from 'react-redux';
import ChildLabel from '../../components/ChildLabel/ChildLabel';
import { userRoleType } from '../../constants/constants';
import useGetChildInformation from '../../hooks/useGetChildInformation';

const SeasonUpdateContainer = () => {
  const SeasonUpdateStyle = useStyles(customSeasonUpdateStyle);
  const { userRole, userData } = useSelector(state => state?.auth);
  const { children, childrenLoading, parentDetails } = useSelector(
    state => state?.common
  );

  const isParent = userRole === userRoleType.PARENT;
  const [selectedChild, setSelectedChild] = useState();
  const [fetchChildInformation] = useGetChildInformation();

  useEffect(() => {
    children?.length && setSelectedChild(children[0]);
  }, [children]);

  useEffect(() => {
    fetchChildInformation();
  }, []);

  return (
    <View style={SeasonUpdateStyle.container}>
      <View style={SeasonUpdateStyle.wrapperTop}>
        <ChildLabel
          data={children}
          setSelectedChild={setSelectedChild}
          selectedChild={selectedChild}
        />
      </View>
      {/* <ScrollView style={SeasonUpdateStyle.scroll}> */}
      <View style={SeasonUpdateStyle.wrapper}>
        <View style={SeasonUpdateStyle.column}>
          <UpcomingMatches selectedChild={selectedChild} />
        </View>
        <View style={SeasonUpdateStyle.column}>
          <UpcomingTraining selectedChild={selectedChild} />
        </View>
        <View style={SeasonUpdateStyle.column}>
          <SeasonUpdates selectedChild={selectedChild} />
        </View>
      </View>
      {/* </ScrollView> */}
    </View>
  );
};

export default SeasonUpdateContainer;
