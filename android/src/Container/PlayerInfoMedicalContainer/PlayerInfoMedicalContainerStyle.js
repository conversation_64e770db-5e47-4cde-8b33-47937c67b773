import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateContainerStyle = colors => ({
  PlayerInfoMedicalsWrapper: isTabDevice()
    ? {
        width: wp('67%'),
        height: hp('60%'),
      }
    : {
        width: wp('95%'),
        height: hp('40%'),
      },
  container: isTabDevice()
    ? {
        width: '100%',
        height: hp('50%'),
        marginLeft: wp('2%'),
      }
    : {
        width: '100%',
        height: hp('40%'),
        marginLeft: wp('2%'),
        paddingLeft: hp('1%'),
        paddingBottom: hp('5%'),
      },
  dataRow: {},
  list: {
    backgroundColor: colors.red,
    height: 20,
    paddingBottom: 40,
  },
});
export default PlayerInfoUpdateContainerStyle;
