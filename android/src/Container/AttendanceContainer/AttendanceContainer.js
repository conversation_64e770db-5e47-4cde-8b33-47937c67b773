import React, { useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import useApi from '../../hooks/useApi';
import customAttendanceStyles from './AttendanceStyles';
import AttendanceUser from './AttendanceUser';
import { AntDesign } from '@expo/vector-icons';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import { isTabDevice } from '../../config/appConfig';
import { EVENT_SERVICE } from '../../constants/services';
import useStyles from '../../hooks/useStyles';
import {
  MATCH_ATTENDANCE_SAVE_FAILED,
  MATCH_ATTENDANCE_SAVE_REQUEST,
  MATCH_ATTENDANCE_SAVE_SUCCESS,
  MATCH_INVITEES_FAILED,
  MATCH_INVITEES_REQUEST,
  MATCH_INVITEES_SUCCESS,
  RESET_INVITEES,
} from '../../store/actionTypes/MatchLog/MatchLogActions';

const AttendanceContainer = ({ setShowAttendanceModal, selectedEvent }) => {
  const attendanceStyles = useStyles(customAttendanceStyles);
  const dispatch = useDispatch();
  const { _id } = selectedEvent;
  const [isSubmit, setIsSubmit] = useState(false);
  const [fetchData] = useApi();
  const [inviteesPageCount, setInviteesPageCount] = useState(1);
  const {
    invitees,
    isInviteesSaved,
    isInviteesSaving,
    isInviteesLoading,
    inviteesTotalRecords,
    inviteesPage,
  } = useSelector(state => state?.matchLog);
  const [playerSearch, setPlayerSearch] = useState('');
  const [changedInvitees, setChangedInvitees] = useState([]);

  const mergedInvitees = useMemo(() => {
    return (
      invitees?.map(invitee => {
        const changedInvitee = changedInvitees.find(
          changed => changed._id === invitee._id
        );
        return changedInvitee ? { ...invitee, ...changedInvitee } : invitee;
      }) || []
    );
  }, [JSON.stringify(invitees), JSON.stringify(changedInvitees)]);

  useEffect(() => {
    _id && getAttendanceUser(_id, 1, playerSearch);
  }, [_id, playerSearch]);

  const handelChangedInvitees = (item, type) => {
    setChangedInvitees(changedInvitees => {
      let tmpChangedInvitees = [...changedInvitees];
      const selectedIndex = tmpChangedInvitees.findIndex(
        tmpChangedInvitee => tmpChangedInvitee._id == item._id
      );

      if (selectedIndex > -1) {
        tmpChangedInvitees[selectedIndex].isAttended = type;
      } else {
        tmpChangedInvitees = [
          ...tmpChangedInvitees,
          { ...item, isAttended: type },
        ];
      }

      return tmpChangedInvitees;
    });
  };

  const getAttendanceUser = (eventId, page, search) => {
    fetchData(
      `/api/v1/events/${eventId}/invitees?searchByNameLike=${search}&type=PLAYER&page=${page}&size=50`,
      MATCH_INVITEES_REQUEST,
      MATCH_INVITEES_SUCCESS,
      MATCH_INVITEES_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  const saveAction = () => {
    const saveInvitees = changedInvitees.map(item => ({
      eventId: item.eventId,
      userId: item.userId,
      isParticipating: item.isParticipating,
      isAttended: item.isAttended,
    }));

    setIsSubmit(true);

    fetchData(
      `/api/v1/events/${_id}/invitees`,
      MATCH_ATTENDANCE_SAVE_REQUEST,
      MATCH_ATTENDANCE_SAVE_SUCCESS,
      MATCH_ATTENDANCE_SAVE_FAILED,
      saveInvitees,
      null,
      'PUT',
      true,
      EVENT_SERVICE
    );
  };

  useEffect(() => {
    isInviteesSaved && isSubmit && setShowAttendanceModal(false);
  }, [isInviteesSaved]);

  useEffect(() => {
    return () => {
      dispatch({ type: RESET_INVITEES });
    };
  }, []);

  const loadNextPage = () => {
    if (inviteesTotalRecords > invitees?.length || 0) {
      const nextPage = inviteesPage + 1;
      setInviteesPageCount(nextPage);
      selectedEvent &&
        getAttendanceUser(selectedEvent._id, nextPage, playerSearch);
    }
  };

  const renderItem = ({ item }) => (
    <AttendanceUser item={item} onChangedInvitees={handelChangedInvitees} />
  );

  const handleSearch = () => {
    playerSearch && setPlayerSearch('');
  };

  //TODO Need to Do UI for search inviteesPageCount

  return (
    <View style={attendanceStyles.container}>
      <View style={attendanceStyles.wrapper}>
        <View style={attendanceStyles.titleRow}>
          <Text style={attendanceStyles.title}>Attendance</Text>

          <View style={attendanceStyles.titleRowRight}>
            <View style={attendanceStyles.searchBarContainer}>
              <TextInput
                style={attendanceStyles.searchBarInput}
                placeholder="Search User"
                placeholderTextColor="#FFFF"
                onChangeText={data => setPlayerSearch(data)}
                value={playerSearch}
              />
              <View style={attendanceStyles.searchBarIcon}>
                {/* <TouchableOpacity onPress={handleSearch}> */}
                <TouchableOpacity onPress={handleSearch}>
                  <AntDesign
                    // name={!searchBarText ? 'Search' : 'close'}
                    name={!playerSearch ? 'search1' : 'close'}
                    color="white"
                    size={isTabDevice() ? 16 : 13}
                  />
                </TouchableOpacity>
              </View>
            </View>

            <View style={attendanceStyles.buttons}>
              {isInviteesSaving ? (
                <View style={attendanceStyles.button}>
                  <ActivityIndicator size="small" color="#0000ff" />
                  <Text style={attendanceStyles.buttonText}>Saving</Text>
                </View>
              ) : (
                <TouchableOpacity
                  style={attendanceStyles.button}
                  onPress={() => saveAction()}
                >
                  <Text style={attendanceStyles.buttonText}>Save</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={attendanceStyles.button2}
                onPress={() => setShowAttendanceModal(false)}
              >
                <Text style={attendanceStyles.buttonText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        {isInviteesLoading && inviteesPageCount < 2 ? (
          <ActivitySpinner />
        ) : invitees?.length ? (
          <FlatList
            numColumns={isTabDevice() ? 5 : 3}
            data={mergedInvitees || []}
            renderItem={renderItem}
            keyExtractor={item => item.userId}
            // style={attendanceStyles.flatList}
            onEndReached={loadNextPage}
            contentContainerStyle={attendanceStyles.flatList}
          />
        ) : (
          <NoContentMessage message="No Content" />
        )}
      </View>
    </View>
  );
};

export default AttendanceContainer;
