import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const contactStyle = colors => ({
  container: {
    flex: 1,
    paddingLeft: wp('4%'),
  },
  contactsText: {
    color: colors.white,
    fontSize: wp('2.5%'),
    fontWeight: 'bold',
    marginBottom: hp('2%'),
  },
  contactView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: 10,
  },
  contactText: {
    color: colors.white,
    fontSize: wp('2%'),
    marginBottom: hp('1%'),
  },
  contactNumber: {
    color: colors.aquaBlue,
    fontSize: wp('2.3%'),
  },
});
export default contactStyle;
