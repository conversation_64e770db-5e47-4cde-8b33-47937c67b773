import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import React, { useEffect, useMemo, useState, useCallback } from 'react';
import {
  ActivityIndicator,
  Image,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useSelector } from 'react-redux';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import { isTabDevice } from '../../config/appConfig';
import { useDispatch } from 'react-redux';
import {
  S3_BUCKET_LOCATION,
  imageError,
  messageFileTypes,
  userRoleType,
} from '../../constants/constants';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';
import { get_url_extension, stringLength } from '../../helpers';
import useApi from '../../hooks/useApi';
import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';

import { UPLOAD_IMAGE_SUCCESS } from '../../store/actionTypes/UploadImage/uploadImageAction';

import customProfileImageStyle from './profileImageStyle';
import useS3bucketLocation from '../../hooks/useS3bucketLocation';
import useFileUpload from '../../hooks/useFileUpload';
import useGenerateImageUrls from '../../hooks/useGeneratedImageUrls';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
export default function ProfileImageWrapper({
  isToggleEnabled,
  isEditMode,
  setIsEditMode,
  PlayerData,
  statGraphView,
  statGraphCategoryName,
  statGraphCriteriaName,
  SavePlayer,
  onPressShare,
  showShareOption,
  isPlayer,
  isPlayerInfor,
  toggleSwitch,
  removeErrorMessage,
  setRemoveErrorMessage,
  isInPlayerInfo,
}) {
  const dispatch = useDispatch();
  const [getFileObject, preSignedUrl] = useGenerateImageUrls();
  const [getBucketLocation, bucketLocation] = useS3bucketLocation();
  const [upload, uploadedContent, uploadProgress] = useFileUpload();
  const { uploadPofileImageLoading } = useSelector(state => state?.addUser);
  const { playerLabelIdData, teamData } = useSelector(state => state?.team);
  const [apiCall] = useApi();
  const ProfileImageStyle = useStyles(customProfileImageStyle);
  const colors = useColors();
  const { firstName, lastName, profileImageUrl, uniqueUserId } =
    PlayerData || {};
  const { userData } = useSelector(state => state?.auth);
  const { shareReportLoading } = useSelector(state => state.playerIAP);

  const isCoach =
    userRoleType.COACH === userData?.type ||
    userRoleType.HEAD_COACH === userData?.type;

  const isParent = userRoleType.PARENT === userData?.type;

  const fullName = `${firstName || ''} \n${lastName || ''}`;
  const [isImageUploading, setImageUploading] = useState(false);
  const [image, setImage] = useState(null);
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: undefined,
  });

  const isFirstTwoTabs = useMemo(
    () =>
      isCoach
        ? !playerLabelIdData || playerLabelIdData == 1 || playerLabelIdData == 2
        : !playerLabelIdData || playerLabelIdData == 1,
    [playerLabelIdData, isCoach]
  );

  useEffect(() => {
    getBucketLocation({
      path: S3_BUCKET_LOCATION.profileImages,
      service: USER_MANAGEMENT_SERVICE,
    });
  }, []);

  const startUploading = useCallback(
    async (filePath, fileName) => {
      if (bucketLocation.filePath) {
        const picture = await fetch(filePath);

        const pictureBlob = await picture.blob();
        const file = new File([pictureBlob], fileName);

        console.log(
          file,
          bucketLocation.filePath,
          bucketLocation?.bucketName || ''
        );

        upload(file, bucketLocation.filePath, bucketLocation?.bucketName || '');
      }
    },
    [bucketLocation]
  );

  useEffect(() => {
    if (uploadedContent) {
      dispatch({
        type: UPLOAD_IMAGE_SUCCESS,
        payload: uploadedContent,
      });

      getFileObject({
        fileKey: uploadedContent.fileKey,
        bucketName: uploadedContent.bucketName,
        id: 'user-image_profile',
      });
      setImageUploading(false);
    }
  }, [uploadedContent]);

  const getFileInfo = async fileURI => {
    const fileInfo = await FileSystem.getInfoAsync(fileURI);
    return fileInfo;
  };

  const pickImage = async () => {
    try {
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled) {
        const fileExtension = get_url_extension(result.assets[0].uri);

        if (messageFileTypes.includes(fileExtension.toLowerCase())) {
          const fileInfo = await getFileInfo(result.assets[0].uri);
          if (3 > Number(fileInfo?.size / 1048576)) {
            setImageUploading(true);
            startUploading(result.assets[0].uri, `photo.${fileExtension}`);
            setImage(result.assets[0].uri);
            setIsFileError({
              status: false,
              error: undefined,
            });
          } else {
            setIsFileError({
              status: true,
              error: imageError.MAX_FILE_SIZE_ERROR,
            });
            setImageUploading(false);
            setImage('');
          }
        }
      } else {
        setImageUploading(false);
      }
    } catch (e) {
      setImageUploading(false);
    }
  };

  useEffect(() => {
    removeErrorMessage &&
      setIsFileError({
        status: false,
        error: undefined,
      });
    setRemoveErrorMessage(false);
  }, [removeErrorMessage && removeErrorMessage]);

  useEffect(() => {
    setImage(null);
  }, [isEditMode]);
  useEffect(() => {
    if (!uploadPofileImageLoading && isImageUploading) {
      setImageUploading(false);
    }
  }, [uploadPofileImageLoading]);
  return (
    <View
      style={
        isPlayer
          ? ProfileImageStyle.playerContainer
          : ProfileImageStyle.container
      }
    >
      {isTabDevice() ? (
        <View>
          <View style={ProfileImageStyle.userImgWrapper}>
            {isPlayerInfor && !isToggleEnabled && (
              <Image
                style={ProfileImageStyle.exclamationIcon}
                source={require('../../../assets/buttons/exclamation.png')}
              />
            )}
            {isImageUploading ? (
              <ActivityIndicator
                size="small"
                color="white"
                style={ProfileImageStyle.userImg}
              />
            ) : (
              <ProfileImage
                imageStyles={ProfileImageStyle.userImg}
                profileImageUrl={image || profileImageUrl}
              />
            )}
            {isEditMode && (
              <>
                <TouchableOpacity
                  style={ProfileImageStyle.updatePicButton}
                  onPress={() => pickImage()}
                >
                  <Image
                    style={ProfileImageStyle.updatePicIcon}
                    source={require('../../../assets/icons/camera.png')}
                  />
                  <Text style={ProfileImageStyle.updatePicText}>
                    Update picture
                  </Text>
                </TouchableOpacity>

                {isFileError.status ? (
                  <Text style={ProfileImageStyle.profileUploadError}>
                    {isFileError.error}
                  </Text>
                ) : undefined}
              </>
            )}
          </View>
          <View style={ProfileImageStyle.userNameWrapper}>
            <Text style={ProfileImageStyle.firstName}>
              {stringLength(fullName, 42)}
            </Text>
            {uniqueUserId && (
              <Text style={ProfileImageStyle.playerId}>
                Player ID: {uniqueUserId}
              </Text>
            )}
          </View>
        </View>
      ) : (
        <View style={ProfileImageStyle.userImgWrapper}>
          <View style={ProfileImageStyle.colOne}>
            {!isToggleEnabled && (
              <Image
                style={ProfileImageStyle.exclamationIcon}
                source={require('../../../assets/buttons/exclamation.png')}
              />
            )}

            {isImageUploading ? (
              <ActivityIndicator
                size="small"
                color="white"
                style={ProfileImageStyle.userImg}
              />
            ) : (
              <ProfileImage
                imageStyles={ProfileImageStyle.userImg}
                profileImageUrl={image || profileImageUrl}
              />
            )}

            {isEditMode && isInPlayerInfo && (
              <>
                <TouchableOpacity
                  style={ProfileImageStyle.updatePicButton}
                  onPress={() => pickImage()}
                >
                  <Image
                    style={ProfileImageStyle.updatePicIcon}
                    source={require('../../../assets/icons/camera.png')}
                  />
                  <Text style={ProfileImageStyle.updatePicText}>
                    Update picture
                  </Text>
                </TouchableOpacity>
                {isFileError.status ? (
                  <Text style={ProfileImageStyle.profileUploadError}>
                    {isFileError.error}
                  </Text>
                ) : undefined}
              </>
            )}
          </View>
          <View style={ProfileImageStyle.colTwo}>
            <View style={ProfileImageStyle.userNameContainer}>
              <View style={ProfileImageStyle.topRow}>
                <Text style={ProfileImageStyle.firstName}>
                  {stringLength(fullName, 35)}
                </Text>
                {uniqueUserId && (
                  <Text style={ProfileImageStyle.playerId}>
                    Player ID: {uniqueUserId}
                  </Text>
                )}
              </View>
              <View style={ProfileImageStyle.bottomRow}>
                {!isEditMode && isFirstTwoTabs && (
                  <View style={ProfileImageStyle.editModeView}>
                    <TouchableOpacity
                      onPress={() => {
                        setIsEditMode(true);
                      }}
                      disabled={isParent && !teamData?.data?.length}
                      style={isParent && !teamData?.data?.length ? {...ProfileImageStyle.btn,backgroundColor : 'grey'} :ProfileImageStyle.btn}
                    >
                      
                      <Text style={ProfileImageStyle.btnTxt}>Edit</Text>
                    </TouchableOpacity>
                  </View>
                )}
                {isEditMode && isCoach && isInPlayerInfo && (
                  <View style={ProfileImageStyle.switchView}>
                    <Text style={ProfileImageStyle.switchText}>
                      {!isToggleEnabled ? 'Unavailable' : 'Available'}
                    </Text>
                    <View style={ProfileImageStyle.switch}>
                      <Switch
                        style={ProfileImageStyle.unavailableSwitch}
                        trackColor={{ false: '#23344B', true: '#23344B' }}
                        thumbColor={isToggleEnabled ? '#36D982' : '#D94136'}
                        ios_backgroundColor="#23344B"
                        onValueChange={toggleSwitch}
                        value={isToggleEnabled}
                      />
                      <Text style={ProfileImageStyle.switchText2}>
                        {!isToggleEnabled ? 'No' : 'Yes'}{' '}
                      </Text>
                    </View>
                  </View>
                )}
              </View>
            </View>
          </View>
          <View style={ProfileImageStyle.colThree}>
            {!isEditMode && showShareOption && !shareReportLoading && (
              <View style={ProfileImageStyle.editModeView}>
                <TouchableOpacity
                  style={ProfileImageStyle.btn2}
                  onPress={() => onPressShare()}
                >
                  <Image
                    style={ProfileImageStyle.shareIcon}
                    source={require('../../../assets/buttons/shareIcon.png')}
                  />
                </TouchableOpacity>
              </View>
            )}
            {shareReportLoading && (
              <View style={ProfileImageStyle.btn2}>
                <ActivityIndicator color={colors.green} size="small" />
              </View>
            )}
            {isEditMode && (
              <View style={ProfileImageStyle.editModeViewSave}>
                {isInPlayerInfo && (
                  <TouchableOpacity
                    onPress={() => SavePlayer()}
                    style={ProfileImageStyle.btnSave}
                  >
                    <Text style={ProfileImageStyle.btnTxtSave}>Save</Text>
                  </TouchableOpacity>
                )}
                {isFirstTwoTabs && (
                  <TouchableOpacity
                    onPress={() => {
                      setIsFileError({
                        status: false,
                        error: undefined,
                      }),
                        setIsEditMode(false);
                    }}
                    style={
                      isInPlayerInfo
                        ? ProfileImageStyle.btnCancel
                        : {
                            ...ProfileImageStyle.btnCancel,
                            marginTop: hp('5%'),
                          }
                    }
                  >
                    <Text style={ProfileImageStyle.btnTxtCancel}>Cancel</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        </View>
      )}

      {/* {statGraphView && (
        <View>
          <Text style={ProfileImageStyle.statGraphCategoryName}>{statGraphCategoryName}</Text>
          <Text style={ProfileImageStyle.statGraphCriteriaName}>{statGraphCriteriaName}</Text>
        </View>
      )} */}
      {/* <View style={{ display: 'flex', flexDirection: 'row' }}>
        {!statGraphView && (
          <TouchableOpacity
            style={ProfileImageStyle.btn}
            onPress={() => {
              setIsEditMode(state => !state);
            }}
          >
            <Text style={ProfileImageStyle.btnTxt}>{isEditMode ? 'Done' : 'Edit'}</Text>
          </TouchableOpacity>
        )}
        {showShareButton() && (
          <TouchableOpacity style={ProfileImageStyle.btn2}>
            <Image
              style={ProfileImageStyle.shareIcon}
              source={require('../../../assets/buttons/shareIcon.png')}
            />
            <Text style={ProfileImageStyle.btnTxt}>Share</Text>
          </TouchableOpacity>
        )}
      </View> */}
    </View>
  );
}
