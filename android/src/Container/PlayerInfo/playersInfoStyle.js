import { Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const playersInfoStyle = colors => ({
  container: isTabDevice()
    ? {
      borderTopWidth: 1,
      borderTopColor: colors.borderBlue,
      paddingTop: hp('4%'),
      width: '100%',
      height: wp('100%'),
    }
    : {
      paddingTop: hp('2%'),
      height: hp('100%'),
    },
  wrapper: isTabDevice()
    ? {
      height: wp('100%'),
    }
    : {
      height: hp('90%'),
      paddingRight: wp('4%'),
      paddingLeft: wp('4%'),
    },
  playerInfoScreen: isTabDevice()
    ? {
      paddingBottom: 100,
    }
    : {
      flexDirection: 'column',
      justifyContent: 'space-between',
      height: hp('100%')
    },
  playerInfoScreenInner: isTabDevice()
    ? {
      flexDirection: 'column',
      justifyContent: 'space-between',
      height: '100%',
    }
    : {
      flexDirection: 'column',
      justifyContent: 'space-between',
      // marginBottom: wp('60%'),
      // height: '100%',
      // height: 2000,
    },
  dataWrapper: isTabDevice()
    ? {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: hp('5%'),
      height: wp('36%'),
      paddingLeft: wp('4%'),
    }
    : {
      width: wp('92%'),
      flexDirection: 'column',
      justifyContent: 'space-between',
      marginBottom: hp('5%'),
      backgroundColor: colors.borderBlue,
      padding: wp('2%'),
      borderRadius: wp('2%'),
    },
  dataColumn: {
    flexDirection: 'column',
    width: '50%',
    justifyContent: 'space-between',
  },
  dataRow: isTabDevice()
    ? {
      flexWrap: 'wrap',
      flexDirection: 'row',
    }
    : {
      width: wp('88%'),
      flexDirection: 'column',
      marginBottom: hp('2%'),
    },
  dataRowLeft: isTabDevice()
    ? {
      width: '45%',
      flexDirection: 'row',
    }
    : {
      width: '100%',
      flexDirection: 'row',
    },
  errorText: {
    color: colors.red,
    fontSize: 12,
    position: 'absolute',
    top: '25%',
    right: 20,
  },
  dataRowRight: isTabDevice()
    ? {
      width: '50%',
      height: wp('6%'),
      position: 'relative',
    }
    : {
      width: '100%',
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('2%'),
      height: hp('5%'),
      justifyContent: 'center',
    },
  teamDataRowRight: isTabDevice()
    ? {
      width: wp('15%'),
      height: wp('10%'),
    }
    : {
      // width: wp('5%'),
      // height: wp('6%'),
    },
  teamScrollWrapper: isTabDevice()
    ? {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      // marginLeft: wp('1%'),
      marginTop: wp('1%'),
      color: colors.white,
    }
    : {
      display: 'flex',
      flexDirection: 'row',
      marginLeft: wp('2%'),
      marginTop: wp('2%'),
    },
  dataRowRightPhone: isTabDevice()
    ? {
      width: '100%',
    }
    : {},
  nameLabel: isTabDevice()
    ? {
      fontSize: wp('1.3%'),
      color: colors.green,
      padding: wp('1%'),
      paddingLeft: 0,
      fontFamily: 'Poppins-Regular',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.green,
      padding: wp('1%'),
      fontFamily: 'Poppins-Regular',
    },
  nameValue: isTabDevice()
    ? {
      fontSize: wp('1.3%'),
      color: colors.white,
      padding: wp('1%'),
      fontFamily: 'Poppins-Regular',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Regular',
      paddingTop: 5,
      paddingBottom: 5,
      paddingLeft: 10,
      paddingRight: 10,
    },
  teamValue: isTabDevice()
    ? {
      height: wp('3%'),
      paddingTop: 0,
      color: colors.white,
    }
    : {
      paddingTop: 0,
      color: colors.white,
    },
  nameValueInput: isTabDevice()
    ? {
      fontSize: wp('1.3%'),
      color: colors.white,
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('1%'),
      marginRight: wp('2%'),
      padding: 9,
      paddingLeft: 15,
      paddingRight: 15,
      height: hp('6%'),
      fontFamily: 'Poppins-Regular',
      width: '100%',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('2%'),
      paddingBottom: 5,
      paddingLeft: 10,
      paddingRight: 10,
      height: hp('5%'),
      fontFamily: 'Poppins-Regular',
    },
  nameValueDateInput: isTabDevice()
    ? {
      fontSize: wp('1.3%'),
      color: colors.white,
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('1%'),
      marginRight: wp('2%'),
      paddingLeft: 7,
      paddingRight: 7,
      height: hp('6%'),
      fontFamily: 'Poppins-Regular',
      width: '100%',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('2%'),
      height: hp('5%'),
      paddingBottom: 5,
      paddingLeft: 10,
      paddingRight: 10,
      fontFamily: 'Poppins-Regular',
    },

  nameValueDateInputWithValue: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
      color: colors.white,
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('1%'),
      marginRight: wp('2%'),
      // padding: ,
      paddingLeft: 7,
      paddingRight: 7,
      height: hp('6%'),
      fontFamily: 'Poppins-Regular',
      width: '100%',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      backgroundColor: colors.semiDarkBlue,
      fontFamily: 'Poppins-Regular',
      borderRadius: wp('2%'),
      height: hp('5%'),
      paddingBottom: 5,
      paddingLeft: 10,
      paddingRight: 10,
    },
  dropDownMoveUp: {
    position: 'relative',
    zIndex: 5,
  },
  dropDownMoveUp2: {
    position: 'relative',
    zIndex: 4,
  },
  dropDownMoveUp3: {
    position: 'relative',
    zIndex: 3,
  },
  datePickerWrapper: isTabDevice()
    ? {
      position: 'absolute',
      borderRadius: wp('3%'),
      bottom: wp('-2%'),
      left: 0,
      zIndex: 3,
      width: wp('72%'),
      borderWidth: 10,
      borderColor: colors.semiDarkBlue,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 12,
      },
      shadowOpacity: 0.58,
      shadowRadius: 16.0,

      elevation: 24,
    }
    : {
      position: 'absolute',
      borderRadius: wp('3%'),
      top: 0,
      left: 0,
      zIndex: 3,
      width: wp('100%'),
      borderWidth: 10,
      borderColor: colors.semiDarkBlue,
      backgroundColor: colors.white,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 12,
      },
      shadowOpacity: 0.58,
      shadowRadius: 16.0,

      elevation: 24,
    },
  datePicker: {},
  editDate: {
    fontSize: 14,
    color: '#fff',
  },
  textInput: {
    height: hp('5%'),
    fontSize: wp('1.5%'),
    color: colors.white,
  },

  /*----------------- DropDown styles ----------------*/
  dropdownView: isTabDevice()
    ? {
      width: wp('17%'),
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('1%'),
      height: hp('6%'),
    }
    : {
      width: wp('84%'),
      marginTop: wp('-1%'),
      backgroundColor: colors.borderBlue,
    },
  dropdown: isTabDevice()
    ? {
      justifyContent: 'flex-start',
      height: hp('3%'),
    }
    : {
      justifyContent: 'flex-start',
      height: hp('3%'),
    },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
      color: colors.lightGrey,
      fontSize: wp('1.3%'),
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
    },
  dropdownSelectedContainer: isTabDevice()
    ? {
      borderColor: colors.borderBlue,
      height: hp('6%'),
      width: '100%',
    }
    : {
      // backgroundColor: colors.borderBlue,
      backgroundColor: colors.semiDarkBlue,
      height: wp('10%'),
    },
  dropdownList: isTabDevice()
    ? {
      backgroundColor: colors.borderBlue,
      borderColor: colors.borderBlue,
      paddingLeft: wp('1%'),
      borderBottomLeftRadius: wp('2%'),
      borderBottomRightRadius: wp('2%'),
      marginTop: hp('1%'),
      borderRadius: wp('100%'),
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 5,
      },
      shadowOpacity: 0.34,
      shadowRadius: 6.27,

      elevation: 10,
    }
    : {
      backgroundColor: colors.borderBlue,
      borderColor: colors.borderBlue,
      marginTop: hp('1%'),
      borderRadius: wp('100%'),
    },
  dropDownLabel: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.3%'),
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
      marginLeft: wp('1%'),
    },
  placeholderStyle: isTabDevice()
    ? {
      color: colors.lightGrey,
      fontSize: wp('1.3%'),
    }
    : {
      color: colors.lightGrey,
      fontSize: wp('3.5%'),
    },
  dropdownTopArea: {
    ...(Platform.OS === 'android'
      ? {
        backgroundColor: isTabDevice()
          ? colors.borderBlue
          : colors.semiDarkBlue,
        borderColor: isTabDevice() ? colors.transparent : colors.semiDarkBlue,
        height: hp('6%'),
      }
      : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.transparent,
        height: wp('10%'),
        width: '100%',
        position: 'absolute',
        zIndex: 5,
      }),
  },

  /*----------------- DropDown styles End----------------*/

  editView2: isTabDevice()
    ? {}
    : {
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('2%'),
      padding: wp('2%'),
    },
  contactsAreaWrapper: isTabDevice()
    ? {
      paddingLeft: wp('4%'),
      paddingTop: hp('5%'),
      marginTop: wp('5%')
    }
    : {
      marginBottom: wp('5%')
      // height: 400,
    },
  contactsAreaWrapper2: isTabDevice()
    ? {
      paddingLeft: wp('4%'),
      paddingTop: hp('5%'),
    }
    : {
      paddingBottom: 100,
      marginTop: wp('-30%'),
    },
  contactsAreaWrapper3: isTabDevice()
    ? {
      paddingLeft: wp('4%'),
      paddingTop: hp('5%'),
    }
    : {
      minHeight: 1000,
      maxHeight: 1500,
      marginTop: wp('-30%'),
    },
  contactsText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('2.5%'),
      fontFamily: 'Poppins-Bold',
      marginBottom: hp('2%'),
    }
    : {
      color: colors.white,
      fontSize: wp('5%'),
      fontFamily: 'Poppins-Bold',
      marginBottom: wp('2%'),
    },
  contactView: isTabDevice()
    ? {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: -hp('2%'),
    }
    : {
      backgroundColor: colors.borderBlue,
      padding: hp('2%'),
      borderRadius: wp('2%'),
    },
  contactView2: isTabDevice()
    ? {
      flexDirection: 'row',
      flexGrow: 2,
      justifyContent: 'space-between',
      minHeight: wp('10%')
    }
    : {
    },
  contactRow: isTabDevice()
    ? {
      flex: 1,
      marginRight: 15,
    }
    : {
      marginBottom: hp('2%'),
    },
  contactTextWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.3%'),
      marginBottom: hp('1%'),
      fontFamily: 'Poppins-Regular',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.aquaBlue,
      fontFamily: 'Poppins-Regular',
      padding: wp('1%'),
    },
  contactNumber: isTabDevice()
    ? {
      color: colors.aquaBlue,
      fontSize: wp('1.2%'),
      fontFamily: 'Poppins-Regular',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Regular',
    },
  contactNumberEdit: isTabDevice()
    ? {
      backgroundColor: colors.borderBlue,
      borderRadius: wp('1.5%'),
      padding: hp('1.5%'),
      paddingLeft: hp('2.5%'),
      paddingRight: hp('2.5%'),
      color: colors.aquaBlue,
      fontSize: wp('1.3%'),
      fontFamily: 'Poppins-Regular',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Regular',
    },
  validationTxt: {
    color: colors.red,
    fontSize: wp('1.5'),
    fontFamily: 'Poppins-Medium',
    marginLeft: wp('4.5%'),
  },
  requiredAstric: isTabDevice()
    ? {
      top: 15,
      left: -10,
      color: colors.red,
      fontWeight: 'bold',
      fontSize: wp('0.7%'),
    }
    : {
      top: 8,
      left: 0,
      color: colors.red,
      fontWeight: 'bold',
      fontSize: wp('1.5%'),
    },
  requiredAstric2: isTabDevice()
    ? {
      top: 13,
      left: -7,
      color: colors.red,
      fontWeight: 'bold',
      fontSize: wp('0.7%'),
    }
    : {
      top: 8,
      left: 0,
      color: colors.red,
      fontWeight: 'bold',
      fontSize: wp('1.5%'),
    },
  datePickerContainer: {},
  datePickerSelector: isTabDevice()
    ? {
      width: wp('40%'),
      position: 'absolute',
      left: wp('27%'),
      top: wp('25%'),
    }
    : {
      width: '90%',
      position: 'absolute',
      left: 12,
      top: wp('40%'),
    },
  datePickerWrapper: isTabDevice()
    ? {
      zIndex: 3,
      width: '100%',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 12,
      },
      shadowOpacity: 0.58,
      shadowRadius: 16.0,
      elevation: 24,
    }
    : {
      position: 'absolute',
      borderRadius: wp('3%'),
      top: 0,
      left: 0,
      zIndex: 3,
      width: '100%',
      borderWidth: 10,
      borderColor: colors.semiDarkBlue,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 12,
      },
      shadowOpacity: 0.58,
      shadowRadius: 16.0,
      elevation: 24,
    },
  datePicker: {},

  picker: isTabDevice()
    ? {
      backgroundColor: colors.borderBlue,
      color: 'white',
    }
    : {
      backgroundColor: colors.borderBlue,
      color: 'white',
      height: wp('30%'),
    },
  textInput: isTabDevice()
    ? {
      color: colors.white,
      fontSize: hp('2%'),
      width: wp('24%'),
      padding: hp('1.5%'),
      height: hp('6%'),
    }
    : {
      height: hp('5%'),
      paddingLeft: hp('1%'),
      color: colors.white,
      fontSize: hp('2%'),
    },
  scrollView: isTabDevice()
    ? {
      height: hp('60%'),
      marginBottom: wp('100%'),
    }
    : {
      height: hp('170%'),
      marginBottom: wp('130%'),
    },
  btnTxt: {
    fontSize: wp('1.5%'),
    color: colors.white,
    fontFamily: 'Poppins-Regular',
  },
  btnUploadTxt: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Regular',
    }
    : {
      fontSize: wp('5%'),
      color: colors.white,
      fontFamily: 'Poppins-Regular',
    },
  btnUploadIcon: isTabDevice()
    ? {
      width: wp('2%'),
      resizeMode: 'contain',
      marginLeft: wp('1.5%'),
    }
    : {
      width: wp('5%'),
      resizeMode: 'contain',
      marginLeft: wp('5%'),
    },
  btnUpload: isTabDevice()
    ? {
      backgroundColor: colors.aquaBlue,
      width: '100%',
      height: wp('5%'),
      padding: wp('1.2%'),
      borderRadius: wp('1%'),
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      marginRight: hp('2%'),
      marginTop: wp('2%'),
    }
    : {
      backgroundColor: colors.aquaBlue,
      width: '100%',
      height: wp('15%'),
      padding: wp('3%'),
      borderRadius: wp('3%'),
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginRight: hp('2%'),
      marginTop: wp('2%'),
      marginBottom: wp('2%'),
    },
});
export default playersInfoStyle;
