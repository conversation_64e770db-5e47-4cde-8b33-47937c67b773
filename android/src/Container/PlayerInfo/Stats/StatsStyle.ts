import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  iapWrapper: isTabDevice()
    ? {
        height: wp('70%'),
        width: '100%',
        paddingBottom: wp('5%'),
      }
    : {
        width: wp('100%'),
        height: hp('75%'),
        paddingBottom: hp('10%'),
      },
  iapContent: isTabDevice()
    ? {
        width: wp('70%'),
        height: '100%',
      }
    : {
        height: hp('65%'),
        width: wp('100%'),
      },
  spinnerWrapper: isTabDevice()
    ? {
        width: wp('70'),
        height: '90%',
      }
    : {},
  iapData: isTabDevice()
    ? {
        // padding: wp('2%'),
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.borderBlue,
        marginBottom: hp('1%'),
      }
    : {
        // width: wp('90%'),
        paddingTop: wp('4%'),
        paddingBottom: wp('2%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.borderBlue,
        marginBottom: hp('1%'),
        marginLeft: wp('2%'),
        marginRight: wp('2%'),
      },
  categories: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingBottom: hp('1%'),
        marginBottom: hp('3%'),
        borderBottomWidth: 1,
        borderBottomColor: colors.darkBlue,
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: hp('2%'),
        marginTop: hp('2%'),
        zIndex: 10,
      },
  stats: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        // backgroundColor: colors.green,
      },
  statsData: {
    width: 100,
    height: 50,
    backgroundColor: '#1DC4D2',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  comment: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: hp('2%'),
        paddingBottom: hp('2%'),
        borderRadius: wp('2%'),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: hp('2%'),
        paddingBottom: hp('2%'),
        marginLeft: wp('2%'),
        marginRight: wp('2%'),
        borderRadius: wp('2%'),
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: hp('20%'),
        height: hp('17%'),
      },
  commentText: isTabDevice()
    ? {
        color: colors.white,
        backgroundColor: colors.borderBlue,
        borderBottomWidth: 0,
        width: '80%',
        height: 50,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        backgroundColor: colors.borderBlue,
        borderBottomWidth: 0,
        width: '100%',
        height: 50,
        fontSize: wp('2.5%'),
        marginBottom: wp('1%'),
      },
  saveButton: {
    backgroundColor: colors.aquaBlue,
    width: wp('8%'),
    height: hp('6%'),
    borderRadius: wp('1%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveText: {
    color: colors.white,
    fontSize: wp('1.5%'),
  },
  commentsList: isTabDevice()
    ? {
        height: hp('32%'),
      }
    : {
        height: wp('60%'),
        marginBottom: wp('10%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  mobileComment: {
    width: '100%',
  },
  mobileCommentsaveButton: {
    width: '100%',
    padding: wp('2%'),
    borderRadius: wp('3%'),
    backgroundColor: colors.aquaBlue,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  mobileCommentsaveText: {
    color: colors.white,
    fontSize: wp('4%'),
  },
  iapScroll: {},
  iapInnerScroll: {
    marginBottom: wp('20%'),
  },
  filterButton: isTabDevice()
    ? {
        alignItems: 'center',
        alignSelf: 'flex-end',
        backgroundColor: colors.green,
        borderRadius: 30,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 10,
        padding: wp('0.4%'),
        width: wp('10%'),
      }
    : {
        alignItems: 'center',
        alignSelf: 'flex-end',
        backgroundColor: colors.green,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 10,
        marginRight: 10,
        marginTop: -3,
        padding: wp('0.4%'),
        height: wp('10%'),
        width: wp('20%'),
      },
  filterButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
});
