import React, { useEffect, useState, FC, useMemo } from 'react';
import { Text, View } from 'react-native';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useDispatch, useSelector } from 'react-redux';
import StateCategories from '../../../components/PlayerInfo/IAP/IAPCategories';
import TableStats from '../../../components/Table/TableStats';
import { isTabDevice } from '../../../config/appConfig';
import {
  statsCategoryList,
  StateApiStrings,
  statsCategory,
  filterType,
} from '../../../constants/data';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';
import { PLAYER_STATS_CATEGORY_SET } from '../../../store/actionTypes/PlayerIAP/PlayerIapAction';
import { RootStore } from '../../../store/store';
import PlayerStatsFilter from '../../PlayerStatsFilterContainer/PlayerStatsFilter';
import customStatStyles from './StatsStyle';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../../constants/services';
import {
  PLAYER_STATS_FAIL,
  PLAYER_STATS_REQUEST,
  PLAYER_STATS_SUCCESS,
  PLAYER_STATS_SEASON_REQUEST,
  PLAYER_STATS_SEASON_SUCCESS,
  PLAYER_STATS_SEASON_FAIL,
  PLAYER_STATS_TOURNAMENT_REQUEST,
  PLAYER_STATS_TOURNAMENT_SUCCESS,
  PLAYER_STATS_TOURNAMENT_FAIL,
  PLAYER_STATS_TOTAL_TABLE_DATA_REQUEST,
  PLAYER_STATS_TOTAL_TABLE_DATA_SUCCESS,
  PLAYER_STATS_TOTAL_TABLE_DATA_FAIL,
  RESET_TABLE_DATA,
  PLAYER_STATS_GET_TEAMS_REQUEST,
  PLAYER_STATS_GET_TEAMS_SUCCESS,
  PLAYER_STATS_GET_TEAMS_FAIL,
  PLAYER_STATS_GET_OPPONENT_REQUEST,
  PLAYER_STATS_GET_OPPONENT_SUCCESS,
  PLAYER_STATS_GET_OPPONENT_FAIL,
} from '../../../store/actionTypes/PlayerStats/PlayerStatsActionTypes';
import useApiPromise from '../../../hooks/useApiPromise';
import TagList from '../../../components/TagList/TagList';
import { dateTimeConversion } from '../../../helpers/index';
import { NOT_RATED } from '../../../constants/constants';
import { teamType } from '../../../store/reducers/PlayerStats/PlayerStatsReducer';

interface IWarbTab {
  wrap: any;
  children: any;
}
const WrapTab = ({ wrap, children }: IWarbTab) =>
  isTabDevice() ? wrap(children) : children;

interface PlayerInfoData {
  sportsProfileId: string;
  userId: string;
}

interface StatsDataType {
  PlayerInfoData: PlayerInfoData;
  teamID: string;
}

const State: FC<StatsDataType> = ({ PlayerInfoData, teamID }) => {
  const { sportsProfileId } = PlayerInfoData || {};
  const StatsStyle = useStyles(customStatStyles);
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [fetchStatsTableDataApi] = useApi();
  const [fetchSeasonData] = useApiPromise();
  const [fetchTournamentData] = useApiPromise();
  const [fetchOponents] = useApiPromise(); 
  const [fetchListOfTeams] = useApiPromise();
  const [fetchTableData] = useApiPromise();
  const dispatch = useDispatch();
  const { selectedIAPCategory: selectedStatCategory } = useSelector(
    (state: RootStore) => state.playerIAP
  );
  const [selectedSeasons, setSelectedSeasons] = useState([]);
  const [selectedTournaments, setSelectedTournaments] = useState([]);
  const [selectedTeams, setSelectedTeams] = useState<teamType[]>([]);
  const [selectedOpponent, setSelectedOpponent] = useState([]);
  const initialDateRange = {
    startDate: '',
    endDate: '',
  };
  const [selectedDateRange, setSelectedDateRange] = useState(initialDateRange);
  const [selectedTempDateRange, setSelectedTempDateRange] =
    useState(initialDateRange);
  const [isEndDateError, setIsEndDateError] = useState(false);
  const [tagListData, setTagListData] = useState<any>([]);
  const [isStartDateError, setIsStartDateError] = useState(false);
  const [selectedCategoryTemp, setSelectedCategoryTemp] = useState(null);

  const {
    seasonsData,
    tournamentsData,
    tableTotalData,
    tableDataLoading,
    teamData,
    opponentData,
    aggregateData,
    aggregateTotalRecords,
    aggregatePage,
    averageData,
    averageTotalRecords,
    averagePage,
    matchWiseData,
    matchWiseTotalRecords,
    matchWisePage,
  } = useSelector((state: RootStore) => state?.playerStats);

  useEffect(() => {
    statsCategoryList?.length && selectCategory(statsCategoryList?.[0]);
  }, [statsCategoryList]);

  const onCategoryItemPress = (item: any) => {
    setSelectedCategoryTemp(item);
    selectCategory(item);
  };

  const selectedFilterProps = {
    selectedSeasons,
    selectedTeams,
    selectedTournaments,
    setSelectedSeasons,
    setSelectedTournaments,
    setSelectedTeams,
    seasonsData,
    tournamentsData,
    teamData,
    teamID,
    selectedStatCategory,
    selectedDateRange,
    setSelectedDateRange,
    isEndDateError,
    isStartDateError,
    opponentData,
    selectedOpponent,
    setSelectedOpponent,
    selectedTempDateRange,
    setSelectedTempDateRange,
  };

  const getReadableDateStamp = (dateStamp: string) => {
    const { year, month, date } = dateTimeConversion(new Date(dateStamp));

    return `${date < 10 ? 0 : ''}${date}/${
      month < 10 ? 0 : ''
    }${month}/${year}`;
  };

  //Add types to selected filter options to be able to remove tags in onRemoveTagHandler
  useEffect(() => {
    const customSelectedSeasons = selectedSeasons?.map((item: Object) => ({
      ...item,
      type: filterType?.SEASON,
    }));
    const customSelectedTournaments = selectedTournaments?.map(
      (item: Object) => ({
        ...item,
        type: filterType?.TOURNAMENT,
      })
    );
    const customSelectedTeams = selectedTeams?.map((item: Object) => ({
      ...item,
      type: filterType?.TEAM,
    }));
    const customSelectedOpponents = selectedOpponent?.map((item: Object) => ({
      ...item,
      type: filterType?.OPPONENT,
    }));
    const customSelectedDateRange = selectedDateRange?.endDate
      ? [
          {
            _id: 'dateRange',
            type: filterType?.DATE_RANGE,
            name: `${getReadableDateStamp(
              selectedDateRange?.startDate
            )} - ${getReadableDateStamp(selectedDateRange?.endDate)}`,
          },
        ]
      : [];

    setTagListData([
      ...customSelectedSeasons,
      ...customSelectedTournaments,
      ...customSelectedTeams,
      ...customSelectedOpponents,
      ...customSelectedDateRange,
    ]);
  }, [
    selectedTeams,
    selectedTournaments,
    selectedSeasons,
    selectedOpponent,
    selectedDateRange,
  ]);

  const seasonIds = useMemo(
    () => selectedSeasons?.map(({ _id }) => _id),
    [selectedSeasons]
  );

  const tournamentIds = useMemo(
    () => selectedTournaments?.map(({ _id }) => _id),
    [selectedTournaments]
  );

  const teamIds = useMemo(
    () => selectedTeams?.map(({ _id }) => _id),
    [selectedTeams]
  );

  const opponentIds = useMemo(
    () => selectedOpponent?.map(({ _id }) => _id),
    [selectedOpponent]
  );

  const getSeasonData = () => {
    fetchSeasonData(
      `/api/v1/seasons?playedMatches=true&page=1&size=50&sportsProfileId=${sportsProfileId}`,
      PLAYER_STATS_SEASON_REQUEST,
      PLAYER_STATS_SEASON_SUCCESS,
      PLAYER_STATS_SEASON_FAIL,
      null,
      '',
      'GET',
      undefined,
      EVENT_SERVICE
    );
  };

  const getTournamentData = () => {
    fetchTournamentData(
      `/api/v1/tournaments?playedMatches=true&page=1&size=50&sportsProfileId=${sportsProfileId}`,
      PLAYER_STATS_TOURNAMENT_REQUEST,
      PLAYER_STATS_TOURNAMENT_SUCCESS,
      PLAYER_STATS_TOURNAMENT_FAIL,
      null,
      '',
      'GET',
      undefined,
      EVENT_SERVICE
    );
  };

  const getListOfTeams = () => {
    fetchListOfTeams(
      `/api/v1/sport-profiles/${sportsProfileId}/teams?playedInMatches=true&page=1&size=50`,
      PLAYER_STATS_GET_TEAMS_REQUEST,
      PLAYER_STATS_GET_TEAMS_SUCCESS,
      PLAYER_STATS_GET_TEAMS_FAIL,
      null,
      '',
      'GET',
      undefined,
      EVENT_SERVICE
    );
  };

  const getListOfOpponents = () => {
    const teamIds = teamData?.map(({ _id }) => _id);
    fetchOponents(
      `/api/v1/opponents?page=1&size=50&playedMatchAgainstTeamIds=${teamIds}`,
      PLAYER_STATS_GET_OPPONENT_REQUEST,
      PLAYER_STATS_GET_OPPONENT_SUCCESS,
      PLAYER_STATS_GET_OPPONENT_FAIL,
      null,
      '',
      'GET',
      undefined,
      EVENT_SERVICE
    );
  };

  useEffect(() => {
    teamData?.length && getListOfOpponents();
  }, [teamData]);

  useEffect(() => {
    PlayerInfoData?.userId && getListOfTeams();
  }, [PlayerInfoData]);

  const selectCategoryType = (selectedStatCategory: string) => {
    const selectedCatergoryName = statsCategoryList?.find(
      item => item?._id === selectedStatCategory
    )?.name;
    return (
      (selectedCatergoryName === statsCategory.AGGREGATE &&
        StateApiStrings.Aggregate) ||
      (selectedCatergoryName === statsCategory.AVERAGE &&
        StateApiStrings.Average) ||
      (selectedCatergoryName === statsCategory.MATCH_WISE &&
        StateApiStrings.matchWise)
    );
  };

  const getOverallPlayerStat = () => {
    if (sportsProfileId?.length && selectedStatCategory?.length) {
      fetchTableData(
        `/api/v1/sport-profiles/${sportsProfileId}/overall-player-stats?type=${selectCategoryType(
          selectedStatCategory
        )}&seasonIds=${seasonIds}&tournamentIds=${tournamentIds}&teamIds=${teamIds}`,
        PLAYER_STATS_TOTAL_TABLE_DATA_REQUEST,
        PLAYER_STATS_TOTAL_TABLE_DATA_SUCCESS,
        PLAYER_STATS_TOTAL_TABLE_DATA_FAIL,
        null,
        '',
        'GET',
        undefined,
        EVENT_SERVICE
      );
    }
  };

  useEffect(() => {
    setSelectedDateRange(initialDateRange);
    setIsEndDateError(false);
    setIsStartDateError(false);
    if (selectedStatCategory !== '3') {
      getOverallPlayerStat();
    }
  }, [selectedStatCategory]);

  const getFilterData = () => {
    getSeasonData();
    getTournamentData();
  };

  useEffect(() => {
    getFilterData();
  }, []);

  const getCurrentTeam = (data: teamType[] = [], teamID = '') => {
    return data?.find(({ _id }) => _id === teamID) || {};
  };

  //autoSelect current team
  useEffect(() => {
    const currentTeam = getCurrentTeam(teamData, teamID);
    if (Object.keys(currentTeam || {})?.length) {
      const { _id, name }: teamType = currentTeam;
      setSelectedTeams([
        {
          _id,
          name,
        },
      ]);
    }
  }, [teamData, teamID]);

  const fetchStatsTableData = (tabName: string, page = 1) => {
    const startDate = new Date(selectedDateRange?.startDate);
    const endDate = new Date(selectedDateRange?.endDate);

    fetchStatsTableDataApi(
      `/api/v1/sport-profiles/${
        PlayerInfoData?.sportsProfileId
      }/player-stats?type=${tabName}&${
        selectedStatCategory !== '3'
          ? `seasonIds=${seasonIds}&tournamentIds=${tournamentIds}&teamIds=${teamIds}`
          : `opponentIds=${opponentIds}&teamIds=${teamIds}`
      }&page=${page}&size=20${
        selectedStatCategory === '3' && selectedDateRange?.endDate
          ? `&startDate=${startDate?.toISOString()}&endDate=${endDate?.toISOString()}`
          : ''
      }`,
      PLAYER_STATS_REQUEST,
      PLAYER_STATS_SUCCESS,
      PLAYER_STATS_FAIL,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE,
      selectedStatCategory
    );
  };

  const selectCategory = (item: any) => {
    dispatch({
      type: PLAYER_STATS_CATEGORY_SET,
      payload: item._id,
    });
  };

  const selectedStatsCatergoryName = () => {
    return selectedStatCategory == 1
      ? StateApiStrings?.Aggregate
      : selectedStatCategory == 2
      ? StateApiStrings?.Average
      : StateApiStrings?.matchWise;
  };
  const [isOnCloseClicked, setIsOnCloseClicked] = useState(false);
  useEffect(() => {
    dispatch({
      type: RESET_TABLE_DATA,
    });
    if (!filterModalVisible && selectedStatCategory && selectedTeams?.length) {
      fetchStatsTableData(selectedStatsCatergoryName());
      if (selectedStatCategory !== '3') {
        getOverallPlayerStat();
      }
    }
  }, [
    selectedStatCategory,
    teamID,
    filterModalVisible,
    selectedTeams,
    selectedTournaments,
    selectedSeasons,
    selectedOpponent,
    selectedDateRange,
  ]);

  const onReachEndHandler = () => {
    if (selectedStatCategory === '1') {
      if (aggregateTotalRecords > (aggregateData?.length || 0)) {
        fetchStatsTableData(selectedStatsCatergoryName(), aggregatePage + 1);
      }
    }
    if (selectedStatCategory === '2') {
      if (averageTotalRecords > (averageData?.length || 0)) {
        fetchStatsTableData(selectedStatsCatergoryName(), averagePage + 1);
      }
    }
    if (selectedStatCategory === '3') {
      if (matchWiseTotalRecords > (matchWiseData?.length || 0)) {
        fetchStatsTableData(selectedStatsCatergoryName(), matchWisePage + 1);
      }
    }
  };

  const removeTag = (item: any, selecteditems: any) => {
    return selecteditems?.filter(({ _id }: any) => _id !== item?._id);
  };
  const onRemoveTagHandler = (selectedItem: any) => {
    if (selectedItem?.type === filterType.SEASON) {
      const filteredSeason = removeTag(selectedItem, selectedSeasons);
      setSelectedSeasons(filteredSeason);
    }
    if (selectedItem?.type === filterType.TOURNAMENT) {
      const filteredTournaments = removeTag(selectedItem, selectedTournaments);
      setSelectedTournaments(filteredTournaments);
    }
    if (selectedItem?.type === filterType.OPPONENT) {
      const filteredOpponents = removeTag(selectedItem, selectedOpponent);
      setSelectedOpponent(filteredOpponents);
    }
    if (selectedItem?.type === filterType.DATE_RANGE) {
      setSelectedDateRange(initialDateRange);
    }
    if (selectedItem?.type === filterType.TEAM) {
      if (selectedItem?._id !== teamID) {
        const filteredTeams = removeTag(selectedItem, selectedTeams);
        setSelectedTeams(filteredTeams);
      }
    }
  };

  const onModalCloseHandler = () => {
    //if start date is selected & end date is not, show error
    if (
      selectedStatCategory === '3' &&
      selectedTempDateRange.startDate &&
      !selectedTempDateRange.endDate
    ) {
      setIsEndDateError(true);
    } else if (
      selectedStatCategory === '3' &&
      !selectedTempDateRange.startDate &&
      selectedTempDateRange.endDate
    ) {
      setIsStartDateError(true);
    } else {
      setFilterModalVisible(false);
    }
  };

  const onCloseHandler = () => {
    setIsStartDateError(false);
    setIsEndDateError(false);
    setFilterModalVisible(false);
  };

  useEffect(() => {
    selectedDateRange.endDate && setIsEndDateError(false);
    selectedDateRange.startDate && setIsStartDateError(false);
  }, [JSON.stringify(selectedDateRange)]);

  //Reset filter selection when switching between tabs
  useEffect(() => {
    setSelectedOpponent([]);
    setSelectedSeasons([]);
    setSelectedTournaments([]);
  }, [selectedStatCategory]);

  const tableData = useMemo(() => {
    const data =
      selectedStatCategory === '1'
        ? aggregateData
        : selectedStatCategory === '2'
        ? averageData
        : matchWiseData;

    return data?.map(col => {
      let selecColumn = { ...col };
      if (!selecColumn.rating) {
        selecColumn.rating = NOT_RATED;
      }
      return { ...selecColumn };
    });
  }, [selectedStatCategory, aggregateData, averageData, matchWiseData]);

  const tableDataPage = useMemo(
    () =>
      selectedStatCategory === '1'
        ? aggregatePage
        : selectedStatCategory === '2'
        ? averagePage
        : matchWisePage,
    [selectedStatCategory, aggregatePage, averagePage, matchWisePage]
  );

  const tableDataTotalRecords = useMemo(
    () =>
      selectedStatCategory === '1'
        ? aggregateTotalRecords
        : selectedStatCategory === '2'
        ? averageTotalRecords
        : matchWiseTotalRecords,
    [
      selectedStatCategory,
      aggregateTotalRecords,
      averageTotalRecords,
      matchWiseTotalRecords,
    ]
  );

  return (
    <WrapTab
      wrap={(children: any) => (
        <KeyboardAwareScrollView
          contentContainerStyle={StatsStyle.iapInnerScroll}
          enableOnAndroid
        >
          {children}
        </KeyboardAwareScrollView> 
      )}
    > 
      <PlayerStatsFilter
        modalVisible={filterModalVisible}
        onHandleFilter={onModalCloseHandler}
        onClose={onCloseHandler}
        {...selectedFilterProps}
      />
      <View style={StatsStyle.iapContent}>
        <View style={StatsStyle.iapData}>
          <ScrollView style={{paddingBottom: 10}}>
          <View style={StatsStyle.categories}>
            <StateCategories
              title={'Select Stats Option'}
              categories={statsCategoryList}
              onCategoryItemPress={onCategoryItemPress}
              selectedIAPCategory={selectedStatCategory}
              selectedCategoryTemp={selectedCategoryTemp}
            />
            <TouchableOpacity
              style={StatsStyle.filterButton}
              onPress={() => {
                setFilterModalVisible(true);
              }}
            >
              <Text style={StatsStyle.filterButtonText}>Filter</Text>
            </TouchableOpacity>
          </View>
          <View style={StatsStyle.stats}>
            <View>
              <TagList
                tagList={tagListData}
                onRemove={item => onRemoveTagHandler(item)}
              />
            </View>
            <TableStats
              tableData={tableData}
              tableTotalData={tableTotalData}
              selectedStatCategory={selectedStatCategory}
              tableDataLoading={tableDataLoading}
              tableDataPage={tableDataPage}
              tableDataTotalRecords={tableDataTotalRecords}
              onReachEndHandler={onReachEndHandler}
              getReadableDateStamp={getReadableDateStamp}
            />
          </View>
          </ScrollView>
        </View>
      </View>
    </WrapTab>
  );
};
export default State;
