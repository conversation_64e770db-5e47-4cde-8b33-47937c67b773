import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPStatGraphStyles = colors => ({
  scroll: isTabDevice()
    ? {}
    : {
        paddingBottom: wp('50%'),
      },
  legendIconContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        width: '100%',
        marginTop: wp('1%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        width: '100%',
        marginTop: wp('3%'),
        marginBottom: wp('-5%'),
      },
  legendIconWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendIcon: {
    backgroundColor: colors.aquaBlue,
    borderRadius: wp('100%'),
    width: 15,
    height: 15,
    marginRight: 10,
  },
  legendIconText: {
    fontSize: 15,
    color: colors.white,
    fontFamily: 'Poppins-Bold',
  },
});
export default IAPStatGraphStyles;
