import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { IAPGraphPageSize } from '../../../constants/constants';
import { FOOTBALL_SERVICE } from '../../../constants/services';
import useApi from '../../../hooks/useApi';
import {
  IAP_STAT_GRAPH_DATE_RANGE_CHANGED,
  IAP_STAT_SET_GRAPH_VIEW,
  PLAYER_IAP_STAT_GRAPH_DATA_FAILED,
  PLAYER_IAP_STAT_GRAPH_DATA_REQUEST,
  PLAYER_IAP_STAT_GRAPH_DATA_SUCCESS,
} from '../../../store/actionTypes/PlayerIAP/PlayerIapAction';

import { ScrollView } from 'react-native';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';
import NoContentMessage from '../../../components/NoContents/NoContentMessage';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import Chart from './Chart';
import customIAPStatGraphStyles from './IAPStatGraphStyles.js';

const IAPStatGraph = ({ sportProfileId, iapCategoryId, criteriaId }) => {
  const IAPStatGraphStyles = useStyles(customIAPStatGraphStyles);
  const [getGraphData] = useApi();
  const dispatch = useDispatch();
  const [labels, setLabels] = useState([]);
  const [currentValues, setCurrentValues] = useState([]);
  const [targetValues, setTargetValues] = useState([]);
  const {
    statGraphDateRange,
    statGraphData,
    statGraphLoading,
    statGraphCategoryName,
    statGraphCriteriaName,
  } = useSelector(state => state.playerIAP);

  useEffect(() => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const startDate = new Date(currentDate.getFullYear(), currentMonth, 1);
    const endDate = new Date(currentDate.getFullYear(), currentMonth + 1, 0);
    dispatch({
      type: IAP_STAT_SET_GRAPH_VIEW,
      payload: true,
    });

    dispatch({
      type: IAP_STAT_GRAPH_DATE_RANGE_CHANGED,
      payload: {
        from: startDate,
        to: endDate,
      },
    });

    return () => {
      dispatch({
        type: IAP_STAT_SET_GRAPH_VIEW,
        payload: false,
      });
    };
  }, []);

  useEffect(() => {
    const loadGraphData = () => {
      const startDate = new Date(statGraphDateRange.from).toISOString();
      const endDate = new Date(statGraphDateRange.to).toISOString();
      const page = 1;
      getGraphData(
        `/api/v1/sport-profiles/${sportProfileId}/categories/${iapCategoryId}/criteria/${criteriaId}/iap-stats?startDate=${startDate}&endDate=${endDate}&page=${page}&size=${IAPGraphPageSize}`,
        PLAYER_IAP_STAT_GRAPH_DATA_REQUEST,
        PLAYER_IAP_STAT_GRAPH_DATA_SUCCESS,
        PLAYER_IAP_STAT_GRAPH_DATA_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
    };

    sportProfileId && iapCategoryId && criteriaId && loadGraphData();
  }, [sportProfileId, iapCategoryId, criteriaId, statGraphDateRange]);

  useEffect(() => {
    if (statGraphData && statGraphData.length) {
      const currentValueList: any[] = [];
      const targetValueList: any[] = [];

      statGraphData.forEach(
        ({
          current,
          target,
          dateTime,
        }: {
          current: number;
          target: number;
          dateTime: Date;
        }) => {
          currentValueList.push([dateTime, current]);
          targetValueList.push([dateTime, target]);
        }
      );

      setCurrentValues(currentValueList);
      setTargetValues(targetValueList);
    } else {
      setCurrentValues([]);
      setTargetValues([]);
    }
  }, [statGraphData]);

  return (
    <View>
      {statGraphLoading ? (
        <ActivitySpinner />
      ) : targetValues?.length ? (
        <ScrollView
          contentContainerStyle={IAPStatGraphStyles.scroll}
          scrollEnabled={isTabDevice() ? false : true}
        >
          <Chart
            statGraphCategoryName={statGraphCategoryName}
            statGraphCriteriaName={statGraphCriteriaName}
            summarySelectedDetails={{
              summaryFromDate: new Date(statGraphDateRange.from).toISOString(),
              summaryToDate: new Date(statGraphDateRange.to).toISOString(),
            }}
            currentValues={currentValues}
            targetValues={targetValues}
          />
        </ScrollView>
      ) : (
        <NoContentMessage message={'No data available'} />
      )}
    </View>
  );
};

export default IAPStatGraph;
