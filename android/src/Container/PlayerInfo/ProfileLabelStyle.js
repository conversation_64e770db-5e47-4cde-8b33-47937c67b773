import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const ProfileLabelStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('60%'),
      }
    : {
        marginTop: hp('2%'),
        width: wp('100%'),
        borderBottomWidth: 1,
        borderBottomColor: colors.borderBlue,
      },
  lableView: isTabDevice()
    ? {
        marginTop: hp('1.9%'),
        marginBottom: hp('1.9%'),
        justifyContent: 'space-between',
        marginRight: hp('7%'),
      }
    : {
        marginBottom: hp('1%'),
        marginHorizontal: wp('4%'),
      },
  text: isTabDevice()
    ? {
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      }
    : {
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.fontBlue,
      },
  textSelected: isTabDevice()
    ? {
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-Bold',
        color: colors.green,
      }
    : {
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.green,
      },
  list: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
});
export default ProfileLabelStyle;
