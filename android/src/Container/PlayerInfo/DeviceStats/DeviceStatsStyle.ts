import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  wrapper: isTabDevice()
    ? {
        height: hp('80%'),
        width: '100%',
        paddingBottom: wp('5%'),
      }
    : {
        width: wp('100%'),
        height: hp('53%'),
        paddingBottom: hp('10%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
      },
  content: isTabDevice()
    ? {
        width: wp('70%'),
        padding: wp('2%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.borderBlue,
        marginBottom: hp('1%'),
      }
    : {
        width: wp('90%'),
        padding: wp('4%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.borderBlue,
        marginBottom: hp('1%'),
        marginLeft: wp('2%'),
        marginRight: wp('2%'),
      },
  contentWrapper: {
    paddingTop: wp('2%'),
    paddingBottom: wp('2%'),
  },
  txtWhite: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  headerText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2.5%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  syncText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  syncButton: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('100%'),
        padding: wp('1%'),
        paddingTop: wp('0.5%'),
        paddingBottom: wp('0.5%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('100%'),
        padding: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginTop: wp('4%'),
      },
  statsWrapper: isTabDevice()
    ? {
        width: '100%',
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        borderRadius: wp('0.5%'),
        backgroundColor: colors.darkBlue,
        marginBottom: hp('1%'),
        height: hp('65%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        width: '100%',
        padding: wp('4%'),
        borderRadius: wp('2%'),
        marginTop: wp('3%'),
        backgroundColor: colors.darkBlue,
        height: hp('30%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  deviceSelectionWrapper: isTabDevice()
    ? {
        marginTop: hp('7%'),
        paddingRight: wp('2%'),
      }
    : {
        marginTop: hp('3%'),
        marginBottom: hp('3%'),
        paddingRight: wp('2%'),
        paddingLeft: wp('2%'),
      },
  spinnerWrapper: isTabDevice()
    ? {
        width: wp('70'),
        height: '90%',
      }
    : {},
});
