import React, { useState } from 'react';
import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import SelectionModal from '../../../components/modal/SelectionModal/SelectionModal';
import { isTabDevice } from '../../../config/appConfig';
import { playerStatDevicesArray } from '../../../constants/constants';
import useStyles from '../../../hooks/useStyles';
import { SET_SELECTED_DEVICE } from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import { RootStore } from '../../../store/store';
import customDeviceStatsStyle from './DeviceStatsStyle';

const SelectPlayerDeviceContainer = () => {
  const dispatch = useDispatch();
  const deviceStatsStyle = useStyles(customDeviceStatsStyle);

  const [isDeviceSelectionModalOpen, setIsDeviceSelectionModalOpen] =
    useState<boolean>(false);
  const { selectedDevice, PlayerMakerSyncedData } = useSelector(
    (state: RootStore) => state?.deviceStats
  );

  const isDeviceSynced = !!Object.keys(PlayerMakerSyncedData || {})?.length;

  return (
    <View style={deviceStatsStyle.deviceSelectionWrapper}>
      <View>
        <SelectionModal
          isOnPlayerDevice={true}
          title={`Select device`}
          items={playerStatDevicesArray}
          onCloseHook={setIsDeviceSelectionModalOpen}
          onSelectItemHook={item => {
            item?.[0]?.value &&
              dispatch({
                type: SET_SELECTED_DEVICE,
                payload: { data: item[0].value },
              });
          }}
          selectedItemLabel={selectedDevice || ''}
          defaultValues={[selectedDevice || '']}
          isModalOpen={isDeviceSelectionModalOpen}
          selectFirstOptionOnInitialRender
          isCheckedIconShown={[isDeviceSynced]}
          openModal={() => {}}
        />
      </View>
    </View>
  );
};

export default SelectPlayerDeviceContainer;
