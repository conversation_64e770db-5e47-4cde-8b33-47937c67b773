import { Platform } from 'react-native';

import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

//!TODO android/AK-2292
//TODO need to update this file UI once after android development done

const PlayerCommonStyle = colors => ({
  navHeaderView: isTabDevice()
    ? {
        flexDirection: 'row',
      }
    : {
        flexDirection: 'row',
        paddingLeft: wp('3%'),
      },
  topNavSelected: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        marginTop: hp('3%'),
        marginBottom: hp('3%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('3%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        // width: Platform.OS === 'android' ? wp('47.5%') : wp('45%'),
      },
  topNavNotSelected: isTabDevice()
    ? {
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
      }
    : {
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('3%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        // width: Platform.OS === 'android' ? wp('47.5%') : wp('45%'),
      },
  navHeadertext: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2.5%'),
        fontFamily: 'Poppins-Bold',
        marginLeft: wp('3%'),
        marginRight: wp('3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
  headerStyle: isTabDevice()
    ? {
        flexDirection: 'row',
        paddingBottom: 10,
      }
    : {
        flexDirection: 'row',
        paddingBottom: 10,
        paddingLeft: Platform.OS === 'android' ? wp('2.5%') : 0,
      },
  headerText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2.5%'),
        fontWeight: 'bold',
        marginTop: hp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontWeight: 'bold',
        marginTop: hp('2%'),
        marginLeft: wp('2%'),
      },
  dateView: {
    backgroundColor: '#36D982',
    height: 35,
    width: 80,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  dateText: {
    color: '#fff',
  },
  msg: {
    fontSize: 15,
    color: '#fff',
  },
  optionView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  btnText: {
    color: '#fff',
    marginHorizontal: 5,
  },
  highlight: {
    backgroundColor: '#3f4e63',
    borderRadius: 10,
    marginVertical: 5,
  },
  contain: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingLeft: 10,
    justifyContent: 'space-between',
  },
  primary: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
export default PlayerCommonStyle;
