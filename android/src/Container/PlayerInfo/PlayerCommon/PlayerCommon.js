import React, { useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import customPlayerCommonStyle from './PlayerCommonStyle';

// container

import PlayerInfoMedicalInjuriesContainer from '../../../Container/PlayerInfoMedicalContainer/PlayerInfoMedicalInjuriesContainer';
import PlayerInfoUpdateContainer from '../../../Container/PlayerInfoUpdateContainer/PlayerInfoUpdateContainer';
import useStyles from '../../../hooks/useStyles';
import PlayerInfoActivityContainer from '../../PlayerInfoActivityContainer/PlayerInfoActivityContainer';
import PlayerInfoMedicalHistoryContainer from '../../PlayerInfoMedicalContainer/PlayerInfoMedicalHistoryContainer';

export default function PlayerCommon({
  option,
  message,
  highlight,
  navHeader,
  header,
  HeaderText,
  navHeadertext1,
  navHeadertext2,
  navHeadertext3,
  playerLabelIdData,
}) {
  const PlayerCommonStyle = useStyles(customPlayerCommonStyle);
  const [tabSelected, setTabSelected] = useState(1);

  const playerInfoComponent = () => {
    switch (playerLabelIdData) {
      case 3:
        return <PlayerInfoUpdateContainer />;
      case 4:
        return (
          <>
            {navHeader && (
              <View style={PlayerCommonStyle.navHeaderView}>
                <TouchableOpacity
                  style={
                    tabSelected === 1
                      ? PlayerCommonStyle.topNavSelected
                      : PlayerCommonStyle.topNavNotSelected
                  }
                  onPress={() => setTabSelected(1)}
                >
                  <Text style={PlayerCommonStyle.navHeadertext}>
                    {navHeadertext1}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={
                    tabSelected === 2
                      ? PlayerCommonStyle.topNavSelected
                      : PlayerCommonStyle.topNavNotSelected
                  }
                  onPress={() => setTabSelected(2)}
                >
                  <Text style={PlayerCommonStyle.navHeadertext}>
                    {navHeadertext2}
                  </Text>
                </TouchableOpacity>
                {navHeadertext3 && (
                  <TouchableOpacity
                    style={
                      tabSelected === 3
                        ? PlayerCommonStyle.topNavSelected
                        : PlayerCommonStyle.topNavNotSelected
                    }
                    onPress={() => setTabSelected(3)}
                  >
                    <Text style={PlayerCommonStyle.navHeadertext}>
                      {navHeadertext3}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
            {tabSelected === 1 ? (
              <PlayerInfoMedicalHistoryContainer option={option} />
            ) : (
              <PlayerInfoMedicalInjuriesContainer option={option} />
            )}
          </>
        );
      case 5:
        return (
          <View>
            {navHeader && (
              <View style={PlayerCommonStyle.navHeaderView}>
                <TouchableOpacity
                  style={
                    tabSelected === 2
                      ? PlayerCommonStyle.topNavSelected
                      : PlayerCommonStyle.topNavNotSelected
                  }
                  onPress={() => setTabSelected(2)}
                >
                  <Text style={PlayerCommonStyle.navHeadertext}>
                    {navHeadertext1}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={
                    tabSelected === 1
                      ? PlayerCommonStyle.topNavSelected
                      : PlayerCommonStyle.topNavNotSelected
                  }
                  onPress={() => setTabSelected(1)}
                >
                  <Text style={PlayerCommonStyle.navHeadertext}>
                    {navHeadertext2}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={
                    tabSelected === 3
                      ? PlayerCommonStyle.topNavSelected
                      : PlayerCommonStyle.topNavNotSelected
                  }
                  onPress={() => setTabSelected(3)}
                >
                  <Text style={PlayerCommonStyle.navHeadertext}>
                    {navHeadertext3}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            <PlayerInfoActivityContainer selectedTab={tabSelected} />
          </View>
        );
      default:
        return <View></View>;
    }
  };

  return (
    <View style={{}}>
      {header && (
        <View style={PlayerCommonStyle.headerStyle}>
          <Text style={PlayerCommonStyle.headerText}>{HeaderText}</Text>
        </View>
      )}
      {playerInfoComponent()}
    </View>
  );
}
