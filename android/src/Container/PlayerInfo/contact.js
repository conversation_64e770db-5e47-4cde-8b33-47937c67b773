import React, { useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import customContactStyle from './contactStyle';
import useStyles from '../../hooks/useStyles';

export default function Contacts() {
  const contactStyle = useStyles(customContactStyle);
  return (
    <View style={contactStyle.container}>
      <Text style={contactStyle.contactsText}>Contacts</Text>
      <View style={contactStyle.contactView}>
        <View>
          <Text style={contactStyle.contactText}>Player Contact</Text>
          <Text style={contactStyle.contactNumber}>+65 6436 7228</Text>
        </View>
        <View>
          <Text style={contactStyle.contactText}>Parent Contact</Text>
          <Text style={contactStyle.contactNumber}>+65 6436 4785</Text>
        </View>
        <View>
          <Text style={contactStyle.contactText}>Parent Contact</Text>
          <Text style={contactStyle.contactNumber}>+65 6436 1122</Text>
        </View>
      </View>
    </View>
  );
}
