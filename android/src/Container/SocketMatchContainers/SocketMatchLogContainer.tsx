import React, { FC, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  MatchLogActions,
  matchSocketTypes,
  userRoleType,
} from '../../constants/constants';
import { getArrayIndexUsingKey } from '../../helpers/common';
import { RootStore } from '../../store/store';

interface SocketMatchLogContainerType {
  matchId: string;
  onMainActionClicked?: Function;
  onMatchActionClicked?: Function;
  onRefreshHandler?: Function;
  onPlayerActionClicked?: Function;
  deleteLogsForMatch?: Function;
}
export interface ActionMessageType {
  _id: string;
  matchId: string;
  timeStamp: string;
  activityId: string;
  comment: string;
  type: string;
  elapsedTime: string;
  performedByOpponent: boolean;
  socketMessageType: string;
}

export const mainActions = [
  MatchLogActions.START_GAME,
  MatchLogActions.END_GAME,
  MatchLogActions.FIRST_HALF_END,
  MatchLogActions.SECOND_HALF_START,
];

export const matchActions = [
  MatchLogActions.PENALTY_WON,
  MatchLogActions.PENALTY_CONCEDED,
  MatchLogActions.PENALTY_MISSED,
  MatchLogActions.RED_CARD,
  MatchLogActions.YELLOW_CARD,
  MatchLogActions.CORNER,
  MatchLogActions.ASSIST,
  MatchLogActions.GOAL_SCORED,
  MatchLogActions.GOAL_CONCEDED,
];

export const subActions = [MatchLogActions.SUB_IN, MatchLogActions.SUB_OUT];

const SocketMatchLogContainer: FC<SocketMatchLogContainerType> = ({
  matchId,
  onMainActionClicked,
  onMatchActionClicked,
  onPlayerActionClicked,
  onRefreshHandler,
  deleteLogsForMatch,
}) => {
  const dispatch = useDispatch();

  const {
    activities = [],
    matchSocketMessages = [],
    eventLogs,
    selectedTeamId,
    selectedEventIndex,
    matchSocketReverseMessages,
  } = useSelector((state: any) => state?.matchLog);

  const { matchActivity } = useSelector((state: any) => state?.playerMatchLog);
  const { userData } = useSelector((state: RootStore) => state?.auth);

  const isCoach =
    userRoleType.HEAD_COACH === userData?.type ||
    userRoleType.COACH === userData?.type;

  const matchActivitySummary = useMemo(
    () => eventLogs?.[selectedTeamId]?.[selectedEventIndex]?.matchLog,
    [eventLogs, selectedTeamId, selectedEventIndex]
  );

  const getAction = (action: string) => {
    const activity = activities?.find(
      (activity: any) => activity._id === action
    );
    return activity || null;
  };

  useEffect(() => {
    const [firstSocketMessage, secondSocketMessage] = (
      matchSocketMessages || []
    )?.slice(0, 2);

    if (
      matchSocketTypes.MATCH_ACTIVITY_CREATE ===
        firstSocketMessage?.socketMessageType &&
      firstSocketMessage?.matchId === matchId
    ) {
      const activityCodeFirstSocket = getAction(
        firstSocketMessage?.activityId || ''
      );
      const activityCodeSecondSocket = getAction(
        secondSocketMessage?.activityId || ''
      );
      if (isCoach) {
        const activityIndexDifference =
          firstSocketMessage?.activityIndex - matchActivitySummary?.length;

        if (MatchLogActions.MOVE_PLAYER === activityCodeFirstSocket?.code) {
          //if first activity and second activity are move player need to call swap
          //swap with empty place scenario need to cover

          if (MatchLogActions.MOVE_PLAYER === activityCodeSecondSocket?.code) {
            activityIndexDifference > 1
              ? onRefreshHandler?.()
              : onMatchActionClicked?.(
                  firstSocketMessage,
                  secondSocketMessage,
                  MatchLogActions.MOVE_PLAYER
                );
          } else {
            activityIndexDifference > 1
              ? onRefreshHandler?.()
              : onMatchActionClicked?.(
                  firstSocketMessage,
                  {},
                  MatchLogActions.MOVE_PLAYER
                );
          }
        } else if (
          subActions?.includes(activityCodeFirstSocket?.code) &&
          subActions?.includes(activityCodeSecondSocket?.code)
        ) {
          const subInSocketMsg =
            activityCodeFirstSocket?.code === MatchLogActions.SUB_IN
              ? firstSocketMessage
              : secondSocketMessage;
          const subOutSocketMsg =
            activityCodeFirstSocket?.code === MatchLogActions.SUB_OUT
              ? firstSocketMessage
              : secondSocketMessage;

          activityIndexDifference > 2
            ? onRefreshHandler?.()
            : onMatchActionClicked?.(
                subInSocketMsg,
                subOutSocketMsg,
                MatchLogActions.SUB_IN
              );
        } else if (mainActions?.includes(activityCodeFirstSocket?.code)) {
          //if activities are main activities
          activityIndexDifference > 1
            ? onRefreshHandler?.()
            : onMainActionClicked?.(
                firstSocketMessage,
                activityCodeFirstSocket?.code
              );
        } else if (matchActions?.includes(activityCodeFirstSocket?.code)) {
          // on click actions other than move player
          activityIndexDifference > 1
            ? onRefreshHandler?.()
            : onMatchActionClicked?.(
                firstSocketMessage,
                null,
                activityCodeFirstSocket?.code
              );
        }
      } else {
        //hanlde all actions for player
        const activityIndexDifference =
          firstSocketMessage?.activityIndex - matchActivity?.length;
        activityIndexDifference > 1
          ? onRefreshHandler?.()
          : onPlayerActionClicked?.(
              firstSocketMessage,
              activityCodeFirstSocket?.code
            );
      }
    }
  }, [JSON.stringify(matchSocketMessages), matchId]);

  useEffect(() => {
    if (
      matchSocketTypes.MATCH_ACTIVITY_DELETE ===
        matchSocketReverseMessages?.socketMessageType &&
      matchSocketReverseMessages?.matchId === matchId
    ) {
      if (isCoach) {
        const indexOfRecord = getArrayIndexUsingKey(
          eventLogs?.[selectedTeamId]?.[selectedEventIndex]?.matchLog,
          '_id',
          matchSocketReverseMessages?._id
        );

        indexOfRecord >= 0 &&
          deleteLogsForMatch?.(
            eventLogs?.[selectedTeamId]?.[selectedEventIndex]?.matchLog?.[
              indexOfRecord
            ],
            indexOfRecord
          );
      } else {
        const indexOfRecord = getArrayIndexUsingKey(
          matchActivity,
          '_id',
          matchSocketReverseMessages?._id
        );
        indexOfRecord >= 0 &&
          deleteLogsForMatch?.(matchActivity?.[indexOfRecord], indexOfRecord);
      }
    }
  }, [JSON.stringify(matchSocketReverseMessages), matchId]);

  return <></>;
};

export default SocketMatchLogContainer;
