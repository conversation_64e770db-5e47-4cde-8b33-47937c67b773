import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import customUserCreationTypeStyles from './userCreationTypeStyle';
import useStyles from '../../hooks/useStyles';

const UserCreationType = ({
  userCreationTypeMode,
  handleUserCreationType,
  isEditMode,
}) => {
  const UserCreationTypeStyles = useStyles(customUserCreationTypeStyles);
  const userCreationTypes = ['Koach', 'New'];
  return (
    //for rc 8 we hide this user type on Add user screen.
    <></>
    // <View style={UserCreationTypeStyles.userCreationTypeContainer}>
    //   <View style={UserCreationTypeStyles.userCreationTypeTitle}>
    //     <Text style={UserCreationTypeStyles.Text1}>User Type</Text>
    //   </View>
    //   <View
    //     style={UserCreationTypeStyles.userCreationTypeWrapper}
    //   >
    //     {userCreationTypes.map(userCreationType => (
    //       <TouchableOpacity
    //         key={userCreationType}
    //         onPress={() => handleUserCreationType(userCreationType)}
    //         disabled={isEditMode}
    //         style={
    //           userCreationType === userCreationTypeMode
    //             ? UserCreationTypeStyles.TouchablePressed
    //             : UserCreationTypeStyles.TouchableOpacity
    //         }
    //       >
    //         <Text style={UserCreationTypeStyles.Text}>{userCreationType}</Text>
    //       </TouchableOpacity>
    //     ))}
    //   </View>
    // </View>
  );
};

export default UserCreationType;
