import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const UserTypeStyles = colors => ({
  container: {
    flexDirection: 'row',
  },
  TouchablePressed: isTabDevice()
    ? {
        padding: wp('1%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        marginRight: wp('1%'),
        width: wp('10%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('10%'),
        width: wp('20%'),
        height: wp('10%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      },
  TouchableOpacity: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        padding: wp('1%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        borderRadius: wp('1%'),
        marginRight: wp('1%'),
        width: wp('10%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        borderRadius: wp('10%'),
        width: wp('20%'),
        height: wp('10%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
      },
  Text1: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.6%'),
        textAlign: 'left',
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  Text: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3%'),
        textAlign: 'left',
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
      },
  userCreationTypeContainer: isTabDevice()
    ? {
        marginTop: wp('10%'),
      }
    : {
        marginLeft: wp('2%'),
        marginTop: wp('3%'),
      },
  userCreationTypeTitle: isTabDevice() ? {} : {},
  userCreationTypeWrapper: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        backgroundColor: colors.tileBackground,
        borderRadius: wp('1%'),
        width: wp('22.5%'),
        justifyContent: 'space-between',
        marginTop: wp('2%'),
        paddingLeft: wp('0.7%'),
        paddingRight: wp('0.7%'),
        padding: wp('0.7%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        borderRadius: wp('10%'),
        width: wp('41%'),
        justifyContent: 'space-between',
        marginTop: wp('2%'),
        paddingLeft: 0,
        paddingRight: 0,
      },
});
export default UserTypeStyles;
