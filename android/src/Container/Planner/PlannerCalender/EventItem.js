import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { eventType, userRoleType } from '../../../constants/constants';
import {
  dateTimeUTCConversion,
  stringLength,
  stringToTitleCase,
} from '../../../helpers/index';
import { SET_SELECTED_MATCH_DETAILS } from '../../../store/actionTypes/MatchPlan/MatchPlanActions';
import useMatchPlanService from '../../../hooks/ServiceHook/MatchPlanServicehook/useMatchPlanServicehook';
import { isTabDevice } from '../../../config/appConfig';


const EventItem = ({
  item,
  selectedEvent,
  setSelectedEvent,
  onRSVPButtonClicked,
  onEventViewButtonClicked,
  isPastDate,
  EventsComponentStyles
}) => {
  const navigator = useNavigation();
  const dispatch = useDispatch();
  const { fetchMatchPlanDataByMatchId } = useMatchPlanService();
  const [isDisableBtn, setIsDisableBtn] = useState(false);

  const { userData } = useSelector(state => state?.auth);
  const userType = userData?.type;

  const isParentOrPlayer =
    userType === userRoleType.PLAYER ||
    userType === userRoleType.PARENT;

  const { hours12String, amPm, minutesString } = dateTimeUTCConversion(
    item.startTime
  );

  useEffect(() => {
    const onFetchMatchPlan = async (matchId) => {
      try {
        if (isParentOrPlayer && item.type === eventType.MATCH) {
          const result = await fetchMatchPlanDataByMatchId(matchId);
          setIsDisableBtn(!result?.visible || false);
        } else {
          setIsDisableBtn(false);
        }
      } catch (error) {
        console.error('Error fetching match plan:', error);
        setIsDisableBtn(false);
      }
    };

    if (item._id && item.type === eventType.MATCH) {
      onFetchMatchPlan(item._id);
    }
  }, [item._id, isParentOrPlayer]);


  return (
    <TouchableOpacity onPress={() => setSelectedEvent(item._id)}>
    <View
      style={
        selectedEvent == item._id
          ? EventsComponentStyles.eventContainerSelected
          : EventsComponentStyles.eventContainer
      }
    >
      <View style={EventsComponentStyles.eventRow1}>
        <Text style={EventsComponentStyles.eventName}>
          {stringLength(item.team.teamName, isTabDevice() ? 20 : 15)}{' '}
          {stringToTitleCase(item.type)}
        </Text>
      </View>

      <View style={EventsComponentStyles.eventRow2}>
        <Text
          style={
            selectedEvent == item._id
              ? EventsComponentStyles.locationSelected
              : EventsComponentStyles.location
          }
        >
          {stringLength(item.location.name, isTabDevice() ? 20 : 40)}
        </Text>
        <Text
          style={
            selectedEvent == item._id
              ? EventsComponentStyles.timeSelected
              : EventsComponentStyles.time
          }
        >
          {hours12String}:{minutesString} {amPm}
        </Text>
      </View>

      {selectedEvent == item._id && (
        <View style={EventsComponentStyles.eventRow3}>
          {userType !== userRoleType.PLAYER &&
            userType !== userRoleType.PARENT && (
              <TouchableOpacity
                style={EventsComponentStyles.eventRsvp}
                onPress={() => onRSVPButtonClicked(item)}
              >
                <Text style={EventsComponentStyles.eventButtonText}>
                  RSVP
                </Text>
              </TouchableOpacity>
            )}
          <View style={EventsComponentStyles.eventButtons}>
            <TouchableOpacity
              style={EventsComponentStyles.eventButton}
              onPress={() => onEventViewButtonClicked(item)}
            >
              <Text style={EventsComponentStyles.eventButtonText}>
                View
              </Text>
            </TouchableOpacity>
            {item.type === eventType.MATCH && !isPastDate ? (
              <TouchableOpacity
              style={[
                EventsComponentStyles.eventButton,
                isDisableBtn && { opacity: 0.5 }
              ]}
                disabled={isDisableBtn}
                onPress={() => {
                  dispatch({
                    type: SET_SELECTED_MATCH_DETAILS,
                    payload: item,
                  });
                  navigator.navigate('MatchPlan', {
                    matchId: item._id,
                    opponent: item.opponentId,
                    teamId: item.teamId,
                  });
                }}
              >
                <Text style={EventsComponentStyles.eventButtonText}>
                  Plan
                </Text>
              </TouchableOpacity>
            ) : null}
          </View>
        </View>
      )}
    </View>
  </TouchableOpacity>
  );
};

export default EventItem