import { AntDesign } from '@expo/vector-icons';
import React, { memo, useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import { Calendar } from 'react-native-calendars';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { useSelector } from 'react-redux';
import { isTabDevice } from '../../../config/appConfig';
import { userRoleType } from '../../../constants/constants';
import {
  dateTimeUTCConversion,
  eventSearchByDate,
  getCurrentMonthRange,
  getNextMonth,
  getPrevMonth,
  replaceDate,
} from '../../../helpers/index';
import { useCalenderEventsApiHook } from '../../../hooks/PlannerAPIHook/usePlannerApiHook';
import useCalender from '../../../hooks/useCalender';
import useStyles from '../../../hooks/useStyles';
import { RootStore } from '../../../store/store';
import DayComponent from './DayComponent';
import customPlannerCalenderStyle from './PlannerCalenderStyles';

type CalendarsScreenType = {
  onDayClick: Function;
};

const CalendarsScreen: React.FC<CalendarsScreenType> = ({ onDayClick }) => {
  const PlannerCalenderStyle = useStyles(customPlannerCalenderStyle);
  const { fetchCalenderData } = useCalenderEventsApiHook();
  const [{ syncCalender }] = useCalender();
  const { userData } = useSelector((state: any) => state?.auth);
  const { createEventSuccess, deleteEventSuccess } = useSelector(
    (state: any) => state.event
  );
  const { calendar = [] } = useSelector((state: RootStore) => state?.planner);
  const { teamData } = useSelector((state: any) => state?.team);
  const [dateInfo, setDateInfo] = useState<any>({
    isToday: false,
    schedule: [],
    tomorrowSchedule: [],
    scheduleShow: [],
    scheduleCount: 0,
    nextDay: {},
  });
  const [individualMonth, setIndividualMonth] = useState<any>({});
  const dateTime = dateTimeUTCConversion(new Date());
  const [selectedMonth, setSelectedMonth] = useState<any>(new Date());
  const { year, monthNumberString, dateNumberString } = dateTimeUTCConversion(
    new Date()
  );
  const [currentMonth, setCurrentMonth] = useState(
    `${year}-${monthNumberString}-${dateNumberString}`
  );
  const { selectedChild } = useSelector((state: RootStore) => state?.common);
  const isParent = userRoleType.PARENT === userData?.type;

  const addDay = (date: Date, days: number) => {
    let result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  };

  const requestUrl = () => {
    const { year, month } = dateTimeUTCConversion(selectedMonth);
    // const { dateDefaultFormat: nextDate } = getNextMonth([year, month]);
    // const { dateDefaultFormat: prevDate } = getPrevMonth([year, month]);

    // const newNextDate = addDay(nextDate, 1);

    const result =  getCurrentMonthRange([year,month])
    const prevDate = result.start
    const newNextDate = result.end

    switch (userData?.type) {
      case userRoleType.HEAD_COACH:
        return `/api/v1/events?startDate=${new Date(
          prevDate
        ).toISOString()}&endDate=${new Date(
          newNextDate
        ).toISOString()}&page=1&size=10000`;
      case userRoleType.PARENT:
        return `/api/v1/events?participantIds=${
          selectedChild?.id
        }&startDate=${new Date(prevDate).toISOString()}&endDate=${new Date(
          newNextDate
        ).toISOString()}&page=1&size=10000`;
      default:
        return `/api/v1/events?participantIds=${
          userData?.id
        }&startDate=${new Date(prevDate).toISOString()}&endDate=${new Date(
          newNextDate
        ).toISOString()}&page=1&size=10000`;
    }
  };

  useEffect(() => {
    if (isParent && selectedChild?.id) {
      fetchCalenderData(requestUrl());
    }
  }, [JSON.stringify(selectedChild), isParent]);

  useEffect(() => {
    fetchCalenderData(requestUrl());
  }, [selectedMonth]);

  useEffect(() => {
    if (createEventSuccess) {
      syncCalender();
      fetchCalenderData(requestUrl());
    }
  }, [createEventSuccess]);

  useEffect(() => {
    if (deleteEventSuccess) {
      syncCalender();
      fetchCalenderData(requestUrl());
    }
  }, [deleteEventSuccess]);

  const clickOnNext = () => {
    const { year, month } = dateTimeUTCConversion(selectedMonth);
    const { year: nextYear, month: nextMonth } = getNextMonth([year, month]);
    setCurrentMonth(`${nextYear}-${nextMonth}`);
  };

  const clickOnPrev = () => {
    const { year, month } = dateTimeUTCConversion(selectedMonth);
    const { year: nextYear, month: nextMonth } = getPrevMonth([year, month]);
    setCurrentMonth(`${nextYear}-${nextMonth}`);
  };
  const sleetedDayClick = (event: any) => {
    onDayClick(event);
  };

  const { yearMonthDateString } = dateTime;

  //Select First date of every month
  useEffect(() => {
    if (Object.keys(individualMonth).length) {
      const { dateString } = individualMonth;
      const nextDay = getNextMonth(dateString.split('-'));
      const { year, dateNumberString, monthNumberString } = nextDay;
      dateInfo.nextDay = nextDay;
      if (teamData?.data?.length && calendar?.length) {
        const filteredSchedule = eventSearchByDate(
          calendar,
          dateString,
          teamData
        );

        dateInfo.schedule = filteredSchedule;

        if (filteredSchedule.length >= 2) {
          dateInfo.scheduleCount = filteredSchedule.length - 2;
          dateInfo.scheduleShow = [filteredSchedule[0], filteredSchedule[1]];
        } else {
          dateInfo.scheduleShow = filteredSchedule;
        }

        dateInfo.tomorrowSchedule = eventSearchByDate(
          calendar,
          `${year}-${monthNumberString}-${dateNumberString}`,
          teamData
        );
      }
      if (yearMonthDateString === dateString) {
        dateInfo.isToday = true;
      }
      setDateInfo({ ...dateInfo });
    }
  }, [teamData, calendar, individualMonth]);

  useEffect(() => {
    if (Object.keys(individualMonth).length) {
      onDayClick({
        ...dateInfo,
        ...individualMonth,
        userType: userData?.type,
      });
    }
  }, [individualMonth]);

  const getMonth = (dateString: string) => {
    return dateString?.split('-')?.splice(1, 1);
  };

  const getDate = (dateString: string) => {
    return dateString?.split('-')?.splice(2, 1);
  };

  const onMonthChangeHandler = (selectedMonth: any) => {
    const { month, year, dateString } = selectedMonth || {};

    let defaultDateSelection: any;

    if (+getMonth(yearMonthDateString) === month) {
      defaultDateSelection = getDate(yearMonthDateString);
    } else {
      defaultDateSelection = '01';
    }

    let customMonth = {
      dateString: replaceDate(dateString, defaultDateSelection),
      day: defaultDateSelection,
      month,
      timestamp: Date.parse(replaceDate(dateString, defaultDateSelection)),
      year,
    };

    setIndividualMonth(customMonth);
  };
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={
        isParent
          ? PlannerCalenderStyle.containerParent
          : PlannerCalenderStyle.container
      }
    >
      <Calendar
        current={currentMonth}
        hideExtraDays
        disableAllTouchEventsForDisabledDays
        firstDay={1}
        hideArrows={false}
        theme={{
          backgroundColor: '#ffffff',
          calendarBackground: '#ffffff00',
          textSectionTitleColor: '#b6c1cd',
          textSectionTitleDisabledColor: '#d9e1e8',
          selectedDayBackgroundColor: '#00adf5',
          selectedDayTextColor: '#ffffff',
          todayTextColor: '#00adf5',
          dayTextColor: '#2d4150',
          textDisabledColor: '#d9e1e8',
          dotColor: '#00adf5',
          selectedDotColor: '#ffffff',
          arrowColor: '#FF9011',
          disabledArrowColor: '#d9e1e8',
          monthTextColor: 'white',
          indicatorColor: 'blue',
          textDayFontWeight: '300',
          textMonthFontWeight: 'bold',
          textDayHeaderFontWeight: '300',
          textDayFontSize: 5,
          textMonthFontSize: isTabDevice() ? hp('3%') : wp('3%'),
          textDayHeaderFontSize: isTabDevice() ? hp('2%') : wp('3%'),
          'stylesheet.calendar.header': {
            week: PlannerCalenderStyle.week,
          },
        }}
        renderArrow={direction => {
          if (direction == 'left') {
            return <AntDesign name="arrowleft" size={24} color="#FFF" />;
          }

          if (direction == 'right') {
            return <AntDesign name="arrowright" size={24} color="#FFF" />;
          }
        }}
        dayComponent={({ date, state }) => (
          <DayComponent individualDate={date} onDayClick={sleetedDayClick} />
        )}
        onMonthChange={month => {
          onMonthChangeHandler(month);
        }}
        onPressArrowLeft={(subtractMonth: Function) => {
          clickOnPrev();
          return subtractMonth(1);
        }}
        onPressArrowRight={(addMonth: Function) => {
          clickOnNext();
          return addMonth(1);
        }}
        onVisibleMonthsChange={([month]) => {
          return setSelectedMonth(month.timestamp);
        }}
      />
    </ScrollView>
  );
};

export default memo(CalendarsScreen);
