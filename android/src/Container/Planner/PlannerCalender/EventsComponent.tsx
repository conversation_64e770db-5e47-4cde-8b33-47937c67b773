import React from 'react';
import { userRoleType } from '../../../constants/constants';

import DayEvent from './DayEvents';
import WeekEvents from './WeekEvents';

type EventsComponentType = {
  events: any;
  onEventViewClicked: Function;
  onEventRsvpClicked: Function;
};
const EventsComponent: React.FC<EventsComponentType> = ({
  events,
  onEventViewClicked,
  onEventRsvpClicked,
}) => {
  const { userType } = events;

  const onEventViewButtonClicked = (event: any) => {
    onEventViewClicked(event);
  };

  const onRSVPButtonClicked = (event: any) => {
    onEventRsvpClicked(event);
  };
  // TODO: According to the Card No - AK-1621 commented out this code.

  // return userType === userRoleType.PLAYER ? (
  //   <WeekEvents
  //     events={events}
  //     onEventViewButtonClicked={onEventViewButtonClicked}
  //   />
  // ) : (
  //   <DayEvent
  //     events={events}
  //     onRSVPButtonClicked={onRSVPButtonClicked}
  //     onEventViewButtonClicked={onEventViewButtonClicked}
  //   />
  // );
  return (
    <DayEvent
      events={events}
      onRSVPButtonClicked={onRSVPButtonClicked}
      onEventViewButtonClicked={onEventViewButtonClicked}
    />
  );
};

export default EventsComponent;
