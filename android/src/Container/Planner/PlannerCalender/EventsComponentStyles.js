import { StyleSheet, Dimensions, Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { colorPalette } from '../../../constants/constants';

const EventsComponentStyles = colors => ({
  eventsContainer: isTabDevice()
    ? {
        marginBottom: wp('15%'),
      }
    : {
        marginTop: wp('2.5%'),
        marginBottom: wp('2%'),
      },
  eventContainerSelected: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        padding: wp('1.5%'),
        marginTop: wp('0.5%'),
        borderRadius: 15,
        width: '100%',
      }
    : {
        backgroundColor: colors.darkBlue,
        padding: wp('2%'),
        marginTop: wp('2%'),
        borderRadius: wp('5%'),
      },
  eventContainer: isTabDevice()
    ? {
        // backgroundColor: colors.darkBlue,
        padding: wp('1.5%'),
        marginTop: wp('0.5%'),
        borderRadius: wp('1%'),
        width: '97.5%',
      }
    : {
        padding: wp('2%'),
        paddingBottom: wp('1%'),
        borderRadius: wp('5%'),
      },
  eventRow1: {
    marginBottom: wp('1%'),
  },
  eventRow2: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('1.5%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('5.5%'),
      },
  eventRow3: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        flexDirection: 'row',
        // justifyContent: 'space-between',
        width: wp('100%'),
      },
  eventWeekRow1: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginBottom: wp('1%'),
  },
  eventWeekRow2: isTabDevice()
    ? {
        marginBottom: wp('3.5%'),
      }
    : {
        marginBottom: wp('5.5%'),
      },
  eventWeekRow3: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventRsvp: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        width: wp('10%'),
        height: hp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        width: wp('15%'),
        height: hp('3%'),
        borderRadius: wp('4%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: wp('5%'),
      },
  eventWeekButtons: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: wp('15%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: wp('35%'),
      },
  eventButtons: isTabDevice()
    ? {
        flexDirection: 'row',
        // width: wp('40%'),
        justifyContent: 'space-between',
      }
    : {
        flexDirection: 'row',
        width: wp('40%'),
      },
  eventButton: isTabDevice()
    ? {
        backgroundColor: colors.green,
        width: wp('7%'),
        height: hp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: wp('2%'),
      }
    : {
        backgroundColor: colors.green,
        width: wp('15%'),
        height: hp('3%'),
        borderRadius: wp('4%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: wp('5%'),
      },
  eventWeekButton: isTabDevice()
    ? {
        backgroundColor: colors.green,
        width: wp('7%'),
        height: hp('3%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.green,
        width: wp('15%'),
        height: hp('3%'),
        borderRadius: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  eventButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('3%'),
      },
  eventName: isTabDevice()
    ? {
        color: colors.white,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontWeight: 'bold',
        fontSize: wp('3.3%'),
        fontFamily: 'Poppins-Medium',
      },
  eventWeekName: isTabDevice()
    ? {
        color: colors.white,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontWeight: 'bold',
        fontSize: wp('3.3%'),
      },
  location: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontWeight: 'bold',
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  locationSelected: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },

  eventWeeklocation: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontWeight: 'bold',
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  eventWeeklocationSelected: {
    color: colors.green,
    fontWeight: 'bold',
    fontSize: wp('1.5%'),
    fontFamily: 'Poppins-Medium',
  },
  time: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontSize: wp('3.3%'),
        fontFamily: 'Poppins-Medium',
      },
  timeSelected: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3.3%'),
        fontFamily: 'Poppins-Medium',
      },
  eventWeekTime: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontWeight: 'bold',
        fontSize: wp('3.3%'),
        fontFamily: 'Poppins-Medium',
      },
  eventWeekTimeSelected: isTabDevice()
    ? {
        color: colors.green,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontWeight: 'bold',
        fontSize: wp('3.3%'),
        fontFamily: 'Poppins-Medium',
      },
  eventWeekDate: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        marginTop: wp('0.3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('2.7%'),
        marginTop: wp('1%'),
      },

  eventsToday: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        marginBottom: wp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
      },
  eventsDate: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        marginBottom: wp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
      },
  eventsTomorrowWrapper: {
    paddingTop: wp('2.5%'),
    // paddingLeft: wp('1.5%'),
    paddingRight: wp('1.5%'),
    marginTop: wp('2%'),
    marginBottom: wp('2%'),
    borderTopWidth: 1,
    borderTopColor: colors.darkBlue,
  },
  eventsTomorrow: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
      },
  eventsNoEvents: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Medium',
        marginTop: wp('10%'),
        textAlign: 'center',
      },
  eventWeekSelector: {
    flexDirection: 'row',
    marginBottom: wp('2%'),
  },
  eventWeekSelected: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('3%'),
        padding: wp('0.5%'),
        width: '50%',
        flexDirection: 'row',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('5%'),
        padding: wp('2%'),
        marginTop: Platform.OS === 'android' ? wp('2.5%') : 0,
        width: '50%',
        flexDirection: 'row',
        justifyContent: 'center',
      },
  eventWeek: isTabDevice()
    ? {
        padding: wp('0.5%'),
        width: '50%',
        flexDirection: 'row',
        justifyContent: 'center',
      }
    : {
        padding: wp('2%'),
        marginTop: Platform.OS === 'android' ? wp('2.5%') : 0,
        width: '50%',
        flexDirection: 'row',
        justifyContent: 'center',
      },
  eventWeekText: isTabDevice()
    ? {
        fontSize: wp('1.6%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        // paddingTop: Platform.OS === 'android' ? wp('1%') : 0,
      },
  eventListWrapper: isTabDevice()
    ? {}
    : {
        // height: wp('20%'),
      },
  eventsFlatList: isTabDevice()
    ? {
        height: hp('70%'),
        marginBottom: wp('2%'),
      }
    : {
        height: wp('30%'),
        // paddingBottom: hp('50%'),
        // backgroundColor: colors.black,
      },
});
export default EventsComponentStyles;
