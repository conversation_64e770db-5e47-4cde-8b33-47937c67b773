import { StyleSheet, Dimensions, Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { isRTL } from 'expo-localization';
import { colorPalette } from '../../constants/constants';

const PlannerStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        left: 0,
        height: hp('90%'),
        backgroundColor: colors.midDarkBlue,
        marginTop: hp('10%'),
        zIndex: 2,
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
        position: 'absolute',
        top: 0,
        left: 0,
        height: hp('90%'),
        backgroundColor: colors.midDarkBlue,
        marginTop: hp('10%'),
        zIndex: 2,
      },
  calendarContainer: isTabDevice()
    ? {
        width: wp('65'),
        height: hp('87%'),
      }
    : {
        width: wp('100%'),
        height: hp('42%'),
      },
  calendarContainerParent: isTabDevice()
    ? {
        width: wp('65'),
        height: hp('80%'),
      }
    : {
        width: wp('100%'),
        height: hp('52%'),
      },
  calendarWrapper: isTabDevice()
    ? {
        marginBottom: hp('1%'),
      }
    : {},
  calendar: {},
  childWrapper: isTabDevice()
    ? {
        padding: wp('2%'),
        paddingBottom: 0,
      }
    : {
        paddingLeft: wp('4%'),
        paddingTop: wp('4%'),
      },
  legands: isTabDevice()
    ? {
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        marginLeft: wp('1.5%'),
        marginRight: wp('1.5%'),
        borderTopWidth: 1,
        borderTopColor: colors.darkBlue,
      }
    : {
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        marginLeft: wp('1.5%'),
        marginRight: wp('1.5%'),
        borderTopWidth: 1,
        borderTopColor: colors.darkBlue,
      },
  legandsList: {},
  addEventModal: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        left: 0,
        height: hp('90%'),
        backgroundColor: colors.midDarkBlue,
        // marginTop: hp('10%'),
        zIndex: 10,
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
        position: 'absolute',
        top: 0,
        left: 0,
        height: hp('90%'),
        backgroundColor: colors.midDarkBlue,
        marginTop: hp('10%'),
        zIndex: 2,
      },
  eventsContainer: isTabDevice()
    ? {
        width: wp('35%'),
        height: hp('87%'),
        backgroundColor: colors.veryDarkBlue,
        position: 'relative',
        padding: wp('2%'),
      }
    : {
        width: wp('100%'),
        height: hp('30%'),
      },
  eventsContainerParent: isTabDevice()
    ? {
        width: wp('35%'),
        height: hp('85%'),
        backgroundColor: colors.veryDarkBlue,
        position: 'relative',
        padding: wp('2%'),
      }
    : {
        width: wp('100%'),
        height: hp('38%'),
      },
  eventsWrapper: isTabDevice()
    ? {}
    : {
        marginTop: hp('1%'),
        marginBottom: hp('2%'),
        marginLeft: wp('3%'),
        marginRight: wp('3%'),
        height: hp('28%'),
      },
  addButton: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        width: wp('35%'),
        height: hp('10%'),
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        bottom: 0,
        left: 0,
      }
    : {
        backgroundColor: colors.aquaBlue,
        width: wp('10%'),
        height: wp('10%'),
        borderRadius: wp('100'),
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: hp('30%'),
        right: wp('4%'),
        // bottom: wp('40%'),
      },
  addButtonText: isTabDevice()
    ? {
        fontSize: hp('5%'),
      }
    : { fontSize: wp('5%') },
  legand: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: wp('1%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: wp('3%'),
        marginRight: wp('3%'),
      },
  legandBall: isTabDevice()
    ? {
        width: wp('1%'),
        height: hp('1.8%'),
        borderRadius: hp('100%'),
        marginRight: wp('0.5%'),
      }
    : {
        width: wp('2%'),
        height: wp('2%'),
        borderRadius: wp('100%'),
        marginRight: wp('2%'),
      },
  legandText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2.4%'),
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
      },
  eventsControllerButtons: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        position: 'absolute',
        bottom: 0,
        right: wp('2%'),
        paddingBottom: hp('1%'),
      }
    : {
        marginLeft: wp('4%'),
        marginRight: wp('4%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
  eventsControllerButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        padding: hp('1%'),
        width: '35%',
        height: hp('5%'),
        marginRight: wp('1%'),
        // borderTopLeftRadius: wp('1%'),
        // borderTopRightRadius: wp('1%'),
        borderRadius: hp('10%'),
        flexDirection: 'row',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        padding: wp('2%'),
        width: '45%',
        marginRight: wp('1%'),
        borderRadius: wp('3%'),
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: wp('3%'),
      },
  eventsControllerButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3'),
        fontWeight: 'bold',
      },
});
export default PlannerStyle;
