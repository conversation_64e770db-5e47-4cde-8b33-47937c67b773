import React, { useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import AddEventForm from '../../../components/AddEventForm/AddEventForm';
import PlayerEventViewModal from '../../../components/modal/PlayerEventViewModal/EventViewModal';
import { eventType, userRoleType } from '../../../constants/constants';
import { useEventViewApiHook } from '../../../hooks/PlannerAPIHook/useAddEventApiHook';

const EventViewContainer = ({ showModal, closeModal, event }) => {
  const [formattedEvent, setFormattedEvent] = useState(null);
  const {
    opponentDetails,
    seasonDetails,
    tournamentDetails,
    rsvpData,
    rsvpDataLoading,
  } = useSelector(state => state.planner);
  const { userData, userRole } = useSelector(state => state?.auth);
  const { selectedChild } = useSelector(state => state?.common);
  const isParent = userRoleType.PARENT === userData?.type;

  const [rsvpAbsenceNote, setRsvpAbsenceNote] = useState(null);
  const [rsvpResponseState, setRsvpResponseState] = useState(false);

  const {
    saveUserRsvpResponse,
    getTournamentData,
    getOpponentData,
    getSeasonData,
    getSelectedUserData,
  } = useEventViewApiHook();

  const onSaveUserRsvpResponse = response => {
    if (event?._id && userData?.id) {
      const requestBody = [
        response
          ? {
              userId: isParent ? selectedChild?.id : userData?.id,
              isParticipating: response,
            }
          : {
              userId: isParent ? selectedChild?.id : userData?.id,
              isParticipating: response,
              rsvpReason: rsvpAbsenceNote,
            },
      ];

      saveUserRsvpResponse(event._id, requestBody);

      rsvpAbsenceNote || rsvpData?.rsvpReason
        ? window.setTimeout(() => {
            setRsvpResponseState(!rsvpResponseState);
          }, 1)
        : null;
    }
    setRsvpAbsenceNote(null);

    window.setTimeout(() => {
      closeModal();
    }, 1000);
  };

  useEffect(() => {
    if (event && showModal) {
      const { tournamentId, seasonId, opponentId, _id } = event;
      setFormattedEvent(event);
      if (event.type === eventType.MATCH) {
        tournamentId && getTournamentData(tournamentId);

        opponentId && getOpponentData(opponentId);

        seasonId && getSeasonData(seasonId);
      }
      userData &&
        getSelectedUserData(_id, isParent ? selectedChild?.id : userData?.id);
    }
  }, [event, showModal, rsvpResponseState]);

  useEffect(() => {
    if (opponentDetails && formattedEvent) {
      const updatedEvent = {
        ...formattedEvent,
        opponent: opponentDetails,
      };
      setFormattedEvent(updatedEvent);
    }
  }, [opponentDetails]);

  useEffect(() => {
    if (seasonDetails && formattedEvent) {
      const updatedEvent = {
        ...formattedEvent,
        season: seasonDetails,
      };
      setFormattedEvent(updatedEvent);
    }
  }, [seasonDetails]);

  useEffect(() => {
    if (tournamentDetails && formattedEvent) {
      const updatedEvent = {
        ...formattedEvent,
        tournament: tournamentDetails,
      };
      setFormattedEvent(updatedEvent);
    }
  }, [tournamentDetails]);

  useEffect(() => {
    if (rsvpData && formattedEvent) {
      const updatedEvent = {
        ...formattedEvent,
        rsvpData,
      };
      setFormattedEvent(updatedEvent);
    }
  }, [rsvpData]);

  const isMatch = useMemo(() => {
    if (event?.type) {
      return event.type === eventType.MATCH;
    } else {
      return false;
    }
  }, [event]);

  const isPlayer = useMemo(() => {
    if (userRole) {
      return userRole === userRoleType.PLAYER;
    } else {
      return false;
    }
  }, [userRole]);

  if (formattedEvent && showModal) {
    if (isPlayer || isParent) {
      return (
        <View>
          <PlayerEventViewModal
            showModal={showModal}
            closeModal={closeModal}
            event={formattedEvent}
            isMatch={isMatch}
            saveUserRsvpResponse={onSaveUserRsvpResponse}
            isPlayer={isPlayer}
            isParent={isParent}
            rsvpDataLoading={rsvpDataLoading}
            userData={userData}
            setRsvpAbsenceNote={setRsvpAbsenceNote}
          />
        </View>
      );
    }
    return (
      <AddEventForm
        modalAction={() => {
          closeModal();
          setFormattedEvent(null);
        }}
        event={formattedEvent}
        isMatch={isMatch}
        // saveUserRsvpResponse={saveUserRsvpResponse}
        isPlayer={isPlayer}
      />
    );
  }
  return null;
};

export default EventViewContainer;
