import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import EventRSVPModal from '../../../components/modal/EventRSVPModal/EventRSVPModal';
import {
  rsvpListPageSize,
  RSVP_RESPONSES,
  userRoleType,
} from '../../../constants/constants';
import { useRSVAPHook } from '../../../hooks/PlannerAPIHook/usePlannerApiHook';
import { RootStore } from '../../../store/store';

type RsvpViewContainerType = {
  showModal: any;
  closeModal: Function;
  event: any;
  setShowModal: Function;
};

const RsvpViewContainer: React.FC<RsvpViewContainerType> = ({
  showModal,
  closeModal,
  event,
  setShowModal,
}) => {
  const { userData } = useSelector((state: RootStore) => state.auth);
  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;

  const { getRSVData, loadNextPage } = useRSVAPHook();
  const [rsvpStatus, setRsvpStatus] = useState('');
  const { rsvpListCurrentPage, rsvpList, rsvpTotalRecords, rsvpListLoading } =
    useSelector((state: RootStore) => state.planner);

  useEffect(() => {
    showModal && setRsvpStatus(RSVP_RESPONSES.GOING);
  }, [showModal]);

  useEffect(() => {
    if (event?._id && !isPlayer && !isParent && rsvpStatus) {
      getRSVData(event._id, userRoleType.PLAYER, rsvpListPageSize, rsvpStatus);
    }
  }, [event, rsvpStatus]);

  const onLoadNextPage = () => {
    if (event?._id && rsvpListCurrentPage) {
      loadNextPage(
        event._id,
        rsvpListCurrentPage + 1,
        rsvpListPageSize,
        rsvpStatus
      );
    }
  };

  return (
    <View>
      <EventRSVPModal
        showModal={showModal}
        closeModal={() => {
          closeModal();
          setRsvpStatus('');
        }}
        loading={rsvpListLoading}
        responses={rsvpList}
        loadNextPage={onLoadNextPage}
        rsvpTotalRecords={rsvpTotalRecords}
        setShowModal={setShowModal}
        rsvpStatus={rsvpStatus}
        setRsvpStatus={setRsvpStatus}
      />
    </View>
  );
};

export default RsvpViewContainer;
