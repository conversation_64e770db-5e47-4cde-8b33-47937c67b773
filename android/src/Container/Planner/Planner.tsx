import { AntDesign } from '@expo/vector-icons';
import React, { useEffect, useMemo, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { useDispatch, useSelector } from 'react-redux';
import {
  checkIsPastDate,
  dateTimeConversion,
  dateTimeUTCConversion,
  eventSearchByDate,
  getNextDate,
} from '../../helpers/index';
import useCalender from '../../hooks/useCalender';
import useGetChildInformation from '../../hooks/useGetChildInformation';
import useStyles from '../../hooks/useStyles';
import { RESET_TEAMS } from '../../store/actionTypes/Team/TeamAction';
import Calender from './PlannerCalender/PlannerCalander';
import customPlannerStyle from './PlannerStyles';

import {
  RESET_PLANNER,
  SET_NOTIFCATION_EVENT_ID,
  SET_SELECT_EVENT,
} from '../../store/actionTypes/Planner/PlannerAction';

import { userRoleType } from '../../constants/constants';

import EventComponent from './PlannerCalender/EventsComponent';

import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import AddEventForm from '../../components/AddEventForm/AddEventForm';
import ChildLabel from '../../components/ChildLabel/ChildLabel';
import { isTabDevice } from '../../config/appConfig';
import { usePlannerApiHook } from '../../hooks/PlannerAPIHook/usePlannerApiHook';
import {
  SET_PLANNER_MODAL_STATE,
  SET_SELECTED_CHILD,
} from '../../store/actionTypes/common/commonActionTypes';
import { RootStore } from '../../store/store';
import EventViewContainer from './PlannerEventView/EventViewContainer';
import RsvpViewContainer from './RSVPView/RsvpViewContainer';
import { useEventViewApiHook } from '../../hooks/PlannerAPIHook/useAddEventApiHook';

export default function Planner() {
  const PlannerStyle = useStyles(customPlannerStyle);
  const dispatch = useDispatch();
  const { matchPlanSaveLoading, matchPlanLoading } = useSelector(
    (state: RootStore) => state.matchPlan
  );
  const {
    RsvpSaveAllLoading,
    calendarLoading,
    calendar = [],
    selectedDateEvents,
    RsvpInviteeFetchData,
    WeekType,
    WeekSchedule,
    notificationEventId,
  } = useSelector((state: RootStore) => state?.planner);

  const [{ init }] = useCalender();

  const [events, setEvents] = useState({});
  const [fetchChildInformation] = useGetChildInformation();

  const [isPastDate, setIsPastDate] = useState(false);
  const [eventName, setEventName] = useState(new Date().toDateString());
  const [eventTimestamp, setEventTimestamp] = useState(Date.now());

  const [addEventModalVisible, setAddEventModalVisible] = useState(false);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [showRsvpModal, setShowRsvpModal] = useState(false);
  const [calenderKey, setCalenderKey] = useState(Number(new Date()));
  const [allEventsId, setAlleventsId] = useState([]);

  const { getFetchTeamDetails, getMoreTeamDetails } = usePlannerApiHook();
  const { getEventDetail } = useEventViewApiHook();

  const {
    teamData,
    teamInitialPage,
    teamInitialSize,
    teamPage,
    teamSize,
    selectedTeam,
  } = useSelector((state: any) => state?.team);
  const { selectedChild, children, childInformation } = useSelector(
    (state: RootStore) => state?.common
  );
  const { userData } = useSelector((state: RootStore) => state?.auth);
  const [selectedEventItem, setSelectedEventItem] = useState(null);
  const [dateInfo, setDateInfo] = useState<any>({
    isToday: true,
    schedule: [],
    tomorrowSchedule: [],
    nextDay: {},
  });

  const showLoader = useMemo(() => {
    if (matchPlanSaveLoading || matchPlanLoading) {
      return true;
    } else if (RsvpSaveAllLoading) {
      return true;
    } else if (calendarLoading) {
      return true;
    }

    return false;
  }, [
    matchPlanSaveLoading,
    matchPlanLoading,
    RsvpSaveAllLoading,
    calendarLoading,
  ]);

  const isParent = userRoleType.PARENT === userData?.type;

  useEffect(() => {
    fetchChildInformation();

    if (isParent) {
      setSelectedChild(children[0]);
    }

    return () => {
      dispatch({ type: RESET_TEAMS });
      dispatch({ type: RESET_PLANNER });
    };
  }, []);

  useEffect(() => {
    if (addEventModalVisible || showEventDetails || showRsvpModal) {
      dispatch({
        type: SET_PLANNER_MODAL_STATE,
        payload: { data: { isPlannerModalOpened: true } },
      });
    } else {
      dispatch({
        type: SET_PLANNER_MODAL_STATE,
        payload: { data: { isPlannerModalOpened: false } },
      });
    }
  }, [addEventModalVisible, showEventDetails, showRsvpModal]);

  const setSelectedChild = (payload: any) => {
    dispatch({
      type: SET_SELECTED_CHILD,
      payload,
    });
  };

  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    notificationEventId && handleEventPopup(notificationEventId);
  }, [notificationEventId]);

  const handleEventPopup = async (notificationEventId: string) => {
    try {
      const notficationEventDetail = await getEventDetail(notificationEventId);
      dispatch({
        type: SET_NOTIFCATION_EVENT_ID,
        payload: undefined,
      });

      onEventViewClicked({
        ...notficationEventDetail,
        team: {
          ...notficationEventDetail.team,
          teamName: notficationEventDetail.team.name,
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (childInformation?.sportsProfileId) {
      userData?.type &&
        getFetchTeamDetails(
          userData,
          teamInitialPage,
          teamInitialSize,
          childInformation
        );
      setCalenderKey(Number(new Date()));
    }
  }, [JSON.stringify(childInformation)]);

  useEffect(() => {
    if (userData?.type !== userRoleType.PARENT) {
      userData?.type &&
        getFetchTeamDetails(
          userData,
          teamInitialPage,
          teamInitialSize,
          childInformation
        );
    }
  }, []);

  const renderItem = ({ item }: any) => (
    <View style={PlannerStyle.legand}>
      <View
        style={[PlannerStyle.legandBall, { backgroundColor: `${item.colour}` }]}
      ></View>
      <Text style={PlannerStyle.legandText}>{item.teamName}</Text>
    </View>
  );
  const onDayClick = (event: any) => {
    const { dateString, monthString, date, utc_timestamp } =
      dateTimeUTCConversion(event.timestamp);

    setIsPastDate(checkIsPastDate(event.timestamp));

    event.isPastDate = checkIsPastDate(event.timestamp);

    setEventName(`${monthString} ${date}, ${dateString}`);
    setEventTimestamp(utc_timestamp);
    setEvents(event);
    dispatch({ type: SET_SELECT_EVENT, payload: event });
  };

  const onEventViewClicked = (eventItem: any) => {
    setSelectedEventItem(eventItem);
    setShowEventDetails(true);
  };

  const onEventRsvpClicked = (eventItem: any) => {
    setSelectedEventItem(eventItem);
    setShowRsvpModal(true);
  };

  useEffect(() => {
    const selectedDate = selectedDateEvents?.timestamp
      ? new Date(selectedDateEvents?.timestamp)
      : new Date();

    if (selectedDateEvents?.timestamp) {
      dateInfo.isToday = false;
    }

    const { timestamp } = dateTimeUTCConversion(selectedDate);

    const { yearMonthDateString } = dateTimeConversion(selectedDate);
    setIsPastDate(checkIsPastDate(selectedDate));

    dateInfo.isPastDate = checkIsPastDate(selectedDate);

    if (teamData?.data?.length && calendar?.length) {
      dateInfo.schedule = eventSearchByDate(
        calendar,
        yearMonthDateString,
        teamData
      );
      const nextDay = getNextDate(yearMonthDateString.split('-'));
      const { year, dateNumberString, monthNumberString } = nextDay;
      dateInfo.nextDay = nextDay;
      dateInfo.tomorrowSchedule = eventSearchByDate(
        calendar,
        `${year}-${monthNumberString}-${dateNumberString}`,
        teamData
      );
    }

    setEvents({ ...dateInfo, timestamp, userType: userData?.type });
  }, [teamData, calendar]);

  useEffect(() => {
    const { thisWeekSchedule, nextWeekSchedule } = WeekSchedule || {};

    setAlleventsId([]);
    if (WeekType === 'currentWeek') {
      const ThisweekSchedule = thisWeekSchedule
        .filter((ev: any) => new Date(ev?.startTime) >= new Date())
        .map((eventData: any) => {
          return eventData._id;
        });
      setAlleventsId(ThisweekSchedule);
    } else if (WeekType === 'nextWeek') {
      const NextweekSchedul = nextWeekSchedule.map((eventData: any) => {
        return eventData._id;
      });
      setAlleventsId(NextweekSchedul);
    }
  }, [WeekSchedule, WeekType]);

  return (
    <>
      {showLoader && <ActivitySpinner isFullScreen={true} />}

      <View style={PlannerStyle.container}>
        <View
          style={
            isParent
              ? PlannerStyle.calendarContainerParent
              : PlannerStyle.calendarContainer
          }
        >
          {isParent && (
            <View style={PlannerStyle.childWrapper}>
              <ChildLabel
                data={children}
                setSelectedChild={setSelectedChild}
                selectedChild={selectedChild}
              />
            </View>
          )}
          <View style={PlannerStyle.calendarWrapper}>
            <Calender onDayClick={onDayClick} key={calenderKey} />
          </View>
          <View style={PlannerStyle.legands}>
            <FlatList
              horizontal
              data={teamData.data}
              renderItem={renderItem}
              keyExtractor={item => item._id}
              onEndReached={() =>
                getMoreTeamDetails(
                  userData,
                  teamPage,
                  teamSize,
                  childInformation
                )
              }
              style={PlannerStyle.legandsList}
            />
          </View>
        </View>
        <View
          style={
            isParent
              ? PlannerStyle.eventsContainerParent
              : PlannerStyle.eventsContainer
          }
        >
          <View style={PlannerStyle.eventsWrapper}>
            <EventComponent
              events={events}
              onEventViewClicked={onEventViewClicked}
              onEventRsvpClicked={onEventRsvpClicked}
            />
          </View>

          {userData?.type === userRoleType.PLAYER ||
          userData?.type === userRoleType.PARENT ? (
            <View style={PlannerStyle.eventsControllerButtons}></View>
          ) : (
            !isPastDate && (
              <TouchableOpacity
                style={PlannerStyle.addButton}
                onPress={() => setAddEventModalVisible(true)}
              >
                <View>
                  <AntDesign
                    name="plus"
                    size={40}
                    color="#FFF"
                    style={PlannerStyle.addButtonText}
                  />
                </View>
              </TouchableOpacity>
            )
          )}
        </View>

        <RsvpViewContainer
          showModal={showRsvpModal}
          closeModal={() => setShowRsvpModal(false)}
          event={selectedEventItem}
          setShowModal={setShowRsvpModal}
        />
      </View>

      {selectedEventItem && showEventDetails && (
        <View
          style={{
            ...PlannerStyle.container,
            marginTop: isTabDevice() ? 0 : hp('1%'),
            height: isTabDevice() ? hp('100%') : hp('100%'),
          }}
        >
          <EventViewContainer
            showModal={showEventDetails}
            closeModal={() => setShowEventDetails(false)}
            event={selectedEventItem}
          />
        </View>
      )}

      {addEventModalVisible && (
        <View
          style={{
            ...PlannerStyle.addEventModal,
            marginTop: isTabDevice() ? 0 : hp('1%'),
            height: isTabDevice() ? hp('100%') : hp('100%'),
          }}
        >
          <AddEventForm
            eventName={eventName}
            eventTimestamp={eventTimestamp}
            modalAction={() => setAddEventModalVisible(false)}
            event={''}
          />
        </View>
      )}
    </>
  );
}
