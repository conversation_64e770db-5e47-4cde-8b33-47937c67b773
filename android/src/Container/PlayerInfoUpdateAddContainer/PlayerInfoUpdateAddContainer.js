import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import PlayerInfoUpdateForm from '../../components/PlayerInfoUpdateForm/PlayerInfoUpdateForm';

const PlayerInfoMedicalHistoryAddContainer = ({
  setModalVisible,
  addUpdate,
}) => {
  const selectedUpdateData = {
    _id: '',
    message: '',
    createdDate: new Date().toISOString(),
  };

  return (
    <PlayerInfoUpdateForm
      selectedUpdateData={selectedUpdateData}
      modalAction={setModalVisible}
      action={addUpdate}
    />
  );
};
export default PlayerInfoMedicalHistoryAddContainer;
