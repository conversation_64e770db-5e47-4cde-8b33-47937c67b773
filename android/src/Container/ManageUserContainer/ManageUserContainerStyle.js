import { Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const ManageUserContainerStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: '100%',
        flexDirection: 'column',
      },
  leftView: isTabDevice()
    ? {
        width: wp('26%'),
        // justifyContent: 'center',
        paddingLeft: wp('2%'),
      }
    : {
        width: wp('100%'),
        height: hp('35%'),
        paddingLeft: wp('2%'),
        // paddingRight: wp('2%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('90%'),
        paddingLeft: wp('3%'),
        borderLeftWidth: 1,
        borderLeftColor: colors.borderBlue,
      }
    : {
        width: wp('100%'),
        height: hp('45%'),
        marginTop: hp('3%'),
        paddingLeft: wp('2%'),
        // borderTopWidth: 1,
        // borderTopColor: colors.borderBlue,
      },
  ProfileLabelView: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  commonView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactsView: {
    flex: 1,
  },
  columnView: {
    flex: 1,
    flexDirection: 'column',
  },
  addUserButton: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('21%'),
        height: wp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1.5%'),
        width: wp('96%'),
        height: wp('10%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginBottom: wp('1%'),
      },
  addUserButtonText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  title: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('2.5%'),
      },
  contentArea: {
    marginTop: wp('2%'),
  },
  bottomRow: isTabDevice()
    ? {
        flexDirection: 'row',
        zIndex: 5,
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        zIndex: 5,
        width: wp('96%'),
      },
  search: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        width: wp('54%'),
        marginRight: wp('2%'),
        borderRadius: wp('1%'),
        shadowOpacity: 0,
      }
    : {
        backgroundColor: colors.borderBlue,
        width: wp('60%'),
        height: wp('11%'),
        marginRight: wp('2%'),
        borderRadius: wp('1.5%'),
        shadowOpacity: 0,
      },
  searchText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('1.5%'),
        paddingTop: hp('1%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('3%'),
      },
  dropdownView: isTabDevice()
    ? {
        width: wp('13%'),
      }
    : {
        width: wp('34%'),
      },
  dropdown: {
    justifyContent: 'flex-start',
    backgroundColor: colors.borderBlue,
  },
  dropdownSelected: {
    backgroundColor: colors.borderBlue,
    borderColor: colors.borderBlue,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Regular',
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        height: wp('5%'),
        borderRadius: wp('1%'),
        borderWidth: wp('0.5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        height: wp('11%'),
        borderRadius: wp('1.5%'),
        borderWidth: wp('0.5%'),
      },
  dropdownList: {
    backgroundColor: colors.borderBlue,
    borderColor: colors.borderBlue,
    // marginTop: wp('1%'),
    borderBottomLeftRadius: wp('1.5%'),
    borderBottomRightRadius: wp('1.5%'),
  },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Regular',
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.2%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
  dropdownTopArea: isTabDevice()
    ? {
        ...(Platform.OS === 'android'
          ? {
              borderColor: colors.transparent,
              backgroundColor: colors.borderBlue,
              height: hp('6%'),
            }
          : {
              backgroundColor: colors.borderBlue,
              borderColor: colors.borderBlue,
              borderRadius: wp('100%'),
            }),
      }
    : {
        ...(Platform.OS === 'android'
          ? {
              backgroundColor: colors.borderBlue,
              borderColor: colors.transparent,
              marginTop: wp('0.4%'),
              height: wp('10%'),
            }
          : {
              backgroundColor: colors.borderBlue,
              borderColor: colors.borderBlue,
              borderRadius: wp('100%'),
            }),
      },
  controllerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  Btn: isTabDevice()
    ? {
        paddingHorizontal: wp('1%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('4%'),
        height: wp('4%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        paddingHorizontal: wp('1%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1.5%'),
        width: wp('10%'),
        height: wp('10%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  colorPick: isTabDevice()
    ? {
        width: wp('2.5%'),
        height: wp('2.5%'),
        borderRadius: wp('0.5%'),
      }
    : {
        width: wp('6%'),
        height: wp('6%'),
        borderRadius: wp('1%'),
      },
  icon: isTabDevice()
    ? {
        width: wp('1.5%'),
        height: wp('1.5%'),
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
      },
  SessionBtnContainer: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        width: wp('21%'),
        height: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('4%'),
        width: wp('95.5%'),
        height: wp('13%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('2%'),
        paddingLeft: wp('9%'),
        paddingRight: wp('9%'),
      },
  SessionBtnText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
export default ManageUserContainerStyle;
