import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const UserListWrapperStyle = colors => ({
  container: isTabDevice()
    ? {
        width: '100%',
        marginTop: wp('-2%'),
        height: hp('72%'),
      }
    : {
        width: '100%',
        height: hp('30%'),
        marginBottom: hp('1%'),
      },
  teamName: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      },
  list: isTabDevice()
    ? {
        height: hp('90%'),
      }
    : {
        height: wp('50%'),
      },
});
export default UserListWrapperStyle;
