import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const UserTypeSelectStyle = colors => ({
  container: {
    width: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  list: isTabDevice() ? {} : {},
  allUserType: {
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    width: wp('21%'),
    height: wp('5%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginBottom: wp('1%'),
  },
  alluserTypeSelected: {
    backgroundColor: colors.aquaBlue,
    borderRadius: wp('1%'),
    width: wp('21%'),
    height: wp('5%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginBottom: wp('1%'),
  },
  alluserTypeText: {
    fontSize: wp('1.3%'),
    color: colors.white,
    fontWeight: 'bold',
  },
});
export default UserTypeSelectStyle;
