import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateContainerStyle = colors => ({
  PlayerInfoUpdateWrapper: isTabDevice()
    ? {
        width: wp('67%'),
      }
    : {
        width: wp('95%'),
      },
  container: isTabDevice()
    ? {
        width: '100%',
        height: hp('60%'),
        // marginLeft: wp('2%')
      }
    : {
        width: '100%',
        height: hp('45%'),
        paddingBottom: hp('2%'),
        marginLeft: wp('5%'),
      },
  dataRow: {
    // width: '100%'
  },
});
export default PlayerInfoUpdateContainerStyle;
