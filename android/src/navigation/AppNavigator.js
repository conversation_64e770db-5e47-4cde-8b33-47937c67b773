import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Amplify } from 'aws-amplify';
import Axios from 'axios';
import Constants from 'expo-constants';
import * as ScreenOrientation from 'expo-screen-orientation';
import React, { useEffect, useRef, useState } from 'react';
import { AppState } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ForceModal from '../components/modal/ForceModal/ForceModal';
import TeamWrapper from '../components/Teams/TeamsWrapper';
import { isTabDevice } from '../config/appConfig';
import {
  APP_STATE_BACKGROUND,
  forcedModalType,
  userRoleType,
  WebSocketParams,
} from '../constants/constants';
import {
  FOOTBALL_SERVICE,
  MESSAGING_SERVICE,
  USER_MANAGEMENT_SERVICE,
  EVENT_SERVICE,
} from '../constants/services';
import MatchLogAPIHooks from '../Container/MatchLogContainer/MatchLogAPIHook';
import PlayerMatchLogScreen from '../Container/PlayerMatchLogContainer/PlayerMatchLogScreen';
import useApi from '../hooks/useApi';
import useApiPromise from '../hooks/useApiPromise';
import useAuth from '../hooks/useAuth';
import { useGetAmplifyConfiguration } from '../hooks/useConfigurationData';
import useDebounce from '../hooks/useDebounce';
import useDeviceInfo from '../hooks/useDeviceInfo';
import WS from '../hooks/useWSMessages';
import AddUserScreen from '../screens/AddUser/addUserScreen';
import LandingScreen from '../screens/Landing/LandingScreen';
import LoginScreen from '../screens/login/LoginScreen';
import MatchLogScreen from '../screens/MatchLog/MatchLogScreen';
import MatchReportScreen from '../screens/MatchReport/MatchReportScreen';
import MessageScreenMobileMain from '../screens/Messages/MessageScreenMobileMain';
import MessageScreenMobileMessage from '../screens/Messages/MessageScreenMobileMessage';
import MessageScreenTabView from '../screens/Messages/MessageScreenTabView';
import PlannerScreen from '../screens/Planner/Planner';
import PlayerInfoScreen from '../screens/PlayerInfo/PlayerInfoScreen';
import TeamsScreen from '../screens/Team/TeamsScreen';
import TeamMatchesScreen from '../screens/TeamMatches/TeamMatchesScreen';
import TeamMatchPlanScreen from '../screens/TeamMatchPlan/TeamMatchPlanScreen';
import TrainingScreen from '../screens/Trainings/Training';
import UserManagementScreen from '../screens/UserManagement/UserManagementScreen';
import { useNetInfo } from '@react-native-community/netinfo';
import {
  SET_CURRENT_ENVIRONMENT,
  SET_CURRENT_ENVIRONMENT_SUCCESS,
} from '../store/actionTypes/auth';
import {
  GET_CHILD_TEAMS_INFORMATION_FAILED,
  GET_CHILD_TEAMS_INFORMATION_REQUEST,
  GET_CHILD_TEAMS_INFORMATION_SUCCESS,
  IS_SCREEN_ACTIVE,
  USER_INFORMATION_FAILED,
  USER_INFORMATION_REQUEST,
  USER_INFORMATION_SUCCESS,
} from '../store/actionTypes/common/commonActionTypes';
import { CHECK_IS_APP_ACTIVE } from '../store/actionTypes/Message/MessageAction';
import AddSessionScreen from '../screens/AddSession/addSessionScreen';
import { TrainingTeamMobileScreen } from '../screens/Trainings/Training';
import { TrainingTeamEventMobileScreen } from '../screens/Trainings/Training';
import { TrainingTabScreen } from '../screens/Trainings/Training';

const { Navigator, Screen } = createStackNavigator();

const AppStack = () => {
  const [username, setUserName] = useState('');
  const [password, setPassword] = useState('');
  const signIn = useAuth();

  const { configurationData, sessionToken, userData } = useSelector(
    state => state?.auth
  );

  const { Auth } = configurationData || {};
  const { wss } = Auth || {};

  const [deviceInfo] = useDeviceInfo();
  const [isMobileDevice, setIsMobileDevice] = useState(false);

  const [initiateWS, ws, newMessage] = WS(wss, WebSocketParams.MESSAGE_WS);
  useEffect(() => {
    !ws && userData?.id && initiateWS();
  }, [userData]);

  useEffect(() => {
    setIsMobileDevice(deviceInfo?.deviceType === 'phone');
  }, [JSON.stringify(deviceInfo)]);

  useEffect(() => {
    if (!sessionToken && configurationData && username && password && Amplify) {
      Amplify.configure(configurationData);
      signIn(username, password);
    }
  }, [configurationData, Amplify]);

  useEffect(() => {
    if (configurationData && Amplify) {
      Amplify.configure(configurationData);
    }
  }, [configurationData, Amplify]);

  useEffect(() => {
    const handleChangeOrientation = async () => {
      if (isTabDevice()) {
        await ScreenOrientation.lockAsync(
          ScreenOrientation.OrientationLock.LANDSCAPE
        );
      } else {
        await ScreenOrientation.lockAsync(
          ScreenOrientation.OrientationLock.PORTRAIT_UP
        );
      }
    };
    handleChangeOrientation();
  }, []);

  return (
    <Navigator
      headerMode="none"
      initialRouteName={sessionToken ? 'landing' : 'Login'}
      keyboardHandlingEnabled
    >
      <Screen name="Login">
        {props => (
          <LoginScreen
            {...props}
            username={username}
            password={password}
            handleUserName={setUserName}
            handlePassword={setPassword}
          />
        )}
      </Screen>
      <Screen name="landing" component={LandingScreen} />
      <Screen name="TeamWrapper" component={TeamWrapper} />
      <Screen name="Teams" component={TeamsScreen} />
      <Screen name="PlayerInfoScreen" component={PlayerInfoScreen} />
      <Screen name="PlannerScreen" component={PlannerScreen} />
      <Screen name="Matches" component={TeamMatchesScreen} />
      <Screen name="MatchPlan" component={TeamMatchPlanScreen} />
      <Screen name="MatchLog" component={MatchLogScreen} />
      <Screen name="UserManagement" component={UserManagementScreen} />
      <Screen name="AddUser" component={AddUserScreen} />
      <Screen name="AddSession" component={AddSessionScreen} />
      <Screen name="PlayerMatchLog" component={PlayerMatchLogScreen} />
      <Screen name="MatchReport" component={MatchReportScreen} />
      {/* <Screen name="Training" component={TrainingScreen} /> */}
      {isMobileDevice ? (
        <>
          <Screen name="Training" component={TrainingTeamMobileScreen} />
          <Screen name="TrainingEvents" component={TrainingTeamEventMobileScreen} />
        </>
      ) : (
        <>
          <Screen name="Training" component={TrainingTabScreen} />
        </>
      )}
      {isMobileDevice ? (
        <>
          <Screen name="Messaging" component={MessageScreenMobileMain} />
          <Screen name="MessagingChat" component={MessageScreenMobileMessage} />
        </>
      ) : (
        <>
          <Screen name="Messaging" component={MessageScreenTabView} />
        </>
      )}
    </Navigator>
  );
};

const Container = () => {
  const netInfo = useNetInfo();
  const [isOfflineAlert, setIsOfflineAlert] = useState(false);
  const [fetchData] = useApi();
  const [fetchChildTeamsData] = useApi();
  const [setConnectionIdForMessage] = useApi();
  const [getCurrentEnvironmentDetails] = useApi();
  const [setConnectionIdForMatchLog] = useApiPromise();
  const [isShowUpdateModal, setIsShowUpdateModal] = useState(false);
  const { userData, currentEnvironment, sessionToken } = useSelector(
    state => state?.auth
  );
  const [fetchAmplifyConfigurationData] = useGetAmplifyConfiguration();

  useEffect(() => {
    if (netInfo.isInternetReachable) {
      setIsOfflineAlert(false);
    } else {
      if (netInfo.isInternetReachable !== null) {
        setTimeout(() => {
          setIsOfflineAlert(true);
        }, 100);
      }
    }
  }, [netInfo]);

  const { selectedMessageChatId, connectionId, isScreenActive } = useSelector(
    state => state?.message
  );
  const {
    userInformation,
    childTeamsInformation,
    loadChildTeamsInformation,
    connectionIdMatchWs,
    wsIsConnect,
  } = useSelector(state => state?.common);
  const { getAllMatchPlayers } = MatchLogAPIHooks();

  const matchWsIsConnect = wsIsConnect?.[WebSocketParams.MATCH_WS];

  const { selectedEvent } = useSelector(state => state?.matchLog);
  const { currentMatch } = useSelector(state => state?.playerMatchLog);
  const isCoach =
    userRoleType.HEAD_COACH === userData?.type ||
    userRoleType.COACH === userData?.type;

  const matchId = !isCoach ? currentMatch?._id : selectedEvent?._id;

  const checkUpdateAvailable = () => {
    Axios.get(
      `${Constants.expoConfig.extra.APP_VALIDATION_URL}${Constants.expoConfig.android.versionCode}`
    )
      .then(response => {
        setIsShowUpdateModal(!response.data);
      })
      .catch(() => {
        setIsShowUpdateModal(false);
      });
  };
  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  const dispatch = useDispatch();

  const newAppStateVisble = appStateVisible !== APP_STATE_BACKGROUND;
  const debouncedNewAppStateVisble = useDebounce(newAppStateVisble, 1000);

  const checkUserInfo = () => {
    const email = userData.emailId;

    fetchData(
      `/api/v1/users?email=${email}`,
      USER_INFORMATION_REQUEST,
      USER_INFORMATION_SUCCESS,
      USER_INFORMATION_FAILED,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  useEffect(() => {
    userData && checkUserInfo();
  }, [userData]);

  useEffect(() => {
    checkUpdateAvailable();
    AppState.addEventListener('change', nextAppState => {
      appState.current = nextAppState;
      setAppStateVisible(appState.current);
    });
  }, []);

  useEffect(() => {
    if (isScreenActive != debouncedNewAppStateVisble) {
      dispatch({
        type: CHECK_IS_APP_ACTIVE,
        payload: {
          data: debouncedNewAppStateVisble,
        },
      });
    }
  }, [debouncedNewAppStateVisble, isScreenActive]);

  const [isUnauthorizedModalOpen, setIsUnauthorizedModalOpen] = useState(false);
  const [isZeroTeamUnauthorizedModalOpen, setIsZeroTeamUnauthorizedModalOpen] =
    useState(false);

  useEffect(() => {
    if (userInformation && userInformation?.type === userRoleType.PARENT) {
      !userInformation?.childrenIds?.length
        ? setIsUnauthorizedModalOpen(true)
        : fetchChildTeamsData(
            `/api/v1/teams?page=1&size=10&playerIds=${userInformation?.childrenIds?.join(
              ','
            )}`,
            GET_CHILD_TEAMS_INFORMATION_REQUEST,
            GET_CHILD_TEAMS_INFORMATION_SUCCESS,
            GET_CHILD_TEAMS_INFORMATION_FAILED,
            null,
            '',
            'GET',
            null,
            FOOTBALL_SERVICE
          );
    }
  }, [userInformation]);

  useEffect(() => {
    userInformation?.childrenIds?.length &&
      !childTeamsInformation?.length &&
      loadChildTeamsInformation &&
      setIsZeroTeamUnauthorizedModalOpen(true);
  }, [
    JSON.stringify(childTeamsInformation),
    userInformation,
    loadChildTeamsInformation,
  ]);

  useEffect(() => {
    const selectedChatKey = Object.keys(selectedMessageChatId || {})?.[0];
    const selectedChatId = selectedMessageChatId?.[selectedChatKey]?._id;
    let payload = {
      connectionId: connectionId,
    };

    if (selectedChatId && isScreenActive) {
      payload.chatId = selectedChatId;
    }

    if (connectionId) {
      setConnectionIdForMessage(
        `/api/v1/connections`,
        '',
        '',
        '',
        payload,
        '',
        'PUT',
        false,
        MESSAGING_SERVICE,
        { ...payload }
      );
    }
  }, [selectedMessageChatId, isScreenActive, connectionId]);

  useEffect(() => {
    /**
     * Need  to call `getAllMatchPlayers` to get all PLayer Details, Before setting a connectionId & Match Id  `setConnectionIdForMatchLog`
     * Converted async await
     */
    const handleMatchLogConnection = async () => {
      try {
        let payload = {
          connectionId: connectionIdMatchWs,
        };
        if (matchId && newAppStateVisble) {
          await getAllMatchPlayers(matchId);
          payload.matchId = matchId;
        }

        if (connectionIdMatchWs && matchWsIsConnect) {
          await setConnectionIdForMatchLog(
            `/api/v1/connections`,
            '',
            '',
            '',
            payload,
            '',
            'PUT',
            false,
            EVENT_SERVICE
          );
        }
      } catch (error) {
        console.log(error);
      }
    };
    handleMatchLogConnection();
  }, [newAppStateVisble, connectionIdMatchWs, matchId, matchWsIsConnect]);

  const fetchCurrentEnvironmentDetails = userId => {
    getCurrentEnvironmentDetails(
      `/api/v1/users/${userId}/clubId`,
      '',
      SET_CURRENT_ENVIRONMENT_SUCCESS,
      '',
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  useEffect(() => {
    if (newAppStateVisble && sessionToken) {
      currentEnvironment
        ? fetchAmplifyConfigurationData(currentEnvironment)
        : fetchCurrentEnvironmentDetails(userData?.id);
    }
    dispatch({ type: IS_SCREEN_ACTIVE, payload: newAppStateVisble });
  }, [newAppStateVisble, currentEnvironment]);

  return (
    <NavigationContainer>
      <AppStack />
      {isOfflineAlert && (
        <ForceModal
          type={forcedModalType.APP_OFFLINE}
          onClose={() => {
            setIsOfflineAlert(false);
          }}
          goBack={() => setIsOfflineAlert(false)}
        />
      )}
      {isShowUpdateModal ? (
        <ForceModal type={forcedModalType.FORCED_UPDATE} />
      ) : isUnauthorizedModalOpen ? (
        <ForceModal
          type={forcedModalType.UNAUTHORIZED_PARENT}
          onClose={setIsUnauthorizedModalOpen}
        />
      ) : isZeroTeamUnauthorizedModalOpen ? (
        <ForceModal
          type={forcedModalType.UNAUTHORIZED_PARENT_WITH_NO_TEAMS}
          onClose={setIsZeroTeamUnauthorizedModalOpen}
        />
      ) : null}
    </NavigationContainer>
  );
};

export default Container;
