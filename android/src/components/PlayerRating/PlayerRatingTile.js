import React, { useState } from 'react';
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native';
import ProfileImage from '../ProfileImage/ProfileImage';

import unAvailableIcon from '../../../assets/icons/unavailable.png';
import useStyles from '../../hooks/useStyles';
import customPlayerRatingTileStyle from './PlayerRatingTileStyle';
import { NOT_RATED } from '../../constants/constants';

const PlayerRatingTile = ({ playerData, setUpdatedValue }) => {
  const PlayerRatingTileStyle = useStyles(customPlayerRatingTileStyle);
  const [rating, setRating] = useState(playerData?.rating || NOT_RATED);

  const returnVaildRatingValue = value => {
    const numberValue = Number.parseInt(value);
    let tmpValue = 0;
    if (Number.isNaN(numberValue)) {
      tmpValue = 0;
    } else if (numberValue <= 0) {
      tmpValue = 0;
    } else if (numberValue > 10) {
      tmpValue = 10;
    } else {
      tmpValue = numberValue;
    }
    return tmpValue;
  };

  const onAddChange = isAdd => {
    let tmpRate = isAdd
      ? returnVaildRatingValue(rating) + 1
      : returnVaildRatingValue(rating) - 1;

    let vaildRate = returnVaildRatingValue(tmpRate);

    setRating(() => {
      setUpdatedValue({
        rating: vaildRate,
        userId: playerData?.userId,
      });
      return vaildRate || 1;
    });
  };

  const renderRatingInput = () => {
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity
          onPress={() => onAddChange(false)}
          disabled={rating == NOT_RATED || rating == 1}
        >
          <View style={PlayerRatingTileStyle.minusButton}>
            <Text style={PlayerRatingTileStyle.minusButtonText}>-</Text>
          </View>
        </TouchableOpacity>
        <TextInput
          editable={false}
          keyboardType="numeric"
          placeholderTextColor="#595959"
          style={PlayerRatingTileStyle.numberInput}
          value={rating.toString()}
        />
        <TouchableOpacity
          onPress={() => onAddChange(true)}
          disabled={rating == 10}
        >
          <View style={PlayerRatingTileStyle.plusButton}>
            <Text style={PlayerRatingTileStyle.plusButtonText}>+</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={PlayerRatingTileStyle.player}>
      <ProfileImage
        imageStyles={PlayerRatingTileStyle.proImage}
        profileImageUrl={playerData?.profileImageUrl}
      />
      {!playerData?.isAvailable && (
        <View style={PlayerRatingTileStyle.availableIconWrapper}>
          <Image style={PlayerRatingTileStyle.icon} source={unAvailableIcon} />
        </View>
      )}
      <Text style={PlayerRatingTileStyle.playerName}>
        {playerData.firstName || ''} {playerData.lastName || ''}
      </Text>
      {renderRatingInput()}
    </View>
  );
};

export default PlayerRatingTile;
