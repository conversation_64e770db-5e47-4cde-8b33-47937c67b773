import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerRatingTileStyle = colors => ({
  player: isTabDevice()
    ? {
        marginLeft: wp('0.5%'),
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
        marginRight: wp('0%'),
        flexDirection: 'column',
        justifyContent: 'start',
        alignItems: 'center',
      }
    : {
        margin: wp('1.5%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
      },
  proImage: isTabDevice()
    ? {
        width: wp('12%'),
        height: wp('12%'),
        borderRadius: wp('2%'),
        resizeMode: 'cover',
        marginBottom: wp('0.5%'),
      }
    : {
        width: wp('27%'),
        height: wp('27%'),
        borderRadius: wp('2%'),
        resizeMode: 'cover',
        marginBottom: wp('0.5%'),
      },
  playerName: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.3%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
        width: wp('20%'),
      }
    : {
        color: colors.green,
        fontSize: wp('2.5%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
        width: wp('27%'),
      },
  numberInput: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        width: wp('5%'),
        height: wp('3.5%'),
        marginLeft: wp('0.5%'),
        marginRight: wp('0.5%'),
        textAlign: 'center',
      }
    : {
        fontSize: wp('3.3%'),
        color: colors.white,
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        width: wp('14%'),
        height: wp('6%'),
        marginLeft: wp('1.5%'),
        marginRight: wp('1.5%'),
        textAlign: 'center',
      },
  minusButton: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        backgroundColor: colors.red,
        borderRadius: wp('1%'),
        position: 'relative',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        backgroundColor: colors.red,
        borderRadius: wp('2%'),
        // position: 'relative'
      },
  plusButton: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        // padding: wp('1%'),
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        backgroundColor: colors.green,
        borderRadius: wp('2%'),
        position: 'relative',
      },
  plusButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('3%'),
        position: 'absolute',
        top: wp('-0.7%'),
        left: wp('0.7%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        position: 'absolute',
        top: wp('-1.3%'),
        left: wp('1.1%'),
      },
  minusButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('4%'),
        position: 'absolute',
        top: wp('-1.5%'),
        left: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        position: 'absolute',
        top: wp('-1.4%'),
        left: wp('1.6%'),
      },
  icon: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
      }
    : {
        width: hp('2%'),
        height: hp('2%'),
      },
  availableIconWrapper: isTabDevice()
    ? {
        backgroundColor: colors.white,
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        position: 'absolute',
        top: -wp('1%'),
        right: -wp('0.8%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignSelf: 'center',
      }
    : {
        backgroundColor: colors.white,
        width: hp('2%'),
        height: hp('2%'),
        borderRadius: hp('100%'),
        position: 'absolute',
        top: -hp('0.8%'),
        right: -hp('0.5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignSelf: 'center',
      },
});

export default PlayerRatingTileStyle;
