import React, { useEffect, useMemo, useState } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { USER_MANAGEMENT_SERVICE } from '../../../constants/services';
import useApiPromise from '../../../hooks/useApiPromise';
import useStyles from '../../../hooks/useStyles';
import { useSelector } from 'react-redux';

import customUserTypeTileStyle from './UserTypeTileStyle';

interface IItem {
  id: string;
  typeName: string;
}

interface UserTypeTileProps {
  item: IItem;
  selectedType: string;
  handleChange: Function;
  disabled: any;
  index: number;
}

const UserTypeTile = ({
  item,
  selectedType,
  handleChange,
  disabled,
  index,
}: UserTypeTileProps) => {
  const [fetchData] = useApiPromise();

  const { creatUserSuccess }: any = useSelector((state: any) => state?.addUser);

  const UserTypeTileStyle = useStyles(customUserTypeTileStyle);
  let { typeName, id } = item;

  const singleUserTypeStyle = useMemo(() => {
    if (index === 2) {
      return id === selectedType
        ? UserTypeTileStyle.userTypeSelectedParent
        : UserTypeTileStyle.userTypeParent;
    } else {
      return id === selectedType
        ? UserTypeTileStyle.userTypeSelected
        : UserTypeTileStyle.userType;
    }
  }, [index, selectedType, id]);
  const [totalUsers, setTotalUsers] = useState(0);

  const getTotalUsersCount = (selectedUserType: string) => {
    let requestUrl = `/api/v1/users?page=1&size=1&type=${selectedUserType}`;

    fetchData(
      requestUrl,
      '-',
      '-',
      '-',
      null,
      '',
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    ).then(res => {
      setTotalUsers(res?.data?.totalRecords || 0);
    });
  };

  useEffect(() => {
    if (selectedType || creatUserSuccess) {
      getTotalUsersCount(id);
    }
  }, [selectedType, creatUserSuccess]);

  return (
    <View>
      <TouchableOpacity onPress={() => !disabled && handleChange(id)}>
        <View style={{ ...singleUserTypeStyle }}>
          <Text style={UserTypeTileStyle.userTypeText}>{typeName}</Text>
          <View style={UserTypeTileStyle.userTypeCountWrapper}>
            <Text style={UserTypeTileStyle.userTypeCount}>
              {totalUsers || 0}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default UserTypeTile;
