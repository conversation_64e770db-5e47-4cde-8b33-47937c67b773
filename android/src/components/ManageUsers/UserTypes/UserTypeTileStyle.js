import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const UserTypeTileStyle = colors => ({
  userTypeSelectedParent: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: hp('10'),
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('4%'),
        width: wp('31%'),
        height: wp('13%'),
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  userTypeParent: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: hp('10%'),
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('4%'),
        width: wp('31%'),
        height: wp('13%'),
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  userType: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: hp('10%'),
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('4%'),
        width: wp('31%'),
        height: wp('13%'),
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  userTypeSelected: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: hp('10'),
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('4%'),
        width: wp('31%'),
        height: wp('13%'),
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  userTypeText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  userTypeCount: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        paddingTop: 3,
      },
  userTypeCountWrapper: {
    backgroundColor: colors.blackOpcaity,
    borderRadius: 10,
    paddingTop: 2,
    paddingBottom: 2,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 10,
    paddingRight: 10,
  },
});
export default UserTypeTileStyle;
