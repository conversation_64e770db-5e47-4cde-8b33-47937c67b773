import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import useStyles from '../../../hooks/useStyles';

import customTeamTileStyles from './TeamTileStyles';

const TeamTile = ({
  item,
  selectedTeam,
  handleChange,
  openAddTeamModal,
  disabled,
}) => {
  const TeamTileStyles = useStyles(customTeamTileStyles);
  let { teamName, _id } = item;

  return (
    <View>
      <TouchableOpacity
        onPress={() =>
          !disabled && (_id !== '-1' ? handleChange(item) : openAddTeamModal())
        }
      >
        <View
          style={
            _id === selectedTeam._id && _id !== '-1'
              ? TeamTileStyles.teamTileSelected
              : _id !== '-1'
              ? TeamTileStyles.teamTile
              : TeamTileStyles.teamTileAddTeam
          }
        >
          <Text style={TeamTileStyles.teamTileText}>{teamName}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default TeamTile;
