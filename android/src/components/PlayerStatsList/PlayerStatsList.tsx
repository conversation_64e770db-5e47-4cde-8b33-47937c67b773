import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import React, { FC, useCallback } from 'react';
import useStyles from '../../hooks/useStyles';
import CustomPlayerDropdownStyles from '../../Container/PlayerStatsFilterContainer/PlayerStatsFilterStyles';
import {
  seasonsType,
  tournamentsType,
  teamType,
} from '../../store/reducers/PlayerStats/PlayerStatsReducer';

interface ISelectedItem {
  _id: string;
  name: string;
}

interface IPlayerStatsList {
  data: ISelectedItem[];
  title: string;
  onEndReachedHandler?: () => void;
  isMultiple?: boolean;
  selectedItems: seasonsType[] | tournamentsType[] | teamType[];
  setSelectedItems: Function;
  teamID?: string;
}

const PlayerStatsList: FC<IPlayerStatsList> = ({
  data,
  isMultiple = false,
  title,
  onEndReachedHandler,
  setSelectedItems,
  selectedItems,
  teamID,
}) => {
  const PlayerDropdownStyles = useStyles(CustomPlayerDropdownStyles);

  const isItemSelected = useCallback(
    (selectedValue: ISelectedItem) => {
      return selectedItems?.some(value => value._id === selectedValue._id);
    },
    [JSON.stringify(selectedItems)]
  );

  const selectedItemsHandler = (item: ISelectedItem) => {
    if (!title) {
      return;
    }
    if (!isItemSelected(item)) {
      if (!isMultiple) {
        setSelectedItems([item]);
      } else {
        setSelectedItems((tmpSelectedItems: ISelectedItem[]) => [
          item,
          ...tmpSelectedItems,
        ]);
      }
    } else {
      if (!isMultiple) {
        setSelectedItems([]);
      } else {
        if (item?._id !== teamID) {
          setSelectedItems((tmpSelectedItems: ISelectedItem[]) =>
            tmpSelectedItems.filter(
              (tmpSelectedItem: ISelectedItem) =>
                tmpSelectedItem._id !== item._id
            )
          );
        }
      }
    }
  };

  const renderItem = ({ item }: { item: ISelectedItem }) => {
    return (
      <TouchableOpacity onPress={() => selectedItemsHandler(item)}>
        <View
          style={
            isItemSelected(item)
              ? PlayerDropdownStyles.listItemSelected
              : PlayerDropdownStyles.listItem
          }
        >
          <Text
            style={PlayerDropdownStyles.listItemText}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {item.name}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={PlayerDropdownStyles.listContainer}>
      <Text style={PlayerDropdownStyles.listTitle}>{title}</Text>
      <FlatList
        style={PlayerDropdownStyles.list}
        data={data}
        renderItem={renderItem}
        keyExtractor={item => item._id}
        onEndReached={onEndReachedHandler}
        onEndReachedThreshold={20}
      />
    </View>
  );
};

export default PlayerStatsList;
