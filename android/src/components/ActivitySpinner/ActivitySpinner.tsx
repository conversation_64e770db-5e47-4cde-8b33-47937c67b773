import React from 'react';
import { ActivityIndicator, View } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import useStyles from '../../hooks/useStyles';

interface IActivitySpinner {
  color?: string;
  isFullScreen?: Boolean;
  isMatchLog?: Boolean;
}

const ActivitySpinner: React.FC<IActivitySpinner> = ({
  color = null,
  isFullScreen,
  isMatchLog,
}) => {
  const styles = useStyles(customStyles);
  return (
    <View
      style={[
        isFullScreen ? styles.container : styles.insideContainer,
        isMatchLog && styles.matchLogContainer,
      ]}
    >
      <ActivityIndicator
        size="large"
        color={color || '#00ff00'}
        style={styles.spinner}
      />
    </View>
  );
};
const customStyles = (colors: any) => ({
  container: {
    width: wp('100%'),
    height: hp('100%'),
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    backgroundColor: colors.darkBlue,
    left: 0,
    top: 0,
    zIndex: 10,
    opacity: 0.8,
  },
  spinner: {
    width: '100%',
    height: '100%',
  },
  insideContainer: {
    width: '100%',
    height: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    // backgroundColor: colors.darkBlue,
    left: 0,
    top: 0,
    zIndex: 10,
  },
  matchLogContainer: {
    top: wp('30%'),
  },
});

export default ActivitySpinner;
