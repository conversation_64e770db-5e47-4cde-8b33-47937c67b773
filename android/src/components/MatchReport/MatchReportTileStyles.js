import { Platform } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const MatchReportTileStyles = colors => ({
  userType: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('12.7%'),
        height: wp('12.7%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1.2%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
        position: 'relative',
        top: -wp('1.5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        width: wp('30%'),
        height: wp('30%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('2%'),
        marginRight: wp('3%'),
        position: 'relative',
        top: -wp('4%'),
      },
  userTypeSelected: {
    backgroundColor: colors.green,
    borderRadius: wp('1%'),
    width: wp('10%'),
    height: wp('10%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('1%'),
  },
  userTypeText: isTabDevice()
    ? {
        fontSize: hp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
        width: wp('10%'),
        marginTop: -hp('1%'),
      }
    : {
        fontSize: Platform.OS === 'android' ? wp('2.3%') : wp('2.7%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
        width: Platform.OS === 'android' ? wp('25%') : wp('15%'),
        marginTop: wp('1%'),
      },
  documentIconWrapper: {
    width: '40%',
    height: '45%',
    marginTop: wp('1.5%'),
    marginBottom: wp('1.5%'),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentIcon: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
    marginTop: wp('1.5%'),
    marginBottom: wp('1.5%'),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  regenerateIcon: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  tileWrapper: isTabDevice()
    ? {
        display: 'flex',
      }
    : {
        width: wp('32%'),
        marginTop: wp('1%'),
      },
  generateIconWrapper: isTabDevice()
    ? {
        height: wp('2.5%'),
        width: wp('2.5%'),
        alignSelf: 'flex-end',
        zIndex: 10,
      }
    : {
        height: wp('7%'),
        width: wp('7%'),
        alignSelf: 'flex-end',
        zIndex: 10,
      },
});
export default MatchReportTileStyles;
