import React from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';

import customMatchReportTileStyles from './MatchReportTileStyles';
import useStyles from '../../hooks/useStyles';

const MatchReportTile = ({
  isLoading,
  item,
  handleChange,
  selected,
  reportReGenerate,
}) => {
  const MatchReportTileStyles = useStyles(customMatchReportTileStyles);
  return (
    <View style={MatchReportTileStyles.tileWrapper}>
      <TouchableOpacity
        style={MatchReportTileStyles.generateIconWrapper}
        onPress={reportReGenerate}
      >
        <Image
          style={{ ...MatchReportTileStyles.regenerateIcon }}
          source={require('../../../assets/icons/regeneate.png')}
        />
      </TouchableOpacity>
      <View style={MatchReportTileStyles.userType}>
        {isLoading && selected._id === item._id ? (
          <ActivityIndicator color="#36d982" />
        ) : (
          <>
            <TouchableOpacity
              onPress={() => !isLoading && handleChange(item)}
              style={MatchReportTileStyles.documentIconWrapper}
            >
              <Image
                style={MatchReportTileStyles.documentIcon}
                source={require('../../../assets/icons/documentIcon.png')}
              />
              <Text style={MatchReportTileStyles.userTypeText}>
                {item?.name || ''}
              </Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
};

export default MatchReportTile;
