import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateFormStyle = colors => ({
  container: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: isTabDevice()
    ? {
        marginTop: wp('5%'),
        width: wp('60%'),
      }
    : {
        width: wp('90%'),
      },
  trainingDocumentsContent: isTabDevice()
    ? {
        width: wp('40%'),
      }
    : {
        width: wp('80%'),
      },
  date: {
    paddingBottom: 100,
  },
  btn_wrapper: {
    marginTop: 20,
  },
  trainginBtnWrappers: {
    marginTop: 80,
  },
  btns: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'flex-end',
      }
    : {
        flexDirection: 'column',
        justifyContent: 'center',
      },
  btn: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        width: wp('8%'),
        height: wp('4.5%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: hp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        width: '100%',
        height: wp('8%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: wp('4%'),
      },
  btnDisabled: isTabDevice()
    ? {
        backgroundColor: colors.semiLightGrey,
        opacity: 0.2,
        width: wp('8%'),
        height: wp('4.5%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: hp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        width: '100%',
        height: wp('8%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: wp('4%'),
      },
  btnNoBackground: isTabDevice()
    ? {
        height: wp('4.5%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: hp('2%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        width: wp('90%'),
        height: wp('8%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: wp('2%'),
      },
  btn2: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        width: wp('8%'),
        height: wp('4.5%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 10,
      }
    : {
        backgroundColor: colors.darkBlue,
        width: '100%',
        height: wp('8%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: wp('2%'),
      },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  box: isTabDevice()
    ? {
        marginBottom: 30,
        padding: wp('2%'),
        borderColor: colors.borderBlue,
        borderWidth: 1,
      }
    : {
        marginBottom: wp('3%'),
      },
  box2: isTabDevice()
    ? {
        marginBottom: 30,
        padding: wp('2%'),
        borderColor: colors.borderBlue,
        borderWidth: 1,
        height: hp('30%'),
      }
    : {},
  dateLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Bold',
        paddingBottom: wp('1.2%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        paddingBottom: wp('1.2%'),
      },
  messageText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        paddingBottom: wp('2.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        padding: wp('2%'),
        height: hp('20%'),
      },
  selectedDate: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        paddingBottom: wp('1.2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        padding: wp('2%'),
      },
  selectedDateWrapper: isTabDevice()
    ? {}
    : {
        borderRadius: wp('2%'),
        backgroundColor: colors.borderBlue,
      },
  error: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.red,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      },
  datePickerWrapper: isTabDevice()
    ? {
        position: 'absolute',
        borderRadius: wp('3%'),
        bottom: wp('-2%'),
        left: 0,
        zIndex: 3,
        width: wp('72%'),
        borderWidth: 10,
        borderColor: colors.semiDarkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      }
    : {
        position: 'absolute',
        borderRadius: wp('3%'),
        top: 0,
        left: 0,
        zIndex: 3,
        width: wp('100%'),
        borderWidth: 10,
        borderColor: colors.semiDarkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },
  datePickerSelector: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'absolute',
        left: wp('27%'),
        top: wp('25%'),
      }
    : {
        width: wp('90%'),
        position: 'absolute',
        left: wp('02%'),
        top: wp('50%'),
      },
  datePickerWrapper: isTabDevice()
    ? {
        zIndex: 3,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      }
    : {
        zIndex: 3,
        width: wp('90%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },
  datePicker: {},
  reportContainer: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        position: 'relative',
        marginTop: wp('-1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        position: 'relative',
        paddingTop: wp('2%'),
      },
  reports: {
    marginTop: 15,
  },
  trainingReportContainer: isTabDevice()
    ? {
        display: 'flex',
        backgroundColor: colors.borderBlue,
        padding: 50,
        borderRadius: 30,
        position: 'relative',
      }
    : {
        display: 'flex',
        backgroundColor: colors.borderBlue,
        padding: 20,
        borderRadius: 30,
        position: 'relative',
      },
  trainingDocumentModalClose: {
    position: 'absolute',
    top: -15,
    right: 0,
    padding: 30,
  },
  upload: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        marginRight: wp('1%'),
        marginTop: wp('-1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        padding: 10,
        marginRight: wp('2%'),
      },
  iconImage: isTabDevice()
    ? {
        height: wp('3%'),
        width: wp('3%'),
        resizeMode: 'contain',
      }
    : {
        height: wp('7%'),
        width: wp('7%'),
        resizeMode: 'contain',
      },
  uploadIconImage: isTabDevice()
    ? {
        height: wp('3%'),
        width: wp('3%'),
        resizeMode: 'contain',
      }
    : {
        height: wp('7%'),
        width: wp('7%'),
        resizeMode: 'contain',
      },
  uploadedWrapper: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        padding: 10,
        paddingRight: 30,
        marginTop: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        padding: 10,
        marginBottom: wp('2%'),
        paddingRight: 40,
        position: 'relative',
      },
  uploadIconImageWrapper: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        marginRight: wp('2%'),
        padding: 10,
        paddingRight: 30,
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        padding: 10,
        marginBottom: wp('2%'),
        marginRight: wp('2%'),
        paddingRight: 30,
        position: 'relative',
      },
  uploadTextWrapper: {
    marginLeft: 10,
  },
  uploadWrapperClose: {
    width: 20,
    height: 20,
    fontSize: 20,
    position: 'absolute',
    top: -3,
    right: 5,
    zIndex: 10,
  },
  uploadIconImage: isTabDevice()
    ? {
        height: wp('3%'),
        width: wp('3%'),
        resizeMode: 'contain',
      }
    : {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        height: wp('7%'),
        width: wp('7%'),
        resizeMode: 'contain',
      },
  uploadIconText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        marginLeft: wp('0.4%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        marginLeft: wp('1%'),
      },
  trainingUpload: {
    backgroundColor: colors.darkBlue,
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'row',
    padding: 10,
    marginVertical: 10,
  },
  report: {
    backgroundColor: colors.borderBlue,
    padding: 15,
    paddingHorizontal: 25,
    display: 'flex',
    flexDirection: 'row',
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: 10,
  },
  trainingReport: {
    backgroundColor: colors.darkBlue,
    padding: 15,
    paddingHorizontal: 15,
    display: 'flex',
    flexDirection: 'row',
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
    // justifyContent: 'space-between',
  },
  trainingReportDisabled: {
    backgroundColor: colors.semiLightGrey,
    opacity: 0.2,
    padding: 15,
    paddingHorizontal: 15,
    display: 'flex',
    flexDirection: 'row',
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  close: {
    color: colors.white,
    position: 'absolute',
    top: 20,
    right: 5,
    padding: 5,
    // backgroundColor: 'red',
    width: 20,
    height: 20,
  },
  trainingClose: {
    position: 'absolute',
    right: 10,
    top: 20,
  },
  download: isTabDevice()
    ? {
        position: 'absolute',
        right: 10,
        top: 25,
      }
    : {
        position: 'absolute',
        right: 10,
        top: 20,
      },
  title: {
    fontSize: 25,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 25,
  },
  scroll: {
    maxHeight: 170,
    minHeight: 20,
  },
});
export default PlayerInfoUpdateFormStyle;
