import * as DocumentPicker from 'expo-document-picker';
import React, { useEffect, useState } from 'react';
import {
  Appearance,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ActivityIndicator,
  KeyboardAvoidingView,
  Keyboard,
  ScrollView,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { dateTimeConversion } from '../../helpers/index';
import useStyles from '../../hooks/useStyles';
import { UPLOAD_REPORT_SUCCESS } from '../../store/actionTypes/UploadReport/uploadreportAction';
import customPlayerInfoUpdateFormStyle from './PlayerInfoUpdateFormStyle';
import { useDispatch } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { videoFileTypes } from '../../constants/constants';
import useFileUpload from '../../hooks/useFileUpload';
import useS3bucketLocation from '../../hooks/useS3bucketLocation';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';

const PlayerInfoUpdateFormReport = ({
  selectedUpdateData,
  modalAction,
  action,
  playerUploadedReport,
  deleteReportAction,
  fileType,
}) => {
  const dispatch = useDispatch();
  const [upload, uploadedContent, , isUploading] = useFileUpload();
  const [getBucketLocation, bucketLocation] = useS3bucketLocation();

  const PlayerInfoUpdateFormStyle = useStyles(customPlayerInfoUpdateFormStyle);
  const { message, createdDate, _id } = selectedUpdateData;

  const [updateMessage, setUpdateMessage] = useState(message);
  const [updateDate, setUpdateDate] = useState(new Date(createdDate));
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: 'Maximum upload file size: 10mb',
  });
  const [fileObject, setFileObject] = useState(null);
  const [inputFileName, setInputFileName] = useState('');
  const [isReportUploading, setIsReportUploading] = useState(false);

  useEffect(() => {
    getBucketLocation({
      path: fileType || '',
      service: USER_MANAGEMENT_SERVICE,
    });
  }, []);

  useEffect(() => {
    fileObject &&
      upload(
        fileObject,
        `${bucketLocation?.filePath}`,
        bucketLocation?.bucketName
      );
  }, [fileObject, bucketLocation]);

  useEffect(() => {
    if (uploadedContent && inputFileName && isReportUploading) {
      dispatch({
        type: UPLOAD_REPORT_SUCCESS,
        payload: {
          data: {
            fileName: inputFileName,
            ...uploadedContent,
          },
        },
      });
      setIsReportUploading(false);
    }
  }, [uploadedContent, inputFileName, isReportUploading]);

  const showMode = currentMode => {
    setShow(true);
    setMode(currentMode);
  };

  const showDatepicker = () => {
    showMode('date');
  };

  const onChange = selectedDate => {
    const currentDate = selectedDate || date;
    setShow(false);
    setUpdateDate(new Date(currentDate));
  };

  const pickDocument = async () => {
    try {
      setTimeout(() => {
        setIsReportUploading(true);
      }, 2000);
      const response = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: false,
        copyToCacheDirectory: true,
      });
      setIsFileError({ status: false, error: '' });

      const result = response.assets[0];

      if (10 > Number(result.size / 1048576)) {
        if (!response.canceled) {
          setIsReportUploading(true);

          const uploadedFileType = result.name.split('.').pop().trim();

          if (videoFileTypes.includes(uploadedFileType)) {
            setIsFileError({
              status: true,
              error: 'Format Not Supported',
            });
          } else {
            await startUploading(result, bucketLocation);
          }
        } else {
          setIsReportUploading(false);
        }
      } else {
        if (result.type !== 'cancel') {
          setIsFileError({
            status: true,
            error: 'Maximum upload file size: 10mb',
          });
        }
      }
    } catch (error) {
      setIsReportUploading(false);
    }
  };

  const startUploading = async (selectedFile, bucketLocation) => {
    if (selectedFile && bucketLocation?.filePath) {
      const file = await fetch(selectedFile?.uri);

      const fileBlob = await file.blob();

      const newFile = new File([fileBlob], `${selectedFile?.name}`);
      setInputFileName(selectedFile?.name || '');
      setFileObject(newFile);
    }
  };

  const { year, month, date, dateString } = dateTimeConversion(updateDate);
  const [onClickSave, setOnClickSave] = useState(false);
  return (
    <>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView>
          <View style={PlayerInfoUpdateFormStyle.container}>
            <KeyboardAvoidingView
              contentContainerStyle={PlayerInfoUpdateFormStyle.scrollView}
              behavior="padding"
            >
              <View style={PlayerInfoUpdateFormStyle.content}>
                <View style={PlayerInfoUpdateFormStyle.box}>
                  <Text style={PlayerInfoUpdateFormStyle.dateLabel}>Date</Text>
                  <View style={PlayerInfoUpdateFormStyle.selectedDateWrapper}>
                    <TouchableOpacity onPress={() => showDatepicker()}>
                      <Text style={PlayerInfoUpdateFormStyle.selectedDate}>
                        {date}/{month}/{year}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={PlayerInfoUpdateFormStyle.box2}>
                  <Text style={PlayerInfoUpdateFormStyle.dateLabel}>
                    Message
                  </Text>
                  <View style={PlayerInfoUpdateFormStyle.selectedDateWrapper}>
                    <TextInput
                      multiline={true}
                      numberOfLines={2}
                      style={PlayerInfoUpdateFormStyle.messageText}
                      onChangeText={text => setUpdateMessage(text)}
                      value={updateMessage}
                      placeholder={'Type Here'}
                      placeholderTextColor="#595959"
                      disableFullscreenUI={true}
                      returnKeyType={'go'}
                      blurOnSubmit={false}
                    />

                    {isError && (
                      <Text style={PlayerInfoUpdateFormStyle.error}>
                        Please enter your message
                      </Text>
                    )}
                  </View>
                </View>
                <View style={PlayerInfoUpdateFormStyle.reportContainer}>
                  {isUploading ? (
                    <ActivityIndicator color="green" />
                  ) : (
                    <>
                      <TouchableOpacity onPress={() => pickDocument()}>
                        <View>
                          <View
                            style={
                              PlayerInfoUpdateFormStyle.uploadIconImageWrapper
                            }
                          >
                            <Image
                              style={PlayerInfoUpdateFormStyle.uploadIconImage}
                              source={require('../../../assets/icons/medical-upload-icon.png')}
                            />
                            <View
                              style={
                                PlayerInfoUpdateFormStyle.uploadTextWrapper
                              }
                            >
                              <Text style={PlayerInfoUpdateFormStyle.btnText}>
                                Upload Report
                              </Text>
                              {isFileError.status && (
                                <Text style={PlayerInfoUpdateFormStyle.error}>
                                  {isFileError.error}
                                </Text>
                              )}
                            </View>
                          </View>
                        </View>
                      </TouchableOpacity>

                      {playerUploadedReport?.length > 0 &&
                        playerUploadedReport.map(({ fileName }, index) => (
                          <View key={index}>
                            <View
                              style={PlayerInfoUpdateFormStyle.uploadedWrapper}
                            >
                              <TouchableOpacity
                                onPress={() => deleteReportAction(index)}
                                style={PlayerInfoUpdateFormStyle.close}
                              >
                                <Ionicons
                                  name="close"
                                  color="white"
                                  style={
                                    PlayerInfoUpdateFormStyle.uploadWrapperClose
                                  }
                                />
                              </TouchableOpacity>
                              <Image
                                style={
                                  PlayerInfoUpdateFormStyle.uploadIconImage
                                }
                                source={require('../../../assets/icons/medical-icon.png')}
                              />
                              <Text
                                style={PlayerInfoUpdateFormStyle.uploadIconText}
                              >
                                {fileName || 'Report'}
                              </Text>
                            </View>
                          </View>
                        ))}
                    </>
                  )}
                </View>
                <View style={PlayerInfoUpdateFormStyle.btn_wrapper}>
                  <View style={PlayerInfoUpdateFormStyle.btns}>
                    <TouchableWithoutFeedback
                      onPress={() => {
                        if (updateMessage == '') {
                          setIsError(true);
                        } else {
                          setOnClickSave(true);
                          action({
                            message: updateMessage,
                            createdDate: new Date(updateDate).toISOString(),
                            updateId: _id,
                            fileUploads: playerUploadedReport.length
                              ? playerUploadedReport
                              : null,
                          });
                          setIsError(false);
                          modalAction(false);
                        }
                      }}
                    >
                      <View style={PlayerInfoUpdateFormStyle.btn}>
                      {onClickSave || isReportUploading ? (
                          <ActivityIndicator color="#FFF" />
                        ) : (
                          <Text style={PlayerInfoUpdateFormStyle.btnText}>
                            Save
                          </Text>
                        )}
                      </View>
                    </TouchableWithoutFeedback>
                    <TouchableWithoutFeedback
                      onPress={() => modalAction(false)}
                    >
                      <View style={PlayerInfoUpdateFormStyle.btn2}>
                        <Text style={PlayerInfoUpdateFormStyle.btnText}>
                          Cancel
                        </Text>
                      </View>
                    </TouchableWithoutFeedback>
                  </View>
                </View>
              </View>
            </KeyboardAvoidingView>

            {show && (
              <View style={PlayerInfoUpdateFormStyle.datePickerWrapper}>
                <DateTimePickerModal
                  isVisible
                  mode={mode}
                  date={updateDate}
                  // style={addUserStyle.datePicker}
                  onConfirm={onChange}
                  is24Hour={true}
                  // display="spinner"
                  onCancel={() => setShow(false)}
                  modalStyleIOS={PlayerInfoUpdateFormStyle.datePickerSelector}
                  pickerContainerStyleIOS={
                    PlayerInfoUpdateFormStyle.datePickerWrapper
                  }
                  maximumDate={new Date()}
                  isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
                />
              </View>
            )}
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </>
  );
};

export default PlayerInfoUpdateFormReport;
