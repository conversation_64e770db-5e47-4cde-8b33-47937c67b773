import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ScrollView,
  Share,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  Platform,
  Linking
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  S3_BUCKET_LOCATION
} from '../../constants/constants';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';
import useGeneratedFileUrl from '../../hooks/useGeneratedFileUrl';
import useStyles from '../../hooks/useStyles';
import customPlayerInfoUpdateFormStyle from './PlayerInfoUpdateFormStyle';
import useFileUploadPromise from '../../hooks/useFileUploadPromise';
import useS3bucketLocationPromise from '../../hooks/useS3bucketLocationPromise';
import { dateTimeConversion } from '../../helpers';

const TrainingFormReport = ({
  selectedEvent,
  onCloseModal,
  onSaveDocuments,
  isPlayerOrParent,
}) => {
  const PlayerInfoUpdateFormStyle = useStyles(customPlayerInfoUpdateFormStyle);

  const { uploadFile } = useFileUploadPromise();
  const { getBucketLocation } = useS3bucketLocationPromise();
  const [s3FileObject, documentUrl, isDownloading] = useGeneratedFileUrl();

  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [activeDocumentIndex, setActiveDocumentIndex] = useState(-1);
  const [selectedDocument, setSelectedDocument] = useState();
  const [uploadedDocuments, setUploadedDocuments] = useState(selectedEvent?.fileUploads || []);
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: 'Maximum upload file size: 500KB',
  });

  const today = Number(
    new Date(
      new Date().getUTCFullYear(),
      new Date().getUTCMonth(),
      new Date().getUTCDate()
    )
  );
  const eventStartDateTimestamp =
    (selectedEvent?.startTime && Number(new Date(selectedEvent?.startTime))) ||
    0;
  const isSessionUploadDisable =
    eventStartDateTimestamp >= today ? false : true;

  const generateFileName = selectedFileName => {
    const sentDate = new Date();
    const {
      monthNumberString,
      dateNumberString,
      hours24String,
      minutesString,
      year,
    } = dateTimeConversion(sentDate);

    const fileNameExtension = selectedFileName.split('.')[1];

    return `Koach_${year}_${monthNumberString}_${dateNumberString}_at_${hours24String}_${minutesString}.${fileNameExtension}`;
  };

  const handleDocumentUpload = async () => {
    try {
      const response = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: false,
        copyToCacheDirectory: true,
      });

      if (response.canceled) return;

      setIsFileError({
        status: false,
        error: '',
      });


      const document = response.assets[0];
      if (10 > Number(document.size / 1048576)) {
        setIsUploading(true);
        try {
          const selectedFileName = generateFileName(document.name);

          const picture = await fetch(document.uri);
          const documentBlob = await picture.blob();
          const file = new File([documentBlob], `${selectedFileName}`);

          const bucketLocation = await getBucketLocation({
            path: S3_BUCKET_LOCATION.trainingDocuments,
            service: USER_MANAGEMENT_SERVICE,
          });

          const uploadedFile = await uploadFile({
            file: file,
            path: bucketLocation.filePath,
            bucket: bucketLocation.bucketName || '',
          });

          const newDocument = {
            fileName: selectedFileName || "",
            ...uploadedFile,
          };

          setUploadedDocuments(prev => [...prev, newDocument]);
        } catch (error) {
          console.error('Document upload failed:', error);
        } finally {
          setIsUploading(false);
        }

      } else {
          setIsFileError({
            status: true,
            error: 'Maximum upload file size: 10mb',
          });
      }

    } catch (error) { 
      console.log(error);
    }
  };

  const handleDocumentDownload = async url => {

    if (!selectedDocument) return;

    try {
      const { fileName = 'document.pdf' } = selectedDocument;
      const sanitizedFileName = fileName.replace(/ /g, '');
      const downloadPath = FileSystem.documentDirectory + sanitizedFileName;

      const { uri } = await FileSystem.downloadAsync(url, downloadPath);

      await Share.share({
        ...(Platform.select({
          ios: { url: uri },
          android: { message: uri },
        })),
      });
    } catch (error) {
      console.error('Document download failed:', error);
    }
  };

  useEffect(() => {
    if (documentUrl) {
      Linking.openURL(documentUrl);
    }
  }, [documentUrl]);

  const handleDocumentDelete = (index) => {
    setUploadedDocuments(prev => prev.filter((_, i) => i !== index));
  };
  return (
    <>
      <View style={PlayerInfoUpdateFormStyle.container}>
        <KeyboardAwareScrollView
          contentContainerStyle={PlayerInfoUpdateFormStyle.scrollView}
        >
          <View style={PlayerInfoUpdateFormStyle.trainingDocumentsContent}>
            <View style={PlayerInfoUpdateFormStyle.trainingReportContainer}>
              <TouchableOpacity
                style={[
                  PlayerInfoUpdateFormStyle.trainingDocumentModalClose,
                  {
                    zIndex: 1000,
                  },
                ]}
                onPress={() => onCloseModal(false)}
              >
                <Ionicons
                  name="close"
                  color="white"
                  style={{ fontSize: 25 }}
                />
              </TouchableOpacity>

              <Text style={PlayerInfoUpdateFormStyle.title}>Session Plans</Text>


              {!isPlayerOrParent && (
                <TouchableOpacity disabled={isSessionUploadDisable} onPress={handleDocumentUpload}>
                  <View
                    style={
                      isSessionUploadDisable
                        ? PlayerInfoUpdateFormStyle.trainingReportDisabled
                        : PlayerInfoUpdateFormStyle.trainingReport
                    }
                  >
                    <Image
                      style={PlayerInfoUpdateFormStyle.iconImage}
                      source={require('../../../assets/icons/medical-upload-icon.png')}
                    />
                    <View style={{ marginLeft: 15 }}>
                      <Text style={PlayerInfoUpdateFormStyle.btnText}>
                        Upload Session Plan
                      </Text>
                      {isFileError.status && (
                        <Text style={PlayerInfoUpdateFormStyle.error}>
                          {isFileError.error}
                        </Text>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              )}
              {isUploading ? (
                <ActivityIndicator color="green" />
              ) : null}
              <ScrollView style={PlayerInfoUpdateFormStyle.scroll}>
                <View style={PlayerInfoUpdateFormStyle.reports}>
                  {uploadedDocuments?.length > 0 &&
                    uploadedDocuments.map((document, index) => (
                      <View key={index}>
                        <View
                          style={[
                            PlayerInfoUpdateFormStyle.trainingReport,
                            {
                              marginLeft: 0,
                            },
                          ]}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              s3FileObject(document);
                              setSelectedDocument(document);
                              setActiveDocumentIndex(index);
                            }}
                            style={{
                              display: 'flex',
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}
                          >
                            <Image
                              style={
                                PlayerInfoUpdateFormStyle.uploadIconImage
                              }
                              source={require('../../../assets/icons/medical-icon.png')}
                            />
                            <Text
                              style={{
                                ...PlayerInfoUpdateFormStyle.btnText,
                                paddingRight: 90,
                                paddingLeft: 10,
                              }}
                              numberOfLines={1}
                              ellipsizeMode="middle"
                            >
                              {document?.fileName || 'Document'}
                            </Text>
                          </TouchableOpacity>
                          {!isPlayerOrParent || isSessionUploadDisable ? (
                            <TouchableOpacity
                              onPress={() => handleDocumentDelete(index)}
                              style={PlayerInfoUpdateFormStyle.trainingClose}
                            >
                              <Ionicons
                                name="close"
                                color="white"
                                style={{ fontSize: 18 }}
                              />
                            </TouchableOpacity>
                          ) : (
                            <TouchableOpacity
                              style={PlayerInfoUpdateFormStyle.download}
                            >
                              {activeDocumentIndex === index &&
                                isDownloading ? (
                                <ActivityIndicator color="green" />
                              ) : (
                                <Ionicons
                                  name="download"
                                  size={18}
                                  color="white"
                                />
                              )}
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    ))}
                </View>
              </ScrollView>

              {!isPlayerOrParent && (
                <View style={PlayerInfoUpdateFormStyle.trainginBtnWrappers}>
                  <View style={PlayerInfoUpdateFormStyle.btns}>
                    <TouchableWithoutFeedback
                      disabled={isSessionUploadDisable}
                      onPress={() => {
                        setIsSaving(true)
                        onSaveDocuments(uploadedDocuments?.length ? uploadedDocuments : []);
                      }}
                    >
                      <View
                        style={
                          isSessionUploadDisable
                            ? PlayerInfoUpdateFormStyle.btnDisabled
                            : PlayerInfoUpdateFormStyle.btn
                        }
                      >
                        {isSaving ? (
                          <ActivityIndicator color="#FFF" />
                        ) : (
                          <Text style={PlayerInfoUpdateFormStyle.btnText}>
                            Save
                          </Text>
                        )}
                      </View>
                    </TouchableWithoutFeedback>
                    <TouchableWithoutFeedback
                      onPress={() => onCloseModal(false)}
                    >
                      <View style={PlayerInfoUpdateFormStyle.btn2}>
                        <Text style={PlayerInfoUpdateFormStyle.btnText}>
                          Cancel
                        </Text>
                      </View>
                    </TouchableWithoutFeedback>
                  </View>
                </View>
              )}
            </View>
          </View>
        </KeyboardAwareScrollView>
      </View>
    </>
  );
};
export default TrainingFormReport;
