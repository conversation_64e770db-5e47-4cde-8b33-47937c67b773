import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const screenWrapperStyles = colors => ({
  statusBar: {
    flex: 1,
    backgroundColor: 'white',
  },
  screenWrapper: {
    position: 'relative',
  },
  screenWrapperHeader: {
    position: 'absolute',
    zIndex: 11,
    width: '100%',
  },
  screenWrapperContent: {
    position: 'absolute',
    zIndex: 10,
    width: '100%',
    height: '100%',
  },
  homepageBackground: {
    width: wp('100%'),
    height: '100%',
    resizeMode: 'stretch',
    top: 0,
    left: 0,
    zIndex: 0,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: colors.darkBlue,
    opacity: 0.96,
    zIndex: 2,
  },
});
export default screenWrapperStyles;
