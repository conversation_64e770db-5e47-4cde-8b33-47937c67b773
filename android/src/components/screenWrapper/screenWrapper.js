import { useNavigationState, useRoute } from '@react-navigation/native';
import React, { useEffect, useMemo, useState } from 'react';
import { Image, StatusBar, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import homepage__background from '../../../assets/backgrounds/homepage__background.png';
import FooterControllers from '../../components/MobileFooterController/mobileFooterController';
import { isTabDevice } from '../../config/appConfig';
import useStyles from '../../hooks/useStyles';
import { SET_CURRENT_ROUTE } from '../../store/actionTypes/common/commonActionTypes';
import ActivitySpinner from '..//ActivitySpinner/ActivitySpinner';
import ModalWrapperLoader from '..//modal/ModalWrapper/ModalWrapperLoader';
import Header from '../header/header';
import customScreenWrapperStyles from './screenWrapperStyles';

export default function ScreenWrapper({ children, isLoading = true }) {
  const dispatch = useDispatch();
  const [isLoaderVisible, setIsLoaderVisible] = useState(true);
  const screenWrapperStyles = useStyles(customScreenWrapperStyles);
  const { allUsersPageNo, allUsersLoading, allTeamsPageNo, allTeamsLoading } =
    useSelector(state => state?.manageUsers);
  const { PlayerInfoDataLoading } = useSelector(state => state?.playerInfo);
  const { matchPlanSaveLoading, matchPlanLoading } = useSelector(
    state => state.matchPlan
  );
  const { teamDataLoading } = useSelector(state => state?.team);
  const { activitiesLoading } = useSelector(
    state => state?.matchLog
  );
  const { RsvpSaveAllLoading, calendarLoading } = useSelector(
    state => state?.planner
  );

  const { teamsLoading, teamsPageNo } = useSelector(
    state => state?.matchReport
  );

  const route = useRoute();
  const routes = useNavigationState(state => state.routes);
  const currentRoute = routes[routes.length - 1].name;

  useEffect(() => {
    if (currentRoute) {
      dispatch({
        type: SET_CURRENT_ROUTE,
        payload: { data: { currentRoute: currentRoute } },
      });
    }
  }, [JSON.stringify(currentRoute)]);

  const showLoader = useMemo(() => {
    if (
      allUsersLoading &&
      !allUsersPageNo &&
      allTeamsLoading &&
      !allTeamsPageNo
    ) {
      return true;
    }
    if (teamDataLoading && activitiesLoading) {
      return true;
    }

    if (teamDataLoading && !route?.name === 'landing') {
      return true;
    }

    if (teamsLoading && !teamsPageNo) {
      return true;
    }

    return false;
  }, [
    allUsersLoading,
    allUsersPageNo,
    allTeamsLoading,
    allTeamsPageNo,
    PlayerInfoDataLoading,
    teamDataLoading,
    activitiesLoading,
    matchPlanSaveLoading,
    matchPlanLoading,
    RsvpSaveAllLoading,
    calendarLoading,
    teamsLoading,
    teamsPageNo,
  ]);

  useEffect(() => {
    let timeoutId;
    if (showLoader) {
      setIsLoaderVisible(true);
      timeoutId = setTimeout(() => {
        setIsLoaderVisible(false);
      }, 5000);
    } else {
      setIsLoaderVisible(false);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [showLoader]);

  return (
    <>
      <View style={screenWrapperStyles.statusBar}>
        <StatusBar barStyle="light-content" />
      </View>
      <View style={screenWrapperStyles.screenWrapperHeader}>
        <Header />
      </View>
      <View style={screenWrapperStyles.screenWrapperContent}>
        <Image
          source={homepage__background}
          style={screenWrapperStyles.homepageBackground}
        />
        <View style={screenWrapperStyles.overlay} />
        {children}
        {showLoader && isLoaderVisible && isLoading && (
          <ModalWrapperLoader transparent>
            <ActivitySpinner isFullScreen={true} />
          </ModalWrapperLoader>
        )}
      </View>
      {!isTabDevice() && <FooterControllers />}
    </>
  );
}
