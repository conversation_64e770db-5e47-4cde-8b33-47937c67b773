import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const HeadStyle = colors => ({
  safeArea: {
    backgroundColor: colors.darkBlue,
  },
  container: {
    width: '100%',
  },
  img: isTabDevice()
    ? {
        width: wp('15%'),
        height: hp('8%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('30%'),
        height: hp('8%'),
        resizeMode: 'contain',
      },
  headerView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: hp('7%'),
        zIndex: 2,
        paddingLeft: '3%',
        paddingRight: '3%',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: hp('7%'),
        zIndex: 2,
        paddingLeft: '3%',
        paddingRight: '3%',
        // backgroundColor: colors.red,
      },
  leftView: isTabDevice()
    ? {
        width: wp('31%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        paddingTop: wp('1%'),
      }
    : {
        width: wp('10%'),
        height: hp('7%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
      },
  centerView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: wp('33%'),
        height: hp('7%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: wp('45%'),
        height: hp('7%'),
        marginLeft: wp('18%'),
      },
  rightView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'flex-end',
        width: wp('33%'),
        paddingRight: wp('3%'),
        paddingTop: wp('0.7%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        width: wp('25%'),
        paddingRight: wp('3%'),
        paddingTop: wp('0.7%'),
      },
  logoWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  logo: {
    width: wp('13%'),
    height: hp('5%'),
    resizeMode: 'contain',
  },
  profileNameView: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        height: wp('2%'),
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        minWidth: wp('7%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        marginRight: hp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        // height: hp('5%'),
        width: wp('12%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        marginRight: hp('1%'),
      },
  reportView: {
    backgroundColor: colors.darkBlue,
    borderRadius: wp('100%'),
    // height: hp('5%'),
    // width: wp('20%'),
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginRight: hp('1%'),
  },
  logView: {
    backgroundColor: colors.green,
    borderRadius: wp('100%'),
    // height: hp('5%'),
    // width: wp('20%'),
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginRight: hp('1%'),
  },
  profileName: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        position: 'relative',
        width: '100%',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        paddingLeft: hp('2%'),
        paddingRight: hp('2%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        fontSize: hp('2%'),
        position: 'relative',
      },
  ongoingMatch: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: hp('0.5%'),
        paddingRight: hp('2%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        fontSize: hp('2%'),
        position: 'relative',
      }
    : {
        color: colors.white,
        paddingLeft: wp('2%'),
        // paddingRight: hp('2%'),
        // paddingTop: hp('0.5%'),
        // paddingBottom: hp('0.5%'),
        fontSize: wp('2.5%'),
        position: 'relative',
      },
  profileImg: {
    height: hp('5%'),
    width: hp('5%'),
    borderRadius: wp('100%'),
  },
  parentText: {
    color: colors.white,
    marginRight: wp('0.5%'),
    fontSize: hp('2%'),
  },
  avatarParent: {
    height: hp('4%'),
    width: hp('4%'),
    borderRadius: wp('100%'),
    marginRight: wp('0.5%'),
  },
  teamChildrenAvatar: isTabDevice()
    ? {
        height: hp('8%'),
        width: hp('8%'),
        borderRadius: wp('100%'),
        marginRight: wp('0.5%'),
      }
    : {
        height: hp('6%'),
        width: hp('6%'),
        borderRadius: wp('100%'),
        marginRight: wp('0.5%'),
      },
  teamChildrenAvatarSelected: isTabDevice()
    ? {
        height: hp('8%'),
        width: hp('8%'),
        borderRadius: wp('100%'),
        borderWidth: 4,
        borderColor: colors.green,
        marginRight: wp('0.5%'),
      }
    : {
        height: hp('6%'),
        width: hp('6%'),
        borderRadius: wp('100%'),
        borderWidth: 4,
        borderColor: colors.green,
        marginRight: wp('0.5%'),
      },
  avatarWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
      },
  notificationWrapper: isTabDevice()
    ? {
        flexWrap: 'wrap',
        alignItems: 'center',
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
      }
    : {
        alignItems: 'center',
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
        marginBottom: wp('2%'),
      },
  notificationText: isTabDevice()
    ? {
        color: colors.white,
        marginRight: wp('0.5%'),
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        marginRight: wp('0.5%'),
        fontSize: hp('1.5%'),
        marginBottom: wp('3%'),
      },
  notificationText2: isTabDevice()
    ? {
        color: colors.white,
        marginLeft: wp('0.7%'),
        fontSize: hp('2%'),
        marginTop: wp('0.9%'),
      }
    : {
        color: colors.white,
        marginLeft: wp('1%'),
        fontSize: hp('1.5%'),
        marginTop: 16,
      },
  notificationToggle: isTabDevice()
    ? {
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'center',
      }
    : {
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'center',
      },
  buttonIcon: isTabDevice()
    ? {}
    : {
        width: wp('9%'),
        height: wp('9%'),
        resizeMode: 'contain',
      },
  logout: {
    backgroundColor: colors.tileBackground,
    width: wp('10%'),
    paddingTop: hp('1%'),
    paddingBottom: hp('1%'),
    position: 'absolute',
    top: hp('4%'),
    right: 0,
    borderRadius: hp('2%'),
    borderWidth: 1,
    borderColor: colors.white,
    justifyContent: 'center',
    flexDirection: 'column',
  },
  logoutSelectionBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.white,
  },
  changePassword: {
    paddingLeft: hp('1%'),
    paddingRight: hp('1%'),
    paddingBottom: hp('1%'),
    color: colors.white,
    fontSize: hp('2%'),
    backgroundColor: colors.Black,
  },
  logoutFeature: {
    paddingTop: hp('1%'),
    paddingLeft: hp('1%'),
    paddingRight: hp('1%'),
    color: colors.white,
    fontSize: hp('2%'),
  },
  backButton: isTabDevice()
    ? {
        marginTop: hp('1%'),
        fontSize: wp('3%'),
      }
    : {
        marginTop: hp('1%'),
        fontSize: wp('7%'),
      },
  matchPlanView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: hp('2%'),
        marginLeft: hp('2%'),
        height: '100%',
      }
    : {
        flexDirection: 'row',
        marginLeft: wp('2%'),
        alignItems: 'center',
        marginTop: hp('1%'),
        width: '100%',
        paddingRight: 5,
      },
  matchPlanViewButtons: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: hp('1%'),
        width: wp('7%'),
        height: wp('2%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        marginRight: hp('1%'),
      },
});
export default HeadStyle;
