import { Feather, Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Auth } from 'aws-amplify';
import React, { useEffect, useState } from 'react';
import { Image, SafeAreaView, Text, View, Platform } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { getCalendars } from 'expo-localization';
import { isTabDevice } from '../../config/appConfig';
import { ROUTE_PATH, userRoleType } from '../../constants/constants';
import {
  EVENT_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import { LOGOUT_SUCCESS } from '../../store/actionTypes/auth';
import {
  FETCH_HEADER_PARENT_INFO_FAILED,
  FETCH_HEADER_PARENT_INFO_REQUEST,
  FETCH_HEADER_PARENT_INFO_SUCCESS,
  SET_ON_SYNC_START,
  HEADER_LOGO_CLICKED,
} from '../../store/actionTypes/common/commonActionTypes';
import customHeaderStyle from './HeaderStyle';

import * as Localization from 'expo-localization';
import {
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native-gesture-handler';
import ProfileImageWrapper from '../../Container/ProfileImageWrapper/ProfileImageWrapper';
import useApi from '../../hooks/useApi';
import useCalender from '../../hooks/useCalender';
import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';
import {
  CREATE_NEW_MESSAGE_CLICK,
  IS_USER_IN_IAP_CHART,
  IS_USER_IN_MATCHES,
  IS_USER_IN_MATCH_LOG,
} from '../../store/actionTypes/common/commonActionTypes';
import { PLAYER_IAP_SET_SHOW_STATS_GRAPH } from '../../store/actionTypes/PlayerIAP/PlayerIapAction';
import ActivitySpinner from '../ActivitySpinner/ActivitySpinner';
import AppVersion from '../AppVersion/AppVersion';
import MessageNotification from '../MessageNotification/MessageNotification';
import ChangePasswordModal from '../modal/ChangePasswordModal/ChangePasswordModal';
import OngoingMatchesModal from '../modal/OngoingMatchesModal/OngoingMatchesModal';
import PlannerCalenderSyncModal from '../modal/PlannerCalenderSyncModal/PlannerCalenderSyncModal';
import ProfileImage from '../ProfileImage/ProfileImage';
import {
  FETCH_ONGOING_MATCH_FAIL,
  FETCH_ONGOING_MATCH_REQUEST,
  FETCH_ONGOING_MATCH_SUCCESS,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import ProfileModal from '../MobileFooterController/ProfileModal/ProfileModal';
import TermAndCondition from '../TermAndCondition/TermAndCondition';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { TermAndConditionVersion } from '../../constants/TermAndCondition';
import {
  TERM_AND_CONDITION_DATA_FETCH_SUCCESS,
  SET_TERM_AND_CONDITION,
} from '../../store/actionTypes/TermAndCondition/termAndCondtionAction';
import {
  ADD_USER_FAIL,
  ADD_USER_REQUEST,
  ADD_USER_SUCCESS,
  USER_TYPE_SUCCESS,
} from '../../store/actionTypes/User/User';
import { USER_TYPE_FAILED } from '../../store/actionTypes/userType/userType';
import useApiPromise from '../../hooks/useApiPromise';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';

const Header = () => {
  const [{ automaticSync }] = useCalender();
  const HeaderStyle = useStyles(customHeaderStyle);
  const theme = useColors();
  const navigation = useNavigation();
  const route = useRoute();
  const [showActionDropdown, setShowActionDropdown] = useState(false);
  const [showBackButton, setShowBackButton] = useState(false);
  const dispatch = useDispatch();
  const [isMatchPlanView, setIsMatchPlanView] = useState(false);
  const {
    sessionToken,
    userRole,
    userData,
    expoPushNotificationToken,
    selectedCalenderID,
    isCalenderSyncEnabled,
  } = useSelector(state => state?.auth);
  const {
    isUserInIAPChart,
    currentRoute,
    isPlannerModalOpened,
    isOnSync,
    updatedChildrenIds,
    isUserInMatches,
    isUserInMatchLog,
  } = useSelector(state => state?.common);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showOngoingMatchesModal, setShowOngoingMatchesModal] = useState(false);
  const {
    parentDetails,
    parentDetailsLoading,
    parentImageLoading,
    isCreateNewMessage,
    children,
  } = useSelector(state => state?.common);
  const { isOngoingMatchAvailable } = useSelector(state => state.matchLog);

  const endDate = new Date(new Date().setDate(new Date().getDate() + 14));
  const startDate = new Date(new Date().setDate(new Date().getDate() - 14));
  const tomorrow = new Date(new Date().setDate(new Date().getDate() + 1));
  const today = Number(
    new Date(
      new Date().getUTCFullYear(),
      new Date().getUTCMonth(),
      new Date().getUTCDate()
    )
  );

  const rangeEndDate = Number(
    new Date(
      endDate.getUTCFullYear(),
      endDate.getUTCMonth(),
      endDate.getUTCDate()
    )
  );
  const rangeStartDate = Number(
    new Date(
      startDate.getUTCFullYear(),
      startDate.getUTCMonth(),
      startDate.getUTCDate()
    )
  );

  const { selectedMatchDetails, matchPlan } = useSelector(
    state => state.matchPlan
  );
  const eventStartDateTimestamp =
    (selectedMatchDetails?.startTime &&
      Number(new Date(selectedMatchDetails?.startTime))) ||
    0;

  const isCoach =
    userRoleType.COACH === userData?.type ||
    userRoleType.HEAD_COACH === userData?.type;

  const isValidTimeStampForMatchLogForCoach =
    currentRoute == ROUTE_PATH.MATCH_PLAN
      ? eventStartDateTimestamp >= rangeStartDate &&
        eventStartDateTimestamp <= rangeEndDate
      : true;

  const isValidTimeStampForMatchLogForOthers =
    currentRoute == ROUTE_PATH.MATCH_PLAN
      ? eventStartDateTimestamp >= today &&
        eventStartDateTimestamp <= rangeEndDate
      : true;

  const isValidTimeStampForMatchLog = isCoach
    ? isValidTimeStampForMatchLogForCoach
    : isValidTimeStampForMatchLogForOthers;

  const matchLogBtnVisble = isCoach ? true : matchPlan?.visible || false;

  const [, , onRemoveNotificationTokenPromise] = useApi();
  const [fetchParent] = useApi();
  const [fetchOngoingMatchData] = useApi();
  const [onUpdateUser] = useApiPromise();

  const isPlayerLoggedIn = userRoleType.PLAYER === userData?.type;
  const isParentOrPlayer =
    userRole === userRoleType.PLAYER || userRole === userRoleType.PARENT;

  useEffect(() => {
    isPlayerLoggedIn &&
      userData &&
      fetchParent(
        `/api/v1/users?userIds=${userData?.id}&isFetchParent=true`,
        FETCH_HEADER_PARENT_INFO_REQUEST,
        FETCH_HEADER_PARENT_INFO_SUCCESS,
        FETCH_HEADER_PARENT_INFO_FAILED,
        null,
        '',
        'GET',
        null,
        USER_MANAGEMENT_SERVICE
      );
  }, [userData, isPlayerLoggedIn]);

  const logoutUser = async () => {
    try {
      await Auth.signOut();
      dispatch({
        type: LOGOUT_SUCCESS,
      });
      AsyncStorage.removeItem('TermAndConditionData');
    } catch (error) {
      console.log('logout error', error);
    }
  };

  const handleLogout = async () => {
    if (expoPushNotificationToken) {
      try {
        await onRemoveNotificationTokenPromise(
          'DELETE',
          USER_MANAGEMENT_SERVICE,
          `/api/v1/users/${userData.id}/expoTokens`,
          {
            token: expoPushNotificationToken,
            preferredTimeZone: getCalendars()[0]?.timeZone,
            platform: Platform.OS,
          }
        );
        await logoutUser();
      } catch (error) {
        console.log('error', error);
      }
    } else {
      await logoutUser();
    }
  };

  useEffect(() => {
    if (!sessionToken) {
      // navigation.navigate('Login');
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    }
  }, [sessionToken]);

  useEffect(() => {
    if (route) {
      if (route.name === 'landing') {
        setShowBackButton(false);
      } else {
        setShowBackButton(true);
      }

      if (
        route.name === 'MatchPlan' ||
        route.name === 'Matches' ||
        route.name === 'MatchLog'
      ) {
        setIsMatchPlanView(true);
      } else {
        setIsMatchPlanView(false);
      }
    }
  }, [route]);

  const navigateToLanding = () => {
    if (route && route.name != 'landing') {
      dispatch({ type: HEADER_LOGO_CLICKED, payload: true });
      navigation.reset({
        index: 0,
        routes: [{ name: 'landing' }],
      });
      setTimeout(() => {
        dispatch({ type: HEADER_LOGO_CLICKED, payload: false });
      }, 1000);
    }
  };

  const navigateToMatchLog = () => {
    // if (!isValidTimeStampForMatchLog) {
    //   return;
    // }

    if (userRole === userRoleType.PLAYER || userRole === userRoleType.PARENT) {
      navigation.navigate('PlayerMatchLog');
    } else {
      navigation.navigate('MatchLog');
    }
  };

  const showOngoingMatches = () => {
    setShowOngoingMatchesModal(true);
  };

  const setUserInIAPChart = payload => {
    dispatch({
      type: IS_USER_IN_IAP_CHART,
      payload: payload,
    });
  };

  const setUserInMatches = payload => {
    dispatch({
      type: IS_USER_IN_MATCHES,
      payload: payload,
    });
  };

  const setUserInMatchLog = payload => {
    dispatch({
      type: IS_USER_IN_MATCH_LOG,
      payload: payload,
    });
  };

  const setShowStatGraph = payload => {
    dispatch({
      type: PLAYER_IAP_SET_SHOW_STATS_GRAPH,
      payload: payload,
    });
  };

  const onBackButtonClicked = () => {
    // const state = navigation.getState()
    // const previousRoute = state.routes?.[state.routes.length -2]?.name;
    // if (previousRoute === 'Matches' || previousRoute === 'Training') {
    //   navigation.reset({
    //     index: 0,
    //     routes: [{ name: 'landing' }],
    //   });;
    //   return
    // }
    // if (previousRoute === 'Matches' || previousRoute === 'Training') {
    //   navigation.reset({
    //     index: 0,
    //     routes: [{ name: 'landing' }],
    //   });;

    //   return
    // }

    if (isUserInIAPChart) {
      setShowStatGraph(false);
      setUserInIAPChart(false);
    } else if (isCreateNewMessage) {
      dispatch({
        type: CREATE_NEW_MESSAGE_CLICK,
        payload: { data: false },
      });
    } else if (isUserInMatches & !isTabDevice()) {
      setUserInMatches(false);
      setUserInMatchLog(false);
      if (route && route.name === 'MatchPlan') {
        setUserInMatches(true);
        !isTabDevice() && navigation.pop();
      }
    } else if (isUserInMatchLog) {
      navigation.pop();
      setUserInMatches(true);
    } else {
      navigation.pop();
    }
  };

  useEffect(() => {
    if (
      userData &&
      !isOnSync &&
      selectedCalenderID &&
      isCalenderSyncEnabled &&
      isTabDevice()
    ) {
      automaticSync();
    }
    dispatch({
      type: SET_ON_SYNC_START,
    });
  }, [
    isOnSync,
    userData,
    selectedCalenderID,
    isCalenderSyncEnabled,
    updatedChildrenIds,
  ]);

  useEffect(() => {
    if (sessionToken && userData && isCoach) {
      const loadOngoingMatches = () =>
        fetchOngoingMatchData(
          `/api/v1/matches?ongoing=true${
            userData.type === userRoleType.COACH
              ? `&participantId=${userData.id}`
              : ''
          }&size=20&page=1&startedBeforeDate=${new Date(
            tomorrow
          ).toISOString()}`,
          FETCH_ONGOING_MATCH_REQUEST,
          FETCH_ONGOING_MATCH_SUCCESS,
          FETCH_ONGOING_MATCH_FAIL,
          null,
          '',
          'GET',
          null,
          EVENT_SERVICE
        );
      loadOngoingMatches();
      setInterval(() => loadOngoingMatches(), 60000);
    }
  }, [sessionToken, userData]);

  const [isEnabled, setIsEnabled] = useState(false);
  const [isTermAndConditionEnabled, setTermAndConditionEnabled] =
    useState(false);
  const [isTermAndConditionAccepted, setIsTermAndConditionAccepted] =
    useState(false);
  const [isExpired, setIsExpired] = useState(false);

  useState(false);
  const [isExistingUser, setIsExistingUser] = useState(false);
  const [userId, setUserId] = useState(null);

  const { termAndConditionData, setTermAndCondition } = useSelector(
    state => state.TermAndCondition
  );

  const processTermAndConditionData = (termAndConditionData, newDate) => {
    if (
      !termAndConditionData ||
      termAndConditionData.version !== TermAndConditionVersion
    ) {
      setTermAndConditionEnabled(true);
    } else if (termAndConditionData.termActionType !== 'ACCEPTED') {
      // if (newDate !== termAndConditionData.expireDate) {
      //   if (
      //     newDate >
      //     new Date(termAndConditionData.actionDate).toLocaleDateString()
      //   ) {
      //     dispatch({ type: SET_TERM_AND_CONDITION, payload: true });
      //   }
      // } else {
      //   setIsExpired(true);
      //   dispatch({ type: SET_TERM_AND_CONDITION, payload: true });
      // }
    } else {
    }
  };

  const handleChangeProfilePic = async uri => {
    await onUpdateUser(
      '/api/v1/users',
      ADD_USER_REQUEST,
      ADD_USER_SUCCESS,
      ADD_USER_FAIL,
      { ...userData, profileImage: uri },
      '',
      'PUT',
      null,
      USER_MANAGEMENT_SERVICE
    );

    await onUpdateUser(
      `/api/v1/users?email=${userData.emailId}`,
      '_',
      USER_TYPE_SUCCESS,
      USER_TYPE_FAILED,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  const retrieveData = async () => {
    try {
      const value = await AsyncStorage.getItem('TermAndConditionData');
      const newData = JSON.parse(value);

      if (newData?.userType === 'ExistingUser') {
        setIsExistingUser(true);
      } else {
        if (value !== null) {
          const newDate = new Date().toLocaleDateString();
          termAndConditionData.termActionType === 'ACCEPTED' &&
          termAndConditionData.version === TermAndConditionVersion
            ? setIsTermAndConditionAccepted(true)
            : (processTermAndConditionData(termAndConditionData, newDate),
              setIsTermAndConditionAccepted(false));
        } else {
        }
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  };

  useEffect(() => {
    retrieveData();
  }, [route, termAndConditionData]);

  useEffect(() => {
    setTermAndConditionEnabled(setTermAndCondition);
  }, [setTermAndCondition]);

  useEffect(() => {
    setUserId(userData?.id);
  }, [userData]);

  return (
    <SafeAreaView style={HeaderStyle.safeArea}>
      {isTermAndConditionEnabled && (
        <TermAndCondition
          isTermAndConditionAccepted={isTermAndConditionAccepted}
          isExpired={isExpired}
          logOut={logoutUser}
          userData={userData}
          userId={userId}
          parentDetails={parentDetails}
          setTermAndConditionEnabled={setTermAndConditionEnabled}
        />
      )}
      <View style={HeaderStyle.container}>
        <View style={HeaderStyle.headerView}>
          <View style={HeaderStyle.leftView}>
            <View style={HeaderStyle.logoWrapper}>
              {showBackButton && (
                <TouchableOpacity onPress={() => onBackButtonClicked()}>
                  <Ionicons
                    name="arrow-back"
                    size={24}
                    color="white"
                    style={HeaderStyle.backButton}
                  />
                </TouchableOpacity>
              )}
            </View>
            {isMatchPlanView && matchLogBtnVisble && isTabDevice() && (
              <View style={HeaderStyle.matchPlanView}>
                <View
                  style={{
                    ...HeaderStyle.matchPlanViewButtons,
                    opacity: !isValidTimeStampForMatchLog ? 0.5 : 1,
                  }}
                >
                  <TouchableOpacity
                    disabled={!isValidTimeStampForMatchLog}
                    onPress={() => navigateToMatchLog()}
                  >
                    <Text style={HeaderStyle.profileName}>Match Log</Text>
                  </TouchableOpacity>
                </View>
                {isCoach && (
                  <View style={HeaderStyle.matchPlanViewButtons}>
                    <TouchableOpacity
                      onPress={() => navigation.navigate('MatchReport')}
                    >
                      <Text style={HeaderStyle.profileName}>Reports</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}
            {isOngoingMatchAvailable && !isParentOrPlayer && (
              <TouchableOpacity
                onPress={showOngoingMatches}
                style={HeaderStyle.matchPlanView}
              >
                <Feather name="radio" size={24} color="#36d982" />
                <Text style={HeaderStyle.ongoingMatch}>Ongoing Match</Text>
              </TouchableOpacity>
            )}
          </View>
          <View style={HeaderStyle.centerView}>
            <TouchableWithoutFeedback onPress={() => navigateToLanding()}>
              {isTabDevice() ? (
                <Image
                  style={HeaderStyle.img}
                  source={
                    theme?.homeButtonLogo
                      ? { uri: theme?.homeButtonLogo }
                      : require('../../../assets/buttons/homeButton.png')
                  }
                />
              ) : (
                <Image
                  style={HeaderStyle.img}
                  source={
                    theme?.mobileHoemButtonLogo
                      ? { uri: theme?.mobileHoemButtonLogo }
                      : require('../../../assets/buttons/mobile-homeButton.png')
                  }
                />
              )}
            </TouchableWithoutFeedback>
          </View>
          {isTabDevice() ? (
            <View style={HeaderStyle.rightView}>
              {currentRoute == ROUTE_PATH.PLANNNER_SCREEN &&
                !isPlannerModalOpened && <PlannerCalenderSyncModal />}
              <View style={HeaderStyle.profileNameView}>
                <TouchableOpacity onPress={() => setShowActionDropdown(true)}>
                  <Text style={HeaderStyle.profileName} numberOfLines={1}>
                    {userData?.firstName}
                  </Text>
                </TouchableOpacity>
                {showActionDropdown && (
                  <ProfileModal
                    handleLogout={handleLogout}
                    closeModal={() => setShowActionDropdown(false)}
                    parentDetails={parentDetails}
                    loading={parentDetailsLoading}
                    isPlayerLoggedIn={isPlayerLoggedIn}
                    openChangePassword={() => {
                      setShowActionDropdown(false);
                      setTimeout(() => {
                        setShowChangePasswordModal(true);
                      }, 1); //timeout is needed for iOS. Otherwise 2nd modal won't open
                    }}
                    setIsEnabled={setIsEnabled}
                    isEnabled={isEnabled}
                    setTermAndConditionEnabled={setTermAndConditionEnabled}
                    isExistingUser={isExistingUser}
                    handleChangeProfilePic={handleChangeProfilePic}
                  />
                )}
              </View>
              <TouchableOpacity onPress={() => setShowActionDropdown(true)}>
                <ProfileImage
                  imageStyles={HeaderStyle.profileImg}
                  profileImageUrl={userData?.profileImageUrl}
                />
              </TouchableOpacity>
            </View>
          ) : (
            <View style={HeaderStyle.rightView}>
              {isMatchPlanView && !isTabDevice() && matchLogBtnVisble && (
                <View
                  style={[
                    HeaderStyle.matchPlanView,
                    !isCoach && { width: wp('7%') },
                  ]}
                >
                  <View
                    style={{
                      ...HeaderStyle.matchPlanViewButtons,
                      opacity: !isValidTimeStampForMatchLog ? 0.5 : 1,
                    }}
                  >
                    <TouchableOpacity
                      disabled={!isValidTimeStampForMatchLog}
                      onPress={() => {
                        navigateToMatchLog(),
                          setUserInMatches(false),
                          setUserInMatchLog(true);
                      }}
                    >
                      <Image
                        style={HeaderStyle.buttonIcon}
                        source={require('../../../assets/icons/mobileMatchLogIcon.png')}
                      />
                    </TouchableOpacity>
                  </View>
                  {isCoach && (
                    <View style={HeaderStyle.matchPlanViewButtons}>
                      <TouchableOpacity
                        onPress={() => {
                          navigation.navigate('MatchReport'),
                            setUserInMatches(false),
                            setUserInMatchLog(true);
                        }}
                      >
                        <Image
                          style={HeaderStyle.buttonIcon}
                          source={require('../../../assets/icons/mobileReportsIcon.png')}
                        />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
              )}
              {currentRoute == ROUTE_PATH.PLANNNER_SCREEN &&
                !isPlannerModalOpened && <PlannerCalenderSyncModal />}
            </View>
          )}
        </View>
        <ChangePasswordModal
          showModal={showChangePasswordModal}
          closeModal={() => setShowChangePasswordModal(false)}
        />
        <OngoingMatchesModal
          showModal={showOngoingMatchesModal}
          closeModal={() => setShowOngoingMatchesModal(false)}
        />
      </View>
    </SafeAreaView>
  );
};

export default Header;
