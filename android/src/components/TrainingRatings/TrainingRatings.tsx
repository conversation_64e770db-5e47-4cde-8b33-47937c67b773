import React, { useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { isTabDevice } from '../../config/appConfig';
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';
import useStyles from '../../hooks/useStyles';
import ModalWrapper from '../modal/ModalWrapper/ModalWrapper';
import RPEListStaticContent from './RPEListStaticContent';
import CustomTrainingRatingStyle from './TrainingRatingStyle';
import Entypo from '@expo/vector-icons/Entypo';
import { Ionicons } from '@expo/vector-icons';
import DeleteModal from '../modal/DeleteModal/DeleteModal';

const TrainingRatings = () => {
  const TrainingRatingStyle = useStyles(CustomTrainingRatingStyle);
  const { RPEList, handleRPERating, playerRPEResponse, selectedEventId, handelCloseTrainingRating } = useTrainingHook({
    isInitalComponent: false,
  });


  const initialRPEId = playerRPEResponse[selectedEventId || ""];
  const [RPEId, setRPEId] = useState<string | undefined>(initialRPEId);
  const [isShowInfo, setIsShowInfo] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [pendingRPEId, setPendingRPEId] = useState<string | undefined>(undefined);
  
  const handleRPESelection = (newRPEId: string) => {
    // Case 1: Clicking the same button that's currently selected (deselection)
    if (RPEId === newRPEId) {
      if (initialRPEId === newRPEId) {
        // If trying to deselect the initial value, show confirmation
        setPendingRPEId(undefined);
        setShowDeleteModal(true);
      } else {
        // If deselecting a non-initial value, just deselect
        setRPEId(undefined);
      }
      return;
    }

    // Case 2: Selecting a new value
    if (initialRPEId) {
      // If there's an initial value, show confirmation before changing
      setPendingRPEId(newRPEId);
      setShowDeleteModal(true);
    } else {
      // If no initial value, just set the new value
      setRPEId(newRPEId);
    }
  };

  const onCancel = () => {
    setShowDeleteModal(false);
    setPendingRPEId(undefined);
  };

  const onSubmit = () => {
    setRPEId(pendingRPEId);
    setShowDeleteModal(false);
    setPendingRPEId(undefined);
  };
  const ratingButton = ({ item }: any) => {
    const { labelColor, textColor, _id, label, value } = item;

    let computedStyle = {
      backgroundColor: labelColor,
    };
    
    let computedTextStyle  = { 
      color: textColor,
    }

    if (RPEId) {
      computedStyle = {
        backgroundColor: RPEId === _id ? labelColor : '#0A1528'
      };
      computedTextStyle = {
        color: RPEId === _id ? textColor : '#FFF'
      };
    }

    return (
      <TouchableOpacity
        onPress={() => handleRPESelection(_id)}
        style={[TrainingRatingStyle.buttonContainer, computedStyle]}
      >
        <Text style={[TrainingRatingStyle.numberText, computedTextStyle]}>{value}</Text>
        <Text style={[TrainingRatingStyle.buttonText, computedTextStyle]}>{label}</Text>
      </TouchableOpacity>
    );
  };

  const renderRatingConent = () => (
    <View style={{ ...TrainingRatingStyle.modalView }}>
      <View style={TrainingRatingStyle.modalTitleContainer}>

        <View>
          <Text style={TrainingRatingStyle.header}>Rate of perceived exertion</Text>
          <Text style={TrainingRatingStyle.header}>Submit Your RPE Score!</Text>
        </View>
        <TouchableOpacity     

          onPress={() => handelCloseTrainingRating()}
        >
          <Ionicons name="close" style={{ ...TrainingRatingStyle.closeButton, fontSize: 25, color: 'white' }} />
        </TouchableOpacity>
      </View>
      <TouchableOpacity onPress={() => setIsShowInfo(true)} style={TrainingRatingStyle.infoButton}>
        <Text style={TrainingRatingStyle.infoButtonText}>View info</Text>
        <Entypo name="info-with-circle" size={isTabDevice() ? 20 : 17} color="white" />
      </TouchableOpacity>
      <FlatList
        data={RPEList}
        renderItem={ratingButton}
        contentContainerStyle={{
          ...TrainingRatingStyle.flatListContainerContent,
        }}
        keyExtractor={item => item._id.toString()}
        numColumns={isTabDevice() ? 3 : 1}
        scrollEnabled={isTabDevice() ? false : true}
      />
      <TouchableOpacity
        style={TrainingRatingStyle.confirmButton}
        onPress={() => {
          handleRPERating(RPEId);
        }}
      >
        <Text style={TrainingRatingStyle.infoButtonText}>Confirm</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <ModalWrapper transparent>
        <View style={TrainingRatingStyle.centeredView}>
          <View style={TrainingRatingStyle.overlay}></View>
          {!isShowInfo ? (
            renderRatingConent()
          ) : (
            <RPEListStaticContent setIsShowIno={setIsShowInfo} />
          )}
        </View>
        {showDeleteModal && (
          <DeleteModal
            subMessage=""
            loading={false}
            isTeamModal={false}
            errorMessage=""
            submitAction={onSubmit}
            cancelAction={onCancel}
            message="Are you sure you want to change the rating?"
          />
      )}
      </ModalWrapper>
    </>
  );
};

export default TrainingRatings;
