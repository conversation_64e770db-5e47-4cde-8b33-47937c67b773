import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPNumberInputStyle = colors => ({
  plusMinusButton: isTabDevice()
    ? {
        height: wp('0.1%'),
        width: wp('2.5%'),
        borderRadius: hp('20%'),
      }
    : {
        height: wp('9%'),
        width: wp('6%'),
        marginTop: wp('3%'),
      },
  dataContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      },
  plusMinusButtonText: isTabDevice()
    ? {
        color: colors.white,
        textAlign: 'center',
        fontSize: wp('2%'),
        borderRadius: wp('2%'),
        borderRadius: hp('20%'),
        backgroundColor: colors.veryDarkBlue,
      }
    : {
        color: colors.white,
        textAlign: 'center',
        fontSize: wp('4%'),
        borderRadius: wp('4%'),
        backgroundColor: colors.veryDarkBlue,
      },
  editTextWrapper: isTabDevice()
    ? {
        backgroundColor: colors.veryDarkBlue,
        borderRadius: wp('1%'),
        width: wp('6%'),
        height: wp('3%'),
        marginLeft: wp('1%'),
        marginRight: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.veryDarkBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('7%'),
        margin: wp('1.5%'),
        // marginRight: wp('1.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  editText: {},
  editTextInput: {
    textAlign: 'center',
  },
  plusMinusButton: isTabDevice()
    ? {
        width: hp('5%'),
        height: hp('5%'),
      }
    : {
        width: hp('3.5%'),
        height: hp('3.5%'),
      },
});

export default IAPNumberInputStyle;
