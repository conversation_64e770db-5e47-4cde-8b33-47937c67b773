import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import { dateTimeConversion } from '../../../helpers';
import customIAPCommentsStyles from './IAPCommentsStyles';
import useStyles from '../../../hooks/useStyles';

const IAPComments = ({ comments, loadMoreComments }) => {
  const IAPCommentsStyles = useStyles(customIAPCommentsStyles);
  const Comment = ({ item }) => {
    const timestamp = Date.parse(item.date);
    let dateStr;
    let day;
    if (!isNaN(timestamp)) {
      const { year, month, date, dateString } = dateTimeConversion(timestamp);
      dateStr = date + '/' + month + '/' + year;
      day = dateString;
    }
    return (
      <View style={IAPCommentsStyles.commentContainer}>
        <View style={IAPCommentsStyles.dateWrapper}>
          <Text style={IAPCommentsStyles.date}>{dateStr ? dateStr : ''}</Text>
          <Text style={IAPCommentsStyles.day}>{day ? day : ''}</Text>
        </View>
        <Text style={IAPCommentsStyles.comment}>{item.comment}</Text>
      </View>
    );
  };

  return (
    <FlatList
      data={comments}
      renderItem={Comment}
      keyExtractor={item => item._id}
      onEndReached={() => loadMoreComments()}
      onEndReachedThreshold={1}
      style={IAPCommentsStyles.list}
      nestedScrollEnabled
      // scrollEnabled={isTabDevice() ? true : false}
    />
  );
};

export default IAPComments;
