import React, { useEffect, useState } from 'react';
import { Text, View, TouchableOpacity, TextInput, Image } from 'react-native';
import customIAPNumberInputStyles from './IAPNumberInputStyles';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';

const IAPNumberInput = ({
  defaultValue,
  validate,
  getColour,
  saveValue,
  isEditMode,
  editModeValue, //10
  editModeValueUpdate,
}) => {
  const IAPNumberInputStyles = useStyles(customIAPNumberInputStyles);
  const [value, setValue] = useState(defaultValue);

  useEffect(() => {
    saveValue(value);
  }, [value]);

  useEffect(() => {
    isEditMode && setValue(editModeValue);
  }, [isEditMode, editModeValue]);

  const onStatTextChange = text => {
    let value = Number(text.replace(/[^0-9]/g, ''));
    return validate(value);
  };

  const changeValue = value => {
    setValue(currentVal => validate(Number(currentVal) + value));
  };

  const StatChangeButton = ({ isAdd, buttonText }) => {
    return (
      <TouchableOpacity onPress={() => changeValue(isAdd ? 1 : -1)}>
        <Image
          style={IAPNumberInputStyles.plusMinusButton}
          source={
            isAdd
              ? require('../../../../assets/buttons/plus.png')
              : require('../../../../assets/buttons/minus.png')
          }
        />
      </TouchableOpacity>
    );
  };

  return (
    <View style={IAPNumberInputStyles.dataContainer}>
      {isEditMode && <StatChangeButton isAdd={false} buttonText={'-'} />}
      {isEditMode ? (
        <View style={IAPNumberInputStyles.editTextWrapper}>
          <TextInput
            style={{
              ...IAPNumberInputStyles.editTextInput,
              color: getColour(value),
            }}
            placeholder={'0'}
            placeholderTextColor={'white'}
            selectionColor="green"
            keyboardType={'number-pad'}
            maxLength={2}
            value={value.toString()}
            onChangeText={text => setValue(onStatTextChange(text))}
          />
        </View>
      ) : (
        <View style={IAPNumberInputStyles.editTextWrapper}>
          {isTabDevice() ? (
            <Text
              style={IAPNumberInputStyles.editText}
              style={{
                color: getColour(defaultValue),
              }}
            >
              {defaultValue}
            </Text>
          ) : (
            <Text
              style={IAPNumberInputStyles.editText}
              style={{
                color: getColour(defaultValue),
              }}
            >
              {defaultValue}
            </Text>
          )}
        </View>
      )}
      {isEditMode && <StatChangeButton isAdd={true} buttonText={'+'} />}
    </View>
  );
};

export default IAPNumberInput;
