import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPCommentsStyles = colors => ({
  commentContainer: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginBottom: hp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginBottom: hp('1%'),
      },
  dateWrapper: {
    flexDirection: 'row',
  },
  date: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
  day: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        marginLeft: wp('0.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        marginLeft: wp('0.5%'),
        fontFamily: 'Poppins-Regular',
      },
  comment: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3'),
        marginTop: hp('0.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5'),
        marginTop: hp('0.5%'),
      },
  list: isTabDevice()
    ? {
        // marginBottom: wp('10%'),
      }
    : { marginBottom: wp('20%') },
});

export default IAPCommentsStyles;
