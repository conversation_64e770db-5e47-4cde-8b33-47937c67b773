import React, { useEffect, useState } from 'react';
import { View, Text, FlatList } from 'react-native';
import IAPStatItem from './IAPStatItem';
import customIAPStatsStyles from './IAPStatsStyles';
import useStyles from '../../../hooks/useStyles';

const IAPStats = ({
  statsDefinition,
  statsData,
  setUpdatedStats,
  isEditMode,
  onCriteriaGraphSelect,
  IisShowGraphIcon = true,
}) => {
  const Header = () => {
    const IAPStatsStyles = useStyles(customIAPStatsStyles);
    return (
      <View style={IAPStatsStyles.statsLabelContainer}>
        <View style={IAPStatsStyles.firstCol}>
          <Text style={IAPStatsStyles.statsLabel}>Activity</Text>
        </View>
        <View style={IAPStatsStyles.secondCol}>
          <View style={IAPStatsStyles.statsLabelWrapper}>
            <Text style={IAPStatsStyles.statsLabel2}>Current</Text>
          </View>
        </View>
        <View style={IAPStatsStyles.secondCol}>
          <View style={IAPStatsStyles.statsLabelWrapper}>
            <Text style={IAPStatsStyles.statsLabel2}>Target</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View>
      {statsDefinition && <Header />}
      <View>
        <FlatList
          data={statsDefinition}
          renderItem={({ item }) => (
            <IAPStatItem
              stat={item}
              updateValues={setUpdatedStats}
              isEditMode={isEditMode}
              onCriteriaGraphSelect={onCriteriaGraphSelect}
              IisShowGraphIcon={IisShowGraphIcon}
            />
          )}
          keyExtractor={item => item._id}
          scrollEnabled={false}
        />
      </View>
    </View>
  );
};

export default IAPStats;
