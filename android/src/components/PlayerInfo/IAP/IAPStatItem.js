import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import ButtonImage from '../../../../assets/buttons/greenStatsButton.png';
import IAPNumberInput from './IAPNumberInput';
import customIAPStatItemStyles from './IAPStatItemStyles';
import useStyles from '../../../hooks/useStyles';

const IAPStatItem = ({
  stat,
  statItems,
  updateValues,
  isEditMode,
  onCriteriaGraphSelect,
  IisShowGraphIcon,
}) => {
  const IAPStatItemStyles = useStyles(customIAPStatItemStyles);
  const { activity, maximum, minimum, levels, _id } = stat;
  const [current, setCurrent] = useState(0);
  const [currentEditMode, setCurrentEditMode] = useState(10);
  const [target, setTarget] = useState(0);
  const [targetEditMode, setTargetEditMode] = useState(10);

  useEffect(() => {
    if (!isNaN(stat.current) && !isNaN(stat.target)) {
      setCurrent(stat.current);
      setTarget(stat.target);
      setCurrentEditMode(stat.current);
      setTargetEditMode(stat.target);
    }
  }, [stat]);

  useEffect(() => {
    const saveStat = {
      current: current,
      target: target,
      criteriaId: _id,
    };
    updateValues(saveStat);
  }, [current, target]);

  const validate = value => {
    if (value > maximum) {
      value = maximum;
    }
    if (value < minimum) {
      value = minimum;
    }
    return value;
  };

  const getColour = value => {
    const colorLevel = levels.find(
      level =>
        value === level.start ||
        value === level.end ||
        (value > level.start && value < level.end)
    );
    if (colorLevel) {
      return colorLevel.colorCode.toLowerCase();
    } else {
      return '#fff';
    }
  };

  return (
    <View style={IAPStatItemStyles.statItemContainer}>
      <View style={IAPStatItemStyles.firstCol}>
        <Text style={IAPStatItemStyles.statItem}>{activity}</Text>
      </View>
      <View style={IAPStatItemStyles.secondCol}>
        {/* current */}
        <IAPNumberInput
          defaultValue={current}
          validate={validate}
          getColour={getColour}
          saveValue={setCurrent}
          isEditMode={isEditMode}
          editModeValue={currentEditMode}
          editModeValueUpdate={setCurrentEditMode}
        />
      </View>
      <View style={IAPStatItemStyles.thirdCol}>
        {/* target */}
        <IAPNumberInput
          defaultValue={target}
          validate={validate}
          getColour={getColour}
          saveValue={setTarget}
          isEditMode={isEditMode}
          editModeValue={targetEditMode}
          editModeValueUpdate={setTargetEditMode}
        />
      </View>
      {!isEditMode && IisShowGraphIcon && (
        <View style={IAPStatItemStyles.buttonWrapper}>
          {/* stat button */}
          <TouchableOpacity onPress={() => onCriteriaGraphSelect(_id)}>
            <Image style={IAPStatItemStyles.button} source={ButtonImage} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default IAPStatItem;
