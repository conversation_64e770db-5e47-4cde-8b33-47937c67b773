import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import customIAPCategoriesStyles from './IAPCategoriesStyles';
import { isTabDevice } from '../../../config/appConfig';
import DropDownPicker from '../../DropdownPicker';
import useStyles from '../../../hooks/useStyles';
import SelectionModal from '../../modal/SelectionModal/SelectionModal';
import useInputSelectModal from '../../../hooks/useInputSelectModal';

const IAPCategories = ({
  title,
  categories,
  onCategoryItemPress,
  selectedIAPCategory,
  selectedCategoryTemp,
}) => {
  const IAPCategoriesStyles = useStyles(customIAPCategoriesStyles);
  const [mobileDropdownItems, setMobileDropdownItems] = useState([]);
  const [defaultSelectedItem, setDefaultSelectedItem] = useState({
    label: null,
    value: null,
  });
  const [setIsIAPCatergoryModalOpen, isIAPCatergoryModalOpen] =
    useInputSelectModal();

  useEffect(() => {
    if (categories?.length && !isTabDevice()) {
      const mobileCategories = categories.map(item => {
        return {
          label: item.name,
          value: item._id,
          item,
        };
      });

      const selectedCategory = categories.find(
        item => item._id === selectedIAPCategory
      );

      if (mobileCategories?.length) {
        if (selectedCategory) {
          setDefaultSelectedItem(selectedIAPCategory);
        } else {
          setDefaultSelectedItem(mobileCategories[0].value);
        }
      }
      setMobileDropdownItems(mobileCategories);
    }
  }, [categories, selectedIAPCategory]);

  const onMobileDropdownSelect = item => {
    item && onCategoryItemPress(item.item);
  };

  const CategoryItem = ({ item }) => (
    <View style={IAPCategoriesStyles.container}>
      <TouchableOpacity onPress={() => onCategoryItemPress(item)}>
        <View
          style={
            selectedIAPCategory === item._id
              ? IAPCategoriesStyles.categoryItemWrapper
              : IAPCategoriesStyles.categoryItemWrapperNoColor
          }
        >
          <Text style={IAPCategoriesStyles.categoryItem}>{item.name}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <View>
        {isTabDevice() ? (
          <FlatList
            style={{}}
            data={categories}
            renderItem={CategoryItem}
            keyExtractor={item => item._id}
            horizontal
          />
        ) : (
          <View style={IAPCategoriesStyles.dropdownView}>
            {mobileDropdownItems?.length ? (
              <SelectionModal
                title={title}
                items={mobileDropdownItems}
                onCloseHook={setIsIAPCatergoryModalOpen}
                onSelectItemHook={item => onMobileDropdownSelect(item?.[0])}
                defaultValues={mobileDropdownItems ? [defaultSelectedItem] : []}
                isEnableAutoComplete
                selectFirstOptionOnInitialRender
                selectedItemLabel={selectedCategoryTemp?.name}
                isModalOpen={isIAPCatergoryModalOpen}
              />
            ) : (
              <></>
            )}
          </View>
        )}
      </View>
    </>
  );
};

export default IAPCategories;
