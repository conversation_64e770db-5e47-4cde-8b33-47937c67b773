import { Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPCategoriesStyle = colors => ({
  container: {
    paddingBottom: wp('1%'),
  },
  categoryItemWrapper: {
    backgroundColor: colors.green,
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
  },
  categoryItemWrapperNoColor: {
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('100%'),
    marginRight: wp('2%'),
  },
  categoryItem: {
    fontSize: 18,
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },
  dropdownView: isTabDevice() ? {
    width: wp('50%'),
    marginTop: wp('-1%'),
    marginLeft: wp('2.5%'),
    borderRadius: wp('100%'),
  } : {
    width: wp('65%'),
    marginTop: wp('-1%'),
    marginLeft: wp('2.5%'),
    borderRadius: wp('100%'),
  },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: {
    color: colors.lightGrey,
    fontSize: wp('3.5%'),
  },
  dropdownSelectedContainer: {
    height: wp('8%'),
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('-1%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 5,
        width: wp('89.5%'),
        marginLeft: wp('4%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: 3,
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 5,
        width: wp('89.5%'),
        marginLeft: wp('4%'),
      },
  dropDownLabel: {
    color: colors.white,
    fontSize: wp('3.5%'),
    fontWeight: 'bold',
    width: wp('60%'),
    marginLeft: wp('1%'),
  },
  placeholderStyle: {
    color: colors.lightGrey,
    fontSize: wp('3.5%'),
    fontWeight: 'bold',
  },
  dropdownTopArea: isTabDevice()
    ? {
        ...(Platform.OS === 'android'
          ? {
              backgroundColor: colors.semiDarkBlue,
              borderColor: colors.transparent,
              alignItems: 'flex-end',
            }
          : {
              backgroundColor: colors.darkBlue,
              borderColor: colors.darkBlue,
              borderRadius: wp('100%'),
            }),
      }
    : {
        borderColor: colors.transparent,
        borderRadius: wp('100%'),
        backgroundColor: colors.semiDarkBlue,
      },
});

export default IAPCategoriesStyle;
