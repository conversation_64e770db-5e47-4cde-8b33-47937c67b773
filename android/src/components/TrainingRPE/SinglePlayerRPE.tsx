import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { isTabDevice } from '../../config/appConfig';
import useProfileHook from '../../hooks/ServiceHook/ProfileServiceHook/useProfileHook';
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';
import useStyles from '../../hooks/useStyles';
import ProfileImage from '../ProfileImage/ProfileImage';
import CustomResponeSummaryStyle from './ResponseSummaryStyle';

const SinglePlayerRPE = ({
  item,
  isShowScoreBlock,
  isResponseTab
}: {
  item: any;
  isShowScoreBlock: boolean;
  isResponseTab: boolean;
}) => {
  const ResponseSummaryStyle = useStyles(CustomResponeSummaryStyle);

  const { handleRPEModal, selectedTeam, RPEMap } = useTrainingHook({
    isInitalComponent: false,
  });

  const { navigateProfile, navigateMessageById } = useProfileHook();
  const isUserDeleted = item?.userDetails?.deleted;

  return (
    <View style={ResponseSummaryStyle.flatListItem}>
      <TouchableOpacity
        onPress={async () => {
          await navigateProfile(item.userDetails, selectedTeam);
          handleRPEModal({ eventId: '', showRPEModal: false });
        }}
        style={{ opacity: isUserDeleted ? 0.1 : 1 }}
        disabled={isUserDeleted}
      >
        <ProfileImage
          profileImageUrl={item.userDetails.profileImageUrl}
          imageStyles={ResponseSummaryStyle.avatar}
          style={undefined}
        />
      </TouchableOpacity>
      <View style={ResponseSummaryStyle.flatListItemDetailSection}>
        <TouchableOpacity
          onPress={async () => {
            await navigateProfile(item.userDetails, selectedTeam);
            handleRPEModal({ eventId: '', showRPEModal: false });
          }}
          style={isResponseTab ? ResponseSummaryStyle.flatListItemName : ResponseSummaryStyle.flatListItemName2}
        >
          <Text style={ResponseSummaryStyle.responseSummaryCountText}>
          {item?.userDetails?.firstName ||
                '' + ' ' + item?.userDetails?.lastName ||
                ''}
          </Text>
        </TouchableOpacity>
        <View style={ResponseSummaryStyle.flatListItemCountSection}>
          <>
            {isShowScoreBlock && (
              <View
                style={{
                  ...ResponseSummaryStyle.flatListItemCount,
                  backgroundColor:
                    RPEMap?.[item?.rpeRatingId || '']?.labelColor || 'red',
                }}
              >
                <Text style={ResponseSummaryStyle.responseSummaryCountText}>
                  {RPEMap?.[item?.rpeRatingId || '']?.value || 1}
                </Text>
              </View>
            )}
            {item?.trainingLoad &&
              <Text style={ResponseSummaryStyle.responseSummaryCountText}>
                {item?.trainingLoad || 1}
              </Text>
            }
          </>
          <View style={ResponseSummaryStyle.flatListItemIconContainer}>
            <TouchableOpacity
              onPress={async () => {
                await navigateMessageById(item.userDetails);
                handleRPEModal({ eventId: '', showRPEModal: false });
              }}
              disabled={isUserDeleted}
              style={{ opacity: isUserDeleted ? 0.1 : 1 }}
            >
              <FontAwesome6
                name="message"
                size={isTabDevice() ? 20 : 15}
                color="white"
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SinglePlayerRPE;
