import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';

interface TabButtonProps {
  label: string;
  count?: number;
  isActive: boolean;
  onPress: () => void;
  backgroundColor?: string;
  style: any;
}

const TabResponseButton = ({
  label,
  count,
  isActive,
  onPress,
  backgroundColor,
  style,
}: TabButtonProps) => {
  return (
    <TouchableOpacity onPress={onPress} style={[
      style.responseCountContainer,
      backgroundColor ? { backgroundColor } : {},
    ]}>
        <Text style={style.responseSummaryCountText}>{label}</Text>
 
        <Text style={style.responseSummaryCountText}>{count}</Text>
      </TouchableOpacity>
  );
};

export default TabResponseButton;
