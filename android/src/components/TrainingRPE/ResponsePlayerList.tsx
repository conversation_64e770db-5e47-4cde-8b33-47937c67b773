import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import SinglePlayerRPE from './SinglePlayerRPE';

interface PlayerListProps {
  players: any[];
  isLoading: boolean;
  isShowScoreBlock: boolean;
  isResponseTab: boolean;
  onEndReached: () => void;
  styles: any;
}

const ResponsePlayerList = ({ players, isLoading, onEndReached, styles ,isShowScoreBlock, isResponseTab}: PlayerListProps) => (
  <View style={isResponseTab ? styles.listWrapper : styles.listWrapper2}>
    {!isLoading && players?.length === 0 && (
      <View style={styles.noContentWrapper}>
        <Text style={styles.responseSummaryCountText}>No content</Text>
      </View>
    )}
    {players && (
      <FlatList
        data={players}
        renderItem={({ item }) => <SinglePlayerRPE item={item} isShowScoreBlock={isShowScoreBlock} isResponseTab={isResponseTab}/>}
        onEndReached={onEndReached}
      />
    )}
    {isLoading && (
      <ActivityIndicator
        size="large"
        color={'#00ff00'}
        style={{
          width: '100%',
          height: '100%',
        }}
      />
    )}
  </View>
);

export default ResponsePlayerList