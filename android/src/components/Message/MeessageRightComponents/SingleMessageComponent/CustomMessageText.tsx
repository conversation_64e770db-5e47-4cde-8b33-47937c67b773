import React from 'react';
import { Linking, Text } from 'react-native';
import useStyles from '../../../../hooks/useStyles';
import customMessageStyles from '../../MeessageRightComponents/MessageRightStyles';

const linkRegex = /(https?:\/\/[^\s]+)|(www\.[^\s]+)/g;
const wwwRegex = /www\.[^\s]+/g;

const CustomMessageText = ({ text }: { text: string }) => {
  const MessageRightStyles = useStyles(customMessageStyles);
  const extractLinks = (str: string) => {
    return str.match(linkRegex);
  };

  const links = extractLinks(text);

  if (!links) return <Text style={MessageRightStyles.sentMessage}>{text}</Text>;

  const parts = text.split(linkRegex);

  const renderTextWithLinks = () => {
    return parts.map((part, index) => {
      if (links.includes(part)) {
        return (
          <Text
            style={{
              ...MessageRightStyles.sentMessage,
              color: 'white',
              textDecorationLine: 'underline',
            }}
            onPress={() => {
              if (part.startsWith("http://") || part.startsWith("https://")) {
                Linking.openURL(part);
            } else if (part.match(wwwRegex)) {
                Linking.openURL(`https://${part}`);
            } else {
                Linking.openURL(part);
            }
            }}
          >
            {part}
          </Text>
        );
      }
      return (
        <Text style={MessageRightStyles.sentMessage} key={index}>
          {part}
        </Text>
      );
    });
  };

  return (
    <Text
      style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'flex-end',
      }}
    >
      {renderTextWithLinks()}
    </Text>
  );
};

export default CustomMessageText;
