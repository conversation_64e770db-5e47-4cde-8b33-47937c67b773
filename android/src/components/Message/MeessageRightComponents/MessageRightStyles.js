import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MessageRightStyles = colors => ({
  messageRightTopContainer: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        padding: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        width: '100%',
        justifyContent: 'space-between',
      }
    : {
        backgroundColor: colors.darkBlue,
        padding: wp('4%'),
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      },
  messageRightTopProDetailsWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageRightTopProImage: isTabDevice()
    ? {
        width: wp('5%'),
        height: wp('5%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      }
    : {
        width: wp('13%'),
        height: wp('13%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      },
  messageRightTopProDetails: {
    marginLeft: wp('1%'),
    justifyContent: 'center',
  },
  messageRightTopProName: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
      },
  messageRightTopProStatus: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.green,
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
      },
  MessageFooterContainer: isTabDevice()
    ? {
        backgroundColor: colors.chatFooterBlue,
        marginTop: 0,
        padding: wp('0.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        borderRadius: 20,
      }
    : {
        backgroundColor: colors.chatFooterBlue,
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        padding: wp('1.5%'),
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-evenly',
        width: '100%',
      },
  MessageFooterMessageBox: isTabDevice()
    ? {
        backgroundColor: colors.chatTextBoxBlue,
        color: colors.white,
        // borderRadius: 20,
        width: '80%',
        padding: wp('1%'),
        minHeight: wp('3.5'),
        maxHeight: wp('7%'),
        paddingTop: wp('1%'),
        fontSize: wp('1.2%'),
      }
    : {
        backgroundColor: colors.chatTextBoxBlue,
        color: colors.white,
        padding: wp('1.5%'),
        paddingLeft: wp('2.5%'),
        paddingTop: wp('2%'),
        minHeight: wp('9'),
        maxHeight: wp('20%'),
        // height: 100,
        borderRadius: 10,
        width: '80%',
      },
  icon: isTabDevice()
    ? {
        paddingBottom: wp('0.5%'),
      }
    : {
        paddingBottom: wp('5%'),
      },
  icon2: isTabDevice()
    ? {
        paddingBottom: wp('0.2%'),
      }
    : {
        paddingBottom: wp('4%'),
      },

  forwardedMessage: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('3%'),
      },
  sentMessageContainer: isTabDevice()
    ? {
        alignSelf: 'flex-end',
        alignItems: 'flex-end',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
        maxWidth: '80%',
      }
    : {
        alignSelf: 'flex-end',
        alignItems: 'flex-end',
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
        // maxWidth: '80%',
      },
  sentMessageWrapper: isTabDevice()
    ? {
        backgroundColor: colors.chatGreen,
        paddingTop: wp('0.7%'),
        paddingBottom: wp('0.7%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        borderRadius: 20,
        borderTopRightRadius: 0,
      }
    : {
        backgroundColor: colors.chatGreen,
        padding: wp('3%'),
        borderRadius: 20,
        borderTopRightRadius: 0,
      },
  sentMessage: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
      deleteMessage: isTabDevice()
      ? {
          color: colors.grey,
          fontSize: wp('1.1%'),
          fontFamily: 'Poppins-Italic'
        }
      : {
          color: colors.grey,
          fontSize: wp('3%'),
          fontFamily: 'Poppins-Italic'
        },
  sentMessageFowardWrapper: isTabDevice()
    ? {
        backgroundColor: colors.blackOpcaity,
        padding: wp('1%'),
        borderRadius: wp('0.5%'),
      }
    : {
        backgroundColor: colors.blackOpcaity,
        padding: wp('2.5%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('2%'),
        marginBottom: 5,
      },
  sentMessageFoward: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.1%'),
        fontFamily: 'Poppins-Medium',
        padding: 2,
        paddingLeft: 5,
        paddingRight: 5,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        padding: 2,
        paddingLeft: 5,
        paddingRight: 5,
      },
  sentMessageDetails: isTabDevice()
    ? {
        flexDirection: 'row',
        marginTop: wp('0.5%'),
      }
    : {
        flexDirection: 'row',
        marginTop: wp('1.5%'),
      },
  sentMessageDetailsText: isTabDevice()
    ? {
        color: colors.grey,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Regular',
        marginLeft: wp('0.2%'),
      }
    : {
        color: colors.grey,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Regular',
        marginLeft: wp('1%'),
      },

  receivedMessageContainer: isTabDevice()
    ? {
        alignItems: 'flex-start',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
        maxWidth: '80%',
      }
    : {
        alignItems: 'flex-start',
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
        maxWidth: '80%',
      },
  receivedMessageWrapper: isTabDevice()
    ? {
        backgroundColor: colors.chatBlue,
        paddingTop: wp('0.7%'),
        paddingBottom: wp('0.7%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        borderRadius: 20,
        borderTopLeftRadius: 0,
      }
    : {
        backgroundColor: colors.chatBlue,
        padding: wp('3%'),
        borderRadius: 20,
        borderTopLeftRadius: 0,
      },
  receivedMessage: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  receivedMessageDetailsWrapper: isTabDevice()
    ? {
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginTop: wp('0.5%'),
      }
    : {
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginTop: wp('1.5%'),
      },
  receivedMessageDetailsImage: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  receivedMessageDetailsPic: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      }
    : {
        width: wp('6%'),
        height: wp('6%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      },
  receivedMessageDetailsName: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Regular',
        marginLeft: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Regular',
        marginLeft: wp('1%'),
      },
  receivedMessageDetails: isTabDevice()
    ? {
        flexDirection: 'row',
        marginLeft: wp('2.1%'),
      }
    : {
        flexDirection: 'row',
        marginLeft: wp('5.5%'),
        marginTop: wp('-1%'),
      },
  receivedMessageDetailsText: isTabDevice()
    ? {
        color: colors.grey,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Regular',
        marginLeft: wp('0.2%'),
      }
    : {
        color: colors.grey,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Regular',
        marginLeft: wp('1%'),
      },

  MessageFooterReplyWrapper: isTabDevice()
    ? {
        backgroundColor: colors.chatFooterBlue,
        margin: wp('1%'),
        marginBottom: 0,
        padding: wp('0.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        borderRadius: 20,
      }
    : {
        backgroundColor: colors.chatFooterBlue,
        margin: wp('2%'),
        // marginBottom: 0,
        padding: wp('1.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        borderRadius: 20,
      },
  MessageFooterReplyContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '90%',
        marginLeft: 30,
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '90%',
        marginLeft: 30,
      },
  MessageFooterReplyUser: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
      },
  MessageFooterReplyText: isTabDevice()
    ? {
        color: colors.grey,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.grey,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
      },
  chatBox: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('60%'),
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('60%'),
      },
  chatBoxShort: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('57%'),
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('54%'),
      },
  downloadText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
        marginRight: 10,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        marginRight: 10,
      },
  downloadWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  downloadWrapperType2: {
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  downloadIcon: isTabDevice()
    ? {
        width: wp('5%'),
        height: wp('5%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('15%'),
        height: wp('15%'),
        resizeMode: 'contain',
      },
  previewMedia: isTabDevice()
    ? {
        width: '100%',
        height: wp('15%'),
        resizeMode: 'cover',
      }
    : {
        width: '100%',
        height: wp('45%'),
        resizeMode: 'cover',
      },
  downloadIconWrapper: isTabDevice()
    ? {
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: colors.white,
        paddingBottom: wp('1%'),
      }
    : {
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: colors.white,
        paddingBottom: wp('3%'),
      },
  downloadIconText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
        marginTop: 10,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        marginTop: 10,
      },

  MessageFooterUploadSection: isTabDevice()
    ? {
        backgroundColor: colors.chatFooterBlue,
        margin: wp('1%'),
        marginBottom: -5,
        padding: wp('0.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        borderRadius: 15,
      }
    : {
        backgroundColor: colors.chatFooterBlue,
        margin: wp('2%'),
        // marginBottom: -5,
        height: wp('11.1%'),
        padding: wp('1.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        borderRadius: 20,
      },
  MessageFooterUploadWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '95%',
  },
  MessageFooterUploadContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  MessageFooterUploadText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        marginLeft: 10,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        marginLeft: 10,
      },

  MessageFooterSendButton: isTabDevice()
    ? {
        width: 38,
        height: 38,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        right: 0,
      }
    : {
        width: 30,
        height: 70,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        right: 0,
      },
});

export default MessageRightStyles;
