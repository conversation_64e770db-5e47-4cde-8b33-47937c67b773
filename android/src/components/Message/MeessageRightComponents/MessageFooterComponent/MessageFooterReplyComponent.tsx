import { View, Text, TouchableOpacity } from 'react-native';
import React, { FC } from 'react';
import useStyles from '../../../../hooks/useStyles';
import customMessageStyles from '../../MeessageRightComponents/MessageRightStyles';
import { isTabDevice } from '../../../../config/appConfig';
import { AntDesign } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { RootStore } from '../../../../store/store';
import { createNewMessageUsersType } from '../../../../store/reducers/Message/MessageReducer';

interface IMessageFooterReplyComponent {
  setIsReplyMode: Function;
  selectedMsgContent: any;
  pastMessageChatMemberInfo: createNewMessageUsersType[] | null;
  setSelectedMsgContent: Function;
}

const MessageFooterReplyComponent: FC<IMessageFooterReplyComponent> = ({
  selectedMsgContent,
  setIsReplyMode,
  pastMessageChatMemberInfo: chatMembersInfo,
  setSelectedMsgContent,
}) => {
  const { isUserOwnMessage, senderUserId } = selectedMsgContent;

  const MessageRightStyles = useStyles(customMessageStyles);

  const { userData } = useSelector((state: RootStore) => state?.auth);

  const chatMemberData: createNewMessageUsersType | null = senderUserId?.length
    ? chatMembersInfo?.find(data => data?.id === senderUserId) || null
    : null;

  const renderReplyTitle = () => (
    <Text style={MessageRightStyles.MessageFooterReplyUser}>
      {isUserOwnMessage
        ? `${userData?.firstName || ''} ${userData?.lastName || ''}`
        : `${chatMemberData?.firstName || ''} ${
            chatMemberData?.lastName || ''
          }`}
    </Text>
  );

  const handleClose = () => {
    setIsReplyMode(false);
    setSelectedMsgContent({});
  };
  return (
    <View style={MessageRightStyles.MessageFooterReplyWrapper}>
      <View style={MessageRightStyles.MessageFooterReplyContainer}>
        <View>
          {renderReplyTitle()}
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            style={MessageRightStyles.MessageFooterReplyText}
          >
            {selectedMsgContent?.content}
          </Text>
        </View>
        <TouchableOpacity onPress={handleClose}>
          <AntDesign
            name="close"
            size={isTabDevice() ? 16 : 20}
            color="white"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default MessageFooterReplyComponent;
