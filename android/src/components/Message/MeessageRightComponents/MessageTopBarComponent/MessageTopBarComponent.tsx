import React, { FC, useEffect, useState } from 'react';
import { Image, Text, View, TouchableOpacity } from 'react-native';
import useStyles from '../../../../hooks/useStyles';
import { Entypo } from '@expo/vector-icons';
import { isTabDevice } from '../../../../config/appConfig';
import useGeneratedFileUrl from '../../../../hooks/useGeneratedFileUrl';
import customMessageStyles from '../../MeessageRightComponents/MessageRightStyles';
import { messageTypes } from '../../../../constants/constants';
import CacheImage from '../../../CacheImage/CacheImage';

type MessageTopBarComponentProps = {
  data: any;
  setChatProfileModalOpen: any;
  selectedMessageType: string;
  selectedTeamProfile: any;
};

const MessageTopBarComponent: FC<MessageTopBarComponentProps> = ({
  data,
  setChatProfileModalOpen,
  selectedMessageType,
  selectedTeamProfile,
}) => {
  const MessageRightStyles = useStyles(customMessageStyles);
  const [image, setImage] = useState<any>(null);
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  const { profileImageUrl } = data || {};
  const isSelectedPersonal = selectedMessageType === messageTypes.PERSONAL;

  //reset url when switching between personal and team chats
  useEffect(() => {
    setImage(null);
  }, [isSelectedPersonal]);

  //reset image when switching chats
  useEffect(() => {
    setImage(null);
  }, [JSON.stringify(selectedTeamProfile?.image), profileImageUrl]);

  useEffect(() => {
    url && !isDownloading && setImage(url);
  }, [url, isDownloading]);

  //personal chat comes with ProfileImage Url
  useEffect(() => {
    if (isSelectedPersonal && profileImageUrl?.length) {
      setImage(profileImageUrl);
    }
  }, [profileImageUrl, isSelectedPersonal]);

  //Team chats comes with bucket name and filekey, have to convert to get profile img url
  useEffect(() => {
    if (
      !isSelectedPersonal &&
      selectedTeamProfile?.image &&
      selectedTeamProfile?.image?.bucketName
    ) {
      s3FileObject({
        bucketName: selectedTeamProfile?.image?.bucketName,
        fileKey: selectedTeamProfile?.image?.fileKey,
      });
    }
  }, [JSON.stringify(selectedTeamProfile), isSelectedPersonal]);

  return (
    <View style={MessageRightStyles.messageRightTopContainer}>
      <View style={MessageRightStyles.messageRightTopProDetailsWrapper}>
        <CacheImage
          style={MessageRightStyles.messageRightTopProImage}
          uri={image}
        />
        <View style={MessageRightStyles.messageRightTopProDetails}>
          <Text style={MessageRightStyles.messageRightTopProName}>
            {data?.firstName} {data?.lastName} {data?.name}
          </Text>
          {/* <Text style={MessageRightStyles.messageRightTopProStatus}>
            Online
          </Text> */}
        </View>
      </View>
      {selectedMessageType === messageTypes.TEAMS && (
        <TouchableOpacity onPress={() => setChatProfileModalOpen(true)}>
          <Entypo
            name="info-with-circle"
            size={isTabDevice() ? 28 : 20}
            color="white"
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default MessageTopBarComponent;
