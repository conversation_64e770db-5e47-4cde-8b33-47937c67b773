import React, { FC, useState } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Text,
  TextInput,
} from 'react-native';
import { messageTypes } from '../../../constants/constants';
import customMessageSearchAndTabStyles from '../MeessageLeftComponents/MessageSearchAndTabStyles';
import { AntDesign } from '@expo/vector-icons';

import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

type MessageSearchAndTabProps = {
  selectedMessageType: string;
  setSelectedMessageType: Function;
  isCreateNewMessage: boolean;
  setSearchKey: Function;
  searchKey: string;
  personalMessageCount: string[] | null;
  teamsMessageCount: string[] | null;
};
const MessageSearchAndTab: FC<MessageSearchAndTabProps> = ({
  selectedMessageType,
  setSelectedMessageType,
  isCreateNewMessage,
  setSearchKey,
  searchKey,
  personalMessageCount,
  teamsMessageCount,
}) => {
  const MessageSearchAndTabStyles = useStyles(customMessageSearchAndTabStyles);

  const getTabCount = (messageCount: string[] | null) => {
    return messageCount?.length ? (
      <View style={MessageSearchAndTabStyles.toggleCountWrapper}>
        <Text style={MessageSearchAndTabStyles.toggleCount}>
          {messageCount?.length > 99 ? '99+' : messageCount?.length}
        </Text>
      </View>
    ) : null;
  };

  return (
    <View style={MessageSearchAndTabStyles.container}>
      {isCreateNewMessage ? (
        <View style={MessageSearchAndTabStyles.searchContainer}>
          <TextInput
            style={MessageSearchAndTabStyles.search}
            value={searchKey}
            onChangeText={text => setSearchKey(text)}
            multiline={true}
            placeholder={'Search'}
            placeholderTextColor="#fff"
            disableFullscreenUI={true}
            returnKeyType={'done'}
            blurOnSubmit={true}
          />
          <AntDesign
            name="search1"
            size={24}
            color="white"
            style={MessageSearchAndTabStyles.searchIcon}
          />
        </View>
      ) : (
        <View style={MessageSearchAndTabStyles.toggleBar}>
          <TouchableOpacity
            onPress={() => setSelectedMessageType(messageTypes.PERSONAL)}
            disabled={selectedMessageType === messageTypes.PERSONAL}
          >
            <View
              style={
                selectedMessageType === messageTypes.PERSONAL
                  ? MessageSearchAndTabStyles.selectedText
                  : MessageSearchAndTabStyles.selectionText
              }
            >
              <Text
                style={
                  selectedMessageType === messageTypes.PERSONAL
                    ? MessageSearchAndTabStyles.toggleText
                    : MessageSearchAndTabStyles.toggleTextSelected
                }
              >
                Personal
              </Text>
              {getTabCount(personalMessageCount)}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setSelectedMessageType(messageTypes.TEAMS)}
            disabled={selectedMessageType === messageTypes.TEAMS}
          >
            <View
              style={
                selectedMessageType === messageTypes.TEAMS
                  ? MessageSearchAndTabStyles.selectedText
                  : MessageSearchAndTabStyles.selectionText
              }
            >
              <Text
                style={
                  selectedMessageType === messageTypes.TEAMS
                    ? MessageSearchAndTabStyles.toggleText
                    : MessageSearchAndTabStyles.toggleTextSelected
                }
              >
                Teams
              </Text>
              {getTabCount(teamsMessageCount)}
            </View>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
export default MessageSearchAndTab;
