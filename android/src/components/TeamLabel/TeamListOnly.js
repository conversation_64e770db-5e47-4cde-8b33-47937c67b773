import React, { useEffect, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { isTabDevice } from '../../config/appConfig';
import {
  RESET_TEAM_LABEL,
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import customTeamLabelStyle from './TeamLabelStyle';

import { userRoleType } from '../../constants/constants';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import { TEAM_ID_SUCCESS } from '../../store/actionTypes/TeamID/TeamID';
import ActivitySpinner from '../ActivitySpinner/ActivitySpinner';
import { SET_PLAYER_LABEL_ID } from '../../store/actionTypes/TeamID/TeamID';


import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';

import useGetChildInformation from '../../hooks/useGetChildInformation';
import useInputSelectModal from '../../hooks/useInputSelectModal';

export default function TeamListOnly({ setTeamID }) {
  const TeamLabelStyle = useStyles(customTeamLabelStyle);
  const colors = useColors();
  const [selectedTeamID, setSelectedTeamID] = useState(null);
  const [selectedTeamColor, setSelectedTeamColor] = useState(colors.white);
  const [fetchChildInformation] = useGetChildInformation();
  const { childInformation } = useSelector(state => state?.common);
  const [fetchData] = useApi();

  const dispatch = useDispatch();
  const [setSelectedTeam] = useInputSelectModal();

  const { teamDataLoading, teamData } = useSelector(state => state?.team);
  const { userRole, userData } = useSelector(state => state?.auth);

  const isParent = userRole === userRoleType.PARENT;
  const isPlayer = userRoleType.PLAYER === userData?.type;

  const findURL = () => {
    switch (userRole) {
      case userRoleType.PLAYER:
        return `/api/v1/sport-profiles/${userData.sportsProfileId}/teams?page=1&size=200`;
      case userRoleType.PARENT:
        return `/api/v1/sport-profiles/${childInformation?.sportsProfileId
          }/teams?page=1&size=200`;
    }
  };


  useEffect(() => {
    selectedTeamID && setTeamID(selectedTeamID);
  }, [selectedTeamID]);

  useEffect(() => {
    if (teamData?.data?.[0]?._id) {
      const firstTeam = teamData.data[0];
      setTeamId(firstTeam._id, firstTeam.teamName);
      setSelectedTeamID(firstTeam._id)
    }
  }, [teamData]);


  useEffect(() => {
    if (userData && (isParent)) {
      fetchData(
        findURL(),
        TEAM_REQUEST,
        TEAM_SUCCESS,
        TEAM_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [childInformation?.sportsProfileId]);

  useEffect(() => {
    if (userData && (isPlayer)) {
      fetchData(
        findURL(),
        TEAM_REQUEST,
        TEAM_SUCCESS,
        TEAM_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [userData?.sportsProfileId]);

  useEffect(() => {
    let teamDetails = teamData?.data?.[0];
    setSelectedTeam([
      {
        label: teamDetails?.teamName,
        value: teamDetails?._id,
        textStyle: { color: teamDetails?.colour },
      },
    ]);
  }, [teamData]);

  const setTeamId = (teamId, title) => {
    setSelectedTeamID(teamId);
    dispatch({
      type: TEAM_ID_SUCCESS,
      TeamIdData: teamId,
      TeamNameData: title,
    });
    setSelectedTeamColor(
      teamData?.data?.find(team => team?._id === teamId)?.colour || colors.white
    );
    dispatch({ type: SET_PLAYER_LABEL_ID, PlayerLabelId: null })

  };


  const renderTeamLabel = ({ item }) => (
    <Item title={item.teamName} teamId={item._id} colour={item.colour} />
  );

  const Item = ({ title, teamId, colour }) => (
    <View style={TeamLabelStyle.item}>
      {teamDataLoading ? (
        <ActivitySpinner />
      ) : (
        <TouchableOpacity onPress={() => setTeamId(teamId, title)}>
          <Text
            style={
              selectedTeamID === teamId
                ? TeamLabelStyle.titleSelected
                : TeamLabelStyle.title
            }
          >
            {title}
          </Text>
          <View
            style={[
              {
                backgroundColor: `${colour}`,
              },
              TeamLabelStyle.titleLabel,
            ]}
          ></View>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={TeamLabelStyle.teamChildrenWrapper}>
      {isTabDevice() && (
        <>
          <View
            style={
              isParent
                ? TeamLabelStyle.teamChildrenWrapperRight
                : TeamLabelStyle.teamWrapperRight
            }
          >
            <FlatList
              data={teamData?.data || teamData}
              renderItem={renderTeamLabel}
              keyExtractor={item => item._id}
              horizontal
            />
          </View>
        </>
      )}
    </View>
  );
}
