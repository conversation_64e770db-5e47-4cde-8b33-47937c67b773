import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { isTabDevice } from '../../config/appConfig';
import {
  RESET_TEAM_LABEL,
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import { SET_PLAYER_LABEL_ID } from '../../store/actionTypes/TeamID/TeamID';
import customTeamLabelStyle from './TeamLabelStyle';

import { userRoleType } from '../../constants/constants';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import { TEAM_ID_SUCCESS } from '../../store/actionTypes/TeamID/TeamID';
import ActivitySpinner from '../ActivitySpinner/ActivitySpinner';
import ChildLabel from '../ChildLabel/ChildLabel';

import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';

import useGetChildInformation from '../../hooks/useGetChildInformation';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import SelectionModal from '../modal/SelectionModal/SelectionModal';

export default function TeamLable({
  selectedChild,
  setSelectedChild,
  setTeamID,
}) {
  const TeamLabelStyle = useStyles(customTeamLabelStyle);
  const colors = useColors();
  const [selectedTeamID, setSelectedTeamID] = useState(null);
  const [selectedTeamColor, setSelectedTeamColor] = useState(colors.white);
  const [fetchChildInformation] = useGetChildInformation();
  const [fetchData] = useApi();
  const flatListRef = useRef(null);
  const [setIsTeamModalOpen, isTeamModalOpen, setSelectedTeam, selectedTeam] =
    useInputSelectModal();

  const dispatch = useDispatch();
  const { teamDataLoading, teamData } = useSelector(state => state?.team);
  const { userRole, userData } = useSelector(state => state?.auth);
  const { children} = useSelector(
    state => state?.common
  );

  const isParent = userRole === userRoleType.PARENT;

  const findURL = () => {
    switch (userRole) {
      case userRoleType.PLAYER:
        return `/api/v1/sport-profiles/${userData.sportsProfileId}/teams?page=1&size=200`;
      case userRoleType.COACH:
        return `/api/v1/teams?coachId=${userData.id}&page=1&size=200`;
      default:
        return `/api/v1/teams?page=1&size=200`;
    }
  };


  useEffect(() => {
    setTeamID && selectedTeamID && setTeamID(selectedTeamID);
  }, [selectedTeamID]);

  useEffect(() => {
    fetchChildInformation();
    return () =>
      dispatch({
        type: RESET_TEAM_LABEL,
      });
  }, []);

  useEffect(() => {
    teamData?.data?.[0]?._id && setTeamId(teamData.data[0]._id);
  }, [teamData]);

  useEffect(() => {
    let teamDetails = teamData?.data?.[0];
    setSelectedTeam([
      {
        label: teamDetails?.teamName,
        value: teamDetails?._id,
        textStyle: { color: teamDetails?.colour },
      },
    ]);
  }, [teamData]);

  useEffect(() => {
    if (userRole && userData && !isParent) {
      fetchData(
        findURL(),
        TEAM_REQUEST,
        TEAM_SUCCESS,
        TEAM_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [userData?.id, userRole]);

  const setTeamId = (teamId, title) => {
    setSelectedTeamID(teamId);
    dispatch({
      type: TEAM_ID_SUCCESS,
      TeamIdData: teamId,
      TeamNameData: title,
    });
    setSelectedTeamColor(
      teamData?.data?.find(team => team?._id === teamId)?.colour || colors.white
    );

    if (isTabDevice() && flatListRef.current) {
      const index = teamData?.data?.findIndex(team => team._id === teamId) || 0;
      flatListRef.current.scrollToIndex({ index, animated: true });
    } else {
      console.log("FlatList ref not available");
    }
    dispatch({ type: SET_PLAYER_LABEL_ID, PlayerLabelId: null });

  };

  const renderTeamLabel = ({ item, index }) => (
    <Item title={item.teamName} teamId={item._id} colour={item.colour} index={index} />
  );

  const Item = ({ title, teamId, colour }) => (
    <View style={TeamLabelStyle.item}>
      {teamDataLoading ? (
        <ActivitySpinner />
      ) : (
        <TouchableOpacity onPress={() => {
          setTeamId(teamId, title);
          setSelectedTeam([
            {
              label: title,
              value: teamId,
              textStyle: { color: teamData?.data?.find(team => team?._id === teamId)?.colour },
            },
          ]);
        }}>
          <Text
            style={
              selectedTeamID === teamId
                ? TeamLabelStyle.titleSelected
                : TeamLabelStyle.title
            }
          >
            {title}
          </Text>
          <View
            style={[
              {
                backgroundColor: `${colour}`,
              },
              TeamLabelStyle.titleLabel,
            ]}
          ></View>
        </TouchableOpacity>
      )}
    </View>
  );


  return (
    <View style={TeamLabelStyle.teamChildrenWrapper}>
      {isTabDevice() ? (
        <>
          <View style={TeamLabelStyle.teamChildrenWrapperLeft}>
            {isParent && (
              <ChildLabel
                data={children}
                setSelectedChild={setSelectedChild}
                selectedChild={selectedChild}
              />
            )}
          </View>
          <View
            style={
              isParent
                ? TeamLabelStyle.teamChildrenWrapperRight
                : TeamLabelStyle.teamWrapperRight
            }
          >
            <View style={TeamLabelStyle.teamRightDrowndownView}>
              <SelectionModal
                title={'Select Team'}
                items={
                  teamData?.data
                    ? teamData?.data?.map(({ _id, teamName, colour }) => ({
                      label: teamName,
                      value: _id,
                      textStyle: { color: colour },
                    }))
                    : []
                }
                onCloseHook={setIsTeamModalOpen}
                onSelectItemHook={team => {
                  if(team.length > 0 ){
                    setSelectedTeam(team);
                    setTeamId(team?.[0]?.value);
                  }
                }}
                isEnableAutoComplete
                selectedItemLabel={"Select Team"}
                isModalOpen={isTeamModalOpen}
              />
            </View>
            <View style={TeamLabelStyle.teamList}>
              <FlatList
                data={teamData?.data || teamData}
                renderItem={renderTeamLabel}
                keyExtractor={item => item._id}
                horizontal
                ref={flatListRef}
                initialNumToRender={200}  // Increase this based on your needs
              />
            </View>
          </View>
        </>
      ) : (
        <>
          <View style={TeamLabelStyle.teamChildrenWrapperLeft}>
            {isParent && (
              <ChildLabel
                data={children}
                setSelectedChild={setSelectedChild}
                selectedChild={selectedChild}
              />
            )}
          </View>
          <View style={TeamLabelStyle.teamChildrenWrapperRight}>
            <View style={TeamLabelStyle.dropdownView}>
              <SelectionModal
                title={'Select Team'}
                items={
                  teamData?.data
                    ? teamData?.data?.map(({ _id, teamName, colour }) => ({
                      label: teamName,
                      value: _id,
                      textStyle: { color: colour },
                    }))
                    : []
                }
                onCloseHook={setIsTeamModalOpen}
                onSelectItemHook={team => {
                  setSelectedTeam(team);
                  setTeamId(team?.[0]?.value);
                }}
                defaultValues={
                  [selectedTeam?.[0]?.value || teamData?.data?.[0]?._id] || []
                }
                isEnableAutoComplete
                selectedItemLabel={selectedTeam?.[0]?.label}
                isModalOpen={isTeamModalOpen}
              />
            </View>
          </View>
        </>
      )}
    </View>
  );
}
