import React, { useEffect } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { isTabDevice } from '../../config/appConfig';
import {
  RESET_TEAM_LABEL,
} from '../../store/actionTypes/Team/TeamAction';
import customTeamLabelStyle from './TeamLabelStyle';

import { userRoleType } from '../../constants/constants';
import ChildLabel from '../ChildLabel/ChildLabel';

import useStyles from '../../hooks/useStyles';

import useGetChildInformation from '../../hooks/useGetChildInformation';

export default function ChildListOnly({
  selectedChild,
  setSelectedChild,
}) {
  const TeamLabelStyle = useStyles(customTeamLabelStyle);
  const [fetchChildInformation] = useGetChildInformation();
  const dispatch = useDispatch();
  const { userRole } = useSelector(state => state?.auth);
  const { children } = useSelector(
    state => state?.common
  );

  const isParent = userRole === userRoleType.PARENT;

  useEffect(() => {
    fetchChildInformation();
    return () =>
      dispatch({
        type: RESET_TEAM_LABEL, 
      });
  }, []);

  return (
    <View style={TeamLabelStyle.teamChildrenOnlyWrapper}>
      {isTabDevice() && isParent &&
        <>
          <View style={TeamLabelStyle.teamChildrenWrapperLeft}>
            <ChildLabel
              data={children}
              setSelectedChild={setSelectedChild}
              selectedChild={selectedChild}
            />
          </View>
        </>
      }
    </View>
  );
}
