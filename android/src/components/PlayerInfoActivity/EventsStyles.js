import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const EventsStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('72%'),
        height: hp('60%'),
        marginBottom: hp('1%'),
      }
    : {
        width: wp('97%'),
        marginBottom: wp('6%'),
      },
  header: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  attendanceTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2.5%'),
        fontFamily: 'Poppins-Bold',
        marginRight: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
      },
  list: isTabDevice()
    ? {
      marginBottom: hp('10%'),
    }
  : {
      marginBottom: wp('40%'),
    },
});
export default EventsStyle;
