import { Entypo } from '@expo/vector-icons';
import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import { isTabDevice } from '../../config/appConfig';
import { eventType } from '../../constants/constants';
import { dateTimeUTCConversion, dateTimeConversion } from '../../helpers/index';
import useStyles from '../../hooks/useStyles';
import customEventItemStyle from './EventItemStyles';

const EventItem = ({ item, teams, selectedTab }) => {
  const EventItemStyle = useStyles(customEventItemStyle);
  const { yearLastTwoDigit, month, date, dateString } = dateTimeConversion(
    item.startTime
  );

  const isMatch = type => {
    return type === eventType.MATCH;
  };

  const iconColor = useMemo(() => {
    if (!item || !teams) return 'white';
    return teams.find(t => t._id === item.teamId)?.colour || 'white';
  }, [teams, item]);

  return (
    <View>
      <View style={EventItemStyle.highlight}>
        <View style={EventItemStyle.contain}>
          <View style={EventItemStyle.primary}>
            <View style={EventItemStyle.dateView}>
              <Text style={EventItemStyle.dateText}>
                {date}/{month}/{yearLastTwoDigit}
              </Text>
              <Text style={EventItemStyle.dateText2}>{dateString}</Text>
            </View>

            {isTabDevice() ? (
              <View style={EventItemStyle.primaryInner}>
                <View style={EventItemStyle.iconContainer}>
                  <View
                    style={[
                      EventItemStyle.icon,
                      { backgroundColor: iconColor },
                    ]}
                  >
                    {isMatch(item.type) && (
                      <Entypo
                        name="star"
                        color="#0E1B2E"
                        style={EventItemStyle.star}
                      />
                    )}
                  </View>
                </View>
                <View>
                  <Text style={EventItemStyle.msg}>{item.name}</Text>
                </View>
              </View>
            ) : (
              <View style={EventItemStyle.primaryInner}>
                <View style={EventItemStyle.iconContainer}>
                  <View
                    style={[
                      EventItemStyle.icon,
                      { backgroundColor: iconColor },
                    ]}
                  >
                    {isMatch(item.type) && (
                      <Entypo
                        name="star"
                        color="#0E1B2E"
                        style={EventItemStyle.star}
                      />
                    )}
                  </View>
                </View>
                <View>
                  <Text style={EventItemStyle.msg}>{item.name}</Text>
                </View>
              </View>
            )}
            {item.hasOwnProperty('attended') ? (
              <View style={EventItemStyle.attendanceContainer}>
                <View
                  style={
                    item.attended
                      ? EventItemStyle.attendanceTextPresent
                      : EventItemStyle.attendanceTextAbsent
                  }
                >
                  <Text style={EventItemStyle.attended}>
                    {item.attended ? 'Present' : 'Absent'}
                  </Text>
                </View>
              </View>
            ) : selectedTab === 2 ? (
              <View style={EventItemStyle.attendanceContainer}>
                <View style={EventItemStyle.attendanceTextUnmarked}>
                  <Text style={EventItemStyle.attended}>UnMarked</Text>
                </View>
              </View>
            ) : null}
          </View>
        </View>
      </View>
    </View>
  );
};

export default EventItem;
