import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { presentNotificationAsync } from 'expo-notifications';

const EventItemStyle = colors => ({
  dateView: isTabDevice()
    ? {
        height: wp('5%'),
        width: wp('8%'),
        borderRadius: wp('2%'),
        // alignItems: 'center',
        justifyContent: 'center',
        marginRight: wp('1%'),
      }
    : {
        flexDirection: 'row',
        width: wp('100%'),
      },
  dateGreen: {
    // backgroundColor: colors.green,
  },
  dateBlue: {
    // backgroundColor: colors.aquaBlue,
  },
  dateText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        marginRight: wp('2%'),
        fontFamily: 'Poppins-Bold',
      },
  dateText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        marginRight: wp('2%'),
        fontFamily: 'Poppins-Regular',
      },
  msg: isTabDevice()
    ? {
        fontSize: hp('2%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3.2%'),
        color: colors.white,
        textAlign: 'justify',
        width: wp('60%'),
        fontFamily: 'Poppins-Regular',
      },
  highlight: isTabDevice()
    ? {
        width: wp('100%'),
        marginBottom: hp('-0.5%'),
      }
    : {
        width: wp('100%'),
        marginBottom: hp('1%'),
      },
  highlightColored: {
    backgroundColor: colors.borderBlue,
    borderRadius: 10,
    marginVertical: 5,
    width: '100%',
    marginBottom: hp('-0.5%'),
  },
  contain: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 5,
        paddingLeft: 10,
        justifyContent: 'space-between',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 5,
        paddingLeft: 10,
        justifyContent: 'space-between',
      },
  primary: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'flex-start',
      },
  primaryInner: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        width: wp('52%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        width: wp('52%'),
      },
  icon: isTabDevice()
    ? {
        width: hp('2%'),
        height: hp('2%'),
        borderRadius: wp('100%'),
        // flexDirection: 'row',
        // alignItems: 'center',
        // justifyContent: 'center',
      }
    : {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      },
  iconContainer: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        marginRight: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        marginRight: wp('1%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      },
  star: isTabDevice()
    ? {
        width: hp('2%'),
        height: hp('2%'),
        marginLeft: wp('0.1%'),
        fontSize: wp('1%'),
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        position: 'absolute',
        top: wp('-0.2%'),
        left: 0,
        fontSize: wp('3%'),
      },
  attendanceContainer: isTabDevice()
    ? {
        display: 'flex',
        alignItems: 'flex-end',
      }
    : {
        width: wp('42%'),
        justifyContent: 'flex-start',
        alignItems: 'flex-end',
        marginTop: -wp('2%'),
      },
  attended: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3%'),
      },
  attendanceTextPresent: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('10%'),
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: wp('1%'),
        // marginTop: hp('2%'),
        // marginBottom: hp('2%'),
        width: wp('20%'),
        fontSize: wp('10%'),
        fontFamily: 'Poppins-Bold',
      },
  attendanceTextAbsent: isTabDevice()
    ? {
        color: colors.white,
        backgroundColor: colors.red,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('10%'),
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        backgroundColor: colors.red,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: wp('1%'),
        // marginTop: hp('2%'),
        // marginBottom: hp('2%'),
        width: wp('20%'),
        fontSize: wp('10%'),
        fontFamily: 'Poppins-Bold',
      },
  attendanceTextUnmarked: isTabDevice()
    ? {
        backgroundColor: colors.grey,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('10%'),
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        backgroundColor: colors.grey,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: wp('1%'),
        // marginTop: hp('2%'),
        // marginBottom: hp('2%'),
        width: wp('20%'),
        fontSize: wp('10%'),
        fontFamily: 'Poppins-Bold',
      },
});
export default EventItemStyle;
