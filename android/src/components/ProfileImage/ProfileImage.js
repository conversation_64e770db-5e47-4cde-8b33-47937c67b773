import React from 'react';
import ImagePerLoader from '../ImagePerLoader/ImagePerLoader';

export default function ProfileImage({
  imageStyles,
  style,
  profileImageUrl,
  viewParent = false,
}) {
  return (
    <ImagePerLoader
      imageStyles={imageStyles}
      style={style}
      source={
        profileImageUrl
          ? {
              uri: profileImageUrl,
            }
          : require('../../../assets/profilepictures/default_profile.png')
      }
    />
  );
  // TODO this cache img not work for android. need to fixe this
  // return (
  //   <ImagePerLoader
  //     imageStyles={imageStyles}
  //     style={style}
  //     source={profileImageUrl}
  //   />
  // );
}
