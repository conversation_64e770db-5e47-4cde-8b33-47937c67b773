import { Platform } from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const AddEventFormStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        height: hp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        opacity: 1,
        zIndex: 100,
      }
    : {
        width: wp('100%'),
        height: hp('105%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'flex-start',
        opacity: 1,
        zIndex: 100,
      },
  scrollView: isTabDevice()
    ? {
        marginTop: hp('10%'),
        height: hp('90%'),
      }
    : {
        marginTop: hp('8%'),
        height: hp('80%'),
      },
  overlay: {
    backgroundColor: colors.darkBlue,
    width: wp('100%'),
    height: hp('125%'),
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: 1,
  },
  wrapper: isTabDevice()
    ? {
        width: wp('70%'),
        height: '100%',
        flexDirection: 'column',
      }
    : {
        width: wp('90%'),
        // height: wp('68%'),
        flexDirection: 'column',
      },
  topRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
        position: 'relative',
        width: wp('74.5%')
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('5%'),
      },
  middleRow: {
    flexDirection: 'column',
  },
  dataRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('1%'),
        width: wp('74%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('4%'),
      },
  lastDataRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('1%'),
        width: wp('74%')
      }
    : {
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginBottom: wp('4%'),
      },
  leftRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: wp('19%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('5%'),
        zIndex: 10,
      },
  rightRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: wp('20%'),
      }
    : {
        flexDirection: 'row',
        // justifyContent: 'space-between',
        alignItems: 'center',
        width: wp('90%'),
      },
  requiredAstric: isTabDevice()
    ? {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: hp('0.7%'),
      }
    : {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2%'),
      },
  requiredAstric2: isTabDevice()
    ? {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: hp('0.7%'),
        position: 'absolute',
        top: 15,
        left: hp('18%'),
      }
    : {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: hp('0.7%'),
        position: 'absolute',
        top: 15,
        left: 155,
      },
  inputFieldWrapper: {
    width: '45%',
  },
  inputFieldWrapper2: {
    width: '60%',
  },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: hp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('1.5%'),
      },
  inputField: isTabDevice()
    ? {
        width: '100%',
        height: hp('6%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        width: '100%',
        height: wp('9%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },
  titleCol: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: isTabDevice()
    ? {
        fontSize: hp('2.7%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        // marginLeft: wp('2%'),
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        // marginLeft: wp('2%'),
      },
  titleWrapper: {
    maxWidth: '65%',
    width: wp('40%'),
  },
  buttonCol: isTabDevice() ? {
    flexDirection: 'row',
    alignItems: 'center',
  } : {
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnColored: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        marginRight: wp('1%'),
        borderRadius: wp('1%'),
        height: hp('6%'),
        width: hp('15%'),
        marginRight: hp('3%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        marginRight: wp('2%'),
        borderRadius: wp('2%'),
      },
  btnColored2: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        paddingTop: wp('0.8%'),
        paddingBottom: wp('0.8%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        marginRight: wp('2%'),
        borderRadius: wp('2%'),
      },
  btnColored1: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('0.8%'),
        paddingBottom: wp('0.8%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        marginRight: wp('1%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        marginRight: wp('2%'),
        borderRadius: wp('2%'),
      },
  btnTransparent: {
    //   backgroundColor: colors.aquaBlue,
    paddingTop: wp('1.5%'),
    paddingBottom: wp('1.5%'),
    paddingLeft: wp('3%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: hp('2%'),
        height: hp('6%'),
        width: hp('15%'),
        backgroundColor: colors.aquaBlue,
        textAlign: 'center',
        paddingTop: wp('0.8%'),
        borderRadius: hp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
        height: wp('8%'),
        width: wp('15%'),
        backgroundColor: colors.aquaBlue,
        textAlign: 'center',
        paddingTop: hp('1%'),
        borderRadius: hp('2%'),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
  btnText1: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
      },
  autoCompleteInput: isTabDevice()
    ? {
        color: colors.white,
        height: wp('4.5%'),
        width: wp('31.5%'),
        borderWidth: 0,
        paddingLeft: 10,
        position: 'relative',
        zIndex: 1,
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        height: wp('4.5%'),
        width: wp('31.5%'),
        borderWidth: 0,
        paddingLeft: 10,
        position: 'relative',
        zIndex: 1,
        fontSize: wp('3%'),
      },
  autoCompleteList: isTabDevice()
    ? {
        borderWidth: 0,
        padding: hp('1%'),
        backgroundColor: colors.borderBlue,
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 2,
        left: -hp('1.3%'),
        top: wp('3.7%'),
        height: hp('20%'),
        width: '100%',
      }
    : {
        borderWidth: 0,
        padding: Platform.OS === 'android' ? wp('2%') : wp('0.5%'),
        backgroundColor: colors.borderBlue,
        borderBottomLeftRadius: wp('2.5%'),
        borderBottomRightRadius: wp('2.5%'),
        position: 'absolute',
        zIndex: 2,
        left: Platform.OS === 'android' ? -10 : 0,
        top: 22,
        height: '100%',
        width: '100%',
      },
  autoCompleteInputContainer: {
    borderWidth: 0,
    // color: colors.white,
  },
  autoCompletelistContainerStyle: isTabDevice()
    ? {
        height: hp('5%'),
        width: '100%',
        position: 'absolute',
        left: 0,
        zIndex: 2,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        height: wp('30%'),
        width: '100%',
        position: 'absolute',
        left: 0,
        zIndex: 2,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  autoCompleteListItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: hp('1%'),
        height: hp('3%'),
        paddingBottom: hp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        height: wp('5%'),
        paddingBottom: wp('1%'),
      },
  autoCompleteText: isTabDevice()
    ? {
        height: hp('3%'),
        color: colors.white,
        marginTop: hp('1%'),
        fontSize: hp('2%'),
      }
    : {
        height: Platform.OS === 'android' ? wp('5%') : wp('5%'),
        color: colors.white,
        fontSize: wp('3%'),
      },
  inputFieldTextArea: isTabDevice()
    ? {}
    : {
        // height: wp('30%'),
      },
  /*----------------- DropDown styles ----------------*/
  dropdownView: isTabDevice()
    ? {
        width: '100%',
        borderRadius: wp('10%'),
        height: hp('6%'),
      }
    : {
        width: '100%',
        height: wp('9%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
      },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    // borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: hp('2%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('2.5%'),
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        borderColor: colors.borderBlue,
        height: hp('5%'),
        width: '100%',
      }
    : {
        height: wp('11%'),
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        height: hp('10%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,
        elevation: 10,
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        height: wp('20%'),
        marginTop: wp('-1.8%'),
        // padding: wp('1.5%'),
        borderBottomLeftRadius: wp('2.5%'),
        borderBottomRightRadius: wp('2.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,
        elevation: 10,
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: hp('2%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
      },
  dropdownTopArea: {
    ...(Platform.OS === 'android'
      ? {
          backgroundColor: colors.semiDarkBlue,
          borderColor: colors.transparent,
          height: hp('6%'),
        }
      : {
          backgroundColor: colors.transparent,
          borderColor: colors.transparent,
          width: '100%',
          position: 'absolute',
          zIndex: 5,
        }),
  },
  picker: {
    backgroundColor: colors.borderBlue,
    // color: 'white',
  },
  pickerRepeat: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        marginTop: wp('-11%'),
        marginLeft: wp('-0.3%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        marginTop: wp('-5%'),
      },
  dropdownViewRepeat: isTabDevice()
    ? {
        width: '100%',
        borderRadius: wp('10%'),
        height: hp('6%'),
      }
    : {
        width: '100%',
        height: wp('10%'),
        backgroundColor: colors.red,
        borderRadius: wp('2%'),
      },
  dropdownTopAreaRepeat: {
    backgroundColor: colors.red,
    borderColor: colors.transparent,
    height: wp('6%'),
  },
  dropdownRepeat: {
    justifyContent: 'flex-start',
  },
  dropdownSelectedContainerRepeat: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        borderColor: colors.borderBlue,
        width: '100%',
        height: hp('6%'),
      }
    : {
        borderRadius: 10,
        borderColor: colors.borderBlue,
        width: '100%',
        height: wp('10%'),
      },
  dropDownLabelRepeat: {
    color: colors.white,
    fontSize: wp('20%'),
  },
  placeholderStyleRepeat: {
    backgroundColor: colors.black,
    color: colors.lightGrey,
    fontSize: wp('40%'),
  },
  dropdownListRepeat: {
    backgroundColor: colors.borderBlue,
    borderColor: colors.borderBlue,
    height: hp('10%'),
    borderBottomLeftRadius: wp('1.5%'),
    borderBottomRightRadius: wp('1.5%'),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
    elevation: 10,
  },
  /*----------------- DropDown styles End----------------*/
  locationIcon: isTabDevice()
    ? {
        width: wp('1.7%'),
        height: wp('1.7%'),
        resizeMode: 'contain',
        position: 'absolute',
        left: wp('29%'),
        // top: wp('1%'),
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'contain',
        position: 'absolute',
        left: wp('35%'),
        // top: wp('1%'),
      },
  timerIcon: isTabDevice()
    ? {
        width: wp('1.7%'),
        height: wp('1.7%'),
        resizeMode: 'contain',
        position: 'absolute',
        left: wp('29%'),
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'contain',
        position: 'absolute',
        left: wp('35%'),
        top: wp('2.5%'),
      },
  calendarIcon: isTabDevice()
    ? {
        width: wp('1.5%'),
        height: wp('2%'),
        resizeMode: 'contain',
        // position: 'absolute',
        // left: hp('15%'),
        // top: hp('1.2%'),
      }
    : {
        width: wp('4.5%'),
        height: wp('5%'),
        resizeMode: 'contain',
        position: 'absolute',
        right: wp('2%'),
        top: hp('1.5%'),
      },
  timerText: isTabDevice()
    ? {
        fontSize: hp('2%'),
        color: colors.white,
        paddingLeft: wp('1%'),
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        paddingLeft: wp('3%'),
      },
  datePicker: {},
  text: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  textInput2: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('10%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        paddingLeft: wp('2%'),
        paddingTop: wp('1%'),
        paddingRight: wp('2%'),
        paddingBottom: wp('1%'),
        color: colors.white,
        fontSize: hp('2%'),
        textAlignVertical: 'top'
      }
    : {
        width: wp('85%'),
        height: wp('15%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        paddingLeft: wp('2%'),
        paddingTop: wp('2%'),
        paddingRight: wp('3%'),
        paddingBottom: wp('2%'),
        color: colors.white,
        fontSize: wp('3%'),
        textAlignVertical: 'top'
      },
  textInput: isTabDevice()
    ? {
        width: wp('70%'),
        height: hp('7%'),
      }
    : {
        width: wp('85%'),
        height: wp('15%'),
      },
  textInputWrapper: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        paddingLeft: wp('2%'),
        paddingTop: wp('1%'),
        paddingRight: wp('2%'),
        paddingBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        paddingLeft: wp('2%'),
        paddingTop: wp('2%'),
        paddingRight: wp('3%'),
        paddingBottom: wp('2%'),
      },
  checkboxText: isTabDevice()
    ? {
        fontSize: hp('2%'),
        color: colors.white,
        marginRight: wp('1%'),
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        marginRight: wp('5%'),
      },
  btnCalendar: isTabDevice()
    ? {
        width: hp('20%'),
        height: hp('6%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'relative',
      }
    : {
        width: wp('73%'),
        height: hp('6%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        paddingLeft: wp('3%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },
  btnCalendarText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  errorView: {},
  error: isTabDevice()
    ? {
        color: colors.red,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.red,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
      },
  loader: isTabDevice()
    ? {
        paddingLeft: 10,
        paddingRight: 10,
      }
    : {
        paddingLeft: 5,
        paddingRight: 5,
      },

  datePickerSelector: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'absolute',
        left: wp('27%'),
        top: wp('25%'),
      }
    : {
        width: wp('90%'),
        position: 'absolute',
        left: wp('02%'),
        top: wp('50%'),
      },
  datePickerWrapper: isTabDevice()
    ? {
        zIndex: 3,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
        // width: '5%',
      }
    : {
        zIndex: 3,
        width: wp('90%'),
        // backgroundColor: colors.white,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },
  datePicker: {
    // padding: wp('15%'),
  },
  locationText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        marginLeft: 10,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        marginLeft: 10,
        marginRight: wp('8%'),
      },
  locationTextWrapper: {
    width: '100%',
  },
  repeatEventDetailsContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: wp('1%'),
      }
    : { flexDirection: 'row', justifyContent: 'space-between' },

  repeatEventEndDateContainer: isTabDevice()
    ? {
        alignItems: 'flex-end',
        marginRight: wp('1%'),
      }
    : {},
  repeatEventDetailsFieldWrapper: isTabDevice()
    ? {
        width: '40%',
        flexDirection: 'row',
        alignItems: 'center',
        textAlign: 'center',
        justifyContent: 'flex-start',
      }
    : {
        width: '80%',
      },

  repeatEventDetailsInputField: isTabDevice()
    ? {
        width: '80%',
        height: hp('6%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        width: '100%',
        height: wp('9%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },

  repeatEventDetailsDateField: isTabDevice()
    ? {
        width: '90%',
        height: hp('6%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        width: '100%',
        height: wp('9%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },
  repeatEventDetailsLabelWrapper: isTabDevice()
    ? {
        marginRight: wp('1%'),
      }
    : {},
  repeatEventDetailsLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        marginTop: hp('1%'),

        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
      },
  repeatEventDetailsText: isTabDevice()
    ? {
        textAlign: 'center',
        width: '100%',
        fontSize: hp('2%'),
        color: '#ffff',
        justifyContent: 'center',
        fontFamily: 'Poppins-Regular',
      }
    : {
        textAlign: 'left',
        width: '70%',
        fontSize: wp('3%'),
        color: '#ffff',
        paddingLeft: wp('3%'),
        fontFamily: 'Poppins-Regular',
      },
  modalText: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'left',
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        width: wp('35%'),
      }
    : {
        marginBottom: 15,
        textAlign: 'left',
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        width: wp('70%'),
      },
  modalView: isTabDevice()
    ? {
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      }
    : {
        padding: wp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      },

  count: isTabDevice()
    ? { fontSize: wp('2%'), fontFamily: 'Poppins-Bold', color: colors.green }
    : {
        color: colors.green,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
});

export const customGooglePlaceInputStyle = colors => ({
  textInputContainer: {
    width: '100%',
    height: '100%',
    color: colors.white,
    paddingBottom: isTabDevice() ? 0 : wp('10%'),
  },
  row: isTabDevice()
    ? {
        height: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        width: wp('40%'),
        height: wp('7%'),
      },
  textInput: isTabDevice()
    ? {
        height: hp('6%'),
        backgroundColor: colors.transparent,
        color: colors.white,
        fontSize: hp('2%'),
        paddingLeft: wp('1%'),
      }
    : {
        height: hp('5%'),
        backgroundColor: colors.transparent,
        color: colors.white,
        fontSize: wp('3%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3.5%'),
      },
  listView: isTabDevice()
    ? {
        position: 'absolute',
        zIndex: 100,
        backgroundColor: colors.borderBlue,
        top: hp('6.5%'),
        width: hp('53%'),
        height: hp('27%'),
      }
    : {
        position: 'absolute',
        zIndex: 100,
        backgroundColor: colors.borderBlue,
        top: wp('10%'),
        width: wp('40%'),
        height: wp('30%'),
      },
  placeholder: {
    // color: colors.red,
  },
  googlelistItem: {
    // backgroundColor: colors.borderBlue,
    width: wp('25%'),
    height: wp('20%'),
    padding: 0,
  },
  googleListText: {
    fontSize: isTabDevice() ? hp('2%') : wp('3%'),
    color: colors.white,
  },
});

export default AddEventFormStyle;
