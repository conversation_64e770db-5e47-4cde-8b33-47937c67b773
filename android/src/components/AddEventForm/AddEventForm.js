import { AntDesign } from '@expo/vector-icons';
import React, { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Keyboard,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { Checkbox } from 'react-native-paper';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { TouchableItem } from 'react-native-tab-view';
import { useDispatch, useSelector } from 'react-redux';
import { GOOGLE_API_KEY, isTabDevice } from '../../config/appConfig';
import {
  addEventInfo,
  EventsErrorMessages,
  eventType,
  gameType,
  repeatEventTypes,
  repeatEventTypesValues,
} from '../../constants/constants';
import { dateTimeUTCConversion, openMap, stringLength } from '../../helpers/';
import { avoidedDuplicationData } from '../../helpers/common';
import {
  dateToString,
  getTimeHourMinWithoutSeconds,
} from '../../helpers/DateHelper';
import { useAddEventApiHook } from '../../hooks/PlannerAPIHook/useAddEventApiHook';
import useApi from '../../hooks/useApi';
import useApiPromise from '../../hooks/useApiPromise';
import useRecurringEventDelete from '../../hooks/useRecurringEventDelete';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import useStyles from '../../hooks/useStyles';
import {
  EVENT_DELETE_RESET,
  RESET_EVENT_RELATED_LIST_DATA,
} from '../../store/actionTypes/Event/EventActionTypes';
import { GooglePlacesAutocomplete } from '../GoogleLocationAutoComplete/GoogleLocationAutoComplete';
import EventDeleteModal from '../modal/DeleteModal/DeleteModal';
import SelectionModal from '../modal/SelectionModal/SelectionModal';
import customAddEventFormStyle, {
  customGooglePlaceInputStyle,
} from './AddEventFormStyle';
import DeleteRecurringEventModal from '../modal/DeleteRecurringEventModal/DeleteRecurringEventModal';
import DeleteRecurringConfirmationModal from '../modal/DeleteRecurringEventModal/DeleteRecurringConfirmationModal';
import { deleteRecurringEventTypes } from '../../constants/constants';
import { deleteEventMessages } from '../../constants/constants';

import { dateTimeConversion } from '../../helpers/';
const dateTimePickerSelections = {
  START_TIME_PICKER: 'START_TIME__PICKER',
  END_TIME_PICKER: 'END_TIME_PICKER',
  END_DATE_PICKER: 'END_DATE_PICKER',
  EVENT_DATE_PICKER: 'EVENT_DATE_PICKER',
};

const PLAYERS_NOT_FOUND_FOR_THE_GIVEN_TEAM =
  'PLAYERS_NOT_FOUND_FOR_THE_GIVEN_TEAM';
const START_DATE_NOT_FUTURE_DATE = 'START_DATE_NOT_FUTURE_DATE';

const AddEventForm = ({ eventName, eventTimestamp, modalAction, event }) => {
  const dispatch = useDispatch();
  const AddEventFormStyle = useStyles(customAddEventFormStyle);
  const googlePlaceInputStyle = useStyles(customGooglePlaceInputStyle);
  const ref = useRef();
  const [createEventData, setCreateEventData] = useState();
  const scrollViewRef = useRef(null);
  const [
    setIsRepeatEventModalOpen,
    isRepeatEventModalOpen,
    setSelectedRepeatEvent,
    selectedRepeatEvent,
  ] = useInputSelectModal();

  const [
    setIsGameTypeModalOpen,
    isGameTypeModalOpen,
    setSelectedGameType,
    selectedGameType,
  ] = useInputSelectModal();

  const [setIsTeamModalOpen, isTeamModalOpen, setSelectedTeam, selectedTeam] =
    useInputSelectModal();

  const [
    setIsTournamentModalOpen,
    isTournamentModalOpen,
    setSelectedTournament,
    selectedTournament,
    setIsNewDataAddingCompletedTournament,
    isNewDataAddingCompletedTournament,
  ] = useInputSelectModal();

  const [
    setIsOponentModalOpen,
    isOponentModalOpen,
    setSelectedOponent,
    selectedOponent,
    setIsNewDataAddingCompletedOpponent,
    isNewDataAddingCompletedOpponent,
  ] = useInputSelectModal();

  const [
    setIsSeasonModalOpen,
    isSeasonModalOpen,
    setSelectedSeason,
    selectedSeason,
    setIsNewDataAddingCompletedSeason,
    isNewDataAddingCompletedSeason,
  ] = useInputSelectModal();

  const [selectedLocation, setSelectedLocation] = useState({});
  const [inputNote, setInputNote] = useState('');

  const [selectedOptionMethod, setSelectedOptionMethod] = useState(null);
  const [
    deleteRepeatEventConfirmationMessage,
    setDeleteRepeatEventConfirmationMessage,
  ] = useState(null);
  const [
    isShowRecurringEventDeleteConfirmationModal,
    setIsShowRecurringEventDeleteConfirmationModal,
  ] = useState(false);

  const [isRepeatEventChecked, setIsRepeatEventChecked] = useState(false);
  const [selectedStartTime, setSelectedStartTime] = useState();
  const [selectedEndTime, setSelectedEndTime] = useState(null);
  const [selectedEndDate, setSelectedEndDate] = useState(null);

  useEffect(() => {
    if (ref.current?.getAddressText() === '') {
      handleCreateEventData('location', null);
      setSelectedLocation({});
    }
  }, [ref.current?.getAddressText()]);

  useEffect(() => {
    setSelectedRepeatEvent([repeatEventTypes[0]]);
  }, []);
  useEffect(() => {
    event &&
      setCreateEventData({
        startTime: eventTimestamp
          ? new Date(eventTimestamp)
          : new Date(event?.startTime),
        endTime: eventTimestamp
          ? new Date(eventTimestamp + 2 * 60 * 60 * 1000)
          : new Date(event?.endTime),
        endDate: eventTimestamp
          ? new Date(eventTimestamp + 2 * 60 * 60 * 1000)
          : new Date(event.endTime),
        matchType: event?.type || eventType.TRAINING,
        note: event?.note || '',
        teamId: event?.teamId || '',
        location: event?.location || '',
        tournamentId: event?.tournamentId || '',
        seasonId: event?.seasonId || '',
        opponentId: event?.opponentId || '',
        repeatEventType: 'daily',
        isRepeatEvent: false,
      });
  }, [eventTimestamp, event]);

  useEffect(() => {
    !event &&
      setCreateEventData({
        startTime: selectedStartTime
          ? new Date(selectedStartTime)
          : eventTimestamp
          ? roundUpwardsToTheClosestHalfHour(new Date(eventTimestamp), 'START')
          : new Date(event?.startTime),
        endTime: selectedEndTime
          ? new Date(selectedEndTime)
          : eventTimestamp
          ? roundUpwardsToTheClosestHalfHour(new Date(eventTimestamp), 'END')
          : new Date(event?.endTime),
        endDate: selectedEndDate
          ? new Date(selectedEndDate)
          : eventTimestamp
          ? roundUpwardsToTheClosestHalfHour(new Date(eventTimestamp), 'END')
          : new Date(event.endTime),
        matchType: selectedGameType?.[0]?.value || eventType.TRAINING,
        note: inputNote || '',
        teamId: selectedTeam?.[0]?.value || '',
        location: selectedLocation || '',
        tournamentId: selectedTournament?.[0]?.value || '',
        seasonId: selectedSeason?.[0]?.value || '',
        opponentId: selectedOponent?.[0]?.value || '',
        repeatEventType: selectedRepeatEvent?.[0]?.value || 'daily',
        isRepeatEvent: isRepeatEventChecked || false,
      });
  }, [eventTimestamp, selectedRepeatEvent, selectedEndTime, selectedEndDate]);

  const roundUpwardsToTheClosestHalfHour = (date, status) => {
    if (date && status) {
      const currentTime = new Date();
      const timeHours = new Date(currentTime).getHours();
      const timeMinutes = new Date(currentTime).getMinutes();

      const dateWithCurrentHours = new Date(date).setHours(timeHours);
      const dateWithCurrentHoursAndMinutes = new Date(
        dateWithCurrentHours
      ).setMinutes(timeMinutes);

      const timeString = new Date(
        dateWithCurrentHoursAndMinutes
      ).toTimeString();
      const timeStringArray = timeString.split(' ');
      let diff = 0;
      const timeArray = timeStringArray[0].split(':');
      if (timeArray[1] > 30) {
        diff = 60 - timeArray[1];
      } else if (timeArray[1] < 30) {
        diff = 30 - timeArray[1];
      }
      const defaultDate = new Date(dateWithCurrentHoursAndMinutes).setMinutes(
        new Date(dateWithCurrentHoursAndMinutes).getMinutes() + diff
      );
      if (status === 'END') {
        return new Date(defaultDate + 2 * 60 * 60 * 1000);
      } else {
        return defaultDate;
      }
    }
  };

  const [KeyboardAvoidingViewStyle, setKeyboardAvoidingViewStyle] = useState({
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
  });

  const [tournamentName, setTournamentName] = useState(
    event?.tournament?.name || ''
  );
  const [seasonName, setSeasonName] = useState(event?.season?.name || '');
  const [opponentName, setopponentName] = useState(event?.opponent?.name || '');
  const [teamName, setTeamName] = useState(
    event?.team?.teamName || 'Select Team'
  );
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);
  const [dateTimePickerSelection, setDateTimePickerSelection] = useState(null);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState('');
  const [isSubmit, setIsSubmit] = useState(false);
  const [formattedTeamData, setFormattedTeamData] = useState([]);
  const [eventRelatedList, setEventRelatedList] = useState([]);
  const [autoCompleteSelection, setAutoCompleteSelection] = useState('');
  const [isAutoCompleteResultHide, setIsAutoCompleteResultHide] = useState({
    tournamentResult: true,
    seasonResult: true,
    opponentResult: true,
  });
  const [isShowDeleteModal, setIsShowDeleteModal] = useState(false);
  const [isDeleteSubmit, setIsDeleteSubmit] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [focusAutoCompleteDropdown, setFocusAutoCompleteDropdown] =
    useState('');
  const [isLocationDropdownFocused, setIsLocationDropdownFocused] =
    useState(false);

  const {
    startTime,
    endTime,
    endDate,
    matchType,
    note,
    teamId,
    location,
    tournamentId,
    seasonId,
    opponentId,
    isRepeatEvent,
    repeatEventType,
  } = createEventData || {};

  const {
    eventRelatedListData = [],
    eventRelatedListTotalRecords,
    eventRelatedListPage,
    createEventLoading,
    createEventSuccess,
    createEventFailError,
    createEventRelatedListDataResult,
    createEventRelatedListDataFail,
    deleteEventSuccess,
    deleteEventLoading,
    deleteEventFailError,
  } = useSelector(state => state?.event);
  const { userData } = useSelector(state => state?.auth);
  const { teamData } = useSelector(state => state?.team);

  const gameTypeOptions = [
    {
      label: gameType.PRACTICE,
      value: eventType.TRAINING,
    },
    { label: gameType.MATCH, value: eventType.MATCH },
  ];

  const {
    getTeamDetails,
    getEventRelatedDetails,
    handleEvent,
    handleDeleteEvent,
    createEventRelatedListDataPromise,
    getLocation,
  } = useAddEventApiHook();
  const [apiCall] = useApi();
  const [deleteEventData, getEventCount] = useRecurringEventDelete();

  useEffect(() => {
    if (deleteEventSuccess && isDeleteSubmit) {
      setIsShowDeleteModal(false);
      modalAction(false);
      resetEventDeleteData();
    }
  }, [deleteEventSuccess]);

  const severErrorMsg =
    isSubmit &&
    createEventFailError &&
    (createEventFailError === PLAYERS_NOT_FOUND_FOR_THE_GIVEN_TEAM
      ? 'Sorry, you should have at least one player on the team before creating an event.'
      : createEventFailError === START_DATE_NOT_FUTURE_DATE
      ? 'Cannot create an event for a past time'
      : 'Event creation failed');

  useEffect(() => {
    getTeamDetails(userData);
  }, []);

  useEffect(() => {
    if (teamData?.data) {
      const formattedData = teamData.data.map(item => {
        return {
          label: item.teamName,
          value: item._id,
        };
      });
      setFormattedTeamData(formattedData);
    }
  }, [teamData]);

  useEffect(() => {
    if (createEventSuccess && isSubmit) {
      modalAction(false);
    }
  }, [createEventSuccess]);

  useEffect(() => {
    if (createEventRelatedListDataResult) {
      selectedDropDown(createEventRelatedListDataResult);
    }
  }, [createEventRelatedListDataResult]);

  useEffect(() => {
    if (createEventRelatedListDataFail) {
      selectedDropDown();
    }
  }, [createEventRelatedListDataFail]);

  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        handleKeyAvoidingViewStyle(hp('5%'));
      }
    );
    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    if (eventRelatedListData) {
      setEventRelatedList(eventRelatedListData);
    }
  }, [eventRelatedListData]);

  const customEventRelatedList = () => {
    return eventRelatedList?.map(item => ({
      value: item?._id,
      label: item?.name,
    }));
  };

  useEffect(() => {
    if (event?.location?.name) {
      ref.current?.setAddressText(event.location.name);
    }
  }, [isEdit]);

  const handleCreateEventData = (property, value) => {
    setError('');

    property === 'startTime'
      ? ManipulateCreateEventDate(value)
      : setCreateEventData({ ...createEventData, [property]: value });
  };
  const getEventRelatedList = (type, searchKey, page = 1) => {
    if (searchKey == '') {
      switch (type) {
        case addEventInfo.TOURNAMENTS:
          handleCreateEventData('tournamentId', null);
          break;
        case addEventInfo.SEASONS:
          handleCreateEventData('seasonId', null);
          break;
        case addEventInfo.OPPONENTS:
          handleCreateEventData('opponentId', null);
          break;
      }
    }

    getEventRelatedDetails(type, searchKey, page);
  };

  const selectedDropDown = item => {
    if (item) {
      const selectedItem = item?.[0];
      if (autoCompleteSelection === addEventInfo.TOURNAMENTS) {
        handleCreateEventData('tournamentId', selectedItem?.value);
        setTournamentName(selectedItem?.label);
      } else if (autoCompleteSelection === addEventInfo.SEASONS) {
        handleCreateEventData('seasonId', selectedItem?.value);
        setSeasonName(selectedItem?.label);
      } else if (autoCompleteSelection === addEventInfo.OPPONENTS) {
        handleCreateEventData('opponentId', selectedItem?.value);
        setopponentName(selectedItem?.label);
      }
    }
    setAutoCompleteSelection('');
  };

  const handleKeyAvoidingViewStyle = topValue => {
    const tempKeyboardAvoidingViewStyle = { ...KeyboardAvoidingViewStyle };
    tempKeyboardAvoidingViewStyle.top = topValue;
    setKeyboardAvoidingViewStyle(tempKeyboardAvoidingViewStyle);
  };

  const addEvent = () => {
    setIsError(false);
    setIsSubmit(true);

    const createEventData = {
      startTime: formatDate(startTime),
      endTime: formatDate(endTime),
      location: {
        name: location?.name || location?.formatted_address,
        latitude: `${location?.latitude || location?.geometry?.location?.lat}`,
        longitude: `${
          location?.longitude || location?.geometry?.location?.lng
        }`,
      },
      note: note,
      type: typeof matchType === 'string' ? matchType : matchType.value,
      teamId,
      repeatConfig: isRepeatEvent
        ? {
            startTime: formatDate(startTime),
            endDate: formatDate(endDate),
            frequency: repeatEventType,
          }
        : null,
    };
    if (matchType === eventType.MATCH || matchType?.value === eventType.MATCH) {
      createEventData.tournamentId = tournamentId;
      createEventData.opponentId = opponentId;
      createEventData.seasonId = seasonId;
    }
    if (event?._id) {
      createEventData._id = event._id;
    }
    if (event?.name) {
      createEventData.name = event.name;
    }

    handleEvent(isEdit, createEventData, event);
  };

  const formatDate = date => {
    if (date) {
      const formattedDate = new Date(date).setSeconds(0);
      return new Date(formattedDate).toISOString();
    }
  };

  const onPressSave = () => {
    setError('');
    if (
      matchType &&
      (matchType !== eventType.MATCH ||
        (tournamentId && seasonId && opponentId)) &&
      teamId &&
      startTime &&
      endTime &&
      (!isRepeatEvent || (repeatEventType && endDate))
    ) {
      addEvent();
    } else {
      setError('Please provide required fields');
    }
  };

  const findDateTimePickerValue = () => {
    if (
      dateTimePickerSelection === dateTimePickerSelections.START_TIME_PICKER
    ) {
      return new Date(startTime);
    } else if (
      dateTimePickerSelection === dateTimePickerSelections.EVENT_DATE_PICKER
    ) {
      return startTime;
    } else if (
      dateTimePickerSelection === dateTimePickerSelections.END_TIME_PICKER
    ) {
      return new Date(endTime);
    }
    return endDate;
  };

  const handleDateTimePicker = selectedDateTime => {
    setShow(false);
    setDateTimePickerSelection(null);
    if (
      dateTimePickerSelection === dateTimePickerSelections.START_TIME_PICKER
    ) {
      handleCreateEventData('startTime', selectedDateTime);
      setSelectedStartTime(selectedDateTime);
    } else if (
      dateTimePickerSelection === dateTimePickerSelections.END_TIME_PICKER
    ) {
      handleCreateEventData('endTime', selectedDateTime);
      setSelectedEndTime(selectedDateTime);
    } else if (
      dateTimePickerSelection === dateTimePickerSelections.EVENT_DATE_PICKER
    ) {
      handleCreateEventData('startTime', selectedDateTime);
      setSelectedStartTime(selectedDateTime);
    } else {
      handleCreateEventData('endDate', selectedDateTime);
      setSelectedEndDate(selectedDateTime);
    }
    setDateTimePickerSelection(null);
  };
  const showMode = currentMode => {
    setShow(true);
    setMode(currentMode);
  };

  const showTimepicker = type => {
    setDateTimePickerSelection(type);
    switch (type) {
      case dateTimePickerSelections.END_DATE_PICKER:
        isRepeatEvent && showMode('date');
        break;

      case dateTimePickerSelections.START_TIME_PICKER:
        showMode('time');
        break;

      case dateTimePickerSelections.END_TIME_PICKER:
        showMode('datetime');
        break;
      case dateTimePickerSelections.EVENT_DATE_PICKER:
        showMode('date');
        break;

      default:
        break;
    }
  };
  const ManipulateCreateEventDate = newDate => {
    setCreateEventData({
      ...createEventData,
      startTime: newDate,
      endTime: new Date(new Date(newDate).getTime() + 2 * 60 * 60 * 1000),

      endDate: new Date(new Date(newDate).getTime() + 2 * 60 * 60 * 1000),
    });

    setIsRepeatEventChecked(false);
    setSelectedRepeatEvent([repeatEventTypes[0]]);
    setSelectedStartTime(newDate);

    setSelectedEndTime(
      new Date(new Date(newDate).getTime() + 2 * 60 * 60 * 1000)
    );
    setSelectedEndDate(
      new Date(new Date(newDate).getTime() + 2 * 60 * 60 * 1000)
    );
  };

  const getLocationLonLat = location => {
    location &&
      location.place_id &&
      getLocation(location, GOOGLE_API_KEY)
        .then(res => res.json())
        .then(data => {
          const tempLocation = {
            ...data.results[0],
            formatted_address: location?.description,
          };
          handleCreateEventData('location', tempLocation);
          setSelectedLocation(tempLocation);
        });
  };

  const handleCreateEventDataWithChangeEndDate = (property, value) => {
    setError('');

    let { timestamp } = dateTimeUTCConversion(startTime);
    let finalTimeStamp = '';
    switch (value) {
      case repeatEventTypesValues.DAILY:
        finalTimeStamp = timestamp + 1 * 24 * 60 * 60 * 1000;

        break;

      case repeatEventTypesValues.WEEKLY:
        finalTimeStamp = timestamp + 7 * 24 * 60 * 60 * 1000;
        break;

      case repeatEventTypesValues.BI_WEEKLY:
        finalTimeStamp = timestamp + 14 * 24 * 60 * 60 * 1000;
        break;

      case repeatEventTypesValues.MONTHLY:
        finalTimeStamp = timestamp + 31 * 24 * 60 * 60 * 1000;
        break;
    }

    setSelectedEndDate(new Date(finalTimeStamp));

    setCreateEventData({
      ...createEventData,
      [property]: value,
      endDate: new Date(finalTimeStamp),
    });
  };

  const onHandleDeleteEvent = () => {
    setIsDeleteSubmit(true);
    handleDeleteEvent(event);
  };

  const resetEventDeleteData = () => {
    dispatch({
      type: EVENT_DELETE_RESET,
    });
  };

  const isView = event?._id && !isEdit;

  //reset tournament, season and oponent state when match type practice is selected
  useEffect(() => {
    if (selectedGameType?.[0]?.value === eventType.TRAINING) {
      setSelectedTournament([]);
      setSelectedSeason([]);
      setSelectedOponent([]);
    }
  }, [selectedGameType]);

  const [prevSelectedDataOnCreation, setPrevSelectedDataOnCreation] = useState({
    tournamentId,
    teamId,
    seasonId,
    opponentId,
  });

  useEffect(() => {
    setSelectedGameType([gameTypeOptions[0]]);
    if (matchType === eventType.MATCH) {
      setSelectedGameType([gameTypeOptions[1]]);
    }
  }, [matchType]);

  useEffect(() => {
    matchType &&
      setPrevSelectedDataOnCreation(state => ({
        ...state,
        tournamentId,
        teamId,
        seasonId,
        opponentId,
      }));
  }, [matchType]);

  const renderDefaultValue = (
    selectedOption,
    prevSelectedOption,
    defaultOption
  ) => {
    return isView || isEdit
      ? (selectedOption?.length && [selectedOption?.[0]?.value]) || [
          prevSelectedOption,
        ]
      : (selectedOption?.length && [selectedOption?.[0]?.value]) || [
          defaultOption?.[0]?.value,
        ];
  };

  const renderItemLabel = (selectedOption, defaultOption) => {
    const isValueUniqueId =
      selectedOption?.[0]?.label !== selectedOption?.[0]?.value;

    return isView || isEdit
      ? selectedOption?.length
        ? (isValueUniqueId && selectedOption?.[0]?.label) || ''
        : defaultOption
      : (isValueUniqueId && selectedOption?.[0]?.label) || '';
  };

  const clearEventData = () => {
    dispatch({
      type: RESET_EVENT_RELATED_LIST_DATA,
    });
  };

  const getEventRelatedData = (value, eventType, result) => {
    if (value) {
      getEventRelatedList(eventType, '');
      setAutoCompleteSelection(eventType);
      setIsAutoCompleteResultHide({
        ...isAutoCompleteResultHide,
        [result]: false,
      });
    } else {
      setIsAutoCompleteResultHide({
        ...isAutoCompleteResultHide,
        [result]: true,
      });
      clearEventData();
    }
  };

  const onReachEndHandler = eventType => {
    if (eventRelatedListTotalRecords > eventRelatedListData?.length || 0) {
      getEventRelatedList(eventType, '', eventRelatedListPage + 1);
    }
  };

  const showDeleteRecurringEventConfirmationMessage = async () => {
    if (selectedOptionMethod != null) {
      let futureEventData = await getEventCount(
        selectedOptionMethod,
        event?._id
      );
      let count;

      if (futureEventData.hasOwnProperty('count')) {
        count = parseInt(futureEventData.count);
      }

      switch (selectedOptionMethod) {
        case deleteRecurringEventTypes.singleEvent: {
          setDeleteRepeatEventConfirmationMessage(
            deleteEventMessages.confirmationMessage
          );
          break;
        }

        case deleteRecurringEventTypes.currentAndFollowing: {
          setDeleteRepeatEventConfirmationMessage(
            futureEventData.hasOwnProperty('count') ? (
              count - 1 <= 0 ? (
                deleteEventMessages.confirmationMessage
              ) : (
                <View style={AddEventFormStyle.modalView}>
                  <Text style={AddEventFormStyle.modalText}>
                    {deleteEventMessages.currentAndFollowing}
                    <Text style={AddEventFormStyle.count}>
                      {count - 1}
                      <Text style={AddEventFormStyle.modalText}>
                        {' events?'}
                      </Text>
                    </Text>
                  </Text>
                </View>
              )
            ) : (
              deleteEventMessages.confirmationMessage
            )
          );
          break;
        }
        case deleteRecurringEventTypes.allEvents: {
          setDeleteRepeatEventConfirmationMessage(
            futureEventData.hasOwnProperty('message') ? (
              deleteEventMessages.deleteAllMessage
            ) : count - 1 <= 0 ? (
              deleteEventMessages.confirmationMessage
            ) : (
              <View style={AddEventFormStyle.modalView}>
                <Text style={AddEventFormStyle.modalText}>
                  {deleteEventMessages.currentAndFollowing}
                  <Text style={AddEventFormStyle.count}>
                    {count}
                    <Text style={AddEventFormStyle.modalText}>
                      {' events?'}
                    </Text>
                  </Text>
                </Text>
              </View>
            )
          );

          break;
        }
      }
    }
    if (selectedOptionMethod) {
      setIsShowDeleteModal(false);
      setTimeout(() => {
        setIsShowRecurringEventDeleteConfirmationModal(true);
      }, 0);
    }
  };

  const deleteRecurringEvents = () => {
    deleteEventData(selectedOptionMethod, event?._id);
    setIsDeleteSubmit(true);
  };

  const renderRepeatEventFrequency = () => {
    const frequency = event?.repeatConfig?.frequency;
    return frequency.charAt(0).toUpperCase() + frequency.slice(1);
  };
  const renderRepeatEventEndDate = () => {
    const { year, month, date } = dateTimeConversion(
      event?.repeatConfig?.endDate
    );
    return ` ${date}/${month}/${year}`;
  };
  return (
    <KeyboardAwareScrollView
      extraScrollHeight={isTabDevice() ? 0 : hp('5%')}
      enableOnAndroid
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <ScrollView style={AddEventFormStyle.scrollView} ref={scrollViewRef}>
          <View style={AddEventFormStyle.container}>
            {/* <View style={AddEventFormStyle.overlay}></View> */}
            <View style={AddEventFormStyle.wrapper}>
              <View style={AddEventFormStyle.topRow}>
                <View style={AddEventFormStyle.titleCol}>
                  <AntDesign
                    name="arrowleft"
                    size={30}
                    color="#36d982"
                    onPress={() => modalAction(false)}
                    style={{ marginRight: isTabDevice() ? hp('2%') : wp('2%') }}
                  />
                  <View style={AddEventFormStyle.titleWrapper}>
                    <Text style={AddEventFormStyle.title}>
                      {isView
                        ? `View Event` 
                        : event?._id && isEdit
                        ? `Edit An Event`
                        : `Create An Event`}
                    </Text>
                    {error || severErrorMsg ? (
                      <View style={AddEventFormStyle.errorView}>
                        <Text style={AddEventFormStyle.error}>
                          {error || severErrorMsg}
                        </Text>
                      </View>
                    ) : null}
                  </View>
                </View>
                {event?._id ? (
                  new Date(event.startTime) > new Date() ? (
                    <View style={AddEventFormStyle.buttonCol}>
                      <TouchableWithoutFeedback
                        onPress={() =>
                          event._id && !isEdit
                            ? setIsEdit(true)
                            : !createEventLoading && onPressSave()
                        }
                        style={AddEventFormStyle.btnColored}
                      >
                        <Text style={AddEventFormStyle.btnText}>
                          {!createEventLoading
                            ? isEdit
                              ? 'Save'
                              : 'Edit'
                            : ''}
                          {createEventLoading && (
                            <ActivityIndicator
                              size="small"
                              color="#FFF"
                              style={{
                                ...AddEventFormStyle.loader,
                                paddingLeft: hp('2%'),
                              }}
                            />
                          )}
                        </Text>
                      </TouchableWithoutFeedback>
                      <TouchableWithoutFeedback
                        onPress={() => setIsShowDeleteModal(true)}
                      >
                        <Text
                          style={{
                            ...AddEventFormStyle.btnText,
                            backgroundColor: 'transparent',
                          }}
                        >
                          Delete
                        </Text>
                      </TouchableWithoutFeedback>
                    </View>
                  ) : null
                ) : (
                  <View style={AddEventFormStyle.buttonCol}>
                    <TouchableOpacity
                      onPress={() => !createEventLoading && onPressSave()}
                      style={AddEventFormStyle.btnColored1}
                    >
                      <Text style={AddEventFormStyle.btnText1}>
                        {createEventLoading ? '' : 'Save'}
                        {createEventLoading && (
                          <ActivityIndicator
                            size="small"
                            color="#FFF"
                            style={AddEventFormStyle.loader}
                          />
                        )}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => modalAction(false)}
                      style={AddEventFormStyle.btnColored2}
                    >
                      <Text style={AddEventFormStyle.btnText1}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
              <View style={AddEventFormStyle.middleRow}>
                <View style={{ ...AddEventFormStyle.dataRow, zIndex: 10 }}>
                  <View
                    style={{
                      ...AddEventFormStyle.inputFieldWrapper,
                      opacity: (event?._id ? false : !isView) ? 1 : 0.5,
                    }}
                  >
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Game Type
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <View style={AddEventFormStyle.inputField}>
                      <SelectionModal
                        title={`Select Game Type`}
                        items={gameTypeOptions}
                        onCloseHook={setIsGameTypeModalOpen}
                        onSelectItemHook={item => {
                          setSelectedGameType(item);
                          handleCreateEventData('matchType', item?.[0]?.value);
                        }}
                        selectedItemLabel={
                          isView || isEdit
                            ? matchType === eventType.MATCH
                              ? gameType.MATCH
                              : gameType.PRACTICE
                            : selectedGameType?.[0]?.label
                        }
                        isModalOpen={isGameTypeModalOpen}
                        disableOnPress={isView || isEdit}
                        defaultValues={renderDefaultValue(
                          selectedGameType,
                          matchType,
                          gameTypeOptions?.[0]?.value
                        )}
                        selectFirstOptionOnInitialRender
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      ...AddEventFormStyle.inputFieldWrapper,
                      opacity: (
                        event?._id
                          ? false
                          : (matchType === eventType.MATCH ||
                              matchType?.value === eventType.MATCH) &&
                            !isView
                      )
                        ? 1
                        : 0.5,
                    }}
                  >
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Tournament
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <View
                      style={{
                        ...AddEventFormStyle.inputField,
                      }}
                      pointerEvents={
                        focusAutoCompleteDropdown &&
                        focusAutoCompleteDropdown !== 'Tournament'
                          ? 'none'
                          : 'auto'
                      }
                    >
                      <SelectionModal
                        title={`Select Tournament`}
                        items={avoidedDuplicationData(
                          selectedTournament,
                          customEventRelatedList(),
                          'value'
                        )}
                        onCloseHook={value => {
                          setIsTournamentModalOpen(value);
                          getEventRelatedData(
                            value,
                            addEventInfo.TOURNAMENTS,
                            'tournamentResult'
                          );
                        }}
                        onSelectItemHook={item => {
                          selectedDropDown(item);
                          setSelectedTournament(item);
                        }}
                        isEnableAutoComplete
                        selectedItemLabel={renderItemLabel(
                          selectedTournament,
                          tournamentName
                        )}
                        isEnableAddNew
                        isModalOpen={isTournamentModalOpen}
                        disableOnPress={
                          selectedGameType?.[0]?.value === eventType.TRAINING ||
                          isView ||
                          isEdit
                        }
                        onAutoCompleteChange={searchText => {
                          setIsNewDataAddingCompletedTournament(false);
                          getEventRelatedList(
                            addEventInfo.TOURNAMENTS,
                            searchText
                          );
                          setAutoCompleteSelection(addEventInfo.TOURNAMENTS);
                        }}
                        defaultValues={renderDefaultValue(
                          selectedTournament,
                          prevSelectedDataOnCreation?.tournamentId,
                          customEventRelatedList()?.[0]?.value
                        )}
                        onReachEndHandler={() => {
                          onReachEndHandler(addEventInfo.TOURNAMENTS);
                        }}
                        isNewDataAdding={isNewDataAddingCompletedTournament}
                        addNewAction={newContent => {
                          createEventRelatedListDataPromise(
                            addEventInfo.TOURNAMENTS,
                            newContent
                          )
                            .then(res => {
                              const { _id, name } = res?.data || {};
                              _id &&
                                setIsNewDataAddingCompletedTournament(true);
                              setEventRelatedList([{ _id, name }]);
                            })
                            .catch();
                        }}
                      />
                    </View>
                  </View>
                </View>
                <View style={{ ...AddEventFormStyle.dataRow, zIndex: 9 }}>
                  <View
                    style={{
                      ...AddEventFormStyle.inputFieldWrapper,
                      opacity: (event?._id ? false : !isView) ? 1 : 0.5,
                    }}
                  >
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Team
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <View style={AddEventFormStyle.inputField}>
                      {!!formattedTeamData.length && (
                        <SelectionModal
                          title={`Select Team`}
                          items={formattedTeamData || []}
                          onCloseHook={setIsTeamModalOpen}
                          onSelectItemHook={item => {
                            handleCreateEventData('teamId', item?.[0]?.value);
                            setSelectedTeam(item);
                          }}
                          isEnableAutoComplete
                          selectedItemLabel={renderItemLabel(
                            selectedTeam,
                            teamName
                          )}
                          isModalOpen={isTeamModalOpen}
                          disableOnPress={isView || isEdit}
                          defaultValues={renderDefaultValue(
                            selectedTeam,
                            prevSelectedDataOnCreation?.teamId,
                            formattedTeamData?.[0]?.value
                          )}
                        />
                      )}
                    </View>
                  </View>

                  <View
                    style={{
                      ...AddEventFormStyle.inputFieldWrapper,
                      opacity: (
                        event?._id
                          ? false
                          : (matchType === eventType.MATCH ||
                              matchType?.value === eventType.MATCH) &&
                            !isView
                      )
                        ? 1
                        : 0.5,
                    }}
                  >
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Season
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <View
                      style={AddEventFormStyle.inputField}
                      pointerEvents={
                        focusAutoCompleteDropdown &&
                        focusAutoCompleteDropdown !== 'Season'
                          ? 'none'
                          : 'auto'
                      }
                    >
                      <SelectionModal
                        title={`Select Season`}
                        items={avoidedDuplicationData(
                          selectedSeason,
                          customEventRelatedList(),
                          'value'
                        )}
                        onCloseHook={value => {
                          setIsSeasonModalOpen(value);
                          getEventRelatedData(
                            value,
                            addEventInfo.SEASONS,
                            'seasonResult'
                          );
                        }}
                        onSelectItemHook={item => {
                          selectedDropDown(item);
                          setSelectedSeason(item);
                        }}
                        isEnableAutoComplete
                        selectedItemLabel={renderItemLabel(
                          selectedSeason,
                          seasonName
                        )}
                        isModalOpen={isSeasonModalOpen}
                        disableOnPress={
                          selectedGameType?.[0]?.value === eventType.TRAINING ||
                          isView ||
                          isEdit
                        }
                        onAutoCompleteChange={searchText => {
                          setIsNewDataAddingCompletedSeason(false);
                          getEventRelatedList(addEventInfo.SEASONS, searchText);
                          setAutoCompleteSelection(addEventInfo.SEASONS);
                        }}
                        defaultValues={renderDefaultValue(
                          selectedSeason,
                          prevSelectedDataOnCreation?.seasonId,
                          customEventRelatedList()?.[0]?.value
                        )}
                        onReachEndHandler={() => {
                          onReachEndHandler(addEventInfo.SEASONS);
                        }}
                        isEnableAddNew
                        isNewDataAdding={isNewDataAddingCompletedSeason}
                        addNewAction={newContent => {
                          createEventRelatedListDataPromise(
                            addEventInfo.SEASONS,
                            newContent
                          )
                            .then(res => {
                              const { _id, name } = res?.data || {};
                              _id && setIsNewDataAddingCompletedSeason(true);
                              setEventRelatedList([{ _id, name }]);
                            })
                            .catch();
                        }}
                      />
                    </View>
                  </View>
                </View>
                <View style={{ ...AddEventFormStyle.dataRow, zIndex: 8 }}>
                  <View style={AddEventFormStyle.inputFieldWrapper}>
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Location
                      </Text>
                    </View>
                    <View style={AddEventFormStyle.inputField}>
                      {!isView ? (
                        <GooglePlacesAutocomplete
                          ref={ref}
                          nearbyPlacesAPI="GooglePlacesSearch"
                          styles={googlePlaceInputStyle}
                          placeholder="Location"
                          placeholderColor="#595959"
                          onPress={data => {
                            getLocationLonLat(data);
                          }}
                          onFail={error =>
                            console.log('Google API ERROR :', error)
                          }
                          query={{
                            key: GOOGLE_API_KEY,
                            language: 'en',
                            types: ['address', 'establishment'],
                            // components: `country:${DEFAULT_MAP_COUNTRY}`,
                          }}
                          enableHighAccuracyLocation={true}
                          renderRow={rowData => {
                            const description = rowData.description;
                            return (
                              <View
                                style={{
                                  height: isTabDevice() ? hp('5%') : wp('25%'),
                                }}
                              >
                                <Text
                                  style={googlePlaceInputStyle.googleListText}
                                  numberOfLines={1}
                                  ellipsizeMode="tail"
                                >
                                  {description}
                                </Text>
                              </View>
                            );
                          }}
                          GooglePlacesDetailsQuery={{
                            fields: 'formatted_address',
                          }}
                          debounce={300}
                          disableScroll={false}
                          textInputProps={{
                            onFocus: () => {
                              setIsLocationDropdownFocused(true);
                            },
                            onBlur: () => setIsLocationDropdownFocused(false),
                          }}
                        />
                      ) : (
                        <TouchableItem
                          onPress={() => openMap(event?.location)}
                          style={AddEventFormStyle.locationTextWrapper}
                        >
                          <Text
                            numberOfLines={1}
                            ellipsizeMode="tail"
                            style={AddEventFormStyle.locationText}
                          >
                            {stringLength(event?.location?.name, 45)}
                          </Text>
                        </TouchableItem>
                      )}
                      <Image
                        style={AddEventFormStyle.locationIcon}
                        source={require('../../../assets/icons/locationIcon.png')}
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      ...AddEventFormStyle.inputFieldWrapper,
                      opacity:
                        matchType === eventType.MATCH ||
                        matchType?.value === eventType.MATCH
                          ? 1
                          : 0.5,
                    }}
                  >
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Opponent
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <View
                      style={AddEventFormStyle.inputField}
                      pointerEvents={
                        focusAutoCompleteDropdown &&
                        focusAutoCompleteDropdown !== 'Opponent'
                          ? 'none'
                          : 'auto'
                      }
                    >
                      <SelectionModal
                        title={`Select Opponent`}
                        items={avoidedDuplicationData(
                          selectedOponent,
                          customEventRelatedList(),
                          'value'
                        )}
                        onCloseHook={value => {
                          setIsOponentModalOpen(value);
                          getEventRelatedData(
                            value,
                            addEventInfo.OPPONENTS,
                            'opponentResult'
                          );
                        }}
                        onSelectItemHook={item => {
                          selectedDropDown(item);
                          setSelectedOponent(item);
                        }}
                        isEnableAutoComplete
                        selectedItemLabel={renderItemLabel(
                          selectedOponent,
                          opponentName
                        )}
                        isModalOpen={isOponentModalOpen}
                        disableOnPress={
                          selectedGameType?.[0]?.value === eventType.TRAINING ||
                          isView
                        }
                        onAutoCompleteChange={searchText => {
                          setIsNewDataAddingCompletedOpponent(false);
                          getEventRelatedList(
                            addEventInfo.OPPONENTS,
                            searchText
                          );
                          setAutoCompleteSelection(addEventInfo.OPPONENTS);
                        }}
                        defaultValues={renderDefaultValue(
                          selectedOponent,
                          prevSelectedDataOnCreation?.opponentId,
                          customEventRelatedList()?.[0]?.value
                        )}
                        onReachEndHandler={() => {
                          onReachEndHandler(addEventInfo.OPPONENTS);
                        }}
                        isEnableAddNew
                        isNewDataAdding={isNewDataAddingCompletedOpponent}
                        addNewAction={newContent => {
                          createEventRelatedListDataPromise(
                            addEventInfo.OPPONENTS,
                            newContent
                          )
                            .then(res => {
                              const { _id, name } = res?.data || {};
                              _id && setIsNewDataAddingCompletedOpponent(true);
                              setEventRelatedList([{ _id, name }]);
                            })
                            .catch();
                        }}
                      />
                    </View>
                  </View>
                </View>

                <View style={{ ...AddEventFormStyle.dataRow, zIndex: 7 }}>
                  <View style={AddEventFormStyle.inputFieldWrapper}>
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Event Date
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <TouchableOpacity
                      onPress={() =>
                        showTimepicker(
                          dateTimePickerSelections.EVENT_DATE_PICKER
                        )
                      }
                      style={AddEventFormStyle.inputField}
                      disabled={isView}
                    >
                      <Text style={AddEventFormStyle.timerText}>
                        {dateToString(new Date(startTime))}
                      </Text>

                      <Image
                        style={AddEventFormStyle.timerIcon}
                        source={require('../../../assets/icons/calendarIcon.png')}
                      />
                    </TouchableOpacity>
                  </View>
                  <View style={AddEventFormStyle.inputFieldWrapper}>
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        Start Time
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <TouchableOpacity
                      onPress={() =>
                        showTimepicker(
                          dateTimePickerSelections.START_TIME_PICKER
                        )
                      }
                      disabled={isView}
                      style={AddEventFormStyle.inputField}
                    >
                      <Text style={AddEventFormStyle.timerText}>
                        {startTime &&
                          getTimeHourMinWithoutSeconds(
                            new Date(startTime).toTimeString()
                          )}
                      </Text>

                      <Image
                        style={AddEventFormStyle.timerIcon}
                        source={require('../../../assets/icons/timerIcon.png')}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={{ ...AddEventFormStyle.dataRow, zIndex: 7 }}>
                  <View style={AddEventFormStyle.inputFieldWrapper}>
                    <View style={AddEventFormStyle.inputFieldLabelWrapper}>
                      <Text style={AddEventFormStyle.inputFieldLabel}>
                        End Time
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEventFormStyle.requiredAstric}
                      />
                    </View>
                    <TouchableOpacity
                      style={AddEventFormStyle.inputField}
                      onPress={() =>
                        showTimepicker(dateTimePickerSelections.END_TIME_PICKER)
                      }
                      disabled={isView}
                    >
                      <Text style={AddEventFormStyle.timerText}>
                        {getTimeHourMinWithoutSeconds(
                          new Date(endTime).toTimeString()
                        )}
                      </Text>
                      <Image
                        style={AddEventFormStyle.timerIcon}
                        source={require('../../../assets/icons/timerIcon.png')}
                      />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={AddEventFormStyle.dataRow}>
                  <View
                    style={AddEventFormStyle.inputFieldTextArea}
                    pointerEvents={
                      isLocationDropdownFocused || focusAutoCompleteDropdown
                        ? 'none'
                        : 'auto'
                    }
                  >
                    {isView ? (
                      <View style={AddEventFormStyle.textInputWrapper}>
                        <ScrollView style={AddEventFormStyle.textInput}>
                          <Text style={AddEventFormStyle.text}>{note}</Text>
                        </ScrollView>
                      </View>
                    ) : (
                      <TextInput  
                        // onTouchStart={() => {
                        //   handleKeyAvoidingViewStyle(isTabDevice() ? -250 : -150);
                        // }}
                        style={AddEventFormStyle.textInput2}
                        value={note}
                        onChangeText={text => {
                          handleCreateEventData('note', text);
                          setInputNote(text);
                        }}
                        multiline={true}
                        placeholder={'Notes'}
                        placeholderTextColor="#595959"
                        disableFullscreenUI={true}
                        returnKeyType={'done'}
                        blurOnSubmit={false}
                        editable={!isView}
                      />
                    )}
                  </View>
                </View>

                {!isView && !isEdit && (
                  <View style={AddEventFormStyle.lastDataRow}>
                    <View style={AddEventFormStyle.leftRow}>
                      <Checkbox.Android
                        style={AddEventFormStyle.checkbox}
                        uncheckedColor="white"
                        status={isRepeatEvent ? 'checked' : 'unchecked'}
                        onPress={() => {
                          handleCreateEventData(
                            'isRepeatEvent',
                            !isRepeatEvent
                          );
                          setIsRepeatEventChecked(!isRepeatEvent);
                        }}
                      />

                      <Text style={AddEventFormStyle.checkboxText}>
                        Repeat Event
                      </Text>
                      {isRepeatEvent && (
                        <AntDesign
                          name="star"
                          size={10}
                          color="black"
                          style={AddEventFormStyle.requiredAstric2}
                        />
                      )}
                      <View
                        style={{
                          ...AddEventFormStyle.inputFieldWrapper2,
                          opacity: isRepeatEvent ? 1 : 0.5,
                          marginLeft: isTabDevice() ? hp('6%') : 0,
                        }}
                      >
                        <View style={AddEventFormStyle.inputField}>
                          <SelectionModal
                            title={`Select Event Type`}
                            items={repeatEventTypes}
                            onCloseHook={setIsRepeatEventModalOpen}
                            onSelectItemHook={item => {
                              setSelectedRepeatEvent(item);
                              handleCreateEventDataWithChangeEndDate(
                                'repeatEventType',
                                item?.[0]?.value
                              );
                            }}
                            selectedItemLabel={selectedRepeatEvent?.[0]?.label}
                            isModalOpen={isRepeatEventModalOpen}
                            disableOnPress={isView || !isRepeatEvent}
                            selectFirstOptionOnInitialRender
                            defaultValues={[selectedRepeatEvent?.[0]?.value]}
                          />
                        </View>
                      </View>
                    </View>
                    <View
                      style={AddEventFormStyle.rightRow}
                      onPress={() =>
                        showTimepicker(dateTimePickerSelections.END_DATE_PICKER)
                      }
                    >
                      <Text
                        style={{
                          ...AddEventFormStyle.checkboxText,
                          opacity: isRepeatEvent ? 1 : 0.5,
                        }}
                      >
                        Ends On
                      </Text>
                      <View style={{ opacity: isRepeatEvent ? 1 : 0.5 }}>
                        <TouchableOpacity
                          disabled={!isRepeatEvent}
                          onPress={() =>
                            showTimepicker(
                              dateTimePickerSelections.END_DATE_PICKER
                            )
                          }
                          style={{
                            ...AddEventFormStyle.btnCalendar,
                            opacity: isRepeatEvent ? 1 : 0.5,
                          }}
                        >
                          <Text
                            style={AddEventFormStyle.btnCalendarText}
                            onPress={() =>
                              showTimepicker(
                                dateTimePickerSelections.END_DATE_PICKER
                              )
                            }
                          >
                            {dateToString(new Date(endDate))}
                          </Text>
                          <Image
                            style={AddEventFormStyle.calendarIcon}
                            source={require('../../../assets/icons/calendarIcon.png')}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                )}
                {event?.repeatConfig && (
                  <View style={AddEventFormStyle.repeatEventDetailsContainer}>
                    <View
                      style={AddEventFormStyle.repeatEventFrequencyContainer}
                    >
                      <View
                        style={{
                          ...AddEventFormStyle.repeatEventDetailsFieldWrapper,
                          opacity: 0.5,
                        }}
                      >
                        <View
                          style={
                            AddEventFormStyle.repeatEventDetailsLabelWrapper
                          }
                        >
                          <Text
                            style={AddEventFormStyle.repeatEventDetailsLabel}
                          >
                            Repeat Event
                          </Text>
                        </View>
                        <View
                          style={AddEventFormStyle.repeatEventDetailsInputField}
                        >
                          <Text
                            style={AddEventFormStyle.repeatEventDetailsText}
                          >
                            {renderRepeatEventFrequency()}
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View style={AddEventFormStyle.repeatEventEndDateContainer}>
                      <View
                        style={{
                          ...AddEventFormStyle.repeatEventDetailsFieldWrapper,
                          opacity: 0.5,
                        }}
                      >
                        <View
                          style={
                            AddEventFormStyle.repeatEventDetailsLabelWrapper
                          }
                        >
                          <Text
                            style={AddEventFormStyle.repeatEventDetailsLabel}
                          >
                            Ends on
                          </Text>
                        </View>
                        <View
                          style={AddEventFormStyle.repeatEventDetailsDateField}
                        >
                          <Text
                            style={AddEventFormStyle.repeatEventDetailsText}
                          >
                            {renderRepeatEventEndDate()}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                )}
              </View>
              {show && (
                <View style={AddEventFormStyle.datePickerWrapper}>
                  <DateTimePickerModal
                    isVisible={
                      dateTimePickerSelection ===
                      dateTimePickerSelections.END_DATE_PICKER
                        ? isRepeatEvent
                        : true
                    }
                    mode={mode}
                    date={new Date(findDateTimePickerValue())}
                    // style={addUserStyle.datePicker}
                    onConfirm={handleDateTimePicker}
                    is24Hour={true}
                    // display="spinner"
                    onCancel={() => setShow(false)}
                    modalStyleIOS={AddEventFormStyle.datePickerSelector}
                    pickerContainerStyleIOS={
                      AddEventFormStyle.datePickerWrapper
                    }
                    minimumDate={
                      dateTimePickerSelection ===
                        dateTimePickerSelections.END_TIME_PICKER ||
                      dateTimePickerSelection ===
                        dateTimePickerSelections.END_DATE_PICKER
                        ? new Date(startTime)
                        : dateTimePickerSelection ===
                          dateTimePickerSelections.EVENT_DATE_PICKER
                        ? new Date()
                        : dateTimePickerSelection ===
                          dateTimePickerSelections.START_TIME_PICKER
                        ? new Date()
                        : null
                    }
                  />
                </View>
              )}
            </View>
            {isShowDeleteModal &&
              (event?.repeatConfig ? (
                !isShowRecurringEventDeleteConfirmationModal ? (
                  <DeleteRecurringEventModal
                    submitAction={showDeleteRecurringEventConfirmationMessage}
                    cancelAction={() => {
                      setIsShowDeleteModal(false);
                      setSelectedOptionMethod(null);
                      resetEventDeleteData();
                    }}
                    loading={deleteEventLoading}
                    selectedOptionMethod={selectedOptionMethod}
                    setSelectedOptionMethod={setSelectedOptionMethod}
                  />
                ) : null
              ) : (
                <EventDeleteModal
                  message={`Are you sure you want to remove the scheduled ${matchType?.toLowerCase()}?`}
                  isTeamModal
                  submitAction={onHandleDeleteEvent}
                  cancelAction={() => {
                    setIsShowDeleteModal(false);
                    resetEventDeleteData();
                  }}
                  loading={deleteEventLoading}
                  errorMessage={
                    EventsErrorMessages[deleteEventFailError] ||
                    deleteEventFailError
                  }
                />
              ))}
            {isShowRecurringEventDeleteConfirmationModal ? (
              <DeleteRecurringConfirmationModal
                message={deleteRepeatEventConfirmationMessage}
                isTeamModal
                submitAction={() => deleteRecurringEvents()}
                cancelAction={() => {
                  setIsShowRecurringEventDeleteConfirmationModal(false);
                  resetEventDeleteData();
                  setIsShowDeleteModal(true);
                }}
                loading={deleteEventLoading}
                errorMessage={deleteEventFailError}
              />
            ) : null}
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAwareScrollView>
  );
};

export default AddEventForm;
