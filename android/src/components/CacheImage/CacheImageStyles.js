import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { Platform } from 'react-native';

const cacheImageStyles = colors => ({
  playIconWrapper: isTabDevice()
    ? {
        height: wp('15%'),
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        zIndex: 10,
      }
    : {
        height: wp('45%'),
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        zIndex: 10,
      },
  playIcon: {
    width: 50,
    height: 50,
    zIndex: 10,
    opacity: 0.8,
  },
});

export default cacheImageStyles;
