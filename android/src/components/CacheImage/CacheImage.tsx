import * as FileSystem from 'expo-file-system';
import React, { FC, useEffect, useState } from 'react';
import { Image, View } from 'react-native';
import shorthash from 'shorthash';
import { AntDesign } from '@expo/vector-icons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import useStyles from '../../hooks/useStyles';
import cacheImageStylesFile from './CacheImageStyles';

interface CacheImageProps {
  uri: string;
  style: any;
  isProfilePic?: boolean;
  defaultPicture?: any;
  customLoaderStyle?: any;
  isVideoPreview?: boolean;
}

const CacheImage: FC<CacheImageProps> = ({
  uri,
  style,
  isProfilePic = true,
  defaultPicture = require('../../../assets/profilepictures/default_coach.png'),
  customLoaderStyle = null,
  isVideoPreview = false,
}) => {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [isImageLoaded, setImageLoaded] = useState(false);
  const [isError, setIserror] = useState(false);

  const cacheImageStyles = useStyles(cacheImageStylesFile);

  useEffect(() => {
    if (uri) {
      const name = shorthash.unique(uri?.split('?X-Amz')?.[0]);
      const path = `${FileSystem.cacheDirectory}${name}`;
      downloadImage(path);
    }
  }, [uri]);

  const downloadImage = async (path: string) => {
    const image = await FileSystem.getInfoAsync(path);
    if (image?.exists) {
      setImageUri(image?.uri);
      return;
    }
    const newImage = await FileSystem.downloadAsync(uri, path);
    setImageUri(newImage?.uri);
  };

  return isProfilePic ? (
    <>
      {isVideoPreview && imageUri && isImageLoaded && (
        <View style={cacheImageStyles.playIconWrapper}>
          <AntDesign
            name="play"
            size={45}
            color="white"
            style={cacheImageStyles.playIcon}
          />
        </View>
      )}
      <Image
        style={
          uri && !isError
            ? !isImageLoaded || !imageUri
              ? customLoaderStyle || style
              : style
            : style
        }
        source={
          uri && !isError
            ? !isImageLoaded || !imageUri
              ? require('../../../assets/loader.gif')
              : { uri: imageUri }
            : defaultPicture
        }
        onError={() => setIserror(true)}
        onLoad={() => setImageLoaded(true)}
      />
    </>
  ) : (
    <Image
      style={style}
      source={{ uri: imageUri || '' }}
      onLoad={() => setImageLoaded(true)}
    />
  );
};
export default CacheImage;
