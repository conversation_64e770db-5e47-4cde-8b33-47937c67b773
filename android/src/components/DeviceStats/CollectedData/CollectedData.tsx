import React, { FC } from 'react';
import { Text, View, FlatList, TouchableOpacity } from 'react-native';
import {
  TSelectedType,
  DeviceStatsSessionType,
  DeviceStatsSummaryType,
} from '../../../constants/data';
import useStyles from '../../../hooks/useStyles';
import customCollectedDataStyle from './CollectedDataStyle';
import SummaryData from './SummaryData/SummaryData';
import SessionData from './SessionData/SessionData';
import Filters from '../FilterTypes/Filters';

const CollectedData = ({ selectedType }: { selectedType: string }) => {
  const collectedDataStyle = useStyles(customCollectedDataStyle);

  const renderDatasUI = () => {
    if (selectedType === DeviceStatsSessionType) {
      return <SessionData />;
    }

    if (selectedType === DeviceStatsSummaryType) {
      return <SummaryData />;
    }
  };
  return (
    <View style={collectedDataStyle.mainContainer}>
      <View>
        <Filters />
      </View>
      <View style={collectedDataStyle.contentWrapper}>{renderDatasUI()}</View>
    </View>
  );
};

export default CollectedData;
