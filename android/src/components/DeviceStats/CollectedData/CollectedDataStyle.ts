import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  txtWhite: {
    color: colors.white,
  },
  mainContainer: {},
  contentWrapper: isTabDevice()
    ? {
        paddingTop: wp('2%'),
      }
    : {
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
      },
});
