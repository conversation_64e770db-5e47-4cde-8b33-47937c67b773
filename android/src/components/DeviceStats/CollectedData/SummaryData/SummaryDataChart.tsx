import moment from 'moment';
import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import RNE<PERSON>hartsPro from 'react-native-echarts-pro';
import { useSelector } from 'react-redux';
import { isTabDevice } from '../../../../config/appConfig';
import { months } from '../../../../constants/constants';
import { addLeadingZeros } from '../../../../helpers/index';
import useStyles from '../../../../hooks/useStyles';
import { RootStore } from '../../../../store/store';
import customSummaryDataStyle from './SummaryDataStyle';

const SummaryDataChart = ({
  graphData,
  selectedType,
}: {
  graphData: any;
  selectedType: string;
}) => {
  const summaryDataStyle = useStyles(customSummaryDataStyle);
  const { summarySelectedDetails } = useSelector(
    (state: RootStore) => state?.deviceStats
  );

  const getFormattedDate = (dateString: string) => {
    const dateObject = new Date(dateString);
    const month = months[dateObject.getMonth()].slice(0, 3);
    const date = addLeadingZeros(dateObject.getDate());
    const year = dateObject.getFullYear();

    return { month, date, year };
  };

  const fromDate =
    summarySelectedDetails?.summaryFromDate &&
    getFormattedDate(summarySelectedDetails.summaryFromDate.toJSON());

  const toDate =
    summarySelectedDetails?.summaryToDate &&
    getFormattedDate(summarySelectedDetails.summaryToDate.toJSON());

  const setDateRange = (fromDate: any, toDate: any) => {
    if (fromDate?.year === toDate?.year) {
      return `${fromDate?.month} ${fromDate?.date}  -  ${toDate?.month} ${toDate?.date} ${toDate?.year} `;
    } else {
      return `${fromDate?.month} ${fromDate?.date} ${fromDate?.year}  -  ${toDate?.month} ${toDate?.date} ${toDate?.year} `;
    }
  };

  const option = useMemo(() => {
    return {
      dataZoom: [
        {
          id: 'dataZoomX',
          type: 'inside',
          xAxisIndex: [0],
          filterMode: 'none',
        },
      ],
      tooltip: {
        axisPointer: {
          type: 'cross',
          label: {
            show: false,
          },
        },
        show: true,
        trigger: 'axis',
        position: (pos: any, params: any, el: any, elRect: any, size: any) => {
          var obj = { top: 10 };
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
          return obj;
        },
        formatter: function (params) {
          let selectedData = params[0];
          let chartdate = echarts.format.formatTime(
            `dd/MM/yyyy`,
            selectedData.value[0]
          );

          let hours = Number(new Date(selectedData.value[0]).getHours());
          let hourString = String(hours >= 12 ? 24 - hours : hours);
          let min = Number(new Date(selectedData.value[0]).getMinutes());
          let chartTime = `${hourString.length == 1 ? '0' : ''}${hourString}:${
            String(min).length == 1 ? '0' : ''
          }${min}`;

          let val =
            '<li style="list-style:none">' +
            params[0].marker +
            params[0].seriesName +
            '&nbsp;&nbsp;' +
            params[0].value[1] +
            '</li>';

          if (params.length > 1) {
            val =
              val +
              '<li style="list-style:none">' +
              params[1].marker +
              params[1].seriesName +
              '&nbsp;&nbsp;' +
              params[1].value[1] +
              '</li>';
          }

          return chartdate + ` ${chartTime} ${hours >= 12 ? 'PM' : 'AM'}` + val;
        },
      },
      legend: {
        data: graphData.legendNames,
        align: 'left',
        right: 0,
        textStyle: {
          color: 'white',
          fontSize: isTabDevice() ? 16 : 11,
        },
        itemGap: 30,
      },
      xAxis: {
        // min: 3,
        type: 'time',
        show: true,
        name: setDateRange(fromDate, toDate),
        nameGap: 30,
        nameLocation: 'center',
        nameTextStyle: {
          fontSize: isTabDevice() ? 16 : 9,
          color: 'white',
          fontWeight: 'bold',
          padding: [7, 0, 0, 0],
        },
        boundaryGap: false,

        // boundaryGap: ['20%', '20%'],

        // axisLine: {
        //   // show: true,
        //   lineStyle: {
        //     type: 'solid',
        //     width: 4,
        //     color: '#41d98269',
        //   },
        // },
        axisLabel: {
          fontSize: isTabDevice() ? 14 : 9,
          interval: 'auto',
          rotate: 45,
          textStyle: {
            color: 'white',
          },
        },
      },
      yAxis: {
        type: 'value',
        show: true,
        name: graphData.name,
        nameLocation: 'end',
        nameGap: 20,
        nameTextStyle: {
          fontSize: isTabDevice() ? 16 : 9,
          color: 'white',
          align: 'left',
          fontWeight: 'bold',
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: true,
          // lineStyle: {
          //   type: 'solid',
          //   width: 4,
          //   color: '#41d98269',
          // },
        },
        axisLabel: {
          textStyle: {
            fontSize: isTabDevice() ? 14 : 9,
            color: 'white',
            align: 'right',
          },
        },
      },
      animation: true,
      series: graphData.series,
    };
  }, [graphData, selectedType]);

  const Chart = useCallback(() => {
    return (
      <View style={summaryDataStyle.graphWrapper}>
        <View style={summaryDataStyle.graph}>
          <View style={{ paddingTop: 25, padding: 12 }}>
            <RNEChartsPro height={isTabDevice() ? 430 : 250} option={option} />
          </View>
        </View>
      </View>
    );
  }, []);

  return <Chart />;
};

export default SummaryDataChart;
