import React, { FC } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import customEventStyle from './EventStyle';
import SelectedEventDetail from './Dashboard';
import { deviceStatsEventType } from '../../../constants/constants';
import { sessionDataType } from '../../../store/reducers/DeviceStats/DeviceStatsReducer';
import { RootStore } from '../../../store/store';
import { useSelector } from 'react-redux';
import { DeviceStatsSessionType } from '../../../constants/data';

interface IEvents {
  setSelectedEventType: Function;
  data: sessionDataType;
  handlePrev: () => void;
  handleNext: () => void;
  loading: boolean;
  selectedEventType: string | null;
}

const Events: FC<IEvents> = ({
  setSelectedEventType,
  data,
  selectedEventType,
  ...props
}) => {
  const eventStyle = useStyles(customEventStyle);
  const {
    selectedDeviceStateType,
    hasTrainingDataForSession,
    hasMatchDataForSession,
    hasTrainingDataForSummary,
    hasMatchDataForSummary,
  } = useSelector((state: RootStore) => state?.deviceStats);

  const isMatchDisabled =
    selectedDeviceStateType == DeviceStatsSessionType
      ? !hasMatchDataForSession
      : !hasMatchDataForSummary;
  const isTrainingDisabled =
    selectedDeviceStateType == DeviceStatsSessionType
      ? !hasTrainingDataForSession
      : !hasTrainingDataForSummary;

  const handleMatchClick = () => {
    if (selectedEventType !== deviceStatsEventType.MATCH) {
      setSelectedEventType(deviceStatsEventType.MATCH);
    }
  };

  const handleTrainingClick = () => {
    if (selectedEventType !== deviceStatsEventType.TRAINING) {
      setSelectedEventType(deviceStatsEventType.TRAINING);
    }
  };

  const renderEventType = () => (
    <View style={eventStyle.eventSelection}>
      <Text style={eventStyle.eventSelectionTitle}>Event Type</Text>
      <View style={eventStyle.eventSelections}>
        <TouchableOpacity
          style={
            isMatchDisabled
              ? eventStyle.eventSelectionOptionDisabled
              : selectedEventType === deviceStatsEventType.MATCH
              ? eventStyle.eventSelectionOptionSelected
              : eventStyle.eventSelectionOption
          }
          disabled={isMatchDisabled}
          onPress={handleMatchClick}
        >
          <Text
            style={
              isMatchDisabled
                ? eventStyle.eventSelectionTextDisabled
                : eventStyle.eventSelectionText
            }
          >
            Match
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={
            isTrainingDisabled
              ? eventStyle.eventSelectionOptionDisabled
              : selectedEventType === deviceStatsEventType.TRAINING
              ? eventStyle.eventSelectionOptionSelected
              : eventStyle.eventSelectionOption
          }
          disabled={isTrainingDisabled}
          onPress={handleTrainingClick}
        >
          <Text
            style={
              isTrainingDisabled
                ? eventStyle.eventSelectionTextDisabled
                : eventStyle.eventSelectionText
            }
          >
            Training
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={eventStyle.eventsWrapper}>
      {isTabDevice() && renderEventType()}
      <SelectedEventDetail
        renderEventTypeForMobile={renderEventType}
        data={data}
        {...props}
      />
    </View>
  );
};

export default Events;
