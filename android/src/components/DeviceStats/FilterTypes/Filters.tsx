import React, { useMemo, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import customFilterStyle from './FilterStyle';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { deviceStatfilterList } from '../../../constants/constants';
import { RootStore } from '../../../store/store';
import { useDispatch, useSelector } from 'react-redux';
import { SET_SELECTED_FILTER_TYPE } from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import useGetSessionData from '../../../hooks/useGetSessionData';
import { DEVICE_STATS_CATEGORY_LIST } from '../../../constants/data';

const Filters = () => {
  const {
    selectedFilterType,
    sessionData,
    sessionPaginationCount,
    PlayerMakerSyncedData,
    selectedDeviceStateType,
    summarySelectedDetails,
    selectedEventType,
  } = useSelector((state: RootStore) => state?.deviceStats);
  let pmUserId = PlayerMakerSyncedData?.pmUserId;
  const filterStyle = useStyles(customFilterStyle);
  const dispatch = useDispatch();
  const { getSessionStat, getSummaryStat } = useGetSessionData();

  const currentSessionData = useMemo(
    () => sessionData?.[sessionPaginationCount],
    [sessionPaginationCount, sessionData]
  );

  const setIsClickedFilter = (selectedType: string) => {
    dispatch({
      type: SET_SELECTED_FILTER_TYPE,
      payload: {
        data: selectedType,
      },
    });

    if (selectedDeviceStateType === DEVICE_STATS_CATEGORY_LIST[0].key) {
      currentSessionData &&
        pmUserId &&
        currentSessionData?.phaseId &&
        getSessionStat(
          pmUserId,
          currentSessionData.sessionId,
          selectedType,
          currentSessionData?.phaseId
        );
    } else {
      pmUserId &&
        summarySelectedDetails &&
        summarySelectedDetails.summarySelectedTeam?.length &&
        summarySelectedDetails.summarySelectedPosition?.length &&
        summarySelectedDetails.summaryFromDate &&
        summarySelectedDetails.summaryToDate &&
        selectedEventType &&
        getSummaryStat(
          pmUserId,
          summarySelectedDetails.summaryFromDate?.toJSON(),
          summarySelectedDetails.summaryToDate?.toJSON(),
          selectedType,
          selectedEventType,
          summarySelectedDetails.summarySelectedTeam[0].value,
          summarySelectedDetails.summarySelectedPosition[0].value
        );
    }
  };

  const renderListData = ({ item }: any) => (
    <View>
      <TouchableOpacity onPress={() => setIsClickedFilter(item.id)}>
        <View
          style={
            selectedFilterType === item.id
              ? filterStyle.selectedWrapper
              : filterStyle.unSelectedWrapper
          }
        >
          <Text style={filterStyle.text}>{item.label}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderFilterTypeForTab = () => (
    <View>
      <View style={filterStyle.container}>
        <FlatList
          data={deviceStatfilterList}
          renderItem={renderListData}
          keyExtractor={item => item.id}
          horizontal={true}
        />
      </View>
    </View>
  );

  const renderFilterTypeForMobile = () => (
    <View>
      <View style={filterStyle.container}>
        <FlatList
          data={deviceStatfilterList}
          renderItem={renderListData}
          keyExtractor={item => item.id}
          horizontal={true}
        />
      </View>
    </View>
  );

  return (
    <View>
      {isTabDevice() ? renderFilterTypeForTab() : renderFilterTypeForMobile()}
    </View>
  );
};

export default Filters;
