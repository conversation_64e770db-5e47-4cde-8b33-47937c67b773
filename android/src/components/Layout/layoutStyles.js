import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const LayoutStyles = colors => ({
  logo: isTabDevice()
    ? {
        position: 'absolute',
        top: 0,
        left: hp('5%'),
        width: wp('14%'),
        height: hp('8%'),
        zIndex: 10,
        resizeMode: 'contain',
        marginTop: hp('5%'),
      }
    : {
        position: 'absolute',
        top: 0,
        left: hp('1%'),
        width: wp('20%'),
        height: hp('15%'),
        zIndex: 10,
        resizeMode: 'contain',
        marginTop: hp('5%'),
      },
  homepageBackground: {
    width: wp('100%'),
    height: hp('100%'),
    resizeMode: 'cover',
    alignSelf: 'flex-start',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    opacity: 0.97,
    zIndex: 1,
  },
  contentWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        left: 0,
        paddingTop: hp('10%'),
        width: wp('100%'),
        height: hp('100%'),
        zIndex: 2,
      }
    : {
        flexDirection: 'column',
        position: 'absolute',
        top: 0,
        left: 0,
        paddingTop: hp('8%'),
        width: wp('100%'),
        height: hp('100%'),
        zIndex: 2,
      },
});

export default LayoutStyles;
