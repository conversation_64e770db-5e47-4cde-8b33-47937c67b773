import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerProfileStyle = colors => ({
  playerWrapper: {
    position: 'relative',
  },
  playerExpired: isTabDevice()
    ? {
        height: wp('4%'),
        width: wp('4%'),
        position: 'absolute',
        bottom: 0,
        right: wp('0.5%'),
        zIndex: 10,
      }
    : {
        height: wp('7%'),
        width: wp('7%'),
        position: 'absolute',
        bottom: 0,
        right: wp('2%'),
        zIndex: 10,
      },
  container: isTabDevice()
    ? {
        flexDirection: 'column',
        marginRight: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        marginRight: wp('6%'),
        marginBottom: wp('2%'),
      },
  img: isTabDevice()
    ? {
        width: wp('12%'),
        height: wp('12%'),
        borderRadius: wp('1%'),
      }
    : {
        width: wp('23%'),
        height: wp('23%'),
        borderRadius: wp('2%'),
      },
  nameView: isTabDevice()
    ? {
        width: wp('12%'),
        flexDirection: 'row',
        marginBottom: hp('0.5%'),
        marginTop: hp('1.5%'),
        flexWrap: 'wrap',
      }
    : {
        width: wp('25%'),
        flexDirection: 'row',
        marginBottom: hp('0.5%'),
        marginTop: hp('1.5%'),
        flexWrap: 'wrap',
      },
  firstName: isTabDevice()
    ? {
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.2%'),
        color: colors.green,
        paddingRight: 5,
      }
    : {
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3%'),
        color: colors.green,
        paddingRight: 5,
      },
  lastName: isTabDevice()
    ? {
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.5%'),
        lineHeight: hp('2.5%'),
        color: colors.green,
      }
    : {
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3.7%'),
        color: colors.green,
      },
  age: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.white,
        marginBottom: hp('0.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        marginBottom: hp('1%'),
        fontFamily: 'Poppins-Medium',
      },
  Jersey: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginTop: -5,
      },
});
export default PlayerProfileStyle;
