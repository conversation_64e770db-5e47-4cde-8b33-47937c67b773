import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const ProfileGlanceStyle = colors => ({
  container: isTabDevice()
    ? {
        flex: 1,
        flexDirection: 'column',
        marginLeft: hp('3%'),
      }
    : {
        flex: 1,
        flexDirection: 'column',
        marginLeft: hp('2%'),
        paddingTop: wp('1%'),
      },
  img: isTabDevice()
    ? {
        height: wp('17%'),
        width: wp('17%'),
        borderRadius: wp('2%'),
        marginBottom: hp('2%'),
      }
    : {
        height: wp('25%'),
        width: wp('25%'),
        borderRadius: wp('2%'),
        marginBottom: hp('2%'),
      },
  glanceView: {
    height: wp('60%'),
    marginTop: wp('1.5%'),
    justifyContent: 'flex-start',
    alignContent: 'center',
  },
  firstName: isTabDevice()
    ? {
        fontSize: wp('3%'),
        fontWeight: 'bold',
        color: colors.white,
      }
    : {
        fontSize: wp('4%'),
        fontWeight: 'bold',
        color: colors.white,
      },
  lastName: {
    fontSize: wp('4%'),
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: hp('4%'),
  },
  // If the character limit is more than 10 apply the
  // class to make the font size smaller
  fontSmall: isTabDevice()
    ? {
        fontSize: wp('3%'),
      }
    : {
        fontSize: wp('4%'),
      },
  availabilityIcon: isTabDevice()
    ? {
        width: 50,
        height: 50,
        position: 'absolute',
        top: 0,
        right: 0,
      }
    : {
        width: 25,
        height: 25,
        position: 'absolute',
        top: 0,
        right: 0,
      },
});
export default ProfileGlanceStyle;
