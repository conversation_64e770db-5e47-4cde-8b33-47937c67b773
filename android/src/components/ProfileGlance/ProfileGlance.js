import React from 'react';
import { Image, Text, View } from 'react-native';

import { TouchableOpacity } from 'react-native-gesture-handler';

import customProfileGlanceStyle from './ProfileGlanceStyle';
import { useSelector } from 'react-redux';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import ExclamationIcon from '../../../assets/buttons/exclamation.png';
import useStyles from '../../hooks/useStyles';
import { userRoleType } from '../../constants/constants';

export default function ProfileGlance({ navigation, userRole, selectedChild }) {
  const ProfileGlanceStyle = useStyles(customProfileGlanceStyle);
  const { teamIdData } = useSelector(state => state?.team);
  const { userData } = useSelector(state => state?.auth);
  const { profileImageUrl, firstName, lastName, id } = userData || {};

  const isReduceFontSize =
    firstName?.length > 10 ||
    lastName?.length > 10 ||
    selectedChild?.lastName?.length > 10 ||
    selectedChild?.lastName?.length > 10;

  const { childInformation } = useSelector(state => state?.common);

  const isParent = userRole === userRoleType.PARENT;

  const showAvailabilityIcon = () => {
    return (
      <View style={{ ...ProfileGlanceStyle.img, position: 'absolute' }}>
        <Image
          style={ProfileGlanceStyle.availabilityIcon}
          source={ExclamationIcon}
        />
      </View>
    );
  };

  return (
    <View style={ProfileGlanceStyle.container}>
      <View style={ProfileGlanceStyle.glanceView}>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate('PlayerInfoScreen', {
              teamID: teamIdData,
              profileID: isParent
                ? childInformation?.sportsProfileId
                : userData?.sportsProfileId,
            })
          }
        >
          <ProfileImage
            imageStyles={ProfileGlanceStyle.img}
            profileImageUrl={
              isParent ? selectedChild?.profileImageUrl : profileImageUrl
            }
          />
          {isParent
            ? !childInformation?.isAvailable
              ? showAvailabilityIcon()
              : null
            : !userData?.isAvailable
            ? showAvailabilityIcon()
            : null}
        </TouchableOpacity>
        <Text
          style={[
            ProfileGlanceStyle.firstName,
            isReduceFontSize ? ProfileGlanceStyle.fontSmall : '',
          ]}
        >
          {isParent ? selectedChild?.firstName : firstName}
        </Text>
        <Text
          style={[
            ProfileGlanceStyle.firstName,
            isReduceFontSize ? ProfileGlanceStyle.fontSmall : '',
          ]}
        >
          {isParent ? selectedChild?.lastName : lastName}
        </Text>
      </View>
    </View>
  );
}
