import React, { useState } from 'react';
import { View, Image, TouchableOpacity, Text } from 'react-native';
import ground from '../../../assets/backgrounds/ground.png';
import customFieldStyles from './fieldStyles';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import ExclamationIcon from '../../../assets/buttons/exclamation.png';
import useStyles from '../../hooks/useStyles';

const Field = ({
  playerCoordinates,
  matchPlan,
  openCandidatePlayerModal,
  openCandidatePlayerDeleteModal,
  isMatchLogView,
  isPlayer,
  disabledNet = false,
  isActionSelected = false,
  jerseyNumbers = null,
}) => {
  const FieldStyles = useStyles(customFieldStyles);
  const fieldHeight = 566;
  const fieldWidth = 559;
  const [dimensions, setDimensions] = useState(null);
  const [showPlayerToolTip, setShowPlayerTooltip] = useState({
    show: false,
    coordinateId: null,
  });

  const onPageLayout = event => {
    const { width, height } = event.nativeEvent.layout;
    setDimensions({ width, height });
  };

  const getAssignedPlayer = (positionX, positionY) => {
    if (!matchPlan) return null;
    const { playerCoordinates: coords } = matchPlan;
    if (!coords) return null;
    const matchingPlayer = coords.find(
      item => item.x === positionX && item.y === positionY
    );

    if (matchingPlayer) return matchingPlayer?.playerData;
    return null;
  };

  const getX = x => {
    if (!dimensions) return x;
    return (x / fieldWidth) * dimensions.width;
  };
  const getY = y => {
    if (!dimensions) return y;
    return (y / fieldHeight) * dimensions.height;
  };
  const onPlayerClicked = positionId => {
    openCandidatePlayerModal(positionId);
  };

  return (
    <View style={FieldStyles.fieldContainer}>
      <View style={FieldStyles.fieldWrapper} onLayout={onPageLayout}>
        <Image source={ground} style={FieldStyles.ground} />
        {playerCoordinates &&
          playerCoordinates.map(position => {
            const { profileImageUrl, sportsProfileId, isAvailable } =
              getAssignedPlayer(position.x, position.y) || {};

            return (
              <>
                {showPlayerToolTip.show &&
                showPlayerToolTip.coordinateId === position?.coordinateId ? (
                  <View
                    style={{
                      ...FieldStyles.toolTipPlayers,
                      left: getX(position.x),
                      top: getY(position.y - 45),
                    }}
                  >
                    <Text style={FieldStyles.toolTipPlayersText}>
                      {position?.playerData?.firstName || ''}
                    </Text>
                  </View>
                ) : null}
                <View
                  key={position._id || position.coordinateId}
                  style={{
                    position: 'absolute',
                    left: getX(position.x),
                    top: getY(position.y),
                  }}
                >
                  <View style={FieldStyles.player}>
                    <TouchableOpacity
                      disabled={isPlayer}
                      onPress={() => {
                        if (!disabledNet) {
                          if (
                            isMatchLogView &&
                            !isActionSelected &&
                            position?.playerData
                          ) {
                            if (
                              showPlayerToolTip.coordinateId ===
                              position.coordinateId
                            ) {
                              setShowPlayerTooltip({
                                show: false,
                                coordinateId: null,
                              });
                            } else {
                              setShowPlayerTooltip({
                                show: true,
                                coordinateId: position.coordinateId || '',
                              });
                            }

                            return;
                          }
                          setShowPlayerTooltip({
                            show: false,
                            coordinateId: null,
                          });
                          onPlayerClicked(
                            position._id || position.coordinateId
                          );
                        }
                      }}
                      onLongPress={() =>
                        sportsProfileId &&
                        !isMatchLogView &&
                        openCandidatePlayerDeleteModal(sportsProfileId)
                      }
                      style={
                        isMatchLogView
                          ? position.code === 'GK'
                            ? FieldStyles.matchLogPlayerImgWrapperGK
                            : FieldStyles.matchLogPlayerImgWrapper
                          : position.code === 'GK'
                          ? FieldStyles.playerImgWrapperGK
                          : FieldStyles.playerImgWrapper
                      }
                    >
                      <View style={FieldStyles.jerseyCircle}>
                        <Text
                          style={
                            isMatchLogView
                              ? FieldStyles.jerseyCircleMatchText
                              : FieldStyles.jerseyCircleText
                          }
                        >
                          {jerseyNumbers?.find(
                            data => data?.sportsProfileId === sportsProfileId
                          )?.jersyNo || ''}
                        </Text>
                      </View>
                      {sportsProfileId ? (
                        <View>
                          <ProfileImage
                            style={FieldStyles.playerImg}
                            imageStyles={FieldStyles.playerImg}
                            profileImageUrl={profileImageUrl}
                          />
                          {!isAvailable && (
                            <Image
                              style={
                                isMatchLogView
                                  ? FieldStyles.availabilityIconMatchLog
                                  : FieldStyles.availabilityIcon
                              }
                              source={ExclamationIcon}
                            />
                          )}
                        </View>
                      ) : (
                        <View style={FieldStyles.playerImg} />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </>
            );
          })}
      </View>
    </View>
  );
};

export default Field;
