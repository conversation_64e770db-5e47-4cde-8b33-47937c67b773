import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import customMatchPlanContainerStyles from '../../Container/MatchPlanContainer/MatchPlanContainerStyles';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import useStyles from '../../hooks/useStyles';
import SelectionModalProfileImg from '../modal/SelectionModal/SelectionModalProfileImg';
import customFieldStyles from './fieldStyles';

const Substitute = ({
  substitutesPlayers,
  selectedPlayer,
  dropDownSelectedValues,
  instant,
  index,
  setSelectedSubstitute,
  allPlayers,
  hideDropdownOnclick,
  arrayId,
  substitutes,
  onReachEndHandler,
  searchByText,
  handleSearch,
}) => {
  const FieldStyles = useStyles(customFieldStyles);
  const MatchPlanContainerStyles = useStyles(customMatchPlanContainerStyles);
  const [dropdownData, setDropdownData] = useState([]);

  const [setIsSubstituteModalOpen, isSubstituteModalOpen] =
    useInputSelectModal();

  const currentSelected = useMemo(() => {
    allPlayers.find(
      substitute => substitute.sportsProfileId === selectedPlayer
    );
  }, [allPlayers, selectedPlayer]);

  useEffect(() => {
    if (!isSubstituteModalOpen) {
      handleSearch();
    }
  }, [isSubstituteModalOpen]);

  useEffect(() => {
    if (dropDownSelectedValues) {
      let dropDownArray = [];
      substitutesPlayers.forEach(item => {
        if (
          dropDownSelectedValues.findIndex(
            substitute => substitute.playerId === item.value
          ) === -1
        ) {
          dropDownArray = [...dropDownArray, { ...item }];
        }
      });

      dropDownArray =
        currentSelected && currentSelected.sportsProfileId
          ? [
              ...dropDownArray,
              {
                value: currentSelected.sportsProfileId,
                label: currentSelected.firstName,
                profileImageUrl: currentSelected?.profileImageUrl,
                isAvailable: currentSelected?.isAvailable,
              },
            ]
          : dropDownArray;
      setDropdownData(dropDownArray);
    }
  }, [substitutesPlayers, dropDownSelectedValues, selectedPlayer]);

  const selectedSubstitute = item => {
    setSelectedSubstitute(item, index, arrayId);
  };
  const onClose = () => {};

  const getLabel = id => {
    const selectedPlayer = allPlayers?.find(
      item => item?.sportsProfileId === id
    );
    return selectedPlayer?.firstName
      ? `${selectedPlayer?.firstName} ${selectedPlayer?.lastName || ''} `
      : '';
  };

  const formattedDropDownSelectedValues = useCallback(() => {
    return dropDownSelectedValues.map(player =>
      allPlayers?.find(
        dropDownSelection =>
          dropDownSelection?.sportsProfileId === player?.playerId
      )
    );
  }, [allPlayers, dropDownSelectedValues]);

  return (
    <View style={FieldStyles.dropdownView}>
      <SelectionModalProfileImg
        title={'Select a Player'}
        items={dropdownData}
        onCloseHook={value => {
          setIsSubstituteModalOpen(value);
          if (value) {
            hideDropdownOnclick(arrayId);
          } else {
            onClose(instant.dropdown.name);
          }
        }}
        onSelectItemHook={selectedOption => {
          selectedSubstitute(selectedOption?.[0]);
        }}
        defaultValues={null}
        isEnableAutoComplete
        selectedItemLabel={
          (dropDownSelectedValues?.length &&
            getLabel(dropDownSelectedValues?.[index]?.playerId)) ||
          ''
        }
        isModalOpen={isSubstituteModalOpen}
        renderWithProfileImg
        selectedItem={
          formattedDropDownSelectedValues()?.[index]?.profileImageUrl
        }
        index={index}
        isAvailable={formattedDropDownSelectedValues()?.[index]?.isAvailable}
        onReachEndHandler={onReachEndHandler}
        isSearchByFetch
        searchByText={searchByText}
      />
    </View>
  );
};

export default Substitute;
