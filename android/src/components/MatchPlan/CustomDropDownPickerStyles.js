import { Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const CustomDropDownPickerStyles = colors => ({
  dropdownView: isTabDevice()
    ? {
        width: '100%',
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
      }
    : {
        width: '96%',
        borderRadius: wp('2.5%'),
        backgroundColor: colors.borderBlue,
      },
  picker: {
    backgroundColor: colors.black,
    color: 'white',
  },
  textInput: {
    fontSize: wp('1.5%'),
  },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        borderColor: colors.borderBlue,
        height: hp('5%'),
        width: '100%',
      }
    : {
        height: wp('10%'),
        borderColor: colors.borderBlue,
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        // marginTop: wp('-1%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,

        elevation: 10,
        height: hp('35%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        marginTop: wp('-1.5%'),
        borderBottomLeftRadius: wp('3.5%'),
        borderBottomRightRadius: wp('3.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,

        elevation: 10,
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        // width: '60%',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.2%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  dropdownTopArea: {
    ...(Platform.OS === 'android'
      ? {
          backgroundColor: colors.transparent,
          borderColor: colors.transparent,
        }
      : {
          backgroundColor: colors.transparent,
          borderColor: colors.transparent,
          width: '100%',
          position: 'absolute',
          zIndex: 5,
        }),
  },
});

export default CustomDropDownPickerStyles;
