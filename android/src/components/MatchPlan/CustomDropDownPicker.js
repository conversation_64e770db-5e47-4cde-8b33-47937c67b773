import React from 'react';
import { View } from 'react-native';
import useStyles from '../../hooks/useStyles';
import DropDownPicker from '../DropdownPicker';
import DropDownPickerStyles from './CustomDropDownPickerStyles';

const CustomDropDownPicker = ({
  visibilityData,
  dropdownType,
  controller,
  items,
  defaultValue,
  onChangeItem,
  onOpen,
  onClose,
  dropdownContainerStyle = {},
}) => {
  const CustomDropDownPickerStyles = useStyles(DropDownPickerStyles);

  return (
    <View style={CustomDropDownPickerStyles.dropdownView}>
      <DropDownPicker
        isVisible={visibilityData[dropdownType]}
        items={items}
        controller={instance => controller && controller(instance)}
        defaultValue={defaultValue}
        containerStyle={{ height: 40 }}
        style={CustomDropDownPickerStyles.dropdownTopArea}
        itemStyle={CustomDropDownPickerStyles.dropdown}
        containerStyle={[
          CustomDropDownPickerStyles.dropdownSelectedContainer,
          dropdownContainerStyle,
        ]}
        labelStyle={CustomDropDownPickerStyles.dropDownLabel}
        placeholderStyle={CustomDropDownPickerStyles.placeholderStyle}
        arrowColor="#fff"
        dropDownStyle={CustomDropDownPickerStyles.dropdownList}
        onChangeItem={item => onChangeItem(item)}
        onOpen={() => onOpen(dropdownType)}
        onClose={() => onClose(dropdownType)}
      />
    </View>
  );
};

export default CustomDropDownPicker;
