import React, { useRef, useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import ModalWrapper from '../modal/ModalWrapper/ModalWrapper';
import customTermAndCondition from './TermAndConditionStyles';
import useStyles from '../../hooks/useStyles';
import { generateTermAndConditionData } from '../../constants/TermAndCondition';
import colors from '../../config/colors';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

import { isTabDevice } from '../../config/appConfig';
import { useDispatch, useSelector } from 'react-redux';
import { SET_TERM_AND_CONDITION } from '../../store/actionTypes/TermAndCondition/termAndCondtionAction';

const TermAndCondition = ({
  isTermAndConditionAccepted,
  logOut,
  setTermAndConditionEnabled,
  parentDetails,
  setIsTermAndConditionAccepted,
}) => {
  const TermAndConditionStyles = useStyles(customTermAndCondition);
  const rightColumnRef = useRef(null);
  const { userData } = useSelector(state => state?.auth);

  const [selectedId, setSelectedId] = useState(1);
  const [useScrollPosition, setUseScrollPosition] = useState(true);

  const { children } = useSelector(state => state?.common);

  const TermAndConditionData = generateTermAndConditionData(
    userData,
    parentDetails,
    children
  );

  const { submitTermAndCondition, getTermAndConditionDetails } =
    useTermAndConditionHook();
  const { isTermAndConditionPosting } = useSelector(
    state => state.TermAndCondition
  );

  const handleTitleClick = id => {
    setUseScrollPosition(false);

    setSelectedId(id);
    const index = TermAndConditionData.findIndex(item => item.id === id);
    if (rightColumnRef.current) {
      rightColumnRef.current.scrollToIndex({ index });
    }
  };

  const handleScroll = event => {
    if (useScrollPosition) {
      const contentOffsetY = event.nativeEvent.contentOffset.y;
      const itemHeight = 150;
      const visibleItemIndex = Math.ceil(contentOffsetY / itemHeight);
      if (
        visibleItemIndex >= 0 &&
        visibleItemIndex < TermAndConditionData.length
      ) {
        setSelectedId(TermAndConditionData[visibleItemIndex].id);
      }
    }
  };

  const dispatch = useDispatch();
  const renderItem = ({ item, index }) => {
    return (
      <View style={{ paddingLeft: wp('3%'), paddingRight: wp('3%') }}>
        <Text style={TermAndConditionStyles.contentTitle}>{item.title}</Text>
        {item.content.map(value => (
          <Text style={TermAndConditionStyles.contentText}>{value}</Text>
        ))}
      </View>
    );
  };
  return (
    <>
      <View>
        <View style={TermAndConditionStyles.container}>
          <ModalWrapper transparent>
            <View style={TermAndConditionStyles.centeredView}>
              <View style={TermAndConditionStyles.overlay}></View>
              <View style={TermAndConditionStyles.modalView}>
                <View style={TermAndConditionStyles.modalTitleContainer}>
                  <Text style={TermAndConditionStyles.modalTitle}>
                    Terms & Conditions
                  </Text>
                </View>
                <View style={TermAndConditionStyles.modalBody}>
                  <View style={TermAndConditionStyles.titleList}>
                    {isTabDevice() &&
                      TermAndConditionData.map(item => (
                        <TouchableOpacity
                          key={item.id}
                          onPress={() => handleTitleClick(item.id)}
                          style={TermAndConditionStyles.titles}
                        >
                          <Text
                            style={[
                              TermAndConditionStyles.titleListText,
                              selectedId === item.id && {
                                color: colors.green,
                              },
                            ]}
                          >
                            {item.title}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    <View
                      style={
                        TermAndConditionStyles.termAndConditionAcceptSection
                      }
                    >
                      <>
                        <TouchableOpacity
                          style={TermAndConditionStyles.agreeButton}
                          onPress={() => {
                            isTermAndConditionAccepted
                              ? setTermAndConditionEnabled(false)
                              : logOut();
                          }}
                        >
                          <Text style={TermAndConditionStyles.agreeButtonText}>
                            Cancel
                          </Text>
                        </TouchableOpacity>
                        {/* {!isExpired && (
                            <TouchableOpacity
                              style={TermAndConditionStyles.remindMeLaterButton}
                              onPress={async () => {
                                await submitTermAndCondition(
                                  { termActionType: 'LATER' },
                                  userId
                                ),
                                  getTermAndConditionDetails(userId),
                                  dispatch({
                                    type: SET_TERM_AND_CONDITION,
                                    payload: false,
                                  });
                              }}
                            >
                              <Text
                                style={TermAndConditionStyles.remindMeLaterText}
                              >
                                Remind me Later
                              </Text>
                            </TouchableOpacity>
                          )} */}
                      </>
                    </View>
                  </View>
                  <View style={TermAndConditionStyles.flatList}>
                    <FlatList
                      ref={rightColumnRef}
                      data={TermAndConditionData}
                      renderItem={renderItem}
                      keyExtractor={item => {
                        item.id;
                      }}
                      style={TermAndConditionStyles.content}
                      onScroll={handleScroll}
                      scrollEventThrottle={16}
                    />
                  </View>
                </View>
              </View>
            </View>
          </ModalWrapper>
        </View>
      </View>
    </>
  );
};

export default TermAndCondition;
