import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateStyle = colors => ({
  dateView: isTabDevice()
    ? {
        backgroundColor: 'green',
        height: wp('5%'),
        width: wp('9%'),
        borderRadius: 7,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: wp('1%'),
        marginTop: wp('0.5%'),
      }
    : {
        flexDirection: 'row',
      },
  dateText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('-0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        marginRight: wp('2%'),
        fontFamily: 'Poppins-Bold',
      },
  dateText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        fontFamily: 'Poppins-Medium',
      },
  msgWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('53%'),
      }
    : {
        width: wp('90%'),
      },
  msgWrapperSelected: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('28%'),
      }
    : {
        width: wp('44%'),
      },
  msg: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        textAlign: 'left',
        fontFamily: 'Poppins-Regular',
        marginTop: wp('1%'),
      }
    : {
        fontSize: wp('3.2%'),
        color: colors.white,
        textAlign: 'left',
        marginTop: wp('1%'),
        fontFamily: 'Poppins-Regular',
      },
  optionView: {
    display: 'flex',
    paddingLeft: 15,
    borderLeftWidth: 1,
    borderColor: colors.darkBlue,
    height: '100%',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
    marginLeft: -10,
  },
  reports: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 20,
        height: '100%',
        marginLeft: wp('0.5%'),
        marginRight: wp('0.5%'),
      }
    : {
        marginLeft: wp('-8%'),
        width: wp('20%'),
      },
  reportsMedical: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        height: '100%',
        width: '55%',
      }
    : {
        marginLeft: wp('-8%'),
        width: wp('20%'),
      },
  report: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '40%',
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      },
  reportIcon: {
    backgroundColor: colors.darkBlue,
    padding: 7,
    borderRadius: 8,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: wp('4%'),
    height: wp('4%'),
  },
  length: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: 8,
        marginLeft: 5,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: 10,
        marginTop: 5,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('8%'),
        height: wp('8%'),
      },
  lengthText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  editBtn: isTabDevice()
    ? {
        alignItems: 'center',
        flexDirection: 'row',
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        marginRight: wp('2%'),
      },
  editBtn2: isTabDevice()
    ? {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 5,
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 5,
      },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        marginHorizontal: wp('1%'),
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        marginHorizontal: wp('1%'),
        fontSize: wp('2.5'),
        fontWeight: 'bold',
        fontFamily: 'Poppins-Regular',
      },
  highlight: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 10,
        width: '100%',
        height: 110,
      }
    : {
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        height: 90,
        marginBottom: hp('-0.5%'),
      },
  highlightColored: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        width: '100%',
        height: 110,
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        height: 90,
        marginBottom: hp('-0.5%'),
      },
  contain: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 10,
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      },
  rightView: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
  },
  updateViewSelected: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '105%',
        marginTop: 8,
        width: wp('25%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '104%',
        marginTop: 3,
        width: wp('45%'),
      },

  rightViewSelected: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: '105%',
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        marginTop: 8,
        width: '35%',
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '104%',
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        marginTop: 3,
        width: wp('45%'),
      },
  primary: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        padding: wp('1%'),
        width: '65%',
      }
    : {
        flexDirection: 'column',
        padding: wp('3%'),
        width: wp('35%'),
      },
  primaryUnselected: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        padding: wp('1%'),
        width: '100%',
      }
    : {
        flexDirection: 'column',
        padding: wp('3%'),
        width: wp('35%'),
      },
  icon: isTabDevice()
    ? {
        width: wp('2.5%'),
        height: wp('2.5%'),
      }
    : {
        width: wp('7%'),
        height: wp('7%'),
      },
  iconImage: isTabDevice()
    ? {}
    : {
        width: wp('5%'),
        height: wp('5%'),
        resizeMode: 'contain',
      },
});
export default PlayerInfoUpdateStyle;
