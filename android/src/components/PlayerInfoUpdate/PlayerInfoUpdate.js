import React, { useEffect, useState } from 'react';
import {
  Alert,
  Image,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import customPlayerCommonStyle from './PlayerInfoUpdateStyle';
import { dateTimeConversion } from '../../helpers/index';

import editIcon from '../../../assets/buttons/editIcon.png';
import deleteIcon from '../../../assets/buttons/deleteIcon.png';
import useStyles from '../../hooks/useStyles';

import DeleteModal from '../../components/modal/DeleteModal/DeleteModal';
import { userRoleType } from '../../constants/constants';
import { useSelector } from 'react-redux';
import DownloadReportModal from '../modal/DownloadReportModal/DownloadReportModal';

import { MaterialIcons } from '@expo/vector-icons';
import useColors from '../../hooks/useColors';
import { isTabDevice } from '../../config/appConfig';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

const PlayerInfoUpdate = ({
  deleteItem,
  item,
  openEditModal,
  selectedUpdate,
  setSelectedUpdate,
  setUploadedFile,
  contentType,
  isVisible,
  isFileCountVisible = true,
  iconType,
}) => {
  const colors = useColors();
  const PlayerCommonStyle = useStyles(customPlayerCommonStyle);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openDownloadModal, setOpenDownloadModal] = useState(false);
  const { createdDate, message, _id, fileUploads } = item;

  const { yearLastTwoDigit, month, date, dateString } =
    dateTimeConversion(createdDate);

  const { userData } = useSelector(state => state?.auth);

  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;

  const cancelAction = () => {
    setOpenDeleteModal(false);
  };

  const deleteAction = () => {
    deleteItem(item);
  };

  return (
    <>
      <TouchableWithoutFeedback
        onPress={() => {
          selectedUpdate === _id
            ? setSelectedUpdate(null)
            : setSelectedUpdate(_id);

          fileUploads?.length && setUploadedFile(fileUploads);
        }}
      >
        <View
          style={
            selectedUpdate === _id
              ? PlayerCommonStyle.highlightColored
              : PlayerCommonStyle.highlight
          }
        >
          <View style={PlayerCommonStyle.contain}>
            <View
              style={
                selectedUpdate === _id
                  ? PlayerCommonStyle.primary
                  : PlayerCommonStyle.primaryUnselected
              }
            >
              <View style={PlayerCommonStyle.dateView}>
                <Text style={PlayerCommonStyle.dateText}>
                  {date}/{month}/{yearLastTwoDigit}
                </Text>
                <Text style={PlayerCommonStyle.dateText2}>{dateString}</Text>
              </View>
              <View
                style={
                  selectedUpdate && selectedUpdate === _id
                    ? PlayerCommonStyle.msgWrapperSelected
                    : PlayerCommonStyle.msgWrapper
                }
              >
                <Text
                  style={PlayerCommonStyle.msg}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {message}
                </Text>
              </View>
            </View>

            <View
              style={
                selectedUpdate && selectedUpdate === _id
                  ? isVisible
                    ? PlayerCommonStyle.rightViewSelected
                    : PlayerCommonStyle.updateViewSelected
                  : PlayerCommonStyle.rightView
              }
            >
              {selectedUpdate && selectedUpdate === _id && isVisible && (
                <View
                  style={
                    iconType == 'document'
                      ? PlayerCommonStyle.reports
                      : PlayerCommonStyle.reportsMedical
                  }
                >
                  <View style={PlayerCommonStyle.report}>
                    <TouchableOpacity
                      onPress={() => setOpenDownloadModal(true)}
                      style={PlayerCommonStyle.reportIcon}
                    >
                      <MaterialIcons
                        style={PlayerCommonStyle.iconImage}
                        name="preview"
                        size={isTabDevice() ? 37 : wp('5.5%')}
                        color={colors.green}
                      />
                    </TouchableOpacity>
                    {isFileCountVisible && (
                      <View style={PlayerCommonStyle.length}>
                        <Text style={PlayerCommonStyle.lengthText}>
                          {fileUploads?.length > 0
                            ? `${fileUploads?.length}+`
                            : '0'}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              )}

              {selectedUpdate &&
                selectedUpdate === _id &&
                !isPlayer &&
                !isParent && (
                  <View style={PlayerCommonStyle.optionView}>
                    <TouchableOpacity
                      style={PlayerCommonStyle.editBtn}
                      onPress={() => openEditModal(item)}
                    >
                      <Image source={editIcon} style={PlayerCommonStyle.icon} />
                      <Text style={PlayerCommonStyle.btnText}>Edit</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={PlayerCommonStyle.editBtn2}
                      onPress={() => setOpenDeleteModal(true)}
                    >
                      <Image
                        source={deleteIcon}
                        style={PlayerCommonStyle.icon}
                      />
                      <Text style={PlayerCommonStyle.btnText}>Remove</Text>
                    </TouchableOpacity>
                  </View>
                )}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
      {openDownloadModal && (
        <DownloadReportModal
          item={item}
          setOpenDownloadModal={setOpenDownloadModal}
          contentType={contentType}
        />
      )}

      {openDeleteModal && (
        <DeleteModal
          submitAction={deleteAction}
          cancelAction={cancelAction}
          message={'Are you sure you want to delete?'}
        />
      )}
    </>
  );
};

export default PlayerInfoUpdate;
