import React from 'react';
import { View, Text, TouchableOpacity, TouchableHighlight } from 'react-native';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customNoMatchReportStyle from './NoMatchReportStyle';
import { AntDesign } from '@expo/vector-icons';
import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

const NoMatchReport = ({ showModal, closeModal, reportReGenerate }) => {
  const NoMatchReportStyle = useStyles(customNoMatchReportStyle);
  const colors = useColors();
  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <View style={NoMatchReportStyle.overlay} />
        <View style={NoMatchReportStyle.centeredView}>
          <View style={NoMatchReportStyle.modalArea}>
            <TouchableOpacity
              style={NoMatchReportStyle.closeButton}
              onPress={() => closeModal()}
            >
              <AntDesign name="close" size={20} color={colors.white} />
            </TouchableOpacity>
            <View style={NoMatchReportStyle.messageContainer}>
              <Text style={NoMatchReportStyle.message}>
                Report generation is in progress
              </Text>

              <TouchableHighlight
                onPress={() => reportReGenerate()}
                style={NoMatchReportStyle.reGenerateBtn_wrapper}
              >
                <Text style={NoMatchReportStyle.reGenerateBtn}>
                  Re-generate
                </Text>
              </TouchableHighlight>
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default NoMatchReport;
