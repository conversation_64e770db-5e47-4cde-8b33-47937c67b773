import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const NoMatchReportStyle = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('40%'),
        height: hp('30%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      }
    : {
        width: wp('70%'),
        height: hp('20%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      },
  closeButton: {
    position: 'absolute',
    right: 20,
    top: 20,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
  },
  headerTitle: {
    fontSize: wp('2%'),
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: wp('2%'),
    marginBottom: wp('2%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  messageContainer: {
    alignItems: 'center',
  },
  reGenerateBtn_wrapper: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        marginTop: hp('4%'),
        padding: wp('1.5%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('2%'),
        marginTop: hp('4%'),
        padding: wp('3%'),
      },
  reGenerateBtn: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.7%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
  message: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.7%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.red,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
      },
});

export default NoMatchReportStyle;
