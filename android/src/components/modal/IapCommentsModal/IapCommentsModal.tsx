import { AntDesign } from '@expo/vector-icons';
import React, { FC } from 'react';
import { Text, TouchableOpacity, View, FlatList } from 'react-native';
import useStyles from '../../../hooks/useStyles';

import { dateTimeConversion } from '../../../helpers';
import IAPStats from '../../../components/PlayerInfo/IAP/IAPStats';
import IapCommentsModalStyle from './IapCommentsModalStyle';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import { isTabDevice } from '../../../config/appConfig';

type CommentItem = {
  _id: string;
  date: string; // Adjust the type based on your data structure
  comment: string;

};

type IapCommentsModalType = {
  setIsIapCommentsModalOpen: (isModalOpen: boolean) => void;
  comments: CommentItem[]
  loadMoreComments: Function;
  mergedStats: any
  selectedCategoryName: String | '';
};
const IapCommentsModal: FC<IapCommentsModalType> = ({
  setIsIapCommentsModalOpen: setIsModalOpen,
  comments,
  loadMoreComments,
  mergedStats,
  selectedCategoryName
}) => {
  const IAPStyles = useStyles(IapCommentsModalStyle);

  const Comment = ({ item }: { item: CommentItem }) => {
    const timestamp: any = Date.parse(item?.date);
    let dateStr;
    let day;
    if (!isNaN(timestamp)) {
      const { year, month, date, dateString } = dateTimeConversion(timestamp);
      dateStr = date + '/' + month + '/' + year;
      day = dateString;
    }
    return (
      <View
        style={IAPStyles.commentContainer}
        onStartShouldSetResponder={() => true}
      >
        <View style={IAPStyles.dateWrapper}>
          <Text style={IAPStyles.date}>{dateStr ? dateStr : ''}</Text>
          <Text style={IAPStyles.day}>{day ? day : ''}</Text>
        </View>
        <Text style={IAPStyles.comment}>{item.comment}</Text>
      </View>
    );
  };

  const lastComment = ({ item }: { item: CommentItem }) => {
    const timestamp: any = Date.parse(item?.date);
    let dateStr;
    let day;
    if (!isNaN(timestamp)) {
      const { year, month, date, dateString } = dateTimeConversion(timestamp);
      dateStr = date + '/' + month + '/' + year;
      day = dateString;
    }
    return (
      <View style={IAPStyles.lastComment} onStartShouldSetResponder={() => true}>
        <Text style={IAPStyles.lastCommentText}>Last Updated On: </Text>
        <Text style={IAPStyles.lastCommentText}>{dateStr ? dateStr : ''} {"("}</Text>
        <Text style={IAPStyles.lastCommentTextBlue}>Current</Text>
        <Text style={IAPStyles.lastCommentText}>{")"}</Text>   
      </View>
    );
  };

  return (
    <ModalWrapper transparent visible>
      <View style={IAPStyles.centeredView}>
        <View style={IAPStyles.overlay}></View>
        <View style={IAPStyles.modalArea}>
          <TouchableOpacity
            onPress={() => setIsModalOpen(false)}
            style={IAPStyles.closeButton}
          >
            <AntDesign name="close" size={20} color="#FFFFFF" />
          </TouchableOpacity>
          <View style={IAPStyles.content}> 
            {/* header */}
            <Text style={IAPStyles.title}> 
              IAP Comments
            </Text> 
            {/* IAP stats */}
            <View style={IAPStyles.contentArea}>
              <View style={IAPStyles.leftContent}>
                <Text style={IAPStyles.catName}>
                  {selectedCategoryName}
                </Text>
                <View style={IAPStyles.statsWrapper}>
                  <IAPStats  
                    statsDefinition={mergedStats}
                    statsData={{}}
                    setUpdatedStats={() => console.log()}
                    isEditMode={false}
                    onCriteriaGraphSelect={() => console.log()}
                    IisShowGraphIcon={false}
                  />
                </View>
                {comments?.length != 0 && lastComment({ item: comments?.[0] })}
              </View>
              <View style={IAPStyles.rightContent}>
                {comments?.length != 0 ? (
                  <FlatList
                    data={comments}
                    renderItem={Comment} 
                    keyExtractor={item => item._id}
                    onEndReached={() => loadMoreComments()}
                    onEndReachedThreshold={1}
                    style={IAPStyles.commentsList}
                    contentContainerStyle={{paddingBottom: isTabDevice() ? 100 : 10}}
                  />
                ) : (
                  <Text style={IAPStyles.catName}>No comments</Text>
                ) 
                }
              </View>
            </View>
          </View>
        </View>
      </View>
    </ModalWrapper>
  );
};

export default IapCommentsModal;
