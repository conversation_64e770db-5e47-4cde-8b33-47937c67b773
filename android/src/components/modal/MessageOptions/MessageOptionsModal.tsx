import React, { useEffect, useState, FC } from 'react';
import {
  Alert,
  Modal,
  Text,
  TouchableOpacity,
  View,
  TouchableHighlight,
  FlatList,
  Image,
} from 'react-native';
import { Ionicons, AntDesign } from '@expo/vector-icons';
import useStyles from '../../../hooks/useStyles';
import customMessageOptionsModalStyles from './MessageOptionsModalStyles';
import { messageOptions } from '../../../constants/constants';

interface IMessageOptionsModal {
  setShowMsgOptionsModal: Function;
  setSelectedOption: Function;
  selectedMsgContent: any;
  hasDeleteOption?: boolean;
}

const MESSAGE_OPTIONS = [
  { type: messageOptions.REPLY },
  { type: messageOptions.FORWARD },
];

const MessageOptionsModal: FC<IMessageOptionsModal> = ({
  setShowMsgOptionsModal,
  setSelectedOption,
  selectedMsgContent,
  hasDeleteOption,
}) => {
  const MessageOptionsModalStyles = useStyles(customMessageOptionsModalStyles);
  const [options, setOptions] = useState<any>([]);

  useEffect(() => {
    handleMessageOption();
  }, [selectedMsgContent]);

  const handleMessageOption = () => {
    let updatedOption = MESSAGE_OPTIONS;

    if (selectedMsgContent.type === 'TEXT') {
      updatedOption = [...updatedOption, { type: messageOptions.COPY }];
    }
    if (selectedMsgContent.isUserOwnMessage && hasDeleteOption) {
      updatedOption = [...updatedOption, { type: messageOptions.DELETE }];
    }

    setOptions(updatedOption);
  };

  const renderItem = ({ item }: any) => (
    <TouchableOpacity onPress={() => setSelectedOption(item.type)}>
      <View style={MessageOptionsModalStyles.selectionTextWrapper}>
        <Text style={MessageOptionsModalStyles.selectionText} numberOfLines={1}>
          {item.type}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={MessageOptionsModalStyles.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={MessageOptionsModalStyles.centeredView}>
          <View style={MessageOptionsModalStyles.overlay}></View>
          <View style={MessageOptionsModalStyles.modalView}>
            <TouchableOpacity
              style={MessageOptionsModalStyles.closeButton}
              onPress={() => {
                setShowMsgOptionsModal(false);
              }}
            >
              <AntDesign
                name="close"
                size={20}
                color="#FFFFFF"
                style={MessageOptionsModalStyles.closeText}
              />
            </TouchableOpacity>
            <View style={MessageOptionsModalStyles.selectionWrapper}>
              <Text style={MessageOptionsModalStyles.modalTitle}>
                Message Options
              </Text>
              <FlatList
                data={options}
                renderItem={renderItem}
                keyExtractor={(item, index) => index.toString()}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default MessageOptionsModal;
