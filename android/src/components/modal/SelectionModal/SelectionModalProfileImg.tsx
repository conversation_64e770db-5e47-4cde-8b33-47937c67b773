import { View, Text, TouchableOpacity, Image } from 'react-native';
import React from 'react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import customInputSelectModal from '../InputSelectModal/InputSelectModalStyles';
import InputSelectModalWithProfileImg from '../InputSelectModal/InputSelectModalWithProfileImg';
import ProfileImage from '../../ProfileImage/ProfileImage';
import ExclamationIcon from '../../../../assets/buttons/exclamation.png';

interface IinputSelectModal {
  title?: string;
  items: SelectedItems;
  openModal: () => void;
  multiple?: boolean;
  enableDoneBtn?: boolean;
  isEnableAutoComplete?: boolean;
  onAutoCompleteChange?: (search: string) => void;
  isEnableAddNew?: boolean;
  isNewDataAdding?: boolean;
  addNewAction?: (search: string) => void;
  isSearchByFetch?: boolean;
  onCloseHook: (data: any) => void;
  onSelectItemHook: (data: any[]) => void;
  isUnselectedAllowed?: boolean;
  selectedItem: SelectedItems;
  isModalOpen: boolean;
  disableOnPress?: boolean;
  selectedItemLabel: string;
  selectFirstOptionOnInitialRender?: boolean;
  enableDefaultLabel?: boolean;
  renderWithProfileImg?: boolean;
  index: number;
  isAvailable: boolean;
  onReachEndHandler?: Function;
  searchByText?: Function;
}

type SelectedItems = {
  value: string;
  label: string;
  profileImageUrl?: string | undefined;
  isAvailable?: boolean;
}[];

const SelectionModalProfileImg: React.FC<IinputSelectModal> = ({
  disableOnPress,
  onCloseHook: setIsModalOpen,
  isModalOpen,
  title,
  selectedItemLabel,
  items,
  selectFirstOptionOnInitialRender,
  enableDefaultLabel,
  renderWithProfileImg,
  selectedItem,
  index,
  isAvailable,
  searchByText,
  ...props
}) => {
  const colors = useColors();
  const inputSelectModalStyles = useStyles(customInputSelectModal);

  const renderLabel = () => {
    return (
      selectedItemLabel ||
      (selectFirstOptionOnInitialRender && items?.[0]?.label) ||
      (enableDefaultLabel && title) ||
      ''
    );
  };

  return (
    <>
      <TouchableOpacity
        onPress={() => !disableOnPress && setIsModalOpen(true)}
        style={inputSelectModalStyles.inputField}
      >
        {selectedItemLabel?.length ? (
          <View style={inputSelectModalStyles.profileImageWrapper}>
            <ProfileImage
              style={inputSelectModalStyles.profileImageSelected}
              imageStyles={inputSelectModalStyles.profileImageSelected}
              profileImageUrl={selectedItem || null}
            />
            {!isAvailable && (
              <Image
                style={inputSelectModalStyles.exclamationIcon}
                source={ExclamationIcon}
              />
            )}
          </View>
        ) : null}

        <Text
          ellipsizeMode="tail"
          numberOfLines={1}
          style={inputSelectModalStyles.dropDownLabelWithImage}
        >
          {renderLabel()}
        </Text>
        <MaterialCommunityIcons
          name="arrow-down-drop-circle"
          size={22}
          color={colors.green}
        />
      </TouchableOpacity>
      {isModalOpen && (
        <InputSelectModalWithProfileImg
          onCloseHook={setIsModalOpen}
          title={title}
          items={items}
          searchByText={searchByText}
          {...props}
        />
      )}
    </>
  );
};

export default SelectionModalProfileImg;
