import { View, Text, TouchableOpacity } from 'react-native';
import React from 'react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import InputSelectModal from '../InputSelectModal/InputSelectModal';
import customInputSelectModal from '../InputSelectModal/InputSelectModalStyles';
import InputSelectModalWithProfileImg from '../InputSelectModal/InputSelectModalWithProfileImg';
import { widthPercentageToDP } from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

interface inputSelectModal {
  title?: string;
  items: {
    value: string;
    label: string;
    profileImageUrl?: string | undefined;
    isAvailable?: boolean;
  }[];
  openModal: () => void;
  multiple?: boolean;
  enableDoneBtn?: boolean;
  isEnableAutoComplete?: boolean;
  onAutoCompleteChange?: (search: string) => void;
  isEnableAddNew?: boolean;
  isNewDataAdding?: boolean;
  addNewAction?: (search: string) => void;
  isSearchByFetch?: boolean;
  onCloseHook: (data: any) => void;
  onSelectItemHook: (data: any[]) => void;
  isUnselectedAllowed?: boolean;
  selectedItem?: SelectedItems;
  isModalOpen: boolean;
  disableOnPress?: boolean;
  selectedItemLabel: string;
  selectFirstOptionOnInitialRender?: boolean;
  enableDefaultLabel?: boolean;
  renderWithProfileImg?: boolean;
  onReachEndHandler?: Function;
  defaultValues?: string[] | null;
  isCheckedIconShown?: boolean[] | null;
  isOnPlayerDevice?: boolean | false;
}

type SelectedItems = {
  value: string;
  label: string;
}[];

const SelectionModal: React.FC<inputSelectModal> = ({
  isOnPlayerDevice,
  selectedItem,
  disableOnPress,
  onCloseHook: setIsModalOpen,
  isModalOpen,
  title,
  selectedItemLabel,
  items,
  selectFirstOptionOnInitialRender,
  enableDefaultLabel,
  renderWithProfileImg,
  ...props
}) => {
  const colors = useColors();
  const inputSelectModal = useStyles(customInputSelectModal);

  const renderLabel = () => {
    return (
      selectedItemLabel ||
      (selectFirstOptionOnInitialRender && items?.[0]?.label) ||
      (enableDefaultLabel && title) ||
      ''
    );
  };
  return (
    <>
      <TouchableOpacity
        onPress={() => !disableOnPress && setIsModalOpen(true)}
        style={
          !disableOnPress
            ? inputSelectModal.inputField
            : inputSelectModal.inputField2
        }
        disabled={isOnPlayerDevice ? true : false}
      >
        <Text
          ellipsizeMode="tail"
          numberOfLines={1}
          style={inputSelectModal.dropDownLabel}
        >
          {renderLabel()}
        </Text>
        {!disableOnPress && !isOnPlayerDevice && (
          <MaterialCommunityIcons
            name="arrow-down-drop-circle"
            size={22}
            color={colors.green}
          />
        )}
      </TouchableOpacity>

      {isModalOpen && (
        <InputSelectModal
          onCloseHook={setIsModalOpen}
          title={title}
          items={items}
          {...props}
        />
      )}
    </>
  );
};

export default SelectionModal;
