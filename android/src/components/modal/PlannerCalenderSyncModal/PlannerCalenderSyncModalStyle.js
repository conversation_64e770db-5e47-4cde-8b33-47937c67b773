import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import createPersistoid from 'redux-persist/es/createPersistoid';
import { isTabDevice } from '../../../config/appConfig';

const PlannerCalenderSyncModalStyle = colors => ({
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: wp('6%'),
        maxHeight: hp('34%'),
      },
  modalView2: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: wp('6%'),
        maxHeight: hp('22%'),
      },
  modalView3: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: wp('6%'),
        maxHeight: hp('23%'),
      },
  modalView4: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: wp('6%'),
        maxHeight: hp('17%'),
      },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('-0.5'),
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('4%'),
        top: wp('4%'),
        zIndex: 9,
      },
  closeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
        position: 'absolute',
        right: 20,
        top: 20,
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        position: 'absolute',
        right: -6,
        top: -7,
      },
  syncNowFeatureWrapper: isTabDevice()
    ? {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginRight: wp('2%'),
        width: wp('8%'),
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginRight: wp('2%'),
        width: wp('17%'),
        padding: wp('1%'),
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        borderRadius: wp('20%'),
        backgroundColor: colors.lightBlue,
      },
  syncNowFeatureWrapper2: isTabDevice()
    ? {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: wp('10%'),
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        width: '100%',
      },
  syncNowFeatureIcon: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('1.7%'),
        height: wp('1.7%'),
        marginRight: 7,
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('5%'),
        height: wp('5%'),
      },
  syncNowFeatureIcon2: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('2.5%'),
        height: wp('2.5%'),
        marginRight: 7,
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('8%'),
        height: wp('8%'),
        marginRight: 10,
      },
  syncNowFeatureText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: hp('1.5%'),
        fontFamily: 'Poppins-Bold',
      },
  syncNowFeatureText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('3%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
      },
  unsyncNowFeatureText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: hp('1.6%'),
        fontFamily: 'Poppins-Medium',
      },
  heading: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('3%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: hp('1.8%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
      },
  subText: {
    color: colors.aquaBlue,
    fontSize: hp('1.5%'),
    fontStyle: 'italic',
  },
  modalContainer: isTabDevice()
    ? {}
    : {
        width: wp('57%'),
        height: '100%',
      },
  toggleWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Medium',
        marginTop: wp('0.2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Medium',
        marginTop: wp('0.2%'),
      },
  calendarIcon: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        resizeMode: 'contain',
        marginBottom: wp('1%'),
      }
    : {
        width: wp('6%'),
        height: wp('6%'),
        resizeMode: 'contain',
        marginBottom: wp('2%'),
      },
  PlannerCalenderHeader: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  PlannerCalenderBody: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonsWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  PlannerCalenderFooter: isTabDevice()
    ? {
        justifyContent: 'center',
        alignItems: 'center',
        borderTopWidth: 1,
        borderTopColor: colors.darkBlue,
        marginTop: wp('1%'),
        paddingTop: wp('1%'),
      }
    : {
        justifyContent: 'center',
        alignItems: 'center',
        borderTopWidth: 1,
        borderTopColor: colors.darkBlue,
        marginTop: wp('4%'),
        paddingTop: wp('5%'),
      },
  selectAccountWrapper: isTabDevice()
    ? {
        width: wp('25%'),
      }
    : {
        width: wp('56%'),
      },
  selectAccountList: isTabDevice()
    ? {
        maxHeight: hp('30%'),
      }
    : {
        marginTop: wp('5%'),
        maxHeight: hp('20%'),
      },
  selectAccountItem: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: colors.darkBlue,
        paddingTop: 10,
        paddingBottom: 10,
        width: wp('20%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: colors.darkBlue,
        paddingTop: 10,
        paddingBottom: 10,
        width: wp('50%'),
      },
  selectAccountLeft: {
    marginRight: wp('1.5%'),
  },
  selectAccountImage: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      }
    : {
        width: wp('10%'),
        height: wp('10%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      },
  selectAccountName: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('3%'),
        fontFamily: 'Poppins-Medium',
        lineHeight: hp('4%'),
      }
    : {
        color: colors.white,
        fontSize: hp('1.8%'),
        fontFamily: 'Poppins-Medium',
        lineHeight: hp('1.9%'),
      },
  selectAccountEmail: isTabDevice()
    ? {
        color: colors.grey,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.grey,
        fontSize: hp('1.5%'),
        fontFamily: 'Poppins-Medium',
      },
  btnTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  btnView: isTabDevice()
    ? {
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('7%'),
        width: wp('20%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  greenBg: {
    backgroundColor: colors.green,
    marginRight: 10,
  },
  darkBlueBg: {
    backgroundColor: colors.darkBlue,
  },
});
export default PlannerCalenderSyncModalStyle;
