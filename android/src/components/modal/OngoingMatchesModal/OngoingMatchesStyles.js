import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const OngoingMatchesStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('40%'),
        height: hp('60%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      }
    : {
        width: wp('80%'),
        height: hp('60%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 20,
      },
  closeButton: isTabDevice()
    ? {
        position: 'absolute',
        right: 20,
        top: 20,
      }
    : {
        position: 'absolute',
        right: 10,
        top: 10,
      },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
  },
  headerTitle: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontWeight: 'bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('4%'),
        fontWeight: 'bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  messageContainer: {
    display: 'flex',
    marginLeft: 20,
    marginRight: 20,
    flex: 1,
    marginBottom: 20,
  },
  eventLabel1: isTabDevice()
    ? {
        color: colors.white,
        fontSize: 18,
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: 17,
        fontWeight: 'bold',
      },
  eventLabel2: {
    color: colors.white,
    fontSize: 15,
    fontWeight: 'bold',
  },
  eventLabelTime: {
    color: colors.green,
    fontSize: 15,
    fontWeight: 'bold',
  },
  eventScore: {
    color: colors.white,
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
  },
  eventItem: {
    backgroundColor: colors.darkBlue,
    padding: 10,
    paddingLeft: 15,
    paddingRight: 15,
    marginRight: 10,
    marginLeft: 10,
    marginBottom: 15,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 15,
    flexDirection: 'row',
  },
  eventLeft: {
    alignItems: 'flex-start',
    flex: 0.7,
  },
  eventRight: {
    flex: 0.3,
    alignItems: 'center',
    justifyContent: 'space-evenly',
    flexDirection: 'column',
  },
  eventScoreSeperator: {
    backgroundColor: colors.lightBlue,
    height: 1,
    width: '70%',
  },
  eventDateTimeContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        flexDirection: 'column',
      },
});

export default OngoingMatchesStyles;
