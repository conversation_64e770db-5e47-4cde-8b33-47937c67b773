import { AntDesign, Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import { dateTimeConversion, openMap } from '../../../helpers';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import RsvpNoteModal from '../RsvpNoteModal/RsvpNoteModal';
import RsvpNoteViewModal from '../RsvpNoteModal/RsvpNoteViewModal';
import customEventViewModalStyles from './EventViewModalStyles';
import { isTabDevice } from '../../../config/appConfig';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const RSVPComponent = ({
  isAttending,
  rsvpData,
  handleRsvpResponse,
  setIsShowRsvpNoteModal,
  setShowRsvpViewNoteModal,
}) => {
  const EventViewModalStyles = useStyles(customEventViewModalStyles);

  return (
    <>
      <View style={EventViewModalStyles.rsvpContainer2}>
        <TouchableOpacity
          style={[
            EventViewModalStyles.rsvpButtons,
            isAttending ? EventViewModalStyles.rsvpButtonSelected : {},
          ]}
          onPress={() => handleRsvpResponse(true)}
        >
          <Text
            style={[
              EventViewModalStyles.rsvpButtonText,
              isAttending ? EventViewModalStyles.rsvpButtonTextSelected : {},
            ]}
          >
            Yes
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            EventViewModalStyles.rsvpButtons,
            isAttending === false
              ? EventViewModalStyles.rsvpButtonSelected
              : {},
          ]}
          onPress={() => {
            !rsvpData?.rsvpReason ? setIsShowRsvpNoteModal(true) : null;
          }}
        >
          <Text
            style={[
              EventViewModalStyles.rsvpButtonText,
              isAttending === false
                ? EventViewModalStyles.rsvpButtonTextSelected
                : {},
            ]}
          >
            No
          </Text>
        </TouchableOpacity>
      </View>
      {rsvpData?.rsvpReason ? (
        <View style={EventViewModalStyles.rsvpAbsenceContainer}>
          <TouchableOpacity
            onPress={() => setShowRsvpViewNoteModal(true)}
            style={EventViewModalStyles.responseLink}
          >
            <Text style={EventViewModalStyles.rsvpAbsenceTitle}>
              View Reason
            </Text>
            <Ionicons name="information-circle-sharp" size={24} color="white" />
          </TouchableOpacity>
        </View>
      ) : null}
    </>
  );
};

const EventViewModal = ({
  showModal,
  closeModal,
  saveUserRsvpResponse,
  event,
  isMatch,
  isPlayer,
  rsvpDataLoading,
  isParent,
  userData,
  setRsvpAbsenceNote,
}) => {
  const EventViewModalStyles = useStyles(customEventViewModalStyles);
  const colors = useColors();
  const {
    location,
    team,
    type,
    note,
    startTime,
    endTime,
    opponent,
    tournament,
    season,
    rsvpData,
  } = event;
  const [showRsvpViewNoteModal, setShowRsvpViewNoteModal] = useState(false);
  const [isAttending, setIsAttending] = useState(null);
  const [isPastDate, setIsPastDate] = useState(true);
  const [isShowRsvpNoteModal, setIsShowRsvpNoteModal] = useState(false);
  const [userFullName, setUserFullName] = useState(null);

  const [userProfileUrl, setUserProfileUrl] = useState(null);
  const { selectedChild } = useSelector(state => state?.common);

  useEffect(() => {
    userData && setUserFullName(userData?.firstName + ' ' + userData?.lastName),
      setUserProfileUrl(userData?.profileImageUrl);
  }, []);

  useEffect(() => {
    if (rsvpData) {
      setIsAttending(rsvpData?.isParticipating);
    }
  }, [rsvpData, rsvpDataLoading]);

  const Row = ({ children, ...props }) => (
    <View style={EventViewModalStyles.row} {...props}>
      {children}
    </View>
  );

  const handleRsvpResponse = value => {
    setIsAttending(value);
    saveUserRsvpResponse(value);
    setIsShowRsvpNoteModal(false);
  };

  const {
    dateString,
    dateReadable,
    monthString,
    hours12: startHours,
    minutesString: startMinutes,
    amPm: startAmPm,
  } = dateTimeConversion(startTime);

  const {
    hours12: endHours,
    minutesString: endMinutes,
    amPm: endAmPm,
  } = dateTimeConversion(endTime);

  useEffect(() => {
    startTime && setIsPastDate(new Date(startTime) < new Date());
  }, [startTime]);
  const renderRepeatEventFrequency = () => {
    const frequency = event?.repeatConfig?.frequency;
    return frequency.charAt(0).toUpperCase() + frequency.slice(1);
  };
  const renderRepeatEventEndDate = () => {
    const { year, month, date } = dateTimeConversion(
      event?.repeatConfig?.endDate
    );
    return ` ${date}/${month}/${year}`;
  };

  const renderContent = () => (
    <>
      <View style={EventViewModalStyles.form}>
        <View style={EventViewModalStyles.row}>
          <View style={EventViewModalStyles.inputFieldWrapper}>
            <View style={EventViewModalStyles.inputFieldLabelWrapper}>
              <Text style={EventViewModalStyles.inputFieldLabel}>
                Match Type
              </Text>
              <AntDesign
                name="star"
                size={10}
                color="black"
                style={EventViewModalStyles.requiredAstric}
              />
            </View>
            <View style={EventViewModalStyles.textContainer}>
              <Text style={EventViewModalStyles.label}>{type}</Text>
            </View>
          </View>
          <View style={EventViewModalStyles.inputFieldWrapper}>
            <View style={EventViewModalStyles.inputFieldLabelWrapper}>
              <Text style={EventViewModalStyles.inputFieldLabel}>
                Team
              </Text>
              <AntDesign
                name="star"
                size={10}
                color="black"
                style={EventViewModalStyles.requiredAstric}
              />
            </View>
            <View style={EventViewModalStyles.textContainer}>
              <Text style={EventViewModalStyles.label}>
                {team?.teamName}
              </Text>
            </View>
          </View>
        </View>
        <View style={EventViewModalStyles.row}>
          <View style={EventViewModalStyles.inputFieldWrapper}>
            <View style={EventViewModalStyles.inputFieldLabelWrapper}>
              <Text style={EventViewModalStyles.inputFieldLabel}>
                Start Time
              </Text>
              <AntDesign
                name="star"
                size={10}
                color="black"
                style={EventViewModalStyles.requiredAstric}
              />
            </View>
            <View style={EventViewModalStyles.textContainer}>
              <Text style={EventViewModalStyles.label}>
                {startHours}:{startMinutes} {startAmPm}
              </Text>
              <Image
                style={EventViewModalStyles.timerIcon}
                source={require('../../../../assets/icons/timerIcon.png')}
              />
            </View>
          </View>
          <View style={EventViewModalStyles.inputFieldWrapper}>
            <View style={EventViewModalStyles.inputFieldLabelWrapper}>
              <Text style={EventViewModalStyles.inputFieldLabel}>
                End Time
              </Text>
              <AntDesign
                name="star"
                size={10}
                color="black"
                style={EventViewModalStyles.requiredAstric}
              />
            </View>
            <View style={EventViewModalStyles.textContainer}>
              <Text style={EventViewModalStyles.label}>
                {endHours}:{endMinutes} {endAmPm}
              </Text>
              <Image
                style={EventViewModalStyles.timerIcon}
                source={require('../../../../assets/icons/timerIcon.png')}
              />
            </View>
          </View>
        </View>
        <View style={EventViewModalStyles.row}>
          <View style={EventViewModalStyles.inputFieldWrapper}>
            <View style={EventViewModalStyles.inputFieldLabelWrapper}>
              <Text style={EventViewModalStyles.inputFieldLabel}>
                Location
              </Text>
            </View>
            <TouchableOpacity onPress={() => openMap(location)}>
              <View style={EventViewModalStyles.textContainer}>
                <Text
                  style={EventViewModalStyles.label}
                  numberOfLines={isTabDevice() ? 1 : 2}
                  ellipsizeMode="tail"
                >
                  {location?.name}
                </Text>
                <Image
                  style={EventViewModalStyles.locationIcon}
                  source={require('../../../../assets/icons/locationIcon.png')}
                />
              </View>
            </TouchableOpacity>
          </View>
          {isMatch && (
            <View style={EventViewModalStyles.inputFieldWrapper}>
              <View style={EventViewModalStyles.inputFieldLabelWrapper}>
                <Text style={EventViewModalStyles.inputFieldLabel}>
                  Tournament
                </Text>
                <AntDesign
                  name="star"
                  size={10}
                  color="black"
                  style={EventViewModalStyles.requiredAstric}
                />
              </View>
              <View style={EventViewModalStyles.textContainer}>
                <Text style={EventViewModalStyles.label}>
                  {tournament?.name}
                </Text>
              </View>
            </View>
          )}
        </View>
        {isMatch && (
          <View style={EventViewModalStyles.row}>
            <View style={EventViewModalStyles.inputFieldWrapper}>
              <View style={EventViewModalStyles.inputFieldLabelWrapper}>
                <Text style={EventViewModalStyles.inputFieldLabel}>
                  Season
                </Text>
                <AntDesign
                  name="star"
                  size={10}
                  color="black"
                  style={EventViewModalStyles.requiredAstric}
                />
              </View>
              <View style={EventViewModalStyles.textContainer}>
                <Text style={EventViewModalStyles.label}>
                  {season?.name}
                </Text>
              </View>
            </View>
            <View style={EventViewModalStyles.inputFieldWrapper}>
              <View style={EventViewModalStyles.inputFieldLabelWrapper}>
                <Text style={EventViewModalStyles.inputFieldLabel}>
                  Opponent
                </Text>
                <AntDesign
                  name="star"
                  size={10}
                  color="black"
                  style={EventViewModalStyles.requiredAstric}
                />
              </View>
              <View style={EventViewModalStyles.textContainer}>
                <Text style={EventViewModalStyles.label}>
                  {opponent?.name}
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>
      <View style={[EventViewModalStyles.row2, { width: isTabDevice() ? wp("69%") : "100%" }]}>
        <View style={EventViewModalStyles.notesContainer}>
          <ScrollView contentContainerStyle={{ paddingBottom: 100 }}>
            <Text style={EventViewModalStyles.label}>{note}</Text>
          </ScrollView>
        </View>
      </View>
      {isPlayer || isParent ? (
        event?.repeatConfig && (
          <View style={EventViewModalStyles.repeatEventDetailsContainer}>
            <View
              style={EventViewModalStyles.repeatEventFrequencyContainer}
            >
              <View
                style={{
                  ...EventViewModalStyles.repeatEventDetailsFieldWrapper,
                  opacity: 0.5,
                }}
              >
                <View
                  style={
                    EventViewModalStyles.repeatEventDetailsLabelWrapper
                  }
                >
                  <Text
                    style={EventViewModalStyles.repeatEventDetailsLabel}
                  >
                    Repeat Event
                  </Text>
                </View>
                <View
                  style={
                    EventViewModalStyles.repeatEventDetailsInputField
                  }
                >
                  <Text
                    style={EventViewModalStyles.repeatEventDetailsText}
                  >
                    {renderRepeatEventFrequency()}
                  </Text>
                </View>
              </View>
            </View>
            <View
              style={EventViewModalStyles.repeatEventEndDateContainer}
            >
              <View
                style={{
                  ...EventViewModalStyles.repeatEventDetailsFieldWrapper2,
                  opacity: 0.5,
                }}
              >
                <View
                  style={
                    EventViewModalStyles.repeatEventDetailsLabelWrapper
                  }
                >
                  <Text
                    style={EventViewModalStyles.repeatEventDetailsLabel}
                  >
                    Ends on
                  </Text>
                </View>
                <View
                  style={EventViewModalStyles.repeatEventDetailsDateField}
                >
                  <Text
                    style={EventViewModalStyles.repeatEventDetailsText}
                  >
                    {renderRepeatEventEndDate()}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        )
      ) : (
        <></>
      )}
      {isPlayer || isParent ? (
        <View style={EventViewModalStyles.row3}>
          {!isPastDate ? (
            <View style={EventViewModalStyles.rsvpContainer1}>
              <View style={EventViewModalStyles.rsvpContainer}>
                <Text style={EventViewModalStyles.label2}>
                  Are you going?
                </Text>
                {rsvpDataLoading ? (
                  <View style={EventViewModalStyles.ActivitySpinner}>
                    <ActivitySpinner />
                  </View>
                ) : (
                  <RSVPComponent
                    isAttending={isAttending}
                    rsvpData={rsvpData}
                    handleRsvpResponse={handleRsvpResponse}
                    setIsShowRsvpNoteModal={setIsShowRsvpNoteModal}
                    setShowRsvpViewNoteModal={setShowRsvpViewNoteModal}
                  />
                )}
              </View>
            </View>
          ) : (
            <View style={EventViewModalStyles.rsvpContainer1}>
              <View style={EventViewModalStyles.rsvpContainer}>
                <Text style={EventViewModalStyles.label2}>
                  Are you going?
                </Text>
                {rsvpDataLoading ? (
                  <ActivitySpinner />
                ) : (
                  <>
                    <View style={EventViewModalStyles.rsvpContainer2}>
                      {isAttending === false ? (
                        <View
                          style={[
                            EventViewModalStyles.rsvpButtons,
                            EventViewModalStyles.rsvpButtonSelected,
                          ]}
                        >
                          <Text
                            style={[
                              EventViewModalStyles.rsvpButtonText,
                              EventViewModalStyles.rsvpButtonTextSelected,
                            ]}
                          >
                            No
                          </Text>
                        </View>
                      ) : (
                        <View
                          style={[
                            EventViewModalStyles.rsvpButtons,
                            EventViewModalStyles.rsvpButtonSelected,
                          ]}
                        >
                          <Text
                            style={[
                              EventViewModalStyles.rsvpButtonText,
                              EventViewModalStyles.rsvpButtonTextSelected,
                            ]}
                          >
                            Yes
                          </Text>
                        </View>
                      )}
                    </View>
                    {rsvpData?.rsvpReason ? (
                      <View
                        style={EventViewModalStyles.rsvpAbsenceContainer}
                      >
                        <TouchableOpacity
                          onPress={() => setShowRsvpViewNoteModal(true)}
                          style={EventViewModalStyles.responseLink}
                        >
                          <Text
                            style={EventViewModalStyles.rsvpAbsenceTitle}
                          >
                            View Reason
                          </Text>
                          <Ionicons
                            name="information-circle-sharp"
                            size={24}
                            color="white"
                          />
                        </TouchableOpacity>
                      </View>
                    ) : null}
                  </>
                )}
              </View>
            </View>
          )}
        </View>
      ) : null}
    </>
  )

  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <View style={EventViewModalStyles.overlay}></View>
        <View style={EventViewModalStyles.centeredView}>
          <View style={[EventViewModalStyles.modalArea, isTabDevice() ? { height: hp("85%") } : {}]}>
            <View style={EventViewModalStyles.header}>
              <TouchableOpacity
                style={EventViewModalStyles.backArrow}
                onPress={() => closeModal()}
              >
                <Ionicons name="arrow-back" size={30} color={colors.green} />
              </TouchableOpacity>

              <Text style={EventViewModalStyles.headerTitle}>
                {monthString} {dateReadable}, {dateString}
              </Text>
            </View>
            {
              isTabDevice() && <><View style={{ height: hp('80%'), width: wp('69%') }}>
                <ScrollView >
                  {renderContent()}
                </ScrollView>
              </View></>
            }
            {
              !isTabDevice() && <>{renderContent()}</>
            }


          </View>
          {isShowRsvpNoteModal ? (
            <View>
              <RsvpNoteModal
                cancelAction={setIsShowRsvpNoteModal}
                submitAction={handleRsvpResponse}
                setRsvpAbsenceNote={setRsvpAbsenceNote}
                userData={userData}
                userProfileUrl={
                  isParent ? selectedChild.profileImageUrl : userProfileUrl
                }
                userFullName={
                  isParent
                    ? selectedChild?.firstName + ' ' + selectedChild?.lastName
                    : userFullName
                }
              />
            </View>
          ) : null}
          {showRsvpViewNoteModal && (
            <RsvpNoteViewModal
              cancelAction={value => {
                setShowRsvpViewNoteModal(false);
              }}
              userProfileUrl={
                isParent ? selectedChild.profileImageUrl : userProfileUrl
              }
              userFullName={
                isParent
                  ? selectedChild?.firstName + ' ' + selectedChild?.lastName
                  : userFullName
              }
              rsvpReason={rsvpData?.rsvpReason}
            />
          )}
        </View>
      </ModalWrapper>
    </View>
  );
};

export default EventViewModal;
