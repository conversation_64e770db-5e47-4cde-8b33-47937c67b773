import { Ionicons, AntDesign } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ActivityIndicator } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { S3_BUCKET_LOCATION, userRoleType } from '../../../constants/constants';
import { USER_MANAGEMENT_SERVICE } from '../../../constants/services';
import useApiPromise from '../../../hooks/useApiPromise';
import useColors from '../../../hooks/useColors';
import useFileUpload from '../../../hooks/useFileUpload';
import useInputSelectModal from '../../../hooks/useInputSelectModal';
import useS3bucketLocation from '../../../hooks/useS3bucketLocation';
import useStyles from '../../../hooks/useStyles';
import { CREATE_DOCUMENT_REPOSITORY_SUCCESS } from '../../../store/actionTypes/DocumentRepository/DocumentRepositoryAction';
import { RootStore } from '../../../store/store';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import SelectionModal from '../SelectionModal/SelectionModal';
import CustomAddEditDocumentRepositoryModalStyle from './AddEditDocumentRepositoryModalStyle';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MAX_FILE_SIZE_ERROR = 'Maximum upload file size: 10mb';
const SOMETHING_WENT_WRONG = 'Something went wrong, please try again';

interface AddEditDocumentRepositoryModalProps {
  showModal: any;
  onClose: any;
  isEditMode: any;
  selectedItem: any;
  userId: any;
  setSelectedItem: any;
  appendDocumentRepo:any;
}

const AddEditDocumentRepositoryModal: FC<
  AddEditDocumentRepositoryModalProps
> = ({
  showModal,
  onClose,
  isEditMode,
  selectedItem,
  userId,
  setSelectedItem,
  appendDocumentRepo
}) => {
  const AddEditDocumentRepositoryModalStyle = useStyles(
    CustomAddEditDocumentRepositoryModalStyle
  );

  const colors = useColors();
  const dispatch = useDispatch();

  const [upload, uploadedContent, , isUploading] = useFileUpload();

  const [getBucketLocation, bucketLocation] = useS3bucketLocation();
  useEffect(() => {
    getBucketLocation({
      path: S3_BUCKET_LOCATION.documentRepository,
      service: USER_MANAGEMENT_SERVICE,
    });
  }, []);

  const { userDocumentTypes } = useSelector(
    (state: RootStore) => state?.documentRepository
  );
  const { userRole } = useSelector((state: RootStore) => state.auth);
  const isCoaches = userRole === userRoleType.COACH || userRole === userRoleType.HEAD_COACH;
  const [
    setIsDocumentTypeModalOpen,
    isDocumentTypeModalOpen,
    setSelectedDocumentTypeItems,
    selectedDocumentTypeItems,
  ] = useInputSelectModal();

  const getDocumentTypeLabel = (id: string) => {
    return mappedUserDocumentTypes?.find(document => document?._id === id)
      ?.name;
  };

  const { _id, documentTypeId } = selectedItem || {}; 
  const [inputTitle, setInputTitle] = useState<string | null>(null);
  const [selectedUserDocument, setSelectedUserDocument] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [isFileError, setIsFileError] = useState<any>({
    status: false,
    error: MAX_FILE_SIZE_ERROR,
  });
  const [inputFileName, setInputFileName] = useState('');

  const [selectedFile, setSelectedFile] = useState<{
    bucketName: string;
    fileKey: string;
    fileName: string;
  } | null>(null);

  const [saveDocumentRepository] = useApiPromise();


  useEffect(() => {
    const { title, _id, documentTypeId, document } = selectedItem || {};

    if (_id) {
      setInputTitle(title);
      setSelectedUserDocument(documentTypeId);
      setSelectedFile({ ...document });
    }
  }, [JSON.stringify(selectedItem)]);

  useEffect(() => {
    if (uploadedContent && inputFileName) {
      setSelectedFile({ ...uploadedContent, fileName: inputFileName });
    }
  }, [uploadedContent, inputFileName]);

  const mappedUserDocumentTypes = useMemo(() => {
    return userDocumentTypes?.map(types => {
      return {
        ...types,
        label: types.name,
        value: types._id,
      };
    });
  }, [JSON.stringify(userDocumentTypes)]);

  const onRemoveSelectedDoc = () => {
    setSelectedFile(null);
  };

  const [fileObject, setFileObject] = useState<null | any>(null);

  const startUploading = async (
    selectedFile: any,
    bucketLocation: { bucketName: string; filePath: string } | null
  ) => {
    if (selectedFile && bucketLocation?.filePath) {
      const file = await fetch(selectedFile?.uri);

      const fileBlob = await file.blob();

      const newFile = new File([fileBlob], `${selectedFile?.name}`);
      setInputFileName(selectedFile?.name || '');
      setFileObject(newFile);
    }
  };

  useEffect(() => {
    fileObject &&
      upload(
        fileObject,
        `${bucketLocation?.filePath}`,
        bucketLocation?.bucketName
      );
  }, [fileObject, bucketLocation]);

  const pickDocument = useCallback(async () => {
    try {
      const response: any = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: false,
        copyToCacheDirectory: true,
      });
      if (!response?.canceled) {

        const result = response.assets[0];

        if (10 > Number(result.size / 1048576)) {
          setIsFileError({
            status: false,
            error: undefined,
          });
          await startUploading(result, bucketLocation);
        } else {
          if (result.type !== 'cancel') {
            setIsFileError({
              status: true,
              error: MAX_FILE_SIZE_ERROR,
            });
          }
        }
      } else {
        setIsFileError({
          status: true,
          error: SOMETHING_WENT_WRONG,
        });
      }
    } catch (error) {
      setIsFileError({
        status: true,
        error: SOMETHING_WENT_WRONG,
      });
    }
  }, [bucketLocation]);

  const onSaveDocumentRepository = useCallback(() => {
    if (inputTitle && selectedUserDocument && selectedFile) {
      const payload = {
        [isEditMode ? '_id' : 'userId']: isEditMode ? _id : userId,
        title: inputTitle,
        documentTypeId: selectedUserDocument || null,
        document: { ...selectedFile },
      };
console.log();

      saveDocumentRepository(
        `/api/v1/user-documents`,
        '-',
        '-',
        '-',
        payload,
        null,
        isEditMode ? 'PUT' : 'POST',
        false,
        USER_MANAGEMENT_SERVICE
      ).then(() => {
        dispatch({
          type: CREATE_DOCUMENT_REPOSITORY_SUCCESS,
        });
        setSelectedItem(null);
        setSelectedUserDocument(null);
        setInputTitle('');
        setInputFileName('');
        setErrorMessage('');
        setIsFileError({ status: false, error: MAX_FILE_SIZE_ERROR });
        setSelectedFile(null);
        setFileObject(null);
        setSelectedDocumentTypeItems([]);
      });
    } else {
      setErrorMessage('Please fill required fields');
    }
  }, [
    inputTitle,
    selectedUserDocument,
    uploadedContent,
    isEditMode,
    documentTypeId,
    selectedFile,
  ]);

  //clean-up
  useEffect(() => {
    return () => {
      setSelectedItem(null);
      setSelectedUserDocument(null);
      setInputTitle('');
      setInputFileName('');
      setErrorMessage('');
      setIsFileError({ status: false, error: MAX_FILE_SIZE_ERROR });
      setSelectedFile(null);
    };
  }, []);

  const renderFileUpload = () => {
    if (!selectedFile) {
      return isUploading ? (
        <ActivityIndicator color={colors.white} />
      ) : (
        <TouchableOpacity onPress={pickDocument}>
            <View style={AddEditDocumentRepositoryModalStyle.upload}>    
          <View
              style={AddEditDocumentRepositoryModalStyle.uploadIconImageWrapper}
            >
              <Image
                style={AddEditDocumentRepositoryModalStyle.iconImage}
                source={require('../../../../assets/icons/medical-upload-icon.png')}
              />
              <View style={{ display: 'flex', marginLeft: 10 }}>
                <Text style={AddEditDocumentRepositoryModalStyle.btnText}>
                  Upload Document
                </Text>
                {isFileError.status && (
                  <Text style={AddEditDocumentRepositoryModalStyle.error}>
                    {isFileError.error}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    } else {
      return (
        selectedFile?.fileName && (
          <View style={AddEditDocumentRepositoryModalStyle.uploaded}>
            <Image
              style={AddEditDocumentRepositoryModalStyle.uploadIconImage}
              source={require('../../../../assets/icons/medical-icon.png')}
            />
            <Text
              ellipsizeMode="tail"
              numberOfLines={1}
              style={AddEditDocumentRepositoryModalStyle.btnText2}
            >
              {selectedFile?.fileName}
            </Text>
            <TouchableOpacity
              style={AddEditDocumentRepositoryModalStyle.uploadWrapperClose}
              onPress={onRemoveSelectedDoc}
            >
              <Ionicons name="close" color="white" />
            </TouchableOpacity>
          </View>
        )
      );
    }
  };

  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <KeyboardAwareScrollView scrollEnabled={false}> 
          <View style={AddEditDocumentRepositoryModalStyle.overlay} />
          <View style={AddEditDocumentRepositoryModalStyle.centeredView}>
            <View style={AddEditDocumentRepositoryModalStyle.modalArea}>
              <View style={AddEditDocumentRepositoryModalStyle.content}>
                {appendDocumentRepo()} 
                {isCoaches && <>
                <Text style={AddEditDocumentRepositoryModalStyle.title}>
                  {isEditMode ? 'Edit Document' : 'Upload Document'}
                </Text>
                <View style={AddEditDocumentRepositoryModalStyle.inputFieldContainer}>
                  <View
                    style={AddEditDocumentRepositoryModalStyle.inputFieldWrapper} 
                  >  
                    <View>
                      <View
                        style={
                          AddEditDocumentRepositoryModalStyle.inputFieldLabelWrapper
                        }
                      >
                        <Text
                          style={AddEditDocumentRepositoryModalStyle.inputFieldLabel}
                        >
                          Document Title 
                        </Text>
                        <AntDesign
                          name="star"
                          size={10}
                          color="black"
                          style={
                            AddEditDocumentRepositoryModalStyle.requiredAstric
                          }
                        />
                      </View>
                      <TextInput
                        style={AddEditDocumentRepositoryModalStyle.textInput}
                        onChangeText={setInputTitle}
                        placeholder="Document Title"
                        placeholderTextColor="#fff"
                        value={inputTitle || ''}
                      />
                    </View>
                  </View>
                  <View style={AddEditDocumentRepositoryModalStyle.inputFieldWrapper2}>
                    <View
                      style={
                        AddEditDocumentRepositoryModalStyle.inputFieldLabelWrapper
                      }
                    >
                      <Text
                        style={AddEditDocumentRepositoryModalStyle.inputFieldLabel}
                      >
                        Document Type
                      </Text>
                      <AntDesign
                        name="star"
                        size={10}
                        color="black"
                        style={AddEditDocumentRepositoryModalStyle.requiredAstric}
                      />
                    </View>
                    <SelectionModal
                      title={'Document Type'}
                      items={mappedUserDocumentTypes}
                      onCloseHook={setIsDocumentTypeModalOpen}
                      onSelectItemHook={item => {
                        setSelectedUserDocument(item?.[0]?.value);
                        setSelectedDocumentTypeItems(item);
                      }}
                      defaultValues={
                        isEditMode
                          ? [documentTypeId]
                          : [selectedDocumentTypeItems?.[0]?.value]
                      }
                      isEnableAutoComplete
                      selectedItemLabel={
                        selectedDocumentTypeItems?.[0]?.label ||
                        getDocumentTypeLabel(documentTypeId) ||
                        'Document Type'
                      }
                      isModalOpen={isDocumentTypeModalOpen}
                      selectedItem={selectedDocumentTypeItems.map(
                        ({ value, label }) => ({ value, label })
                      )}
                      openModal={() => {}}
                      
                    />
                  </View>
                </View>
                <View>
                  <View
                    style={
                      AddEditDocumentRepositoryModalStyle.inputFieldLabelWrapper
                    }
                  >
                    <Text
                      style={AddEditDocumentRepositoryModalStyle.inputFieldLabel}
                    >
                      Upload Document
                    </Text>
                    <AntDesign
                      name="star"
                      size={10}
                      color="black"
                      style={AddEditDocumentRepositoryModalStyle.requiredAstric}
                    />
                  </View>
                  {renderFileUpload()}
                </View>
                {errorMessage ? (
                  <View
                    style={
                      AddEditDocumentRepositoryModalStyle.inputFieldWrapper
                    }
                  >
                    <Text style={AddEditDocumentRepositoryModalStyle.error}>
                      {errorMessage}
                    </Text>
                  </View>
                ) : null}
                <View style={AddEditDocumentRepositoryModalStyle.submitRow}>
                  <TouchableOpacity
                    onPress={() => onClose()}
                    style={AddEditDocumentRepositoryModalStyle.cancelButton}
                    disabled={isUploading}
                  >
                    <Text
                      style={AddEditDocumentRepositoryModalStyle.buttonText}
                    > 
                      Cancel
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => onSaveDocumentRepository()}
                    style={AddEditDocumentRepositoryModalStyle.submitButton}
                    disabled={isUploading || !bucketLocation?.bucketName}
                  >
                    {isUploading ? (
                      <ActivityIndicator color={colors.white} />
                    ) : (
                      <Text
                        style={AddEditDocumentRepositoryModalStyle.buttonText}
                      >
                        Save
                      </Text>
                    )}
                  </TouchableOpacity>
                </View>
                </>}
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>
      </ModalWrapper>
    </View>
  );
};
export default AddEditDocumentRepositoryModal;
