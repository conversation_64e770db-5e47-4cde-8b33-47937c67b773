import { StyleSheet, Text, View, Dimensions, Platform } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { isRTL } from 'expo-localization';

const ChangePasswordModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
      // width: wp('40%'),
      // height: hp('20%'),
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.tileBackground,
      borderRadius: 30,
      paddingVertical: wp('5%'),
      paddingHorizontal: wp('3%'),
      width: wp('72%'),
      maxHeight: hp('90%')
    }
    : {
      width: wp('90%'),
      flexDirection: 'column',
      alignItems: 'center',
      backgroundColor: colors.tileBackground,
      borderRadius: 30,
      padding: wp('4%'),
      maxHeight: hp('90%'),
      height: hp('65%')
    },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 5,
    fontSize: wp('2%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  content: isTabDevice()
    ? {
      width: wp('70%'),
    }
    : {
      width: '100%',
    },
  title: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('2.5%'),
      fontFamily: 'Poppins-Bold',
      marginBottom: hp('2%'),
    }
    : {
      color: colors.white,
      fontSize: wp('5%'),
      fontFamily: 'Poppins-Bold',
      marginBottom: wp('2%'),
    },
  inputFieldWrapper: isTabDevice()
    ? {
      marginBottom: wp('1%'),
    }
    : {
      marginBottom: wp('3.5%'),
    },
  inputFieldWrapper2: isTabDevice()
    ? {
      marginBottom: wp('1%'),
      width : wp('30%')
    }
    : {
      marginBottom: wp('3.5%'),
    },
  inputFieldContainer: isTabDevice() ? {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '95%',
  } : {
  },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
      color: colors.green,
      fontSize: wp('1.3%'),
      fontFamily: 'Poppins-Regular',
      marginBottom: wp('0.5%'),
    }
    : {
      color: colors.green,
      fontSize: wp('3.5%'),
      fontFamily: 'Poppins-Regular',
      marginBottom: wp('0.5%'),
    },
  inputField: {
    width: '100%',
    height: hp('6%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  inputView: isTabDevice()
    ? {
      borderRadius: wp('0.7%'),
      paddingRight: wp('1.5%'),
      width: wp('38%'),
      flexDirection: 'row',
      position: 'relative',
      alignItems: 'center',
    }
    : {
      borderRadius: wp('0.7%'),
      padding: wp('1.5%'),
      paddingRight: wp('1.5%'),
      width: wp('60%'),
      flexDirection: 'row',
      position: 'relative',
      alignItems: 'center',
    },
  textInput: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.2%'),
      width: wp('30%'),
      paddingTop: wp('0.7%'),
      paddingBottom: wp('0.7%'),
      paddingLeft: wp('1%'),
      paddingRight: wp('1%'),
      backgroundColor: colors.semiDarkBlue,
      borderRadius: wp('1%'),
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
      width: '100%',
      padding: wp('1.2%'),
      backgroundColor:
        Platform.OS === 'android' ? colors.semiDarkBlue : colors.transparent,
      borderRadius: Platform.OS === 'android' ? wp('2%') : 0,
      paddingLeft: Platform.OS === 'android' ? wp('2%') : 0,
      marginLeft: wp('-1%'),
    },
  errorText: isTabDevice()
    ? {
      color: colors.red,
      fontSize: wp('1.5%'),
      width: wp('24%'),
      padding: wp('1.5%'),
    }
    : {
      color: colors.red,
      fontSize: wp('3%'),
      width: '100%',
      padding: wp('1.5%'),
    },
  submitButton: isTabDevice()
    ? {
      backgroundColor: colors.aquaBlue,
      height: wp('4%'),
      width: wp('10%'),
      borderRadius: wp('1%'),
      alignItems: 'center',
      justifyContent: 'center',
    }
    : {
      backgroundColor: colors.aquaBlue,
      height: wp('7%'),
      width: wp('20%'),
      borderRadius: wp('1%'),
      alignItems: 'center',
      justifyContent: 'center',
    },
  cancelButton: isTabDevice()
    ? {
      height: wp('4%'),
      width: wp('10%'),
      borderRadius: wp('1%'),
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.darkBlue,
      marginRight: wp('1%'),
    }
    : {
      height: wp('7%'),
      width: wp('20%'),
      borderRadius: wp('1%'),
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.darkBlue,
      marginRight: wp('1%'),
    },
  buttonText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.3%'),
      fontFamily: 'Poppins-Medium',
    }
    : {
      color: colors.white,
      fontSize: wp('3%'),
      fontFamily: 'Poppins-Medium',
    },
  submitRow: isTabDevice()
    ? {
      width: '95%',
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      marginTop: wp('1%'),
      marginBottom: wp('1%'),
    }
    : {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      alignItems: 'center',
      marginTop: wp('3%'),
      marginBottom: wp('1%'),
    },
    upload: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        padding: wp('0.5%'),
        paddingTop: wp('0.3%'),
        paddingBottom: wp('0.3%'),
        width: wp('66.5%'),
        display: 'flex',
        flexDirection: 'row',
        marginRight: wp('1%'),
        // marginTop: wp('-1%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('2%'),
        padding: wp('0.5%'),
        paddingTop: wp('0.3%'),
        paddingBottom: wp('0.3%'),
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        marginRight: wp('1%'),
        // marginTop: wp('-1%'),
      },
  uploadIconImageWrapper: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    padding: 10,
    paddingRight: 30,
  },
  uploadIconImage: isTabDevice()
    ? {
        height: wp('2.5%'),
        width: wp('2.5%'),
        resizeMode: 'contain',
        marginRight: wp('0.5%'),
      }
    : {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        height: wp('7%'),
        width: wp('7%'),
        resizeMode: 'contain',
      },
      uploaded: isTabDevice()
      ? {
          backgroundColor: colors.semiDarkBlue,
          borderRadius: wp('1%'),
          padding: wp('1.2%'),
          width: '95%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          marginRight: wp('1%'),
        }
      : {
          backgroundColor: colors.semiDarkBlue,
          borderRadius: wp('2%'),
          padding: wp('2%'),
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          marginRight: wp('1%'),
        },
    uploadWrapperClose: {
      fontSize: wp('1.5%'),
      position: 'absolute',
      top: '50%',
      right: 10,
      zIndex: 2,
    },
  iconImage: isTabDevice()
    ? {
      height: wp('2%'),
      width: wp('2%'),
      resizeMode: 'contain',
    }
    : {
      height: wp('7%'),
      width: wp('7%'),
      resizeMode: 'contain',
    },
  btnText: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.2%'),
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
    },
  btnText2: isTabDevice()
    ? {
      color: colors.white,
      fontSize: wp('1.2%'),
      width: '70%',
    }
    : {
      color: colors.white,
      fontSize: wp('3.5%'),
      width: '75%',
    },
  error: isTabDevice()
    ? {
      color: colors.red,
      fontSize: wp('1%'),
      fontFamily: 'Poppins-Medium',
    }
    : {
      color: colors.red,
      fontSize: wp('2.5%'),
      fontFamily: 'Poppins-Medium',
      textAlign: 'center',
    },
  picker: isTabDevice()
    ? {
      backgroundColor: colors.darkBlue,
      borderRadius: wp('1%'),
      fontSize: wp('2.5%'),
      padding: wp('0.3%'),
      color: 'white',
      width: wp('30%'),
    }
    : {
      backgroundColor: colors.darkBlue,
      color: 'white',
      borderRadius: wp('2%'),
      fontSize: wp('3.5%'),
      padding: wp('0.3%'),
      width: wp('100%'),
    },
  dropdownTextInputWrapperStyle: isTabDevice()
    ? {
      borderWidth: 0,
      backgroundColor: colors.darkBlue,
      borderRadius: wp('1%'),
      padding: wp('1%'),
      width: wp('30%'),
      height: hp('9%'),
    }
    : {
      borderWidth: 0,
      backgroundColor: colors.darkBlue,
      height: wp('10%'),
      color: colors.white,
      width: wp('60%'),
      padding: wp('1.5%'),
      borderRadius: Platform.OS === 'android' ? wp('2%') : 0,
      paddingLeft: Platform.OS === 'android' ? wp('2%') : 0,
    },
  dropdownTextInputStyle: isTabDevice()
    ? {
      fontSize: wp('1.5%'),
    }
    : {
      fontSize: wp('3%'),
    },
  dropdownListItemStyle: isTabDevice()
    ? {
      height: hp('5%'),
      marginBottom: hp('0.5%'),
    }
    : {
      height: hp('5%'),
      marginBottom: hp('1.5%'),
      width: wp('60%'),
    },
  dropdownOverlayCustomStyle: isTabDevice()
    ? {
      marginLeft: wp('-0.3%'),
      marginTop: wp('4%'),
    }
    : {
      marginLeft: wp('0.3%'),
      marginTop: wp('6%'),
    },
  requiredAstric: isTabDevice()
    ? {
      color: colors.red,
      fontFamily: 'Poppins-Bold',
      fontSize: hp('0.7%'),
    }
    : {
      color: colors.red,
      fontFamily: 'Poppins-Bold',
      fontSize: wp('1%'),
    },
});

export default ChangePasswordModalStyles;
