import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import React from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import useStyles from '../../../hooks/useStyles';
import customChangePasswordModal from './styles';

const SuccessModal: React.FC<{
  modalVisible: boolean;
  setModalVisible: Function;
}> = ({ modalVisible, setModalVisible }) => {
  const ChangePasswordModal = useStyles(customChangePasswordModal);

  return (
    <>
      <View style={ChangePasswordModal.container}>
        {modalVisible && (
          <Modal animationType="slide" transparent={true}>
            <View style={ChangePasswordModal.centeredView}>
              <View style={ChangePasswordModal.overlay}></View>
              <View style={ChangePasswordModal.modalArea}>
                <TouchableOpacity
                  style={ChangePasswordModal.closeButton}
                  onPress={() => {
                    setModalVisible(false);
                  }}
                >
                  <AntDesign
                    name="close"
                    size={20}
                    color="#FFFFFF"
                    style={ChangePasswordModal.closeText}
                  />
                </TouchableOpacity>
                <View style={ChangePasswordModal.playerListContainer2}>
                  <MaterialCommunityIcons
                    name="checkbox-marked-circle"
                    size={40}
                    color="#36d982"
                  />
                  <Text style={ChangePasswordModal.title2}>
                    You have successfully changed the password!
                  </Text>
                </View>
              </View>
            </View>
          </Modal>
        )}
      </View>
    </>
  );
};

export default SuccessModal;
