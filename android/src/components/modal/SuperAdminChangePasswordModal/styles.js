import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { isRTL } from 'expo-localization';

const ChangePasswordModalStyles = colors => ({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  error: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        marginTop: wp('1%'),
      }
    : {
        color: colors.red,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        marginTop: wp('2%'),
      },
  modalArea: isTabDevice()
    ? {
        width: wp('36%'),
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('3%'),
      }
    : {
        width: wp('90%'),
        flexDirection: 'column',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('5%'),
      },
  closeButton: {
    position: 'absolute',
    right: 20,
    top: 15,
    fontSize: wp('2%'),
    zIndex: 100,
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  leftContent: isTabDevice()
    ? {
        width: '20%',
      }
    : {
        width: '100%',
        marginBottom: wp('5%'),
      },
  rightContent: isTabDevice()
    ? {
        // padding: wp('1%'),
      }
    : {
        width: '100%',
      },
  title: isTabDevice()
    ? {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        // textAlign: 'center',
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('6%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        // textAlign: 'center',
        marginBottom: wp('4%'),
      },
  title2: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        textAlign: 'center',
        marginTop: wp('2%'),
      }
    : {
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        textAlign: 'center',
        marginTop: wp('4%'),
      },
  subText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Medium',
      },
  userProfile: isTabDevice()
    ? {}
    : {
        flexDirection: 'row',
        // justifyContent: 'center',
        alignItems: 'center',
      },
  image: {
    width: wp('15%'),
    height: wp('17%'),
    borderRadius: wp('2%'),
  },
  userName: isTabDevice()
    ? {
        marginTop: wp('2%'),
      }
    : {
        marginLeft: wp('2%'),
      },
  name: isTabDevice()
    ? {
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      }
    : {
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      },
  requiredAstric: {
    color: colors.red,
    fontWeight: 'bold',
    fontSize: wp('0.7%'),
  },
  inputFieldWrapper: {
    // width: '45%',
    // backgroundColor: colors.black,
  },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      },
  inputField: {
    width: '100%',
    height: hp('6%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  inputView: isTabDevice()
    ? {
        borderRadius: wp('0.7%'),
        // padding: wp('1.5%'),
        paddingRight: wp('1.5%'),
        width: wp('38%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
      }
    : {
        borderRadius: wp('0.7%'),
        padding: wp('1.5%'),
        paddingRight: wp('1.5%'),
        width: wp('60%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
      },
  textInputView: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        width: wp('24%'),
        padding: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        width: '100%',
        padding: wp('1.5%'),
      },
  errorText: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.5%'),
        width: wp('24%'),
        padding: wp('1.5%'),
      }
    : {
        color: colors.red,
        fontSize: wp('3%'),
        width: '100%',
        padding: wp('1.5%'),
      },
  submitButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        height: wp('7%'),
        width: wp('20%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  submitRow: isTabDevice()
    ? {
        width: wp('25%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        width: '100%',
        marginTop: wp('3%'),
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
      },

  textInputView: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        marginTop: hp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 20,
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('4%'),
        marginTop: hp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: wp('7%'),
        height: wp('15%'),
      },

  textInputViewWrapper: isTabDevice()
    ? {
        paddingLeft: 2,
        width: '77%',
        marginRight: 3,
      }
    : {
        paddingLeft: 2,
        width: '70%',
        marginRight: 9,
      },
  textInput: isTabDevice()
    ? {
        height: wp('5%'),
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        height: wp('8%'),
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Regular',
      },
  textInput2: isTabDevice()
    ? {
        height: wp('5%'),
        color: colors.grey,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.grey,
        height: wp('8%'),
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Regular',
      },
  icon: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        width: wp('3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5,5%'),
        width: wp('6%'),
        marginRight: wp('3%'),
      },
  eyeIcon: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        width: wp('3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5,5%'),
        width: wp('6%'),
      },
  btn: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        height: wp('4%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: wp('1%'),
        marginTop: hp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        height: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: wp('4%'),
        marginTop: hp('2%'),
      },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Regular',
      },

  playerListContainer: {
    width: '100%',
  },

  playerListContainer2: {
    width: '100%',
    alignItems: 'center',
  },
});

export default ChangePasswordModalStyles;
