import { AntDesign, Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Modal, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import colors from '../../../config/colors';
import {
  changePasswordConst,
  passwordRegex,
} from '../../../constants/constants';
import useStyles from '../../../hooks/useStyles';
import customChangePasswordModal from './styles';

const SuperAdminChangePasswordModal: React.FC<{
  modalVisible: boolean;
  setModalVisible: Function;
  userDetails: any;
  password: string;
  setPassword: Function;
  onPasswordSubmit: Function;
  passwordResetLoading: boolean;
  isPasswordResetError: boolean;
}> = ({
  modalVisible,
  setModalVisible,
  userDetails,
  password,
  setPassword,
  onPasswordSubmit,
  passwordResetLoading,
  isPasswordResetError,
}) => {
  const ChangePasswordModal = useStyles(customChangePasswordModal);
  const [hasValidPassword, setHasValidPassword] = useState<boolean>(true);
  const [passwordVisibility, setPasswordVisibility] = useState(false);

  const onPasswordSubmitValidate = () => {
    if (passwordRegex.test(password)) {
      setHasValidPassword(true);
      onPasswordSubmit(password, userDetails);
    } else {
      setHasValidPassword(false);
    }
  };

  return (
    <View style={ChangePasswordModal.centeredView}>
      <View style={ChangePasswordModal.container}>
        {modalVisible && (
          <Modal animationType="slide" transparent={true}>
            <View style={ChangePasswordModal.centeredView}>
              <View style={ChangePasswordModal.overlay}></View>
              <View style={ChangePasswordModal.modalArea}>
                <TouchableOpacity
                  style={ChangePasswordModal.closeButton}
                  onPress={() => {
                    setPassword('');
                    setModalVisible(false);
                    setHasValidPassword(true);
                  }}
                >
                  <AntDesign
                    name="close"
                    size={20}
                    color="#FFFFFF"
                    style={ChangePasswordModal.closeText}
                  />
                </TouchableOpacity>
                <View style={ChangePasswordModal.playerListContainer}>
                  <Text style={ChangePasswordModal.title}>Change Password</Text>
                  <Text style={ChangePasswordModal.subText}>
                    Please enter this user's new password.
                  </Text>
                  <View style={ChangePasswordModal.textInputView}>
                    <Ionicons name="mail" style={ChangePasswordModal.icon} />
                    <View style={ChangePasswordModal.textInputViewWrapper}>
                      <TextInput
                        style={ChangePasswordModal.textInput2}
                        value={userDetails?.emailId}
                        editable={false}
                        placeholder="Email Address"
                        placeholderTextColor="#FFF"
                      />
                    </View>
                  </View>
                  <View style={ChangePasswordModal.textInputView}>
                    <Ionicons
                      name="lock-closed"
                      style={ChangePasswordModal.icon}
                    />
                    <View style={ChangePasswordModal.textInputViewWrapper}>
                      <TextInput
                        style={ChangePasswordModal.textInput}
                        onChangeText={password => {
                          setPassword(password);
                          !hasValidPassword &&
                            setHasValidPassword(passwordRegex.test(password));
                        }}
                        value={password}
                        secureTextEntry={!passwordVisibility}
                        placeholder="Password"
                        placeholderTextColor="#FFF"
                      />
                    </View>
                    <TouchableOpacity
                      onPress={() => {
                        setPasswordVisibility(!passwordVisibility);
                      }}
                    >
                      {passwordVisibility ? (
                        <Ionicons
                          name="eye-off-sharp"
                          style={ChangePasswordModal.eyeIcon}
                        />
                      ) : (
                        <Ionicons
                          name="eye-sharp"
                          style={ChangePasswordModal.eyeIcon}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                  <View>
                    {!hasValidPassword ? (
                      <Text style={ChangePasswordModal.error}>
                        {changePasswordConst.PASSWORD_ERROR_MESSAGE}
                      </Text>
                    ) : (
                      isPasswordResetError && (
                        <Text style={ChangePasswordModal.error}>
                          {changePasswordConst.NetWorkError}
                        </Text>
                      )
                    )}

                    <TouchableOpacity
                      onPress={onPasswordSubmitValidate}
                      style={ChangePasswordModal.btn}
                    >
                      {passwordResetLoading ? (
                        <ActivityIndicator color={colors.white} />
                      ) : (
                        <Text style={ChangePasswordModal.btnText}>Submit</Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </Modal>
        )}
      </View>
    </View>
  );
};

export default SuperAdminChangePasswordModal;
