import { AntDesign, Entypo } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import React, { FC, useEffect, useState } from 'react';
import { get_url_extension, dateTimeConversion } from '../../../helpers';
import {
  FlatList,
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import useGeneratedFileUrl from '../../../hooks/useGeneratedFileUrl';
import useStyles from '../../../hooks/useStyles';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import { pastMessageChatMemberInfo } from './../../../store/reducers/Message/MessageReducer';
import customChatProfileModalStyle from './ChatProfileModalStyles';
import useFileUpload from '../../../hooks/useFileUpload';
import {
  messageFileTypes,
  S3_BUCKET_LOCATION,
} from '../../../constants/constants';
import useFileMetaData from '../../../hooks/useFileMetaData';
import useApi from '../../../hooks/useApi';
import {
  SET_TEAM_PROFILE_PICTURE_REQUEST,
  SET_TEAM_PROFILE_PICTURE_SUCCESS,
  SET_TEAM_PROFILE_PICTURE_FAIL,
} from '../../../store/actionTypes/Message/MessageAction';
import { MESSAGING_SERVICE } from '../../../constants/services';
import useS3bucketLocation from '../../../hooks/useS3bucketLocation';

interface IChatProfileModalProps {
  setChatProfileModalOpen: Function;
  memberList: pastMessageChatMemberInfo[];
  selectedTeamProfile: any;
  setImageUploading: Function;
  isImageUplading: boolean;
  isAllowtoClick: boolean;
  selectedTeamProfileId: String;
}

const ChatProfileModal: FC<IChatProfileModalProps> = ({
  setChatProfileModalOpen,
  memberList,
  selectedTeamProfile,
  setImageUploading,
  isImageUplading,
  isAllowtoClick,
  selectedTeamProfileId,
}) => {
  const ChatProfileModalStyle = useStyles(customChatProfileModalStyle);
  const [image, setImage] = useState<any>(null);
  const { name } = selectedTeamProfile;
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const [isFileUploading, setIsFileUploading] = useState(false);
  const [startFileUpload, setStartFileUpload] = useState(false);
  const [s3FilePath, setS3FilePath] = useState<string | null>(null);
  const [filePath, setFilePath] = useState<string | null>(null);
  const [getFileMetaData, fileMetaData] = useFileMetaData();
  const [upload, uploadedContent, uploadProgress] = useFileUpload();
  const [getBucketLocation, bucketLocation] = useS3bucketLocation();
  const [setImageUpload] = useApi();
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: 'Maximum upload file size: 3mb',
  });
  useEffect(() => {
    if (selectedTeamProfile?.image && selectedTeamProfile?.image?.bucketName) {
      s3FileObject({
        bucketName: selectedTeamProfile?.image?.bucketName,
        fileKey: selectedTeamProfile?.image?.fileKey,
      });
    }
  }, [JSON.stringify(selectedTeamProfile)]);

  useEffect(() => {
    setImageUploading(false);
  }, []);

  useEffect(() => {
    getBucketLocation({
      path: S3_BUCKET_LOCATION.chat,
      service: MESSAGING_SERVICE,
    });
  }, []);

  useEffect(() => {
    if (!isDownloading && url?.length) {
      setImage(url);
    }
  }, [isDownloading, url]);

  const changeFileName = (selectedFileName: string) => {
    const sentDate = new Date();
    const {
      monthNumberString,
      dateNumberString,
      hours24String,
      minutesString,
      year,
    } = dateTimeConversion(sentDate);

    const fileNameExtension = selectedFileName.split('.')[1];

    return `Koach_${year}_${monthNumberString}_${dateNumberString}_at_${hours24String}_${minutesString}.${fileNameExtension}`;
  };

  useEffect(() => {
    if (uploadProgress < 100) {
      setIsFileUploading(true);
    }
  }, [uploadProgress]);

  useEffect(() => {
    if (bucketLocation?.filePath && selectedTeamProfileId?.length) {
      setS3FilePath(
        `/${bucketLocation.filePath}` + selectedTeamProfileId + '/'
      );
    }
  }, [selectedTeamProfileId, bucketLocation]);

  //api call to upload image to db
  useEffect(() => {
    if (!isFileUploading && !isImageUplading && uploadedContent) {
      setImageUploading(false);

      let payload = {
        _id: selectedTeamProfileId,
        type: 'TEAM',
        image: {
          fileName: `Team Profile image`,
          bucketName: uploadedContent?.bucketName,
          fileKey: uploadedContent?.fileKey,
        },
      };
      setImageUpload(
        '/api/v1/chats',
        SET_TEAM_PROFILE_PICTURE_REQUEST,
        SET_TEAM_PROFILE_PICTURE_SUCCESS,
        SET_TEAM_PROFILE_PICTURE_FAIL,
        payload,
        '',
        'PUT',
        undefined,
        MESSAGING_SERVICE
      );
    }
  }, [uploadedContent, isFileUploading, isImageUplading]);

  useEffect(() => {
    if (uploadedContent && fileMetaData) {
      const { fileType } = fileMetaData;

      let uploadedFileType = 'file';

      if (['image'].includes(fileType)) {
        uploadedFileType = fileType;
      }

      setIsFileUploading(false);
      setStartFileUpload(false);

      const s3BucketDetails = {
        bucketName: uploadedContent?.bucketName,
        fileKey: uploadedContent?.fileKey,
      };

      if (uploadedContent?.bucketName && uploadedContent?.fileKey) {
        s3FileObject(s3BucketDetails);
      }
    }
  }, [uploadedContent, fileMetaData]);

  useEffect(() => {
    const startUploading = async () => {
      if (filePath) {
        const picture = await fetch(filePath);
        const pictureBlob = await picture.blob();
        const file = new File([pictureBlob], `${selectedFileName}`);
        setStartFileUpload(true);
        //Need to make upload path dynamic
        upload(file, s3FilePath, bucketLocation?.bucketName || '');
      }
    };
    startUploading();
  }, [filePath]);

  const getFileInfo = async (fileURI: string) => {
    const fileInfo = await FileSystem.getInfoAsync(fileURI);
    return fileInfo;
  };

  const pickImage = async () => {
    const response: any = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    if (!response.canceled) {
      setIsFileError({
        status: false,
        error: '',
      });

      const result = response.assets[0];

      const fileExtension = get_url_extension(result.uri);
      if (messageFileTypes.includes(fileExtension.toLowerCase())) {
        const fileInfo: any = await getFileInfo(result.uri);
        if (3 > Number(fileInfo?.size / 1048576)) {
          getFileMetaData(result.uri);

          const filename = result.uri.split('/').pop();
          setSelectedFileName(changeFileName(filename));
          setFilePath(result.uri);
        } else {
          setIsFileError({
            status: true,
            error: 'Maximum upload file size: 3mb',
          });
        }
      } else {
        setIsFileError({
          status: true,
          error: 'Unsupported File Type',
        });
      }
    }
  };

  const renderItem = ({ item = {} }: any) => {
    const { firstName, lastName, profileImageUrl } = item;
    return (
      <TouchableOpacity>
        <View style={ChatProfileModalStyle.listItem}>
          <View style={ChatProfileModalStyle.listItemImageContainer}>
            <Image
              style={ChatProfileModalStyle.listItemImage}
              source={
                profileImageUrl
                  ? { uri: profileImageUrl }
                  : require('../../../../assets/profilepictures/default_coach.png')
              }
            />
          </View>
          <Text style={ChatProfileModalStyle.listItemText}>
            {firstName || ''} {lastName || ''}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderUploadedmag = () => (
    <>
      {image ? (
        <View style={ChatProfileModalStyle.uploadImageWrapper}>
          <Image
            style={ChatProfileModalStyle.uploadImage}
            source={{
              uri: image,
            }}
          />
        </View>
      ) : (
        <View style={ChatProfileModalStyle.uploadImageWrapper}>
          <Entypo name="camera" size={24} color="white" />
        </View>
      )}
    </>
  );

  const renderImgUpload = () => {
    return (
      <TouchableOpacity onPress={() => isAllowtoClick && pickImage()}>
        {isImageUplading ? (
          <ActivitySpinner color="white" />
        ) : (
          renderUploadedmag()
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={ChatProfileModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={ChatProfileModalStyle.centeredView}>
          <View style={ChatProfileModalStyle.overlay}></View>
          <View style={ChatProfileModalStyle.modalView}>
            <TouchableOpacity
              style={ChatProfileModalStyle.closeButton}
              onPress={() => setChatProfileModalOpen(false)}
            >
              <AntDesign
                name="close"
                size={20}
                color="#FFFFFF"
                style={ChatProfileModalStyle.closeText}
              />
            </TouchableOpacity>
            <View style={ChatProfileModalStyle.playerListContainer}>
              <View style={ChatProfileModalStyle.playerListContainerTop}>
                {renderImgUpload()}
                {isFileError.status && (
                  <Text style={ChatProfileModalStyle.error}>
                    {isFileError.error}
                  </Text>
                )}
                <Text style={ChatProfileModalStyle.playerListContainerTopTitle}>
                  {name}
                </Text>
              </View>
              <View style={ChatProfileModalStyle.playerListContainerBottom}>
                <Text
                  style={ChatProfileModalStyle.playerListContainerBottomTitle}
                >
                  Members
                </Text>
                <FlatList
                  data={memberList}
                  renderItem={renderItem}
                  keyExtractor={(_item, index) => index.toString()}
                  style={ChatProfileModalStyle.playerList}
                />
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ChatProfileModal;
