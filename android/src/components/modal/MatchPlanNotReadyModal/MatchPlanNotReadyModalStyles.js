import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MatchPlanNotReadyModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('25%'),
        height: hp('30%'),
        padding: 15,
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      }
    : {
        width: wp('80%'),
        height: hp('20%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      },
  closeButton: isTabDevice()
    ? {
        position: 'absolute',
        right: 20,
        top: 20,
      }
    : {
        position: 'absolute',
        right: 10,
        top: 10,
      },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    width: '100%',
  },
  headerTitle: isTabDevice()
    ? {
        fontSize: hp('3%'),
        fontWeight: 'bold',
        color: colors.white,
        marginBottom: wp('2%'),
        textAlign: 'center',
        width: '100%',
      }
    : {
        fontSize: wp('4%'),
        fontWeight: 'bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  messageContainer: {
    display: 'flex',
    marginLeft: 30,
    marginRight: 30,
    flex: 1,
    marginBottom: 20,
    alignItems: 'center',
  },
  message: isTabDevice()
    ? {
        color: colors.red,
        fontSize: hp('2.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      }
    : {
        color: colors.red,
        fontSize: hp('2.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      },
});

export default MatchPlanNotReadyModalStyles;
