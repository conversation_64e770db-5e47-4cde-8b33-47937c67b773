import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customMatchPlanNotReadyModalStyles from './MatchPlanNotReadyModalStyles';
import { AntDesign } from '@expo/vector-icons';

import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

const MatchPlanNotReadyModal = ({ showModal, closeModal }) => {
  const MatchPlanNotReadyModalStyles = useStyles(
    customMatchPlanNotReadyModalStyles
  );
  const colors = useColors();
  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <View style={MatchPlanNotReadyModalStyles.overlay} />
        <View style={MatchPlanNotReadyModalStyles.centeredView}>
          <View style={MatchPlanNotReadyModalStyles.modalArea}>
            <TouchableOpacity
              style={MatchPlanNotReadyModalStyles.closeButton}
              onPress={() => closeModal()}
            >
              <AntDesign name="close" size={20} color={colors.white} />
            </TouchableOpacity>
            <View style={MatchPlanNotReadyModalStyles.header}>
              <Text style={MatchPlanNotReadyModalStyles.headerTitle}>
                This match has not been planned
              </Text>
            </View>
            <View style={MatchPlanNotReadyModalStyles.messageContainer}>
              <Text style={MatchPlanNotReadyModalStyles.message}>
                Please set the formation and return to this screen
              </Text>
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default MatchPlanNotReadyModal;
