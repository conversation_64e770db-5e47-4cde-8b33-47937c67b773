import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Linking,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch } from 'react-redux';
import useColors from '../../../hooks/useColors';
import useGeneratedFileUrl from '../../../hooks/useGeneratedFileUrl';
import useStyles from '../../../hooks/useStyles';
import { REPORT_RESET } from '../../../store/actionTypes/UploadReport/uploadreportAction';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customDownloadReportModalStyles from './DownloadReportModalStyles';

const DownloadReportModal = ({ item, setOpenDownloadModal, contentType }) => {
  const DownloadReportModalStyles = useStyles(customDownloadReportModalStyles);
  const colors = useColors();
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  const [selectedFile, setSelectedFile] = useState();
  const dispatch = useDispatch();

  useEffect(() => {
    return () => {
      dispatch({
        type: REPORT_RESET,
      });
    };
  }, []);
  const downloadFile = url => {
    if (selectedFile) {
      Linking.openURL(url);
    }
  };

  useEffect(() => {
    url && downloadFile(url);
  }, [url]);

  const [selectedFileIndex, setSelectedFileIndex] = useState(0);

  const { fileUploads, message } = item;
  return (
    <View style={DownloadReportModalStyles.container}>
      <ModalWrapper transparent>
        <View style={DownloadReportModalStyles.centeredView}>
          <View style={DownloadReportModalStyles.overlay}></View>
          <View style={DownloadReportModalStyles.content}>
            <TouchableOpacity
              style={DownloadReportModalStyles.close}
              onPress={() => setOpenDownloadModal(false)}
            >
              <Ionicons
                name="close"
                style={{ fontSize: 25, color: 'white' }}
              />
            </TouchableOpacity>

            <Text style={DownloadReportModalStyles.message}>{contentType}</Text>

            <ScrollView style={DownloadReportModalStyles.scrollText}>
              <Text style={DownloadReportModalStyles.text}>{message}</Text>
            </ScrollView>

            {fileUploads?.length > 0 && (
              <Text style={DownloadReportModalStyles.description}>
                Download Reports
              </Text>
            )}

            <ScrollView style={DownloadReportModalStyles.scroll}>
              <View style={DownloadReportModalStyles.reports}>
                {fileUploads?.length > 0 &&
                  fileUploads?.map((report, index) => (
                    <View style={DownloadReportModalStyles.report} key={index}>
                      <TouchableOpacity
                        onPress={() => {
                          setSelectedFile(report);
                          s3FileObject(report);
                          setSelectedFileIndex(index);
                        }}
                      >
                        <View style={DownloadReportModalStyles.downloadWrapper}>
                          <View
                            style={
                              DownloadReportModalStyles.downloadInnerWrapper
                            }
                          >
                            <Image
                              style={DownloadReportModalStyles.iconImage}
                              source={require('../../../../assets/icons/medical-icon.png')}
                            />
                            <Text
                              style={DownloadReportModalStyles.downloadFile}
                            >
                              {report?.fileName || ''}
                            </Text>
                          </View>

                          {selectedFileIndex === index && isDownloading ? (
                            <ActivityIndicator color="green" />
                          ) : (
                            <Image
                              style={DownloadReportModalStyles.iconImage}
                              source={require('../../../../assets/icons/download-icon.png')}
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                  ))}
              </View>
            </ScrollView>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default DownloadReportModal;
