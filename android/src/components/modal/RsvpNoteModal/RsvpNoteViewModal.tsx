import React, { FC, useEffect } from 'react';
import {
  View,
  ActivityIndicator,
  TouchableHighlight,
  Text,
  Image,
  TextInput,
} from 'react-native';
import colors from '../../../config/colors';
import useStyles from '../../../hooks/useStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customRsvpNoteModal from './RsvpNoteViewModalStyles';
import { AntDesign } from '@expo/vector-icons';
import styles from '../../DropdownPickerModal/item/styles';
import ProfileImage from '../../../components/ProfileImage/ProfileImage';
import { ScrollView } from 'react-native-gesture-handler';

type RsvpNoteViewModalType = {
  loading: boolean;
  cancelAction: Function;
  submitAction: Function;
  setRsvpAbsenceNote: Function;
  errorMessage: any;
  userFullName: string;
  userProfileUrl: string;
  rsvpReason: string;
};
const RsvpNoteViewModal: FC<RsvpNoteViewModalType> = ({
  cancelAction,
  rsvpReason,
  userFullName,
  userProfileUrl,
}) => {
  const RsvpNoteViewModalStyles = useStyles(customRsvpNoteModal);

  return (
    <View>
      <View style={RsvpNoteViewModalStyles.container}>
        <ModalWrapper transparent>
          <View style={RsvpNoteViewModalStyles.centeredView}>
            <View style={RsvpNoteViewModalStyles.overlay}></View>
            <View style={RsvpNoteViewModalStyles.modalView}>
              <View style={RsvpNoteViewModalStyles.modalTitleContainer}>
                <TouchableHighlight
                  underlayColor={'transparent'}
                  onPress={() => cancelAction(false)}
                >
                  <AntDesign
                    style={RsvpNoteViewModalStyles.backIcon}
                    name="left"
                    size={16}
                    color="white"
                  />
                </TouchableHighlight>
                <Text style={RsvpNoteViewModalStyles.modalTitle}>
                  Reason for absence
                </Text>
              </View>
              <View style={RsvpNoteViewModalStyles.playerContainer}>
                <ProfileImage
                  imageStyles={RsvpNoteViewModalStyles.playerProfilePicture}
                  style={RsvpNoteViewModalStyles.loading}
                  profileImageUrl={userProfileUrl}
                />
                <Text style={RsvpNoteViewModalStyles.playerName}>
                  {userFullName}
                </Text>
              </View>
              <View style={RsvpNoteViewModalStyles.TextInputNote}>
                <ScrollView>
                  <TextInput
                    style={RsvpNoteViewModalStyles.TextInputNoteText}
                    multiline={true}
                    value={rsvpReason}
                    editable={false}
                  />
                </ScrollView>
              </View>
            </View>
          </View>
        </ModalWrapper>
      </View>
    </View>
  );
};

export default RsvpNoteViewModal;
