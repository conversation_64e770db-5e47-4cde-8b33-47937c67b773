import React, { FC, useEffect } from 'react';
import {
  View,
  ActivityIndicator,
  TouchableHighlight,
  Text,
  Image,
  TextInput,
} from 'react-native';
import colors from '../../../config/colors';
import useStyles from '../../../hooks/useStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customRsvpNoteModal from './RsvpNoteModalStyles';
import { AntDesign } from '@expo/vector-icons';
import styles from '../../DropdownPickerModal/item/styles';
import ProfileImage from '../../../components/ProfileImage/ProfileImage';

type RsvpNoteModalType = {
  loading: boolean;
  cancelAction: Function;
  submitAction: Function;
  setRsvpAbsenceNote: Function;
  errorMessage: any;
  userFullName: string;
  userProfileUrl: string;
};
const RsvpNoteModal: FC<RsvpNoteModalType> = ({
  loading,
  cancelAction,
  submitAction,
  errorMessage,
  setRsvpAbsenceNote,
  userFullName,
  userProfileUrl,
}) => {
  const RsvpNoteModalStyles = useStyles(customRsvpNoteModal);

  return (
    <View>
      <View style={RsvpNoteModalStyles.container}>
        <ModalWrapper transparent>
          <View style={RsvpNoteModalStyles.centeredView}>
            <View style={RsvpNoteModalStyles.overlay}></View>
            <View style={RsvpNoteModalStyles.modalView}>
              <View style={RsvpNoteModalStyles.modalTitleContainer}>
                <TouchableHighlight
                  underlayColor={'transparent'}
                  onPress={() => cancelAction(false)}
                >
                  <AntDesign
                    style={RsvpNoteModalStyles.backIcon}
                    name="left"
                    size={16}
                    color="white"
                  />
                </TouchableHighlight>
                <Text style={RsvpNoteModalStyles.modalTitle}>
                  Reason for absence
                </Text>
              </View>
              <View style={RsvpNoteModalStyles.playerContainer}>
                <ProfileImage
                  imageStyles={RsvpNoteModalStyles.playerProfilePicture}
                  style={RsvpNoteModalStyles.loading}
                  profileImageUrl={userProfileUrl}
                />
                <Text style={RsvpNoteModalStyles.playerName}>
                  {userFullName}
                </Text>
              </View>
              <View>
                <TextInput
                  style={RsvpNoteModalStyles.TextInputNote}
                  multiline={true}
                  textAlignVertical={'top'}
                  onChangeText={value => setRsvpAbsenceNote(value)}
                />
              </View>

              <View>
                {errorMessage ? (
                  <Text
                    style={RsvpNoteModalStyles.errorMessage}
                    numberOfLines={2}
                  >
                    {errorMessage.includes(
                      RsvpNoteModalStyles.MATCH_ALREADY_STARTED
                    )
                      ? RsvpNoteModalStyles.ErrorMessage
                      : errorMessage}
                  </Text>
                ) : null}
              </View>
              <View style={RsvpNoteModalStyles.modalContent}>
                {loading ? (
                  <View
                    style={{
                      ...RsvpNoteModalStyles.buttonContainer,
                      ...RsvpNoteModalStyles.confirmButton,
                    }}
                  >
                    <ActivityIndicator color={colors.white} />
                  </View>
                ) : (
                  <TouchableHighlight
                    style={{
                      ...RsvpNoteModalStyles.buttonContainer,
                      ...RsvpNoteModalStyles.confirmButton,
                    }}
                    onPress={() => submitAction(false)}
                  >
                    <Text style={RsvpNoteModalStyles.textStyle}>Save</Text>
                  </TouchableHighlight>
                )}

                <TouchableHighlight
                  style={{
                    ...RsvpNoteModalStyles.buttonContainer,
                    ...RsvpNoteModalStyles.cancelButton,
                  }}
                  onPress={() => cancelAction(false)}
                  disabled={loading}
                >
                  <Text style={RsvpNoteModalStyles.textStyle}>Cancel</Text>
                </TouchableHighlight>
              </View>
            </View>
          </View>
        </ModalWrapper>
      </View>
    </View>
  );
};

export default RsvpNoteModal;
