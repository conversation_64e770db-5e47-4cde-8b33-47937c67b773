import React from 'react';
import { Modal, Text, View, TouchableOpacity } from 'react-native';

import customUnsavedChangesModalStyle from './UnsavedChangesModalStyle';
import useStyles from '../../../hooks/useStyles';

const UnsavedChangesModal = ({
  modalVisible,
  setModalVisible,
  setModalResponse,
  message,
}) => {
  const UnsavedChangesModalStyle = useStyles(customUnsavedChangesModalStyle);
  const onButtonPress = value => {
    setModalResponse(value);
    setModalVisible(false);
  };
  return (
    <View style={UnsavedChangesModalStyle.centeredView}>
      <Modal animationType="slide" transparent={true} visible={modalVisible}>
        <View style={UnsavedChangesModalStyle.centeredView}>
          <View style={UnsavedChangesModalStyle.overlay}></View>
          <View style={UnsavedChangesModalStyle.modalView}>
            <Text style={UnsavedChangesModalStyle.modalText}>
              {message || 'Are you sure you want to discard changes made?'}
            </Text>
            <View style={UnsavedChangesModalStyle.buttonRow}>
              <TouchableOpacity
                style={UnsavedChangesModalStyle.openButton}
                onPress={() => onButtonPress(true)}
              >
                <Text style={UnsavedChangesModalStyle.buttonText}>Yes</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  UnsavedChangesModalStyle.openButton,
                  UnsavedChangesModalStyle.cancelButtonColor,
                ]}
                onPress={() => onButtonPress(false)}
              >
                <Text style={UnsavedChangesModalStyle.buttonText}>No</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default UnsavedChangesModal;
