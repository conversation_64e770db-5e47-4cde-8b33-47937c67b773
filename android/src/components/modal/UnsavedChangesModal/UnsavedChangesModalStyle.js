import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const UnsavedChangesModalStyle = colors => ({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonRow: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: {
    margin: 20,
    backgroundColor: '#263b58',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    width: 270,
    height: 170,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  openButton: isTabDevice()
    ? {
        backgroundColor: '#1DC4D2',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: wp('6%'),
        height: 45,
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: '#1DC4D2',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: wp('20%'),
        height: 45,
        alignItems: 'center',
        justifyContent: 'center',
      },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  addView: {
    alignItems: 'flex-end',
    alignContent: 'flex-end',
    paddingHorizontal: 20,
  },
  AddTouchableOpacity: {
    height: 60,
    width: 60,
    paddingBottom: 5,
    borderRadius: 60,
    backgroundColor: '#1dc4d2',
    alignItems: 'center',
  },
  AddIcon: {
    paddingTop: 12,
  },
  cancelButtonColor: {
    backgroundColor: colors.darkBlue,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 18,
  },
});
export default UnsavedChangesModalStyle;
