import React, { FC } from 'react';
import {
  Text,
  View,
  TouchableHighlight,
  ActivityIndicator,
} from 'react-native';
import customDeleteModalStyles from './DeleteRecurringConfirmationModalStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import { deleteRecurringEventsModalTexts } from '../../../constants/constants';
import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

type DeleteRecurringConfirmationModalType = {
  loading: boolean;
  message: string;
  submitAction: Function;
  cancelAction: Function;
  isTeamModal: any;
  subMessage: string;
  errorMessage: any;
};

const DeleteRecurringConfirmationModal: FC<
  DeleteRecurringConfirmationModalType
> = ({
  submitAction,
  cancelAction,
  message,
  subMessage,
  isTeamModal,
  loading,
  errorMessage,
}) => {
  const DeleteModalStyles = useStyles(customDeleteModalStyles);
  const colors = useColors();
  return (
    <View style={DeleteModalStyles.container}>
      <ModalWrapper transparent>
        <View style={DeleteModalStyles.centeredView}>
          <View style={DeleteModalStyles.overlay}></View>
          <View
            style={[
              DeleteModalStyles.modalView,
              isTeamModal && DeleteModalStyles.deleteTeamModalView,
            ]}
          >
            <Text style={DeleteModalStyles.modalText}>{message}</Text>

            <View>
              {errorMessage ? (
                <Text style={DeleteModalStyles.errorMessage} numberOfLines={2}>
                  {errorMessage.includes(
                    deleteRecurringEventsModalTexts.MATCH_ALREADY_STARTED
                  )
                    ? deleteRecurringEventsModalTexts.ErrorMessage
                    : errorMessage}
                </Text>
              ) : null}
            </View>
            <View style={DeleteModalStyles.modalContent}>
              {loading ? (
                <View
                  style={{
                    ...DeleteModalStyles.openButton,
                    ...DeleteModalStyles.yesButton,
                  }}
                >
                  <ActivityIndicator color={colors.white} />
                </View>
              ) : errorMessage ? (
                <TouchableHighlight
                  style={{
                    ...DeleteModalStyles.openButton,
                    ...DeleteModalStyles.yesButton,
                    opacity: 0.5,
                  }}
                  onPress={() => submitAction()}
                  disabled={true}
                >
                  <Text style={DeleteModalStyles.textStyle}>Yes</Text>
                </TouchableHighlight>
              ) : (
                <TouchableHighlight
                  style={{
                    ...DeleteModalStyles.openButton,
                    ...DeleteModalStyles.yesButton,
                  }}
                  onPress={() => submitAction()}
                >
                  <Text style={DeleteModalStyles.textStyle}>Yes</Text>
                </TouchableHighlight>
              )}

              <TouchableHighlight
                style={{
                  ...DeleteModalStyles.openButton,
                  ...DeleteModalStyles.noButton,
                }}
                onPress={() => cancelAction()}
                disabled={loading}
              >
                <Text style={DeleteModalStyles.textStyle}>No</Text>
              </TouchableHighlight>
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default DeleteRecurringConfirmationModal;
