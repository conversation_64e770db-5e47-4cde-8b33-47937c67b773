import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const DeleteRecurringEventModalStyles = colors => ({
  container: {},
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        paddingLeft: wp('1%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('35%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('4%'),
        alignItems: 'center',
        width: wp('80%'),
      },
  modalTitleContainer: { marginLeft: wp('-5%') },
  modalTitle: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
      },
  modalContent: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        marginRight: wp('-12%'),
      }
    : { flexDirection: 'row', alignItems: 'center', alignContent: 'center' },
  confirmButton: {
    backgroundColor: colors.aquaBlue,
    marginHorizontal: 0,
  },
  cancelButton: {
    backgroundColor: colors.darkBlue,
    marginLeft: 10,
  },
  buttonContainer: isTabDevice()
    ? {
        backgroundColor: '#F194FF',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: wp('10%'),
        height: 45,
      }
    : {
        backgroundColor: '#F194FF',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: wp('22%'),
        height: 45,
      },
  textStyle: isTabDevice()
    ? {
        color: 'white',
        fontWeight: 'bold',
        textAlign: 'center',
        fontSize: 17,
      }
    : {
        color: 'white',
        fontWeight: 'bold',
        textAlign: 'center',
        fontSize: 14,
      },
  flatListDataContainer: {
    flex: 1,
    flexDirection: 'row',
    marginBottom: 15,
    alignItems: 'baseline',
  },
  flatListDataOptionText: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,

        fontSize: wp('1.6%'),
        marginLeft: 5,
      }
    : {
        marginBottom: 10,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('4%'),
        marginLeft: 5,
      },

  modalOptionsContainer: isTabDevice()
    ? {
        height: hp('20%'),
        width: '75%',
        marginLeft: wp('-5%'),
      }
    : { height: hp('20%'), width: '75%', marginLeft: wp('-8%') },

  errorMessage: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.red,
        width: wp('30%'),

        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.red,
        width: wp('60%'),
        marginBottom: wp('3%'),
      },
});

export default DeleteRecurringEventModalStyles;
