import { AntDesign, Ionicons, SimpleLineIcons } from '@expo/vector-icons';
import { Video } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import React, { FC, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Linking,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import { messageContentType } from '../../../constants/constants';
import useGeneratedFileUrl from '../../../hooks/useGeneratedFileUrl';
import useStyles from '../../../hooks/useStyles';
import CacheImage from '../../CacheImage/CacheImage';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customAddTeamStyles from './MessageMediaPreviewStyle';
interface MediaPreviewProps {
  closeModal: any;
  fileContent: any;
  userData: any;
  msgTimeDate: any;
  type: any;
}

const MediaPreview: FC<MediaPreviewProps> = ({
  closeModal,
  fileContent,
  userData,
  msgTimeDate,
  type,
}) => {
  const { hours12, minutesString, amPm, sendDate } = msgTimeDate;
  const MediaPreviewStyles = useStyles(customAddTeamStyles);
  const [s3FileObjectPreview, urlPreview, isDownloadingPreview] =
    useGeneratedFileUrl();
  const video = React.useRef(null);
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  const [s3FileObjectShare, urlShare, isDownloadingShare] =
    useGeneratedFileUrl();
  const [status, setStatus] = React.useState({});
  const [urlState, setUrlState] = useState(null);
  const [preLoading, setIsPreloading] = useState(true);

  const [profilePic, setProfilePic] = useState(null);

  useEffect(() => {
    if (userData?.profileImageUrl?.length) {
      setProfilePic(userData?.profileImageUrl);
    }
  }, [userData]);

  const onCloseButtonClicked = () => {
    closeModal();
  };
  useEffect(() => {
    if (fileContent?.fileKey && fileContent?.bucketName) {
      s3FileObjectPreview({
        ...({
          fileKey: fileContent?.fileKey,
          bucketName: fileContent?.bucketName,
        } || {}),
      });
    }
  }, [fileContent]);

  const downloadFile = (url: string) => {
    Linking.openURL(url);
  };
  useEffect(() => {
    url && downloadFile(url);
  }, [url]);
  useEffect(() => {
    urlShare && shareFile(urlShare);
  }, [urlShare]);

  useEffect(() => {
    urlPreview && setUrlState(urlPreview);
  }, [urlPreview]);

  const shareFile = (url: string) => {
    const fileName = fileContent?.fileName || '';
    FileSystem.downloadAsync(
      url,
      FileSystem.documentDirectory + `${fileName?.replace(/ /g, '') || ''}`
    )
      .then(async ({ uri }) => {
        Sharing.shareAsync(uri);
      })
      .catch(error => {
        console.log(error, 'error');
      });
  };

  return (
    <View style={MediaPreviewStyles.centeredView}>
      <ModalWrapper transparent visible>
        <View style={MediaPreviewStyles.centeredView}>
          <View style={MediaPreviewStyles.modalView}>
            <View style={MediaPreviewStyles.modalViewTop}>
              <View style={MediaPreviewStyles.modalViewTop1}>
                <TouchableOpacity onPress={() => onCloseButtonClicked()}>
                  <Ionicons name="arrow-back" size={30} color="white" />
                </TouchableOpacity>
              </View>
              <View style={MediaPreviewStyles.modalViewTop2}>
                <View style={MediaPreviewStyles.modalViewTopPropic}>
                  <CacheImage
                    style={MediaPreviewStyles.messageRightTopProImage}
                    uri={profilePic || ''}
                  />
                </View>
                <View style={MediaPreviewStyles.modalViewTopDetails}>
                  <View style={MediaPreviewStyles.modalViewTopDetails1}>
                    <Text style={MediaPreviewStyles.modalText}>
                      {userData
                        ? `${
                            (userData?.firstName || '') +
                            ' ' +
                            (userData?.lastName || '')
                          }`
                        : 'You'}
                    </Text>
                  </View>
                  <View style={MediaPreviewStyles.modalViewTopDetails2}>
                    <Text style={MediaPreviewStyles.modalText}>
                      {' '}
                      {hours12 || ''}
                      {'.'}
                      {minutesString || ''} {amPm || ''}
                    </Text>
                    <Text style={MediaPreviewStyles.modalText}>
                      {' '}
                      {sendDate || ''}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={MediaPreviewStyles.modalViewTop3}>
                {isDownloadingShare ? (
                  <ActivityIndicator color="green" />
                ) : (
                  <TouchableOpacity
                    onPress={() =>
                      s3FileObjectShare({
                        ...({
                          fileKey: fileContent?.fileKey,
                          bucketName: fileContent?.bucketName,
                        } || {}),
                      })
                    }
                    style={{ width: 25, height: 25 }}
                  >
                    <SimpleLineIcons
                      name="share"
                      size={isTabDevice() ? 24 : 20}
                      color="white"
                    />
                  </TouchableOpacity>
                )}

                {isDownloading ? (
                  <ActivityIndicator color="green" />
                ) : (
                  <TouchableOpacity
                    onPress={() =>
                      s3FileObject({
                        ...({
                          fileKey: fileContent?.fileKey,
                          bucketName: fileContent?.bucketName,
                        } || {}),
                      })
                    }
                    style={{ width: 25, height: 25 }}
                  >
                    <AntDesign
                      name="download"
                      size={isTabDevice() ? 24 : 20}
                      color="white"
                    />
                  </TouchableOpacity>
                )}
              </View>
            </View>
            <View style={MediaPreviewStyles.modalViewBottom}>
              {isDownloadingPreview || !urlState ? (
                <ActivityIndicator color="green" size="large" />
              ) : (
                <View style={MediaPreviewStyles.modalViewBottomImage}>
                  {type === messageContentType.VIDEO ? (
                    <>
                      {preLoading && (
                        <ActivityIndicator
                          color="green"
                          size="large"
                          style={MediaPreviewStyles.modalViewBottomLoader}
                        />
                      )}
                      <Video
                        ref={video}
                        style={MediaPreviewStyles.modalViewBottomImage}
                        source={{
                          uri: urlState,
                        }}
                        useNativeControls
                        resizeMode="contain"
                        isLooping
                        shouldPlay
                        onPlaybackStatusUpdate={status => {
                          setStatus(status);
                        }}
                        onLoadStart={() => setIsPreloading(true)}
                        onReadyForDisplay={() => setIsPreloading(false)}
                      />
                    </>
                  ) : (
                    <CacheImage
                      style={MediaPreviewStyles.modalViewBottomImage}
                      uri={urlState}
                      isProfilePic={false}
                    />
                  )}
                </View>
              )}
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default MediaPreview;
