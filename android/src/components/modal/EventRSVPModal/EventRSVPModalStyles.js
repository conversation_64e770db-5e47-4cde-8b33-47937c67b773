import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

import { colorPalette } from '../../../constants/constants';
import { isTabDevice } from '../../../config/appConfig';

const EventRSVPModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('40%'),
        height: hp('70%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        position: 'relative',
      }
    : {
        width: wp('85%'),
        height: hp('70%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        position: 'relative',
      },
  closeButtonWrapper: {
    position: 'absolute',
    right: 20,
    top: 0,
    width: '5%',
    height: '50%',
  },
  closeButton: isTabDevice()
    ? {
        fontSize: wp('2%'),
      }
    : {
        fontSize: wp('5%'),
      },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
  },
  headerTitle: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  userListContainer: {
    display: 'flex',
    marginLeft: 15,
    marginRight: 15,
    flex: 1,
    marginBottom: 20,
  },
  userListItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  profileImage: {
    width: 50,
    height: 50,
    resizeMode: 'cover',
    borderRadius: 10,
  },
  left: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flex: 1,
  },
  userName: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2%'),
        marginLeft: 20,
        flexShrink: 1,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        marginLeft: 10,
        width: wp('30%'),
      },
  responseText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.8%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
      },
  responseContainer: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        width: hp('16%'),
        height: wp('3%'),
        borderRadius: 40,
        marginTop: 5,
        marginBottom: 5,
        marginRight: 15,
      }
    : {
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('25%'),
        height: wp('7.5%'),
        borderRadius: wp('10%'),
        marginTop: 5,
        marginBottom: 5,
        marginRight: 10,
      },
  responseYes: {
    backgroundColor: colors.green,
  },
  responseNo: {
    backgroundColor: colors.red,
  },
  responseNotAvailable: {
    backgroundColor: colors.lightGrey,
  },
  exclamationIcon: isTabDevice()
    ? {
        position: 'absolute',
        top: 0,
        right: -3,
        width: wp('1.5%'),
        height: wp('1.5%'),
      }
    : {
        position: 'absolute',
        top: -wp('1.5%'),
        right: -wp('2.5%'),
        width: wp('4.5%'),
        height: wp('4.5%'),
      },
  rsvpCountWrapper: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        padding: wp('1.5%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 15,
        width: '100%',
      }
    : {
        backgroundColor: colors.darkBlue,
        padding: wp('2.5%'),
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 15,
      },
  rsvpCountText: isTabDevice()
    ? {
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      }
    : {
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      },
  rsvpCount: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: wp('1%'),
      }
    : {
        minWidth: wp('7%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  rsvpCountTab: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        paddingTop: wp('0.4%'),
        paddingBottom: wp('0.4%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        paddingTop: wp('0.4%'),
        paddingBottom: wp('0.4%'),
      },
  rsvpCountTabWrapper: {
    backgroundColor: colors.borderBlue,
    borderRadius: 5,
    width: '30%',
  },
  rsvpCountDivider: isTabDevice()
    ? {
        borderWidth: 1,
        height: '100%',
        borderColor: colors.borderBlue,
        marginLeft: 5,
        marginRight: 5,
      }
    : {
        borderWidth: 1,
        height: '100%',
        borderColor: colors.borderBlue,
        marginLeft: 5,
        marginRight: 5,
      },
  rsvpResponseContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    position: 'relative',
  },
  responseNoteIcon: isTabDevice()
    ? {
        opacity: 0.5,
        position: 'absolute',
        top: -2,
        right: 4,
      }
    : {
        opacity: 0.5,
        position: 'absolute',
        top: -3,
        right: 4,
      },
  responseNoText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.8%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
      },
  noData: {
    height: hp('30%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default EventRSVPModalStyles;
