import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  TouchableHighlight,
} from 'react-native';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customEventRSVPModalStyles from './EventRSVPModalStyles';
import { AntDesign, Ionicons } from '@expo/vector-icons';
import ProfileImage from '../../ProfileImage/ProfileImage';
import ExclamationIcon from '../../../../assets/buttons/exclamation.png';
import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';
import {
  rsvpTotalCountText,
  RSVP_RESPONSES,
} from '../../../constants/constants';
import RsvpNoteViewModal from '../RsvpNoteModal/RsvpNoteViewModal';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import NoContentMessage from '../../NoContents/NoContentMessage';
import { ActivityIndicator } from 'react-native-paper';
import { isTabDevice } from '../../../config/appConfig';

const EventRSVPModal = ({
  showModal,
  closeModal,
  responses,
  loadNextPage,
  rsvpTotalRecords,
  setShowModal,
  rsvpStatus,
  setRsvpStatus,
  loading,
}) => {
  const EventRSVPModalStyles = useStyles(customEventRSVPModalStyles);
  const colors = useColors();
  const [showRsvpNoteModal, setIsShowRsvpNoteModal] = useState(false);
  const [profileImageUrl, setProfileImageUrl] = useState(null);
  const [fullName, setFullName] = useState(null);
  const [rsvpReason, setRsvpReason] = useState(null);

  const ListItem = ({ item = {} }) => {
    const {
      profileImageUrl,
      firstName,
      lastName,
      isParticipating,
      isAvailable,
      rsvpReason,
    } = item;
    const hasResponded = item.hasOwnProperty('isParticipating');

    return (
      <View style={EventRSVPModalStyles.userListItem}>
        <View style={EventRSVPModalStyles.left}>
          <View>
            <ProfileImage
              imageStyles={EventRSVPModalStyles.profileImage}
              profileImageUrl={profileImageUrl}
            />
            {!isAvailable && (
              <Image
                style={EventRSVPModalStyles.exclamationIcon}
                source={ExclamationIcon}
              />
            )}
          </View>
          <Text style={EventRSVPModalStyles.userName} numberOfLines={2}>
            {firstName || ''} {lastName || ''}
          </Text>
        </View>
        <View
          style={[
            EventRSVPModalStyles.responseContainer,
            hasResponded
              ? isParticipating
                ? EventRSVPModalStyles.responseYes
                : EventRSVPModalStyles.responseNo
              : EventRSVPModalStyles.responseNotAvailable,
          ]}
        >
          <View
            style={
              !isParticipating &&
              EventRSVPModalStyles.rsvpResponseContentContainer
            }
          >
            <Text
              style={
                hasResponded
                  ? isParticipating
                    ? EventRSVPModalStyles.responseText
                    : rsvpReason
                    ? {
                        ...EventRSVPModalStyles.responseNoText,
                        marginLeft: isTabDevice() ? wp('0') : wp('0'),
                      }
                    : EventRSVPModalStyles.responseNoText
                  : EventRSVPModalStyles.responseText
              }
            >
              {hasResponded ? (isParticipating ? 'Yes' : 'No') : 'No Response'}
            </Text>

            {hasResponded ? (
              !isParticipating && rsvpReason ? (
                <View style={EventRSVPModalStyles.responseNoteIcon}>
                  <TouchableOpacity
                    onPress={() => {
                      setTimeout(() => {
                        setIsShowRsvpNoteModal(true);
                      }, 1);
                      setProfileImageUrl(profileImageUrl),
                        setFullName(firstName + ' ' + lastName),
                        setRsvpReason(rsvpReason);
                      closeModal();
                    }}
                  >
                    <Ionicons
                      name="information-circle-sharp"
                      size={isTabDevice() ? 24 : 19}
                      color="black"
                    />
                  </TouchableOpacity>
                </View>
              ) : null
            ) : null}
          </View>
        </View>
      </View>
    );
  };

  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <View style={EventRSVPModalStyles.overlay} />
        <View style={EventRSVPModalStyles.centeredView}>
          <View style={EventRSVPModalStyles.modalArea}>
            <View style={EventRSVPModalStyles.header}>
              <Text style={EventRSVPModalStyles.headerTitle}>
                RSVP Response
              </Text>

              <TouchableOpacity
                style={EventRSVPModalStyles.closeButtonWrapper}
                onPress={() => closeModal()}
              >
                <AntDesign
                  name="close"
                  size={20}
                  color={colors.white}
                  style={EventRSVPModalStyles.closeButton}
                />
              </TouchableOpacity>
            </View>
            {rsvpTotalRecords ? (
              <View style={EventRSVPModalStyles.rsvpCountWrapper}>
                <View
                  style={{
                    ...EventRSVPModalStyles.rsvpCountTabWrapper,
                    backgroundColor:
                      rsvpStatus === RSVP_RESPONSES.GOING
                        ? colors.green
                        : colors.borderBlue,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      setRsvpStatus(RSVP_RESPONSES.GOING);
                    }}
                    style={EventRSVPModalStyles.rsvpCountTab}
                  >
                    <Text style={EventRSVPModalStyles.rsvpCountText}>
                      {rsvpTotalCountText.TOTAL_YES_COUNTS}
                    </Text>
                    <View style={EventRSVPModalStyles.rsvpCount}>
                      <Text
                        style={{
                          ...EventRSVPModalStyles.rsvpCountText,
                          color:
                            rsvpStatus === RSVP_RESPONSES.GOING
                              ? colors.white
                              : colors.green,
                        }}
                      >
                        {rsvpTotalRecords?.respondedWithYes || 0}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    ...EventRSVPModalStyles.rsvpCountTabWrapper,
                    backgroundColor:
                      rsvpStatus === RSVP_RESPONSES.NOT_GOING
                        ? colors.red
                        : colors.borderBlue,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      setRsvpStatus(RSVP_RESPONSES.NOT_GOING);
                    }}
                    style={EventRSVPModalStyles.rsvpCountTab}
                  >
                    <Text style={EventRSVPModalStyles.rsvpCountText}>
                      {rsvpTotalCountText.TOTAL_NO_COUNTS}
                    </Text>
                    <View style={EventRSVPModalStyles.rsvpCount}>
                      <Text
                        style={{
                          ...EventRSVPModalStyles.rsvpCountText,
                          color:
                            rsvpStatus === RSVP_RESPONSES.NOT_GOING
                              ? colors.white
                              : colors.red,
                        }}
                      >
                        {rsvpTotalRecords?.respondedWithNo || 0}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    ...EventRSVPModalStyles.rsvpCountTabWrapper,
                    backgroundColor:
                      rsvpStatus === RSVP_RESPONSES.NO_RESPONSE
                        ? colors.aquaBlue
                        : colors.borderBlue,
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      setRsvpStatus(RSVP_RESPONSES.NO_RESPONSE);
                    }}
                    style={EventRSVPModalStyles.rsvpCountTab}
                  >
                    <Text style={EventRSVPModalStyles.rsvpCountText}>
                      {rsvpTotalCountText.TOTAL_NO_RESPONSE}
                    </Text>
                    <View style={EventRSVPModalStyles.rsvpCount}>
                      <Text
                        style={{
                          ...EventRSVPModalStyles.rsvpCountText,
                          color:
                            rsvpStatus === RSVP_RESPONSES.NO_RESPONSE
                              ? colors.white
                              : colors.aquaBlue,
                        }}
                      >
                        {rsvpTotalRecords?.noResponse || 0}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}

            <View style={EventRSVPModalStyles.userListContainer}>
              {loading ? (
                <ActivityIndicator />
              ) : responses?.length > 0 ? (
                <FlatList
                  data={responses}
                  renderItem={ListItem}
                  keyExtractor={item => item.userId}
                  onEndReached={loadNextPage}
                  onEndReachedThreshold={2}
                />
              ) : (
                <NoContentMessage
                  message={'No data available'}
                  customWrapperStyle={EventRSVPModalStyles.noData}
                />
              )}
            </View>
          </View>
        </View>
      </ModalWrapper>
      {showRsvpNoteModal && (
        <RsvpNoteViewModal
          cancelAction={() => {
            setTimeout(() => {
              setShowModal(true);
            }, 0);
            setIsShowRsvpNoteModal(false);
          }}
          userProfileUrl={profileImageUrl}
          userFullName={fullName}
          rsvpReason={rsvpReason}
        />
      )}
    </View>
  );
};

export default EventRSVPModal;
