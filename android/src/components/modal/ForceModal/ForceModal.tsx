import Constants from 'expo-constants';
import React, { FC } from 'react';
import {
  ActivityIndicator,
  Image,
  Linking,
  Text,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import {
  forcedModalType,
  forgotPasswordModalWording,
  invalidActionMessages,
} from '../../../constants/constants';
import { FontAwesome } from '@expo/vector-icons';
import useLogout from '../../../hooks/useLogout';
import useStyles from '../../../hooks/useStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customForceModalStyles from './ForceModalStyles';
import { AntDesign } from '@expo/vector-icons';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import { messagingForceModalStatus } from '../../../constants/constants';
import { Ionicons } from '@expo/vector-icons';
import useColors from '../../../hooks/useColors';

interface ForceModalTypes {
  type: string;
  onClose?: (type: boolean) => void;
  goBack?: Function;
  isWsReconnect?: boolean;
  errorMessage: string;
}

const ForceModal: FC<ForceModalTypes> = ({
  type,
  onClose,
  goBack,
  isWsReconnect,
  errorMessage,
}) => {
  const colors = useColors();
  const ForceUpdateModalStyles = useStyles(customForceModalStyles);

  const [logoutHandler] = useLogout();

  const handlePress = async () => {
    await Linking.openURL(Constants?.expoConfig?.extra?.STORE_URL);
  };

  const content = () => {
    switch (type) {
      case forcedModalType.FORCED_UPDATE:
        return (
          <>
            {isTabDevice() ? (
              <View style={ForceUpdateModalStyles.descriptionWrapper}>
                <Text style={ForceUpdateModalStyles.heading}>
                  You are currently using an
                </Text>
                <Text style={ForceUpdateModalStyles.heading}>
                  outdated version of KoachHub!
                </Text>
              </View>
            ) : (
              <View style={ForceUpdateModalStyles.descriptionWrapper}>
                <Text style={ForceUpdateModalStyles.heading}>
                  You are currently
                </Text>
                <Text style={ForceUpdateModalStyles.heading}>
                  using an outdated version
                </Text>
                <Text style={ForceUpdateModalStyles.heading}>of KoachHub!</Text>
              </View>
            )}
            <View style={ForceUpdateModalStyles.descriptionWrapper}>
              <Text style={ForceUpdateModalStyles.description}>
                Please update to receive the latest
              </Text>
              <Text style={ForceUpdateModalStyles.description}>
                features and performance improvements
              </Text>
              <TouchableHighlight
                style={ForceUpdateModalStyles.buttonWrapper}
                onPress={() => {
                  handlePress();
                }}
              >
                <Text style={ForceUpdateModalStyles.buttonText}>Update</Text>
              </TouchableHighlight>
            </View>
          </>
        );
      case forcedModalType.FORGOT_MODAL:
        return (
          <>
            <View style={ForceUpdateModalStyles.forceModalWrapper}>
              <View style={ForceUpdateModalStyles.forgotErrorMsg}>
                <TouchableOpacity
                  onPress={() => onClose?.(false)}
                  style={ForceUpdateModalStyles.closeIcon}
                >
                  <AntDesign name="close" size={20} color="#FFFFFF" />
                </TouchableOpacity>
                <View>
                  <Text style={ForceUpdateModalStyles.forgotHeading}>
                    {forgotPasswordModalWording.ERROR_MESSAGE}
                  </Text>
                </View>
                <TouchableOpacity
                  style={ForceUpdateModalStyles.linkWrapper}
                  onPress={() => {
                    Linking.openURL(`mailto:<EMAIL>`);
                    onClose?.(false);
                  }}
                >
                  <Text style={ForceUpdateModalStyles.linkText}>
                    {forgotPasswordModalWording.EMAIL}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        );
      case forcedModalType.UNAUTHORIZED_PARENT:
        return (
          <>
            <View>
              <Text style={ForceUpdateModalStyles.heading}>
                No child assigned to your account
              </Text>
            </View>

            <View style={ForceUpdateModalStyles.descriptionWrapper}>
              <Text style={ForceUpdateModalStyles.descriptionRed}>
                Please contact admin for further information
              </Text>
            </View>

            <TouchableHighlight
              style={ForceUpdateModalStyles.buttonWrapper}
              onPress={() => {
                logoutHandler();
                onClose?.(false);
              }}
            >
              <Text style={ForceUpdateModalStyles.buttonText}>Logout</Text>
            </TouchableHighlight>
          </>
        );
      case forcedModalType.UNAUTHORIZED_PARENT_WITH_NO_TEAMS:
        return (
          <>
            <View>
              <Text style={ForceUpdateModalStyles.heading}>
                Your child is not assigned to a team
              </Text>
            </View>

            <View style={ForceUpdateModalStyles.descriptionWrapper}>
              <Text style={ForceUpdateModalStyles.descriptionRed}>
                Please contact admin for further information
              </Text>
            </View>

            <TouchableHighlight
              style={ForceUpdateModalStyles.buttonWrapper}
              onPress={() => {
                logoutHandler();
                onClose?.(false);
              }}
            >
              <Text style={ForceUpdateModalStyles.buttonText}>Logout</Text>
            </TouchableHighlight>
          </>
        );
      case forcedModalType.RE_ESTABLISH_WS:
        return (
          <>
            <View style={ForceUpdateModalStyles.forceModalWrapper}>
              {isWsReconnect ? (
                <View style={ForceUpdateModalStyles.loaderSpinner}>
                  <TouchableOpacity
                    onPress={() => goBack?.()}
                    style={ForceUpdateModalStyles.closeIcon}
                  >
                    <AntDesign name="close" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                  <Image
                    style={ForceUpdateModalStyles.loading}
                    source={require('../../../../assets/loader.gif')}
                  />
                  <Text style={ForceUpdateModalStyles.description}>
                    {`${messagingForceModalStatus.RECONNECTING}...`}
                  </Text>
                </View>
              ) : (
                <View style={ForceUpdateModalStyles.loaderErrorMsg}>
                  <TouchableOpacity
                    onPress={() => goBack?.()}
                    style={ForceUpdateModalStyles.closeIcon}
                  >
                    <AntDesign name="close" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                  <Ionicons
                    name="cloud-offline-outline"
                    size={isTabDevice() ? 45 : 55}
                    color={colors.green}
                  />
                  <Text style={ForceUpdateModalStyles.description}>
                    {messagingForceModalStatus.WS_CONNECTIONFAILED}
                  </Text>
                  <TouchableHighlight
                    style={ForceUpdateModalStyles.buttonWrapper}
                    onPress={() => {
                      onClose?.(false);
                    }}
                  >
                    <Text style={ForceUpdateModalStyles.buttonText}>
                      {messagingForceModalStatus.RETRY}
                    </Text>
                  </TouchableHighlight>
                </View>
              )}
            </View>
          </>
        );
      case forcedModalType.APP_OFFLINE:
      case forcedModalType.MATCH_PLANER_OFFLINE:
        return (
          <View style={ForceUpdateModalStyles.forceModalWrapper}>
            <View style={ForceUpdateModalStyles.loaderErrorMsg}>
              <TouchableOpacity
                onPress={() => goBack?.()}
                style={ForceUpdateModalStyles.closeIcon}
              >
                <AntDesign name="close" size={20} color="#FFFFFF" />
              </TouchableOpacity>
              <Ionicons
                name="cloud-offline-outline"
                size={isTabDevice() ? 45 : 55}
                color={colors.green}
              />
              <Text style={ForceUpdateModalStyles.description}>
                {forcedModalType.APP_OFFLINE == type
                  ? messagingForceModalStatus.APP_OFFLINE
                  : messagingForceModalStatus.WS_CONNECTIONFAILED}
              </Text>
            </View>
          </View>
        );
      case forcedModalType.INVALID_ACTION:
        return (
          <View style={ForceUpdateModalStyles.forceModalWrapper}>
            <View style={ForceUpdateModalStyles.loaderErrorMsg}>
              <TouchableOpacity
                onPress={() => onClose?.(false)}
                style={ForceUpdateModalStyles.closeIcon}
              >
                <AntDesign name="close" size={20} color="#FFFFFF" />
              </TouchableOpacity>
              <FontAwesome name="ban" size={50} color={colors.red} />
              <Text style={ForceUpdateModalStyles.description}>
                {errorMessage}
              </Text>
              <TouchableHighlight
                style={ForceUpdateModalStyles.buttonWrapper}
                onPress={() => onClose?.(false)}
              >
                <Text style={ForceUpdateModalStyles.buttonText}>
                  {invalidActionMessages.REFRESH}
                </Text>
              </TouchableHighlight>
            </View>
          </View>
        );
    }
  };

  return (
    <View>
      <ModalWrapper visible transparent>
        <View style={ForceUpdateModalStyles.overlay} />
        <View style={ForceUpdateModalStyles.centeredView}>
          <View style={ForceUpdateModalStyles.modalArea}>{content()}</View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default ForceModal;
