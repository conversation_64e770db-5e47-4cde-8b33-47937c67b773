import { AntDesign } from '@expo/vector-icons';
import React, { FC,useCallback } from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import useStyles from '../../../hooks/useStyles';
import customAddPlayerModalStyle from '../AddPlayerModal/AddPlayerModalStyle';
import DeleteUnassignModalStyle from './DeleteUnassignModalStyle';
import * as WebBrowser from 'expo-web-browser';

type DeleteUnassignModalType = {
  setIsDeleteUnassignModalOpen: (isConfirmModalOpen: boolean) => void;
  clubSettings: any;
};
const DeleteUnassignModal: FC<DeleteUnassignModalType> = ({
  setIsDeleteUnassignModalOpen: setIsConfirmModalOpen,
  clubSettings,
}) => {
  const ModalStyle = useStyles(DeleteUnassignModalStyle);
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);


  const getProperUrlForUserIAPNavigation = useCallback(() => {
    const { iapWebUrl } = clubSettings || {};
    return `${iapWebUrl}`;
  }, [clubSettings]);

  const handlePaymentPressButtonAsync = async () => {
    let result = await WebBrowser.openBrowserAsync(
      getProperUrlForUserIAPNavigation()
    );
  };
  
  return (
    <View style={AddPlayerModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={AddPlayerModalStyle.centeredView}>
          <View style={AddPlayerModalStyle.overlay}></View>
          <View
            style={ModalStyle.modalView}
          >
            <TouchableOpacity
              style={ModalStyle.closeButton}
              onPress={() => setIsConfirmModalOpen(false)}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={ModalStyle.modalTitle}>
              Player cannot be deleted!
            </Text>
            <Text style={ModalStyle.modalBody}>
            This player is currently on a recurring payment plan. Please unsubscribe this player from the plan to continue
            </Text>
            <View style={ModalStyle.buttonWrapper}>

              <TouchableOpacity
                onPress={() => handlePaymentPressButtonAsync()}
                style={ModalStyle.buttonNo}
              >
                <Text style={ModalStyle.buttonText}>payments.koachhub.com</Text>
              </TouchableOpacity>
            </View>      
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default DeleteUnassignModal;
