import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { Positions } from 'react-native-calendars/src/expandableCalendar';

const DeleteUnassignModalStyle = colors => ({
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        width:wp('40%'),
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        width:wp('85%'),
        paddingTop: wp('6%'),
        position : "relative"
      },
  closeButton :{
    position: "absolute",
    top : 10,
    right: 10,
  },
  modalTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('4.5%'),
        fontFamily: 'Poppins-Bold',
        marginTop: wp('2%'),
      },
  modalBody: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        marginTop: wp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginTop: wp('2%'),
      },
  buttonWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp('1.5%'),
    justifyContent: 'center'
  },
  buttonNo: isTabDevice()
    ? {
        paddingTop: wp('1%'),
        paddingBottom: wp('2%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        borderRadius: wp('1%'),
        marginTop: wp('3%'),
        backgroundColor: colors.aquaBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        textAlign: 'center',
      }
    : {
        paddingTop: wp('1%'),
        paddingBottom: wp('2%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        borderRadius: wp('2%'),
        marginTop: wp('2%'),
        backgroundColor: colors.aquaBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        textAlign: 'center',
      },
  buttonText: isTabDevice()
      ? {
          color: colors.white,
          fontSize: wp('1.5%'),
          fontFamily: 'Poppins-Bold',
        }
      : {
          color: colors.white,
          fontSize: wp('4%'),
          fontFamily: 'Poppins-Bold',
          marginTop: wp('2%'),
        },
});
export default DeleteUnassignModalStyle;
