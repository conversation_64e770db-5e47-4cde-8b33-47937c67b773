import { AntDesign } from '@expo/vector-icons';
import React, { FC, useState } from 'react';
import { Modal, Text, TextInput, TouchableOpacity, View } from 'react-native';
import useStyles from '../../../hooks/useStyles';
import { userDetailsFromPMType } from '../../../store/reducers/DeviceStats/DeviceStatsReducer';
import customAddPlayerModalStyle from '../AddPlayerModal/AddPlayerModalStyle';
import customSyncDeviceModalStyle from './SyncDeviceModalStyles';

type ApplicationUserSearchModalType = {
  setIsApplicationUserSearchModalOpen: Function;
  isPlayerDetailsScreen: boolean;
  onUserSearch: Function;
  userDetailsFromPM: userDetailsFromPMType | null;
  userDetailsFromPMLoading: boolean;
  onSyncDataClick: Function;
};

const ApplicationUserSearchModal: FC<ApplicationUserSearchModalType> = ({
  setIsApplicationUserSearchModalOpen,
  isPlayerDetailsScreen,
  onUserSearch,
  userDetailsFromPM,
  userDetailsFromPMLoading,
  onSyncDataClick,
}) => {
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);
  const SyncDeviceModalStyle = useStyles(customSyncDeviceModalStyle);
  const [playerMakerId, setPlayerMakerId] = useState('');

  return (
    <View style={AddPlayerModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={AddPlayerModalStyle.centeredView}>
          <View style={AddPlayerModalStyle.overlay}></View>
          <View style={SyncDeviceModalStyle.modalView}>
            <TouchableOpacity
              style={AddPlayerModalStyle.closeButton}
              onPress={() => {
                setIsApplicationUserSearchModalOpen(false);
              }}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={SyncDeviceModalStyle.modalTitle}>
              PlayerMaker Data
            </Text>

            <View style={SyncDeviceModalStyle.playerListContainer}>
              <Text style={SyncDeviceModalStyle.modalHeader}>Player's ID</Text>
              <TextInput
                placeholder={`Enter Player ID`}
                style={SyncDeviceModalStyle.inputField}
                multiline={false}
                placeholderTextColor="#595959"
                value={playerMakerId}
                onChangeText={text => setPlayerMakerId(text.replace(/\s/g, ''))}
                editable={!isPlayerDetailsScreen}
              />
              {isPlayerDetailsScreen ? (
                <>
                  <Text style={SyncDeviceModalStyle.modalHeader}>
                    Player's Name
                  </Text>
                  <TextInput
                    style={SyncDeviceModalStyle.inputField}
                    multiline={false}
                    placeholderTextColor="#595959"
                    value={userDetailsFromPM?.pmUserName || ''}
                    editable={false}
                  />
                  <TouchableOpacity
                    onPress={() => onSyncDataClick(playerMakerId)}
                  >
                    <Text style={SyncDeviceModalStyle.button}>Sync data</Text>
                  </TouchableOpacity>
                </>
              ) : playerMakerId ? (
                <TouchableOpacity
                  onPress={() => onUserSearch(playerMakerId)}
                  disabled={userDetailsFromPMLoading}
                >
                  <Text style={SyncDeviceModalStyle.button}>
                    {userDetailsFromPMLoading ? 'Search...' : 'Search'}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ApplicationUserSearchModal;
