import { AntDesign, MaterialIcons } from '@expo/vector-icons';
import React, { FC, useMemo } from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import customAddPlayerModalStyle from '../AddPlayerModal/AddPlayerModalStyle';
import customSyncDeviceModalStyle from './SyncDeviceModalStyles';

type SyncApplicationSuccessModalType = {
  setIsSyncSuccessModalOpen: Function;
  isErrorMessage: { isError: boolean; message: string };
};

const SyncApplicationSuccessModal: FC<SyncApplicationSuccessModalType> = ({
  setIsSyncSuccessModalOpen,
  isErrorMessage,
}) => {
  const SyncDeviceModalStyle = useStyles(customSyncDeviceModalStyle);
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);
  const colors = useColors();

  const onSearchClick = () => {
    setIsSyncSuccessModalOpen(false, isErrorMessage.isError);
  };
  const getIcon = useMemo(() => {
    const color: string = isErrorMessage.isError ? colors.red : colors.green;
    const iconType: any = isErrorMessage.isError ? `dangerous` : `check-circle`;

    return { color, iconType };
  }, [isErrorMessage.isError]);

  return (
    <View style={AddPlayerModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={AddPlayerModalStyle.centeredView}>
          <View style={AddPlayerModalStyle.overlay}></View>
          <View
            style={{ ...SyncDeviceModalStyle.modalView, alignItem: 'center' }}
          >
            <TouchableOpacity
              style={AddPlayerModalStyle.closeButton}
              onPress={onSearchClick}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={SyncDeviceModalStyle.centerAlign}>
              <MaterialIcons
                name={getIcon?.iconType}
                size={45}
                color={getIcon.color}
                style={SyncDeviceModalStyle.icon}
              />
            </View>
            {isTabDevice() ? (
              !isErrorMessage.isError ? (
                <>
                  <Text style={SyncDeviceModalStyle.modalTitle}>
                    You have successfully synced
                  </Text>
                  <Text
                    style={{
                      ...SyncDeviceModalStyle.modalTitle,
                      textAlign: 'center',
                    }}
                  >
                    your PlayerMaker data
                  </Text>
                </>
              ) : (
                <>
                  <Text
                    style={SyncDeviceModalStyle.modalTitle}
                    numberOfLines={2}
                  >
                    {isErrorMessage.message}
                  </Text>
                </>
              )
            ) : (
              <>
                <Text
                  style={{
                    ...SyncDeviceModalStyle.modalTitle,
                    textAlign: 'center',
                  }}
                >
                  {isErrorMessage.isError
                    ? `${isErrorMessage.message}`
                    : 'You have successfully synced your PlayerMaker data'}
                </Text>
              </>
            )}

            <TouchableOpacity onPress={onSearchClick}>
              <Text style={SyncDeviceModalStyle.buttonSuccess}>Okay</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default SyncApplicationSuccessModal;
