import { AntDesign } from '@expo/vector-icons';
import React, { FC } from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import useStyles from '../../../hooks/useStyles';
import customAddPlayerModalStyle from '../AddPlayerModal/AddPlayerModalStyle';
import customSyncDeviceModalStyle from './SyncDeviceModalStyles';

type SyncApplicationPermissionModalType = {
  setIsPermissionModalOpen: Function;
  playerName: string;
  onClickSyncPM: Function;
  postPMuserDataLoading: boolean;
};
const SyncApplicationPermissionModal: FC<
  SyncApplicationPermissionModalType
> = ({
  setIsPermissionModalOpen,
  playerName,
  onClickSyncPM,
  postPMuserDataLoading,
}) => {
  const SyncDeviceModalStyle = useStyles(customSyncDeviceModalStyle);
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);

  return (
    <View style={AddPlayerModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={AddPlayerModalStyle.centeredView}>
          <View style={AddPlayerModalStyle.overlay}></View>
          <View
            style={{ ...SyncDeviceModalStyle.modalView, alignItems: 'center' }}
          >
            <TouchableOpacity
              style={AddPlayerModalStyle.closeButton}
              onPress={() => setIsPermissionModalOpen(false)}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={SyncDeviceModalStyle.modalTitle}>
              Do you want to sync data to
            </Text>
            <Text style={SyncDeviceModalStyle.playerName}>
              {playerName || 'this player'}
            </Text>

            <View style={SyncDeviceModalStyle.buttonWrapper}>
              <TouchableOpacity
                onPress={() => onClickSyncPM()}
                disabled={postPMuserDataLoading}
              >
                <Text style={SyncDeviceModalStyle.buttonYes}>
                  {postPMuserDataLoading ? 'Loading...' : 'Yes'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => setIsPermissionModalOpen(false)}
                disabled={postPMuserDataLoading}
              >
                <Text style={SyncDeviceModalStyle.buttonNo}>No</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default SyncApplicationPermissionModal;
