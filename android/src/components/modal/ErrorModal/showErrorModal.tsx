import React, { FC } from 'react';
import { Alert, Modal, Text, TouchableOpacity, View } from 'react-native';

import { AntDesign } from '@expo/vector-icons';
import useStyles from '../../../hooks/useStyles';
import customFowardMsgModalStyles from '../ForwardMsgModal/FowardMsgModalStyles';

interface IShowErrorModal {
  setShowForwardModal: Function;
  errorMessage: string;
}

const showErrorModal: FC<IShowErrorModal> = ({
  setShowForwardModal,
  errorMessage,
}) => {
  const FowardMsgModalStyles = useStyles(customFowardMsgModalStyles);

  return (
    <View style={FowardMsgModalStyles.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={FowardMsgModalStyles.centeredView}>
          <View style={FowardMsgModalStyles.overlay}></View>
          <View style={FowardMsgModalStyles.modalView}>
            <TouchableOpacity
              style={FowardMsgModalStyles.closeButton}
              onPress={() => {
                setShowForwardModal(false);
              }}
            >
              <AntDesign
                name="close"
                size={20}
                color="#FFFFFF"
                style={FowardMsgModalStyles.closeText}
              />
            </TouchableOpacity>
            <View style={FowardMsgModalStyles.container}>
              <Text>Error: ${errorMessage}</Text>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default showErrorModal;
