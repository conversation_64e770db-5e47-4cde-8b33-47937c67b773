import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const DeleteModalStyles = colors => ({
  container: {},
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      },
  deleteTeamModalView: isTabDevice()
    ? {
        width: wp('30%'),
      }
    : {
        width: wp('80%'),
      },
  openButton: {
    backgroundColor: '#F194FF',
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    width: 80,
    height: 45,
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 17,
  },
  modalText: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('4.5%'),
        fontFamily: 'Poppins-Bold',
      },
  modalSubText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
      }
    : {
        fontSize: wp('3.3%'),
      },
  errorText: {
    color: colors.red,
  },
  modalContent: isTabDevice() ? {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginTop: wp('2%')
  } : {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginTop: wp('4%')
  },
  yesButton: {
    backgroundColor: colors.aquaBlue,
    marginHorizontal: 0,
  },
  noButton: {
    backgroundColor: colors.darkBlue,
    marginLeft: 10,
  },
});

export default DeleteModalStyles;
