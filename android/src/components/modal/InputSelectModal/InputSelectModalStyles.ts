import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const InputSelectModalStyles = (colors: any) => ({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  modalView: isTabDevice()
    ? {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 15,
        padding: 20,
        paddingVertical: 20,
        paddingBottom: 0,
        // alignItems: 'center',
        shadowColor: '#000',
        width: 450,
        maxHeight: hp('60%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        position: 'relative',
      }
    : {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 15,
        padding: 15,
        paddingVertical: 20,
        paddingBottom: 0,
        // alignItems: 'center',
        shadowColor: '#000',
        width: 320,
        maxHeight: hp('60%'),
        // height: 250,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        position: 'relative',
      },
  modalText: isTabDevice()
    ? {
        marginBottom: 15,
        color: '#fff',
        fontSize: 24,
        fontWeight: 'bold',
      }
    : {
        marginBottom: 15,
        color: '#fff',
        fontSize: 20,
        fontWeight: 'bold',
      },
  searchInputContainer: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 15,
        width: '100%',
        marginBottom: 15,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 15,
        paddingVertical: 20,
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 15,
        width: '100%',
        marginBottom: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 15,
      },
  searchInput: isTabDevice()
    ? {
        color: colors.white,
        fontSize: 18,
        fontWeight: '500',
        width: '93%',
      }
    : {
        color: colors.white,
        fontSize: 16,
        fontWeight: '500',
        width: '93%',
      },
  colorPaletteContainer: {
    backgroundColor: colors.darkBlue,
    padding: 10,
    borderRadius: 15,
    marginTop: 10,
  },
  paletteHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 5,
    paddingRight: 5,
    marginBottom: 15,
  },
  paletteHeaderText: {
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  paletteSelectedColor: {
    width: 27,
    height: 27,
    borderRadius: 8,
    margin: 2,
  },
  palette: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonTouchable: {
    backgroundColor: colors.aquaBlue,
    borderRadius: 20,
    marginTop: 10,
    width: '100%',
    height: 60,
    justifyContent: 'center',
  },
  createButtonText: {
    alignItems: 'center',
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 15,
    color: colors.red,
    fontWeight: 'bold',
  },
  closeButton: isTabDevice()
    ? {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        right: 15,
        top: 15,
        padding: 5,
        zIndex: 1000,
      }
    : {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        right: 15,
        top: 15,
        padding: 1,
        zIndex: 1000,
      },
  unselect: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        // width: wp('26%'),
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        borderRadius: wp('1%'),
        marginBottom: 10,
        padding: 15,
        paddingRight: 10,
        paddingLeft: 1,
      }
    : {
        backgroundColor: colors.darkBlue,
        // width: wp('26%'),
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        paddingVertical: 20,
        borderRadius: wp('3%'),
        marginBottom: 10,
        padding: 15,
        paddingRight: 40,
      },
  select: isTabDevice()
    ? {
        backgroundColor: colors.green,
        // width: wp('26%'),
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        borderRadius: wp('1%'),
        marginBottom: 10,
        padding: 15,
        paddingRight: 8,
      }
    : {
        backgroundColor: colors.green,
        // width: wp('26%'),
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        paddingVertical: 20,
        borderRadius: wp('3%'),
        marginBottom: 10,
        padding: 15,
        paddingRight: 40,
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: '600',
        marginLeft: 20,
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: '600',
        marginLeft: 20,
      },
  selectWithImage: isTabDevice()
    ? {
        paddingVertical: 15,
        paddingTop: 10,
        paddingBottom: 10,
        justifyContent: 'flex-start',
        alignItems: 'center',
      }
    : {
        paddingVertical: 15,
        justifyContent: 'flex-start',
        alignItems: 'center',
      },
  saveButtonContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 15,
    width: '100%',
    margin: 'auto',
  },
  saveButton: {
    backgroundColor: colors.green,
    width: '100%',
    borderRadius: 10,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  saveButtonText: {
    color: colors.white,
  },
  selectButton: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        width: 27,
        height: 27,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 4,
      }
    : {
        backgroundColor: colors.borderBlue,
        width: 20,
        height: 20,
        borderRadius: 100,
        alignItems: 'center',
        justifyContent: 'center',
      },
  selectButtonSelected: isTabDevice()
    ? {
        backgroundColor: colors.green,
        width: 17,
        height: 17,
        borderRadius: 100,
        marginTop: -1,
      }
    : {
        backgroundColor: colors.green,
        width: 14,
        height: 14,
        borderRadius: 100,
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        width: '85%',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        width: '85%',
      },
  dropDownLabelWithImage: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        width: '55%',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  inputField: isTabDevice()
    ? {
        width: '100%',
        height: hp('6%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        // position: 'relative',
        justifyContent: 'space-between',
        padding: 9,
        paddingLeft: 15,
        paddingRight: 15,
      }
    : {
        width: '100%',
        height: hp('5%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'relative',
        paddingTop: 5,
        paddingBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
      },
  inputField2: isTabDevice()
    ? {
        width: '100%',
        height: hp('6%'),
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 9,
        paddingLeft: 15,
        paddingRight: 15,
      }
    : {
        width: '100%',
        height: hp('5%'),
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'relative',
        paddingTop: 5,
        paddingBottom: 5,
        paddingLeft: 10,
        paddingRight: 10,
      },
  profileImage: isTabDevice()
    ? {
        width: 50,
        height: 50,
        resizeMode: 'cover',
        borderRadius: wp('100%'),
      }
    : {
        width: 40,
        height: 40,
        resizeMode: 'cover',
        borderRadius: wp('100%'),
      },
  profileImageSelected: isTabDevice()
    ? {
        width: 30,
        height: 30,
        resizeMode: 'cover',
        borderRadius: wp('100%'),
        marginRight: 10,
      }
    : {
        width: 40,
        height: 40,
        resizeMode: 'cover',
        borderRadius: wp('100%'),
      },
  profileImageWrapper: {
    position: 'relative',
  },
  exclamationIcon: isTabDevice()
    ? {
        width: 23,
        height: 23,
        resizeMode: 'cover',
        borderRadius: wp('100%'),
        position: 'absolute',
        top: -2,
        right: -2,
      }
    : {
        width: 20,
        height: 20,
        resizeMode: 'cover',
        borderRadius: wp('100%'),
        position: 'absolute',
        top: -3,
        right: -3,
      },
});

export default InputSelectModalStyles;
