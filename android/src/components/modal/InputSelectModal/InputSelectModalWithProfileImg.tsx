import { AntDesign } from '@expo/vector-icons';
import React, { FC, useCallback, useEffect, useState, useRef } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customInputSelectModal from './InputSelectModalStyles';
import { Feather } from '@expo/vector-icons';
import { isTabDevice } from '../../../config/appConfig';
import { convertCamelCaseToStandardString } from '../../../helpers/common';
import ProfileImage from '../../ProfileImage/ProfileImage';
import ExclamationIcon from '../../../../assets/buttons/exclamation.png';
import { useRoute } from '@react-navigation/native';
interface InputSelectModalWithProfileImg {
  title?: string;
  items: {
    value: string;
    label: string;
    profileImageUrl?: string | undefined;
    isAvailable?: boolean;
  }[];
  openModal: () => void;
  multiple?: boolean;
  enableDoneBtn?: boolean;
  defaultValues?: string[];
  isEnableAutoComplete?: boolean;
  onAutoCompleteChange?: (search: string) => void;
  isEnableAddNew?: boolean;
  isNewDataAdding?: boolean;
  addNewAction?: (search: string) => void;
  isSearchByFetch?: boolean;
  onCloseHook: (data: any) => void;
  onSelectItemHook: (data: any[]) => void;
  isUnselectedAllowed?: boolean;
  onReachEndHandler?: Function;
  searchByText?: Function;
}

type SelectedItems = {
  value: string;
  label: string;
}[];

const InputSelectModalWithProfileImg: FC<InputSelectModalWithProfileImg> = ({
  items,
  title,
  onCloseHook,
  multiple,
  defaultValues,
  enableDoneBtn = false,
  isEnableAutoComplete,
  isEnableAddNew,
  isNewDataAdding,
  addNewAction,
  onAutoCompleteChange,
  isSearchByFetch,
  onSelectItemHook,
  isUnselectedAllowed,
  onReachEndHandler,
  searchByText,
}) => {
  const colors = useColors();
  const InputSelectModal = useStyles(customInputSelectModal);
  const [isComponentInit, setIsComponentInit] = useState(true);

  const route = useRoute();

  const [errorMessage, setErrorMessage] = useState('');

  const [selectedItems, setSelectedItems] = useState<SelectedItems | null>(
    null
  );

  const flatlistRef = useRef();

  const [listItems, setListItems] = useState(items);
  const [searchText, setSearchText] = useState('');
  const filterItems = (searchText: string) => {
    const lowerCaseSearchText = searchText.toLowerCase();
    const filtered = items.filter(entry =>
      Object.values(entry).some(
        val =>
          typeof val === 'string' &&
          val.toLowerCase().includes(lowerCaseSearchText)
      )
    );
    setListItems([...filtered]);
  };

  useEffect(() => {
    filterItems(searchText);
  }, [JSON.stringify(items), searchText]);

  useEffect(() => {
    if (defaultValues?.length && items?.length) {
      const mappedItems = defaultValues?.map(selectedValue => {
        const selectedItemValueAndLabel = items.find(
          item => item.value === selectedValue
        );

        return {
          ...selectedItemValueAndLabel,
        };
      });

      isComponentInit && setSelectedItems(mappedItems as SelectedItems);
    }
  }, [defaultValues, JSON.stringify(items)]);

  useEffect(() => {
    if (
      (!multiple || !isUnselectedAllowed) &&
      selectedItems?.length &&
      selectedItems?.[0]?.value &&
      !isComponentInit
    ) {
      onModalClose();
    }
  }, [
    JSON.stringify(selectedItems),
    multiple,
    isUnselectedAllowed,
    isComponentInit,
  ]);

  const onSelectItem = useCallback(
    (selectedValue: string) => {
      const isItemExistInSelectedItems = selectedItems?.find(
        item => item.value === selectedValue
      );

      if (isItemExistInSelectedItems && (isUnselectedAllowed || multiple)) {
        setSelectedItems(
          selectedItems?.filter(item => item.value !== selectedValue) || []
        );
      } else {
        const selectedItem = items?.find(item => item.value === selectedValue);

        selectedItem &&
          setSelectedItems((items: SelectedItems | null) => {
            return multiple
              ? [...(items || []), { ...selectedItem }]
              : [{ ...selectedItem }];
          });
      }
      setIsComponentInit(false);
    },
    [JSON.stringify(items), multiple, JSON.stringify(selectedItems)]
  );

  const isItemSelected = useCallback(
    (selectedValue: string) => {
      return selectedItems?.find((item, index) => item.value === selectedValue);
    },
    [JSON.stringify(selectedItems)]
  );

  const renderItem = ({
    item,
    index,
  }: {
    item: {
      value: string;
      label: string;
      profileImageUrl: string;
      isAvailable: boolean;
    };
    index: number;
  }) => {
    return (
      <TouchableOpacity
        onPress={() => onSelectItem(item.value)}
        style={
          isItemSelected(item.value)
            ? [InputSelectModal.select, InputSelectModal.selectWithImage]
            : [InputSelectModal.unselect, InputSelectModal.selectWithImage]
        }
      >
        <View style={InputSelectModal.profileImageWrapper}>
          <ProfileImage
            style={InputSelectModal.profileImage}
            imageStyles={InputSelectModal.profileImage}
            profileImageUrl={item?.profileImageUrl}
          />
          {!item?.isAvailable && (
            <Image
              style={InputSelectModal.exclamationIcon}
              source={ExclamationIcon}
            />
          )}
        </View>
        <Text style={InputSelectModal.buttonText}>{item.label}</Text>
        {multiple && (
          <View style={InputSelectModal.selectButton}>
            {isItemSelected(item.value) && (
              <View style={InputSelectModal.selectButtonSelected}></View>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const onModalClose = () => {
    onSelectItemHook(
      enableDoneBtn || !selectedItems?.length ? [] : selectedItems
    );
    onCloseHook(false);
    enableDoneBtn;
    // onSelectItemHook(selectedItems?.length ? selectedItems : []);
  };

  const onModalSave = () => {
    onCloseHook(false);
    onSelectItemHook(selectedItems?.length ? selectedItems : []);
  };

  const onSearchItem = (searchText: string) => {
    setSearchText(searchText);
    onAutoCompleteChange?.(searchText);
    setIsAddNewItemClicked(false);
    filterItems(searchText);

    const matchId = (route.params as any).matchId;

    if (searchByText) {
      flatlistRef?.current?.scrollToIndex({ index: 0 });
      searchByText(matchId, 1, searchText);
    }
  };

  const [isAddNewItemClicked, setIsAddNewItemClicked] = useState(false);

  useEffect(() => {
    isEnableAddNew &&
      isNewDataAdding &&
      isAddNewItemClicked &&
      onAutoCompleteChange?.(searchText);
  }, [isNewDataAdding, isAddNewItemClicked]);

  useEffect(() => {
    isEnableAddNew &&
      isNewDataAdding &&
      searchText !== '' &&
      listItems.length &&
      setSelectedItems([...listItems]);
  }, [isNewDataAdding, searchText, listItems]);

  const AddNewItem = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          setIsAddNewItemClicked(true);
          addNewAction?.(searchText);
        }}
        style={InputSelectModal.unselect}
      >
        <Text style={InputSelectModal.buttonText}>{searchText} (Add New)</Text>

        {isAddNewItemClicked &&
          (!isNewDataAdding ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <AntDesign name="checkcircle" size={20} color={'green'} />
          ))}
      </TouchableOpacity>
    );
  };

  const renderTitle = () => {
    return convertCamelCaseToStandardString(title || '');
  };

  const renderSearchLabel = () => {
    return (
      `Search ${convertCamelCaseToStandardString(
        title?.replace('Select', '').trim()
      )}` || ''
    );
  };

  return (
    <ModalWrapper transparent>
      <View style={InputSelectModal.overlay} />

      <View style={InputSelectModal.centeredView}>
        <View style={InputSelectModal.modalView}>
          <TouchableOpacity
            style={InputSelectModal.closeButton}
            onPress={onModalClose}
          >
            <AntDesign
              name="close"
              size={isTabDevice() ? 25 : 20}
              color="#FFFFFF"
            />
          </TouchableOpacity>
          <Text style={InputSelectModal.modalText}>{renderTitle()}</Text>
          {isEnableAutoComplete && (
            <View style={InputSelectModal.searchInputContainer}>
              <TextInput
                placeholder={renderSearchLabel()}
                style={InputSelectModal.searchInput}
                multiline={false}
                placeholderTextColor={colors.white}
                onChangeText={text => onSearchItem(text)}
                autoCorrect={false}
              />
              <Feather name="search" size={24} color="white" />
            </View>
          )}

          {listItems.length > 0 ? (
            <FlatList
              data={listItems || []}
              renderItem={renderItem}
              keyExtractor={item => item?.value}
              onEndReached={() =>
                onReachEndHandler ? onReachEndHandler() : null
              }
              ref={flatlistRef}
            />
          ) : isEnableAddNew && searchText !== '' ? (
            <AddNewItem />
          ) : isSearchByFetch ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <View>
              <Text style={InputSelectModal.buttonText}>
                No {searchText} found
              </Text>
            </View>
          )}

          {listItems.length &&
          selectedItems?.length &&
          selectedItems.length > 0 &&
          enableDoneBtn ? (
            <View style={InputSelectModal.saveButtonContainer}>
              <TouchableOpacity
                onPress={onModalSave}
                style={InputSelectModal.saveButton}
              >
                <Text style={InputSelectModal.saveButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          ) : null}

          <Text style={InputSelectModal.errorMessage}>{errorMessage}</Text>
        </View>
      </View>
    </ModalWrapper>
  );
};

export default InputSelectModalWithProfileImg;
