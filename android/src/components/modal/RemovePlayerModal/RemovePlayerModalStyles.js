import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const styles = colors => ({
  container: {
    display: 'none',
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    opacity: 0.95,
  },
  modalView: {
    backgroundColor: colors.borderBlue,
    padding: wp('2%'),
    borderRadius: wp('2%'),
    alignItems: 'center',
  },
  buttonYes: {
    backgroundColor: colors.aquaBlue,
  },
  buttonNo: {
    backgroundColor: colors.darkBlue,
  },
  textStyle: {
    color: colors.white,
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: wp('10%'),
    // marginTop:3
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
  },
});
