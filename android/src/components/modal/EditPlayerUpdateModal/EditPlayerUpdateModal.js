import React from 'react';
import PlayerInfoUpdateForm from '../../../components/PlayerInfoUpdateForm/PlayerInfoUpdateForm';
import ModalWrapper from '../ModalWrapper/ModalWrapper';

const EditPlayerUpdateModal = ({
  setIsEditModeOpen,
  selectedUpdateData,
  editUpdate,
}) => {
  return (
    <ModalWrapper>
      <PlayerInfoUpdateForm
        modalAction={setIsEditModeOpen}
        selectedUpdateData={selectedUpdateData}
        action={editUpdate}
      />
    </ModalWrapper>
  );
};

export default EditPlayerUpdateModal;
