import React, { FC } from 'react';
import {
  Modal,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  TextInput,
} from 'react-native';
import { Checkbox } from 'react-native-paper';

import { AntDesign } from '@expo/vector-icons';
import useStyles from '../../../hooks/useStyles';
import customFowardMsgModalStyles from './FowardMsgModalStyles';
import customMessageSearchAndTabStyles from '../../Message/MeessageLeftComponents/MessageSearchAndTabStyles';
import { messageTypes } from '../../../constants/constants';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';

interface IMessageOptionsModal {
  setShowForwardModal: Function;
  setSelectedMsgType: Function;
  selectedMsgType: String;
  searchKey: any;
  setSearchKey: Function;
  players: any;
  teams: any;
  loading: boolean;
  selectedChat: String[];
  setSelectedChat: Function;
  onSubmitHandler: Function;
  isSubmitError: boolean;
  onReachEndHandler: Function;
  forwardMsgWebsocketLoading: boolean;
}

const ForwardMsgModal: FC<IMessageOptionsModal> = ({
  setShowForwardModal,
  setSelectedMsgType,
  selectedMsgType,
  searchKey,
  setSearchKey,
  players,
  teams,
  loading,
  selectedChat,
  setSelectedChat,
  onSubmitHandler,
  isSubmitError,
  onReachEndHandler,
  forwardMsgWebsocketLoading,
}) => {
  const FowardMsgModalStyles = useStyles(customFowardMsgModalStyles);
  const isSelectedPersonal = selectedMsgType === messageTypes.PERSONAL;
  const MessageSearchAndTabStyles = useStyles(customMessageSearchAndTabStyles);
  const selectedPersonalAndNoPlayers = isSelectedPersonal && !players?.length;
  const selectedTeamAndNoTeams = !isSelectedPersonal && !teams?.length;

  const handleCheckbox = (item: any) => {
    const id = item?.id || item?._id;
    const currentSelection = selectedChat.indexOf(id);
    if (currentSelection < 0) {
      //limit selection to only two
      if (selectedChat.length <= 1) {
        setSelectedChat([...selectedChat, id]);
      }
    } else {
      const removeItem = selectedChat.filter(item => item !== id);
      setSelectedChat(removeItem);
    }
  };

  const renderItem = ({ item }: any) => (
    <TouchableOpacity style={FowardMsgModalStyles.listItem}>
      <Checkbox.Android
        style={FowardMsgModalStyles.listItemCheckbox}
        uncheckedColor="white"
        status={
          selectedChat?.includes(item?.id || item?._id)
            ? 'checked'
            : 'unchecked'
        }
        onPress={() => handleCheckbox(item)}
      />
      <Text style={FowardMsgModalStyles.listItemText} numberOfLines={1}>
        {isSelectedPersonal
          ? `${item?.firstName || ''} ${item?.lastName || ''}`
          : item?.name || ''}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={FowardMsgModalStyles.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={FowardMsgModalStyles.centeredView}>
          <View style={FowardMsgModalStyles.overlay}></View>
          <View style={FowardMsgModalStyles.modalView}>
            <TouchableOpacity
              style={FowardMsgModalStyles.closeButton}
              onPress={() => setShowForwardModal(false)}
            >
              <AntDesign
                name="close"
                size={20}
                color="#FFFFFF"
                style={FowardMsgModalStyles.closeText}
              />
            </TouchableOpacity>
            <View style={FowardMsgModalStyles.container}>
              <View style={MessageSearchAndTabStyles.toggleBar}>
                <TouchableOpacity
                  onPress={() => setSelectedMsgType(messageTypes.PERSONAL)}
                >
                  <Text
                    style={
                      selectedMsgType === messageTypes.PERSONAL
                        ? FowardMsgModalStyles.selectedText
                        : FowardMsgModalStyles.selectionText
                    }
                  >
                    Personal
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => setSelectedMsgType(messageTypes.TEAMS)}
                >
                  <Text
                    style={
                      selectedMsgType === messageTypes.TEAMS
                        ? FowardMsgModalStyles.selectedText
                        : FowardMsgModalStyles.selectionText
                    }
                  >
                    Teams
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={FowardMsgModalStyles.searchContainer}>
                <TextInput
                  style={MessageSearchAndTabStyles.search}
                  value={searchKey}
                  onChangeText={text => setSearchKey(text)}
                  multiline={false}
                  placeholder={'Search'}
                  placeholderTextColor="#fff"
                  disableFullscreenUI={true}
                  returnKeyType={'done'}
                  blurOnSubmit={false}
                />
                <AntDesign
                  name="search1"
                  size={24}
                  color="white"
                  style={MessageSearchAndTabStyles.searchIcon}
                />
              </View>
            </View>
            <View style={FowardMsgModalStyles.chatList}>
              {(selectedPersonalAndNoPlayers && loading) ||
              (selectedTeamAndNoTeams && loading) ? (
                <ActivitySpinner />
              ) : selectedPersonalAndNoPlayers ? (
                <Text style={FowardMsgModalStyles.message}>
                  No Data Available
                </Text>
              ) : selectedTeamAndNoTeams ? (
                <Text style={FowardMsgModalStyles.message}>
                  No Team Data Available
                </Text>
              ) : (
                <>
                  <FlatList
                    data={isSelectedPersonal ? players : teams}
                    renderItem={renderItem}
                    onEndReached={() => onReachEndHandler()}
                  />
                </>
              )}
            </View>
            <View style={FowardMsgModalStyles.errorMessageWrapper}>
              {isSubmitError ? (
                <Text style={FowardMsgModalStyles.errorMessage}>
                  {isSelectedPersonal ? 'No User Selected' : 'No Team Selected'}
                </Text>
              ) : null}
            </View>
            {forwardMsgWebsocketLoading ? (
              <View style={FowardMsgModalStyles.submitLoader}>
                <ActivitySpinner />
              </View>
            ) : (
              <TouchableOpacity onPress={() => onSubmitHandler()}>
                <View style={FowardMsgModalStyles.submitLoader}>
                  <Text style={FowardMsgModalStyles.submit}>Submit</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ForwardMsgModal;
