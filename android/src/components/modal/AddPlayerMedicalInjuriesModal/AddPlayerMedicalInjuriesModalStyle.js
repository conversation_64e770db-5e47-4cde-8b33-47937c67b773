import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const AddPlayerMedicalInjuriesModalStyle = colors => ({
  centeredView: isTabDevice()
    ? {
        position: 'relative',
        position: 'absolute',
        bottom: hp('2%'),
        right: 0,
        zIndex: 10,
      }
    : {},
  addView: isTabDevice()
    ? {
        position: 'absolute',
        bottom: hp('2%'),
        right: 0,
      }
    : {
        position: 'absolute',
        bottom: wp('11.25%'),
        right: 0,
      },
  AddTouchableOpacity: isTabDevice()
    ? {
        height: hp('10%'),
        width: hp('10%'),
        borderRadius: 60,
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('13%'),
        width: wp('13%'),
        borderRadius: 60,
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
      },
  AddIcon: isTabDevice()
    ? {
        fontSize: hp('5%'),
      }
    : {
        fontSize: wp('7%'),
      },
});
export default AddPlayerMedicalInjuriesModalStyle;
