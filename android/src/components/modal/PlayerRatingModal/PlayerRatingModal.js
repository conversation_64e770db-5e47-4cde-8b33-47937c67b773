import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';

import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customPlayerRatingModalStyles from './PlayerRatingModalStyles';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import PlayerRatingTile from '../../PlayerRating/PlayerRatingTile';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';

const PlayerRatingModal = ({
  playerData,
  showModal,
  isLoading,
  saveUpdatedValues,
  setIsShowModal,
  isError,
}) => {
  const PlayerRatingModalStyles = useStyles(customPlayerRatingModalStyles);
  const [updatedValues, setUpdatedValues] = useState([]);
  const renderItem = ({ item }, index) => (
    <PlayerRatingTile
      key={index}
      playerData={item}
      setUpdatedValue={updateValue => {
        let tempUpdated = updatedValues;
        const alreadyExistsIndex = updatedValues.findIndex(
          rating => rating.userId === updateValue.userId
        );
        if (alreadyExistsIndex > -1) {
          tempUpdated[alreadyExistsIndex] = updateValue;
        } else {
          tempUpdated.push(updateValue);
        }

        setUpdatedValues(tempUpdated);
      }}
    />
  );

  return (
    <View style={PlayerRatingModalStyles.container}>
      <ModalWrapper transparent visible={showModal}>
        <View style={PlayerRatingModalStyles.centeredView}>
          <View style={PlayerRatingModalStyles.overlay}></View>
          {/* <View style={PlayerRatingModalStyles.wrapper}> */}
          <View style={PlayerRatingModalStyles.modalView}>
            <View style={PlayerRatingModalStyles.titleRow}>
              <Text style={PlayerRatingModalStyles.title}>Player Ratings</Text>
              {isError && !isLoading && (
                <Text style={PlayerRatingModalStyles.errorText}>
                  An error occured. Please try again later
                </Text>
              )}
              <View style={PlayerRatingModalStyles.buttons}>
                <View style={PlayerRatingModalStyles.button}>
                  <TouchableOpacity
                    onPress={() => {
                      saveUpdatedValues(updatedValues);
                    }}
                  >
                    <Text style={PlayerRatingModalStyles.buttonText}>Save</Text>
                  </TouchableOpacity>
                </View>
                <View style={PlayerRatingModalStyles.button2}>
                  <TouchableOpacity
                    onPress={() => {
                      setUpdatedValues([]);
                      setIsShowModal(false);
                    }}
                  >
                    <Text style={PlayerRatingModalStyles.buttonText}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            {isLoading ? (
              <ActivitySpinner />
            ) : (
              <FlatList
                numColumns={isTabDevice() ? 4 : 3}
                data={playerData || []}
                renderItem={renderItem}
                keyExtractor={item => item.userId}
              />
            )}
          </View>
          {/* </View> */}
        </View>
      </ModalWrapper>
    </View>
  );
};

export default PlayerRatingModal;
