import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';

import { Ionicons } from '@expo/vector-icons';
import ModalWrapper from '../ModalWrapper/ModalWrapper';

import PlayerInfoUpdateAddContainer from '../../../Container/PlayerInfoUpdateAddContainer/PlayerInfoUpdateAddContainer';
import customAddPlayerUpdateModalStyle from './AddPlayerUpdateModalStyle';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';

const AddPlayerUpdateModal = ({ addUpdate }) => {
  const AddPlayerUpdateModalStyle = useStyles(customAddPlayerUpdateModalStyle);
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <View style={AddPlayerUpdateModalStyle.centeredView}>
      {modalVisible && (
        <ModalWrapper>
          <PlayerInfoUpdateAddContainer
            addUpdate={addUpdate}
            setModalVisible={setModalVisible}
          />
        </ModalWrapper>
      )}

      <View style={AddPlayerUpdateModalStyle.addView}>
        <TouchableOpacity
          style={AddPlayerUpdateModalStyle.AddTouchableOpacity}
          onPress={() => setModalVisible(true)}
        >
          <Ionicons
            color="#fff"
            name="add"
            style={AddPlayerUpdateModalStyle.AddIcon}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddPlayerUpdateModal;
