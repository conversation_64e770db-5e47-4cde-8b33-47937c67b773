import { AntDesign } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
  Appearance,
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { dateToString } from '../../../helpers/DateHelper';
import useStyles from '../../../hooks/useStyles';
import customDateRangeModalStyle from './DateRangeModalStyle';

const DateRangeModal = ({
  modalVisible,
  setModalVisible,
  setModalResponse,
  statGraphDateRange,
  title,
  subTitle,
}) => {
  const DateRangeModalStyle = useStyles(customDateRangeModalStyle);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [showToPicker, setshowToPicker] = useState(false);
  const [showFromPicker, setShowFromPicker] = useState(false);

  const START_DATE = 'Start Date';
  const END_DATE = 'End Date';

  useEffect(() => {
    if (statGraphDateRange) {
      setFromDate(new Date(statGraphDateRange.from));
      setToDate(new Date(statGraphDateRange.to));
    }
  }, [statGraphDateRange]);

  const [isError, setIsError] = useState(false);

  const onButtonPress = useCallback(() => {
    if (fromDate || toDate) {
      setModalResponse({
        from: fromDate,
        to: toDate,
      });

      setIsError(false);
      setModalVisible(false);
    } else {
      setIsError(true);
    }
  }, [`${fromDate}`, `${toDate}`]);

  const onCloseModal = () => {
    setIsError(false);
    setModalVisible(false);
  };

  const fromDateSelect = date => {
    setShowFromPicker(false);
    date && setFromDate(date);
  };

  const toDateSelect = date => {
    setshowToPicker(false);
    date && setToDate(date);
  };

  const renderDate = date => (
    <View style={DateRangeModalStyle.inputSelection}>
      <Text style={DateRangeModalStyle.listItemText}>{dateToString(date)}</Text>
      <Image
        style={DateRangeModalStyle.calendarIcon}
        source={require('../../../../assets/icons/calendarIcon.png')}
      />
    </View>
  );

  const renderCalenderIcon = label => (
    <View style={DateRangeModalStyle.inputSelection}>
      <Text style={DateRangeModalStyle.listItemText}>{label}</Text>
      <Image
        style={DateRangeModalStyle.calendarIcon}
        source={require('../../../../assets/icons/calendarIcon.png')}
      />
    </View>
  );

  return (
    <View style={DateRangeModalStyle.container}>
      <View style={DateRangeModalStyle.centeredView}>
        <Modal animationType="slide" transparent={true} visible={modalVisible}>
          <View style={DateRangeModalStyle.centeredView}>
            <View style={DateRangeModalStyle.overlay}></View>
            <View style={DateRangeModalStyle.modalView}>
              <TouchableOpacity
                style={DateRangeModalStyle.closeButton}
                onPress={onCloseModal}
              >
                <AntDesign name="close" size={20} color="#FFFFFF" />
              </TouchableOpacity>
              <View>
                {title ? (
                  <Text style={DateRangeModalStyle.title}>{title}</Text>
                ) : null}
                {subTitle ? (
                  <Text style={DateRangeModalStyle.subTitle}>{subTitle}</Text>
                ) : null}
                <View style={DateRangeModalStyle.dateSelectorWrapper}>
                  <TouchableOpacity
                    style={DateRangeModalStyle.showDatePickerButton}
                    onPress={() => setShowFromPicker(true)}
                  >
                    <Text style={DateRangeModalStyle.modalText}>
                      {START_DATE}
                    </Text>
                    {fromDate
                      ? renderDate(fromDate)
                      : renderCalenderIcon(START_DATE)}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={DateRangeModalStyle.showDatePickerButton}
                    onPress={() => setshowToPicker(true)}
                  >
                    <Text style={DateRangeModalStyle.modalText}>
                      {END_DATE}
                    </Text>
                    {toDate ? renderDate(toDate) : renderCalenderIcon(END_DATE)}
                  </TouchableOpacity>
                </View>
              </View>
              <View style={DateRangeModalStyle.row}>
                {/* Error Message */}
                {isError && (
                  <Text style={{ color: 'red' }}>
                    Please select a "Start Date" or a "End Date" in order to
                    continue
                  </Text>
                )}
              </View>

              <View style={DateRangeModalStyle.row}>
                <TouchableOpacity
                  style={DateRangeModalStyle.button}
                  onPress={onButtonPress}
                >
                  <Text style={DateRangeModalStyle.textStyle}>Generate</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          {showFromPicker && (
            <View style={DateRangeModalStyle.datePickerWrapper}>
              <DateTimePickerModal
                isVisible
                mode="date"
                date={fromDate || undefined}
                style={DateRangeModalStyle.datePicker}
                onConfirm={fromDateSelect}
                // display="spinner"
                onCancel={() => setShowFromPicker(false)}
                modalStyleIOS={DateRangeModalStyle.datePickerSelector}
                pickerContainerStyleIOS={DateRangeModalStyle.datePickerWrapper}
                maximumDate={new Date()}
                isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
              />
            </View>
          )}

          {showToPicker && (
            <View style={DateRangeModalStyle.datePickerWrapper}>
              <DateTimePickerModal
                isVisible
                mode="date"
                date={toDate || undefined}
                style={DateRangeModalStyle.datePicker}
                onConfirm={toDateSelect}
                // display="spinner"
                onCancel={() => setshowToPicker(false)}
                modalStyleIOS={DateRangeModalStyle.datePickerSelector}
                pickerContainerStyleIOS={DateRangeModalStyle.datePickerWrapper}
                maximumDate={new Date()}
                isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
              />
            </View>
          )}
        </Modal>
      </View>
    </View>
  );
};

export default DateRangeModal;
