import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customSelectCandidatePlayerStyles from './SelectCandidatePlayerStyles';
import ProfileImage from '../../../components/ProfileImage/ProfileImage';

import ExclamationIcon from '../../../../assets/buttons/exclamation.png';
import { Searchbar } from 'react-native-paper';
import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

const SelectCandidatePlayerModal = ({
  showModal,
  candidatePlayers,
  onCandidatePlayerSelect,
  visible,
  loading,
  onReachEndHandler,
  handleSearch,
}) => {
  const SelectCandidatePlayerStyles = useStyles(
    customSelectCandidatePlayerStyles
  );
  const colors = useColors();
  const closeModal = () => {
    setSearchText('');
    showModal(false);
  };

  const [players, setPlayers] = useState([]);
  const [searchText, setSearchText] = useState('');
  const flatlistRef = useRef();

  useEffect(() => {
    handleSearch('');
  }, []);

  useEffect(() => {
    candidatePlayers?.length && setPlayers(candidatePlayers);
  }, [candidatePlayers]);

  const filterPlayers = searchText => {
    flatlistRef?.current?.scrollToIndex({ index: 0 });
    handleSearch(searchText);
  };

  const handleSearchTextChange = text => {
    setSearchText(text?.toLowerCase());
    filterPlayers(text?.toLowerCase());
  };

  const PlayerItem = ({ item }) => {
    const {
      profileImageUrl,
      firstName,
      lastName,
      sportsProfileId,
      isAvailable,
    } = item;
    return (
      <TouchableOpacity
        onPress={() => {
          onCandidatePlayerSelect(sportsProfileId);
          setSearchText('');
        }}
      >
        <View style={SelectCandidatePlayerStyles.listItem}>
          <View style={SelectCandidatePlayerStyles.listItemImageContainer}>
            <ProfileImage
              style={SelectCandidatePlayerStyles.listItemImage}
              imageStyles={SelectCandidatePlayerStyles.listItemImage}
              profileImageUrl={profileImageUrl}
            />
            {!isAvailable && (
              <Image
                style={SelectCandidatePlayerStyles.exclamationIcon}
                source={ExclamationIcon}
              />
            )}
          </View>
          <Text style={SelectCandidatePlayerStyles.listItemText}>
            {firstName} {lastName}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={SelectCandidatePlayerStyles.centeredView}>
      <ModalWrapper visible={visible} transparent>
        <View style={SelectCandidatePlayerStyles.overlay} />
        <View style={SelectCandidatePlayerStyles.centeredView}>
          <View style={SelectCandidatePlayerStyles.modalView}>
            <TouchableOpacity
              onPress={() => closeModal()}
              style={SelectCandidatePlayerStyles.closeButton}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            {loading && <ActivityIndicator size="large" color={colors.green} />}
            <View style={SelectCandidatePlayerStyles.header}>
              <Searchbar
                placeholder="Search Player"
                placeholderTextColor="#fff"
                onChangeText={handleSearchTextChange}
                value={searchText}
                iconColor="#FFF"
                inputStyle={SelectCandidatePlayerStyles.searchText}
                style={SelectCandidatePlayerStyles.search}
                onSubmitEditing={() => filterPlayers(searchText)}
                onIconPress={() => filterPlayers(searchText)}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            {!loading && (!candidatePlayers || !candidatePlayers.length) ? (
              <View style={SelectCandidatePlayerStyles.noPlayerWrapper}>
                <Text style={SelectCandidatePlayerStyles.listItemText}>
                  No Player Found
                </Text>
              </View>
            ) : null}
            <FlatList
              style={SelectCandidatePlayerStyles.list}
              data={players}
              renderItem={PlayerItem}
              keyExtractor={item => item.sportsProfileId}
              ref={flatlistRef}
              onEndReached={() => onReachEndHandler(searchText)}
              onEndReachedThreshold={0.5}
            />
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default SelectCandidatePlayerModal;
