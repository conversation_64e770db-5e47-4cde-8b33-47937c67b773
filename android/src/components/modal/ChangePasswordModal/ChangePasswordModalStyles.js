import { StyleSheet, Text, View, Dimensions, Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { isRTL } from 'expo-localization';

const ChangePasswordModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        // width: wp('40%'),
        // height: hp('20%'),
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('3%'),
      }
    : {
        width: wp('70%'),
        flexDirection: 'column',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('3%'),
      },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 5,
    fontSize: wp('2%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  leftContent: isTabDevice()
    ? {
        width: '20%',
      }
    : {
        width: '100%',
        marginBottom: wp('5%'),
      },
  rightContent: isTabDevice()
    ? {
        // padding: wp('1%'),
      }
    : {
        width: '100%',
      },
  title: isTabDevice()
    ? {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        // textAlign: 'center',
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        // textAlign: 'center',
        marginBottom: wp('4%'),
      },
  userProfile: isTabDevice()
    ? {}
    : {
        flexDirection: 'row',
        // justifyContent: 'center',
        alignItems: 'center',
      },
  image: {
    width: wp('15%'),
    height: wp('17%'),
    borderRadius: wp('2%'),
  },
  userName: isTabDevice()
    ? {
        marginTop: wp('2%'),
      }
    : {
        marginLeft: wp('2%'),
      },
  name: isTabDevice()
    ? {
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      }
    : {
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      },
  requiredAstric: isTabDevice()
    ? {
        color: colors.red,
        fontWeight: 'bold',
        fontSize: wp('0.7%'),
      }
    : {
        color: colors.red,
        fontWeight: 'bold',
        fontSize: Platform.OS === 'android' ? wp('1.5%') : wp('0.7%'),
        marginTop: Platform.OS === 'android' ? wp('0.5%') : 0,
        marginLeft: Platform.OS === 'android' ? wp('0.5%') : 0,
      },
  inputFieldWrapper: {
    // width: '45%',
    // backgroundColor: colors.black,
  },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      },
  inputField: {
    width: '100%',
    height: hp('6%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  inputView: isTabDevice()
    ? {
        borderRadius: wp('0.7%'),
        paddingRight: wp('1.5%'),
        // width: wp('38%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
        backgroundColor: colors.darkBlue,
        marginBottom: wp('1%'),
      }
    : {
        borderRadius: wp('0.7%'),
        padding: wp('1.5%'),
        paddingRight: wp('1.5%'),
        width: wp('60%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
        backgroundColor: colors.darkBlue,
      },
  eyeIcon: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2.5%'),
        marginRight: 5,
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        marginRight: 5,
      },
  textInput: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        width: wp('24%'),
        padding: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        width: '90%',
        padding: wp('1.5%'),
        borderRadius: Platform.OS === 'android' ? wp('2%') : 0,
        paddingLeft: Platform.OS === 'android' ? wp('2%') : 0,
      },
  errorText: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.5%'),
        width: wp('24%'),
        padding: wp('1.5%'),
      }
    : {
        color: colors.red,
        fontSize: wp('3%'),
        width: '100%',
        padding: wp('1.5%'),
      },
  submitButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        height: wp('7%'),
        width: wp('20%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  submitButton2: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.darkBlue,
        height: wp('7%'),
        width: wp('20%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  submitRow: isTabDevice()
    ? {
        width: wp('25%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        width: '100%',
        marginTop: wp('3%'),
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        marginTop: wp('3%'),
        marginBottom: wp('1%'),
      },
});

export default ChangePasswordModalStyles;
