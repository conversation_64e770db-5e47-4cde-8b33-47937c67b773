import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const AddTeamStyles = colors => ({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    opacity: 0.97,
  },
  modalView: isTabDevice()
    ? {
        margin: 20,
        backgroundColor: '#263b58',
        borderRadius: 20,
        padding: 35,
        alignItems: 'center',
        shadowColor: '#000',
        width: 380,
        // height: 250,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        // margin: 20,
        backgroundColor: '#263b58',
        borderRadius: 20,
        padding: wp('5%'),
        alignItems: 'center',
        shadowColor: '#000',
        width: wp('90%'),
        // height: hp('55%'),
        // height: 250,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  modalText: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: '#fff',
        fontSize: 20,
        fontWeight: 'bold',
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: '#fff',
        fontSize: wp('6%'),
        fontWeight: 'bold',
      },
  teamNameInput: isTabDevice()
    ? {
        color: '#fff',
        fontSize: 20,
        fontWeight: 'bold',
        padding: 15,
        backgroundColor: colors.darkBlue,
        borderRadius: 15,
        width: '100%',
      }
    : {
        color: '#fff',
        fontSize: wp('3%'),
        fontWeight: 'bold',
        padding: wp('4%'),
        backgroundColor: colors.darkBlue,
        borderRadius: 15,
        width: '100%',
        height: wp('12%'),
      },
  colorPaletteContainer: {
    backgroundColor: colors.darkBlue,
    padding: 10,
    borderRadius: 15,
    marginTop: 10,
  },
  paletteHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 5,
    paddingRight: 5,
    marginBottom: 15,
  },
  paletteHeaderText: isTabDevice()
    ? {
        textAlign: 'center',
        color: '#fff',
        fontSize: 20,
        fontWeight: 'bold',
      }
    : {
        textAlign: 'center',
        color: '#fff',
        fontSize: wp('4%'),
        fontWeight: 'bold',
      },
  paletteSelectedColor: isTabDevice()
    ? {
        width: 27,
        height: 27,
        borderRadius: 8,
        margin: 2,
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        borderRadius: wp('1.5%'),
        margin: wp('1%'),
      },
  palette: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonTouchable: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: 20,
        marginTop: 10,
        width: '100%',
        height: 60,
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('6%'),
        marginTop: wp('2%'),
        width: '100%',
        height: wp('13%'),
        padding: wp('3%'),
        justifyContent: 'center',
      },
  createButtonText: isTabDevice()
    ? {
        alignItems: 'center',
        color: colors.white,
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: 'center',
      }
    : {
        alignItems: 'center',
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      },
  errorMessage: {
    fontSize: 15,
    color: colors.red,
    fontWeight: 'bold',
  },
  closeButton: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 15,
    top: 15,
    padding: 5,
  },
});

export default AddTeamStyles;
