import PropTypes from 'prop-types';
import React, { PureComponent } from 'react';
// import {TouchableOpacity} from "react-native-gesture-handler"
import { Button } from 'react-native-material-buttons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

import styles from './styles';

export default class DropdownItem extends PureComponent {
  static defaultProps = {
    color: 'transparent',
    disabledColor: 'transparent',
    rippleContainerBorderRadius: 0,
    shadeBorderRadius: 0,
  };

  static propTypes = {
    ...Button.propTypes,

    index: PropTypes.number.isRequired,
  };

  constructor(props) {
    super(props);

    this.onPress = this.onPress.bind(this);
  }

  onPress() {
    let { onPress, index } = this.props;

    if ('function' === typeof onPress) {
      onPress(index);
    }
  }

  render() {
    let { children, style, index, listItemStyle, ...props } = this.props;

    return (
      <Button
        {...props}
        style={[
          styles.container,
          style,
          {
            width: '100%',
            paddingBottom: isTabDevice() ? hp('1%') : wp('2%'),
            paddingTop: isTabDevice() ? hp('1%') : wp('2%'),
            height: isTabDevice() ? hp('4%') : undefined,
          },
          listItemStyle,
        ]}
        onPress={this.onPress}
      >
        {children}
      </Button>
    );
  }
}
