import React from 'react';
import { FlatList, ScrollView, Text, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { TouchableOpacity } from 'react-native-gesture-handler';
import useStyles from '../../hooks/useStyles';
import customTagListStyle from './TagListStyle';

interface IItem {
  id: string;
  label: string;
}

interface ITagList {
  tagList: IItem[];
  hasRemove?: boolean;
  onRemove: (id: string) => void;
}

const TagList: React.FC<ITagList> = ({
  tagList,
  onRemove,
  hasRemove = true,
}: any) => {
  const tagListStyle = useStyles(customTagListStyle);

  const tagItem = ({ item }: any) => (
    <View style={tagListStyle.tagContainer}>
      <Text style={tagListStyle.tagLabel}>{item?.name}</Text>
      {hasRemove && (
        <TouchableOpacity onPress={() => onRemove(item)}>
          <Ionicons name="close" color="white" size={15} />
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <ScrollView
      horizontal={true}
      contentContainerStyle={tagListStyle.statsTableContainer}
    >
      <FlatList
        data={tagList}
        renderItem={tagItem}
        keyExtractor={item => item.id}
        horizontal
      />
    </ScrollView>
  );
};

export default TagList;
