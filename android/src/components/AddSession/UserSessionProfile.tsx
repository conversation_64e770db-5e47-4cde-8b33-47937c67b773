import React from 'react';
import { Text, View } from 'react-native';
import { Checkbox, DefaultTheme } from 'react-native-paper';
import colors from '../../config/colors';
import { dateTimeConversion } from '../../helpers';
import { IUserMappedSession } from '../../hooks/SessionAPIHook/useSessionBulkHook';
import useStyles from '../../hooks/useStyles';
import ProfileImage from '../ProfileImage/ProfileImage';
import customUserSessionProfileStyle from './UserSessionProfileStyle';

const theme = {
  ...DefaultTheme,
  roundness: 10,
  colors: {
    ...DefaultTheme.colors,
    accent: colors.green,
    text: colors.white,
  },
};

interface IUserSessionProfileProps {
  userData: IUserMappedSession;
  index: number;
  onUserChecked: (index: number, hasChecked: boolean) => void;
}

const UserSessionProfile = ({
  userData,
  index,
  onUserChecked,
}: IUserSessionProfileProps) => {
  const {
    firstName,
    lastName,
    latestSessionCount,
    numberOfSessionsAvailable,
    profileImageUrl,
    isChecked,
    expiryDate,
    allSessionsExpired,
  } = userData;

  const { dateNumberString, monthNumberString, year } = dateTimeConversion(
    expiryDate as any
  );

  const UserSessionProfileStyle = useStyles(customUserSessionProfileStyle);
  return (
    <View style={UserSessionProfileStyle.userTile}>
      <View style={UserSessionProfileStyle.tileHeader}>
        <View style={UserSessionProfileStyle.imgContainer}>
          <ProfileImage
            imageStyles={UserSessionProfileStyle.itemImage}
            style={UserSessionProfileStyle.imageLoader}
            profileImageUrl={profileImageUrl}
          />
          <View style={UserSessionProfileStyle.tileNameWrapper}>
            <Text style={UserSessionProfileStyle.tileName}>
              {firstName || ''} {lastName || ''}
            </Text>
          </View>
        </View>
        <Checkbox
          status={isChecked ? 'checked' : 'unchecked'}
          theme={theme}
          onPress={() => {
            onUserChecked(index, !isChecked);
          }}
        />
      </View>
      <View style={UserSessionProfileStyle.tileContent}>
        <View style={UserSessionProfileStyle.tileContentRow}>
          <Text style={UserSessionProfileStyle.tileContentText}>
            No. of sessions
          </Text>
          <Text style={UserSessionProfileStyle.tileContentText2}>
            {latestSessionCount === null ? 'N/A' : latestSessionCount}
          </Text>
        </View>
        <View style={UserSessionProfileStyle.tileContentRow}>
          <Text style={UserSessionProfileStyle.tileContentText}>
            No. remaining of sessions
          </Text>
          <Text style={UserSessionProfileStyle.tileContentText2}>
            {numberOfSessionsAvailable === null
              ? 'N/A'
              : numberOfSessionsAvailable}
          </Text>
        </View>
        <View style={UserSessionProfileStyle.tileContentRow}>
          <Text style={UserSessionProfileStyle.tileContentText}>
            Expiry date
          </Text>
          <Text style={UserSessionProfileStyle.tileContentText2}>
            {allSessionsExpired
              ? 'Expired'
              : expiryDate
              ? `${dateNumberString}/${monthNumberString}/${year}`
              : 'N/A'}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default UserSessionProfile;
