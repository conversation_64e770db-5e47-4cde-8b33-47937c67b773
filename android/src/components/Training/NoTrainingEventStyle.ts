import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const NoTrainingEventStyle = (colors : any) => ({
  containerWrapper: isTabDevice()
    ? {
        width: '100%',
      }
    : {
        height: hp('60%'),
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
      },
  headerText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        marginTop: wp('1%'),
        marginBottom: wp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Bold',
        marginTop: wp('1%'),
        marginBottom: wp('2%'),
      },
  list: isTabDevice()
    ? {
        marginBottom: wp('30%'),
        height: '57%',
      }
    : { 
        height: '89%',
      },
      noScheduledWrapper: isTabDevice()
      ? {
          width: wp('40%'),
          margin: 'auto',
          height: hp('70%'),
          justifyContent: 'center',
          alignItems: 'center',
        }
      : {
          width: wp('70%'),
          margin: 'auto',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        },
    noScheduledWrapperTextMain: isTabDevice() ? {
      color: colors.white,
      fontSize: wp('2%'),
      fontFamily: 'Poppins-Bold',
      textAlign: 'center'
    } : {
      color: colors.white,
      fontSize: wp('4%'),
      fontFamily: 'Poppins-Bold',
      textAlign: 'center'
  
    },
    noScheduledWrapperTextSub: isTabDevice() ? {
      color: colors.white,
      fontSize: wp('1.5%'),
      fontFamily: 'Poppins-Regular',
      marginTop: wp("2%"),
      textAlign: 'center'
  
    } : {
      color: colors.white,
      fontSize: wp('3%'),
      fontFamily: 'Poppins-Regular',
      marginTop: wp("2%"),
      textAlign: 'center'
    },
    noScheduledWrapperButton: isTabDevice() ? {
      backgroundColor: colors.green,
      marginTop: wp("5%"),
      padding: wp('1.5%'),
      margin: 5,
      borderRadius: 8,
      width: "40%",
      color: colors.white,
      fontSize: wp('2%'),
      fontFamily: 'Poppins-Bold',
    } : {
      backgroundColor: colors.green,
      marginTop: wp("5%"),
      padding: wp('1.5%'),
      margin: 5,
      borderRadius: 8,
      width: "40%",
      color: colors.white,
      fontSize: wp('2%'),
      fontFamily: 'Poppins-Bold',
    },
    noScheduledWrapperButtonText: isTabDevice() ? {
      color: colors.white,
      fontSize: wp('1.5%'),
      fontFamily: 'Poppins-Regular',
      textAlign: 'center'
  
    } : {
      color: colors.white,
      fontSize: wp('3%'),
      fontFamily: 'Poppins-Regular',
      textAlign: 'center'
    },
});
export default NoTrainingEventStyle;
