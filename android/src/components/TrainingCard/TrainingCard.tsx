import { View, Text, Image } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import useColors from '../../hooks/useColors';
import useStyles from '../../hooks/useStyles';
import customTrainingCardStyle from './TrainingCardStyle';
import { isTabDevice } from '../../config/appConfig';
import { userRoleType } from '../../constants/constants';
import { useSelector } from 'react-redux';
import { ITraningEvent } from '../../store/reducers/Trainings/NewTrainingReducer';
import {formatDate} from "../../helpers/DateHelper"
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';

const TrainingCard = ({ item , handleRPEModal }: { item: ITraningEvent, handleRPEModal : Function }) => {
  const { userRole } = useSelector((state: any) => state.auth);
  const { month, time, date } = formatDate(item.startTime);


  const today = Number(
    new Date()
  );

  const trainingEndDateTimestamp =
    (item?.endTime && Number(new Date(item?.endTime))) || 0;
  const isRpeButtonDisable = trainingEndDateTimestamp >= today ? true : false;
  const isCoaches =
    userRole === userRoleType.COACH || userRole === userRoleType.HEAD_COACH;

  const {
    RPEMap,
    playerRPEResponse,
    handleAttendanceModal,
    handleSessionModal,
  } = useTrainingHook({
    isInitalComponent: false,
  });

  const computeColor = isCoaches
    ? '#0A1528'
    : playerRPEResponse[item._id]
      ? RPEMap?.[playerRPEResponse?.[item._id] || '']?.labelColor
      : '#0A1528';

  const TrainingCardStyles = useStyles(customTrainingCardStyle);
  const colors = useColors();
  return (
    <View style={TrainingCardStyles.container}>
      <View style={TrainingCardStyles.timeSection}>
        <Text style={TrainingCardStyles.timeText}>{month} {date} </Text>
        <Text style={TrainingCardStyles.timeText}>{time}</Text>
      </View>
      <View style={TrainingCardStyles.buttonSection}>
        <TouchableOpacity
          style={{...TrainingCardStyles.buttonContainer, backgroundColor: computeColor}}
          onPress={() => handleRPEModal(item._id)}
          disabled={ !isCoaches ? isRpeButtonDisable: false}
        >
          <Text style={TrainingCardStyles.buttonText}>RPE</Text>
          <Image
            source={require('../../../assets/icons/rpeIcon.png')}
            style={TrainingCardStyles.buttonIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <TouchableOpacity style={TrainingCardStyles.buttonContainer} 
                  onPress={() =>
                    handleSessionModal({ event: item, isSessionModal: true })
                  }
        >
          <Text style={TrainingCardStyles.buttonText}>Session Plans</Text>
          <View style={TrainingCardStyles.buttonIcon}>
            <Ionicons
              name="document"
              color={colors.white}
              size={isTabDevice() ? 25 : 15}
            />
          </View>
        </TouchableOpacity>
        {isCoaches && <TouchableOpacity
          style={[
            {
              ...TrainingCardStyles.buttonContainer,
              backgroundColor: colors.aquaBlue,
            },
            !isTabDevice() && { width: '100%' },
          ]}
          onPress={() =>
            handleAttendanceModal({
              eventId: item?._id,
              isShow: true,
            })
          }
        >
          <Text style={TrainingCardStyles.buttonText}>Attendance</Text>
          <View style={TrainingCardStyles.buttonIcon}>
            <Ionicons
              name="people"
              size={isTabDevice() ? 25 : 15}
              color={colors.white}
            />
          </View>
        </TouchableOpacity>}
      </View>
    </View>
  );
};

export default TrainingCard;
