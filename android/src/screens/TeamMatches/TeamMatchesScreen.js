import React from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import TeamMatchesContainer from '../../Container/TeamMatchesContainer/TeamMatchesContainer';
import useStyles from '../../hooks/useStyles';

export default function TeamMatchesScreen() {
  const LayoutStyles = useStyles(customLayoutStyles);

  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <TeamMatchesContainer />
      </View>
    </ScreenWrapper>
  );
}
