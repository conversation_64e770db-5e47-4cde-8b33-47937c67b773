import React from 'react';
import { View } from 'react-native';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import TrainingContainer from '../../Container/TrainingContainer/TrainingContainer';
import useStyles from '../../hooks/useStyles';

const MatchLogScreen = () => {
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <TrainingContainer />
      </View>
    </ScreenWrapper>
  );
};

export default MatchLogScreen;
