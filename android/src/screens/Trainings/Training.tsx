import React, { ReactNode } from 'react';
import { View, Text } from 'react-native';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import TrainingContainer from '../../Container/TrainingContainer/TrainingContainer';
import useStyles from '../../hooks/useStyles';

import customLayoutStyles from '../../components/Layout/layoutStyles';
import NewTrainingContainer from '../../Container/TrainingContainer/NewTrainingContainer';

interface TrainingScreenWrapperProps {
  children: ReactNode;
}

const TrainingScreenWrapper: React.FC<TrainingScreenWrapperProps> = ({ children }) => {
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        {children}
      </View>
    </ScreenWrapper>
  );
};


export const TrainingTeamMobileScreen: React.FC = () => (
  <TrainingScreenWrapper>
    <NewTrainingContainer screenType='TrainingTeam' />
  </TrainingScreenWrapper>
);

export const TrainingTeamEventMobileScreen: React.FC = () => (
  <TrainingScreenWrapper>
    <NewTrainingContainer screenType='TrainingEvent' />
  </TrainingScreenWrapper>
);

export const TrainingTabScreen: React.FC = () => (
  <TrainingScreenWrapper>
    <NewTrainingContainer screenType='Tablet' />
  </TrainingScreenWrapper>
);


export default {
  TrainingTeamMobileScreen,
  TrainingTeamEventMobileScreen,
  TrainingTabScreen
};