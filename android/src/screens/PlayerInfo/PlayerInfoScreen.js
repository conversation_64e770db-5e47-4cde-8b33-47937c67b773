import * as FileSystem from 'expo-file-system';
import React, { Fragment, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Keyboard,
  Linking,
  Platform,
  Share,
  Switch,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import DateRangeModal from '../../components/modal/DaterangeModal/DateRangeModal';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import ChildListOnly from '../../components/TeamLabel/ChildListOnly';
import TeamLabel from '../../components/TeamLabel/TeamLabel';
import TeamListOnly from '../../components/TeamLabel/TeamListOnly';
import { isTabDevice } from '../../config/appConfig';
import { playerStatDevices, userRoleType } from '../../constants/constants';
import {
  FOOTBALL_SERVICE
} from '../../constants/services';
import DeviceStats from '../../Container/PlayerInfo/DeviceStats/DeviceStats';
import SelectPlayerDeviceContainer from '../../Container/PlayerInfo/DeviceStats/SelectPlayerDeviceContainer';
import IAP from '../../Container/PlayerInfo/IAP/IAP';
import PlayerCommon from '../../Container/PlayerInfo/PlayerCommon/PlayerCommon';
import ProfileInfo from '../../Container/PlayerInfo/playerInfo';
import ProfileImage from '../../Container/PlayerInfo/ProfileImage';
import ProfileLabel from '../../Container/PlayerInfo/ProfileLabel';
import Stats from '../../Container/PlayerInfo/Stats/Stats';
import { dateToYMD } from '../../helpers/';
import { dateToString } from '../../helpers/DateHelper';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import {
  SET_SELECTED_CHILD
} from '../../store/actionTypes/common/commonActionTypes';
import { SET_SELECTED_DEVICE } from '../../store/actionTypes/DeviceStats/DeviceStatsActions';
import {
  PLAYER_INFO_EDIT_UPLOAD_MODAL,
  PLAYER_INFO_FAIL,
  PLAYER_INFO_REQUEST,
  PLAYER_INFO_RESET,
  PLAYER_INFO_SHOW_UPLOAD_MODAL,
  PLAYER_INFO_SUCCESS,
} from '../../store/actionTypes/player/playerAction';
import {
  IAP_STAT_GRAPH_DATE_RANGE_CHANGED,
  PLAYER_IAP_REPORT_FAILED,
  PLAYER_IAP_REPORT_REQUEST,
  PLAYER_IAP_REPORT_SUCCESS,
} from '../../store/actionTypes/PlayerIAP/PlayerIapAction';
import { UPLOAD_IMAGE_RESET } from '../../store/actionTypes/UploadImage/uploadImageAction';
import { USER_IMAGE_UPDATE } from '../../store/actionTypes/userType/userType';
import customPlayerInfoScreenStyle from './PlayerInfoScrenStyle';
import { Foundation } from '@expo/vector-icons';

export default function PlayerInfoScreen({
  navigation,
  route,
  isNotAvailable,
}) {
  const PlayerInfoScreenStyle = useStyles(customPlayerInfoScreenStyle);
  const LayoutStyles = useStyles(customLayoutStyles);
  const dispatch = useDispatch();
  const [teamID, setTeamID] = useState(null);
  const [profileID, setProfileID] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalResponse, setModalResponse] = useState(false);
  const [isToggleEnabled, setIsToggleEnabled] = useState(!isNotAvailable);
  const [isCommentsModalOpen, setIsCommentsModalOpen] = useState(false);

  const {
    statGraphDateRange,
    statGraphView,
    statGraphCategoryName,
    statGraphCriteriaName,
    selectedIAPCategory,
    statGraphCriteriaId,
    shareReportLoading,
    iapReportUrl,
    showStatGraph,
  } = useSelector(state => state.playerIAP);

  const { playerLabelIdData, teamData } = useSelector(state => state?.team);
  const [isEditMode, setIsEditMode] = useState(false);
  const [savePlayerInfo, setSavePlayerInfo] = useState(false);

  const [fetchData] = useApi();
  // const [fetchChildren] = useApi();
  const [fetchIapReportUrl] = useApi();

  const { playerInfoUpdateLoading, playerUpdateSuccess } = useSelector(
    state => state?.player
  );

  const { userData } = useSelector(state => state?.auth);
  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;
  const isCoach =
    userRoleType.COACH === userData?.type ||
    userRoleType.HEAD_COACH === userData?.type;
  const { selectedChild, childInformation } = useSelector(
    state => state?.common
  );

  const { PlayerInfoDataLoading, PlayerInfoData } = useSelector(
    state => state?.playerInfo
  );

  const [removeErrorMessage, setRemoveErrorMessage] = useState(false);

  useEffect(() => {
    return () => {
      dispatch({
        type: PLAYER_INFO_RESET,
      });
      dispatch({
        type: UPLOAD_IMAGE_RESET,
      });
    };
  }, []);

  useEffect(() => {
    if (PlayerInfoData) {
      setIsToggleEnabled(
        PlayerInfoData?.isAvailable === true ||
        PlayerInfoData?.isAvailable === undefined
      );

      if (PlayerInfoData?.profileImageUrl && !isCoach && !isParent) {
        dispatch({
          type: USER_IMAGE_UPDATE,
          payload: PlayerInfoData.profileImageUrl,
        });
      }
    }
  }, [PlayerInfoData]);

  useEffect(() => {
    if (modalResponse) {
      dispatch({
        type: IAP_STAT_GRAPH_DATE_RANGE_CHANGED,
        payload: modalResponse,
      });
    }
  }, [modalResponse]);

  useEffect(() => {
    const { teamID, profileID } = route?.params;
    if (!isParent) {
      setProfileID(profileID);
    }
    setTeamID(teamID);
  }, [route, isParent]);

  useEffect(() => {
    childInformation?.sportsProfileId &&
      setProfileID(childInformation?.sportsProfileId);
  }, [childInformation?.sportsProfileId]);

  useEffect(() => {
    if (profileID && teamID) {
      dispatch({
        type: PLAYER_INFO_RESET,
      });
      fetchData(
        `/api/v1/teams/${teamID}/players/${profileID}`,
        PLAYER_INFO_REQUEST,
        PLAYER_INFO_SUCCESS,
        PLAYER_INFO_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [profileID, teamID]);

  useEffect(() => {
    if (playerUpdateSuccess) {
      setIsEditMode(false);
      setSavePlayerInfo(false);

      dispatch({
        type: 'PLAYER_INFO_UPDATE_RESET',
      });

      fetchData(
        `/api/v1/teams/${teamID}/players/${profileID}`,
        PLAYER_INFO_REQUEST,
        PLAYER_INFO_SUCCESS,
        PLAYER_INFO_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    } else {
      setSavePlayerInfo(false);
    }
  }, [playerUpdateSuccess]);

  const showUploadModal = () =>
    dispatch({
      type: PLAYER_INFO_SHOW_UPLOAD_MODAL,
      payload: true,
    });

  const hideUploadModal = () =>
    dispatch({
      type: PLAYER_INFO_SHOW_UPLOAD_MODAL,
      payload: false,
    });

  const hideEditModal = () =>
    dispatch({
      type: PLAYER_INFO_EDIT_UPLOAD_MODAL,
      payload: false,
    });

  const getIapReportUrl = () => {
    let requestUrl = `/api/v1/sport-profiles/${profileID}`;
    if (
      statGraphCriteriaId &&
      selectedIAPCategory &&
      statGraphDateRange &&
      statGraphView
    ) {
      const startDate = dateToYMD(new Date(statGraphDateRange?.from));
      const endDate = dateToYMD(new Date(statGraphDateRange?.to));
      requestUrl += `/iap-stats/avg/graph?startDate=${new Date(
        startDate
      ).toISOString()}&endDate=${new Date(
        endDate
      ).toISOString()}&returnFormat=summaryByMonth&categoryId=${selectedIAPCategory}&criteriaId=${statGraphCriteriaId}&teamId=${teamID}`;
    } else {
      requestUrl += `/iap-stats/report?teamId=${teamID}`;
    }
    fetchIapReportUrl(
      requestUrl,
      PLAYER_IAP_REPORT_REQUEST,
      PLAYER_IAP_REPORT_SUCCESS,
      PLAYER_IAP_REPORT_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    if (iapReportUrl) {
      Linking.openURL(iapReportUrl);
      dispatch({
        type: 'SHARE_MATCH_REPORT',
        payload: false,
      });
    }
  }, [iapReportUrl]);

  const onPressShare = async url => {
    dispatch({
      type: 'SHARE_MATCH_REPORT',
      payload: true,
    });

    try {
      const playerName = `${PlayerInfoData?.firstName || ''}`.trim();

      const fileName = `${playerName}-IAP-Stats${statGraphView
        ? `-${statGraphCategoryName.replace(
          /\s/g,
          '-'
        )}-${statGraphCriteriaName.replace(/\s/g, '-')}`
        : ''
        }-Report`;

      const fileUri = `${FileSystem.documentDirectory}${fileName.split(' ')[fileName.split(' ').length - 1]
        }.pdf`;

      const { uri, status } = await FileSystem.downloadAsync(url, fileUri);

      dispatch({
        type: 'SHARE_MATCH_REPORT',
        payload: false,
      });

      await Share.share({
        ...Platform.select({
          ios: {
            url: uri,
          },
          android: {
            message: uri,
          },
        }),
      });
    } catch (error) {
      dispatch({
        type: 'SHARE_MATCH_REPORT',
        payload: false,
      });
      console.error('Error sharing report:', error);
    }
  };

  const getDateRangeText = () => {
    const from = statGraphDateRange?.from
      ? dateToString(statGraphDateRange.from).replace(/-/g, '/')
      : '';
    const to = statGraphDateRange?.to
      ? dateToString(statGraphDateRange.to).replace(/-/g, '/')
      : '';
    return from + ' - ' + to;
  };

  const SavePlayer = () => {
    setSavePlayerInfo(true);
  };

  const toggleSwitch = () => {
    setIsToggleEnabled(previousState => !previousState);
  };
  const onCancel = () => {
    setIsEditMode(false);
    setIsToggleEnabled(
      PlayerInfoData?.isAvailable === true ||
      PlayerInfoData?.isAvailable === undefined
    );
    setRemoveErrorMessage(true);
  };

  const isFirstTwoTabs = () => {
    return (
      !playerLabelIdData || playerLabelIdData == 1 || playerLabelIdData == 2
    );
  };

  const isSecondTab = playerLabelIdData == 2 ? (isCoach ? true : false) : true;

  useEffect(() => {
    playerLabelIdData == 7 &&
      dispatch({
        type: SET_SELECTED_DEVICE,
        payload: { data: playerStatDevices.PLAYER_MAKER_DEVICE },
      });
  }, [playerLabelIdData]);

  const setSelectedChild = payload => {
    dispatch({
      type: SET_SELECTED_CHILD,
      payload,
    });
  };

  return (
    <ScreenWrapper>
      <TouchableWithoutFeedback
        onPress={Keyboard.dismiss}
        accessible={false}
        touchSoundDisabled={true}
      >
        <>
          <View style={LayoutStyles.contentWrapper}>
            {(isPlayer || isParent) && !isTabDevice() && (
              <TeamLabel
                selectedChild={selectedChild}
                setSelectedChild={setSelectedChild}
                setTeamID={setTeamID}
              />
            )}
            <View style={PlayerInfoScreenStyle.leftView}>
              {isParent && isTabDevice() && (
                <ChildListOnly
                  setSelectedChild={setSelectedChild}
                  selectedChild={selectedChild}
                />
              )}
              {isTabDevice() && statGraphView && (
                <TouchableOpacity
                  onPress={() => setModalVisible(true)}
                  style={PlayerInfoScreenStyle.dateRangePosition}
                >
                  <Text style={PlayerInfoScreenStyle.dateRange}>
                    Date Range
                  </Text>
                  <View style={PlayerInfoScreenStyle.dateRangeSelector}>
                    <Text style={PlayerInfoScreenStyle.dateRangeSelectorText}>
                      {getDateRangeText()}
                    </Text>
                  </View>
                  <Image
                    style={PlayerInfoScreenStyle.dateRangeArrow}
                    source={require('../../../assets/buttons/whiteDownArrow.png')}
                  />
                </TouchableOpacity>
              )}
              {PlayerInfoDataLoading ? (
                <ActivitySpinner />
              ) : (
                <View>
                  <ProfileImage
                    isToggleEnabled={isToggleEnabled}
                    isEditMode={isEditMode}
                    setIsEditMode={setIsEditMode}
                    PlayerData={PlayerInfoData}
                    statGraphView={statGraphView}
                    statGraphCategoryName={statGraphCategoryName}
                    statGraphCriteriaName={statGraphCriteriaName}
                    SavePlayer={SavePlayer}
                    onPressShare={getIapReportUrl}
                    showShareOption={playerLabelIdData == 2}
                    isPlayerInfor
                    toggleSwitch={toggleSwitch}
                    removeErrorMessage={removeErrorMessage}
                    setRemoveErrorMessage={setRemoveErrorMessage}
                    isInPlayerInfo={
                      playerLabelIdData == 1 || !playerLabelIdData
                    }
                  />
                </View>
              )}
              {isTabDevice() &&
                isEditMode &&
                isCoach &&
                !playerInfoUpdateLoading &&
                !PlayerInfoDataLoading &&
                (!playerLabelIdData || playerLabelIdData == 1) && (
                  <View style={PlayerInfoScreenStyle.switchView}>
                    <Text style={PlayerInfoScreenStyle.switchText}>
                      {!isToggleEnabled ? 'Unavailable' : 'Available'}
                    </Text>
                    <View style={PlayerInfoScreenStyle.switch}>
                      <Switch
                        style={PlayerInfoScreenStyle.unavailableSwitch}
                        trackColor={{ false: '#23344B', true: '#23344B' }}
                        thumbColor={isToggleEnabled ? '#36D982' : '#D94136'}
                        ios_backgroundColor="#23344B"
                        onValueChange={toggleSwitch}
                        value={isToggleEnabled}
                      />
                      <Text style={PlayerInfoScreenStyle.switchText2}>
                        {!isToggleEnabled ? 'No' : 'Yes'}{' '}
                      </Text>
                    </View>
                  </View>
                )}
              {isTabDevice() &&
                !isEditMode &&
                !playerInfoUpdateLoading &&
                !PlayerInfoDataLoading &&
                isFirstTwoTabs() && (
                  <View style={
                    isCoach
                      ? PlayerInfoScreenStyle.editModeView
                      : PlayerInfoScreenStyle.editModeView4
                  }>
                    {shareReportLoading ? (
                      <ActivityIndicator color="#36d982" />
                    ) : (
                      <View style={PlayerInfoScreenStyle.btnWrapper}>
                        <View style={PlayerInfoScreenStyle.btnWrapperRow}>
                          {isSecondTab && (
                            <TouchableOpacity
                              onPress={() => setIsEditMode(true)}
                              disabled={isParent && !teamData?.data?.length}
                              style={isParent && !teamData?.data?.length ? {...PlayerInfoScreenStyle.btn,backgroundColor : 'grey'} :PlayerInfoScreenStyle.btn}
                            >
                              <Text style={PlayerInfoScreenStyle.btnTxt}>
                                Edit
                              </Text>
                            </TouchableOpacity>
                          )}

                          {playerLabelIdData == 2 && (
                            <TouchableOpacity
                              style={PlayerInfoScreenStyle.btn2}
                              onPress={() => getIapReportUrl()}
                            >
                              <Foundation
                                style={PlayerInfoScreenStyle.shareIcon}
                                name="download"
                                size={24}
                                color="white"
                              />
                              <Text style={PlayerInfoScreenStyle.btnTxt}>
                                Download
                              </Text>
                            </TouchableOpacity>
                          )}

                          {!isCoach && playerLabelIdData == 2 &&  !showStatGraph && (
                            <TouchableOpacity
                              onPress={() => setIsCommentsModalOpen(true)}
                              style={PlayerInfoScreenStyle.btnComments}
                            >
                              <Text style={PlayerInfoScreenStyle.btnCommentTxt}>
                                Comments
                              </Text>
                            </TouchableOpacity>
                          )}
                        </View>
                        {playerLabelIdData == 2 && !showStatGraph && isCoach &&  (
                          <TouchableOpacity
                            onPress={() => setIsCommentsModalOpen(true)}
                            style={PlayerInfoScreenStyle.btnCommentsCoach}
                          >
                            <Text style={PlayerInfoScreenStyle.btnCommentTxt}>
                              Comments
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    )}

                  </View>
                )}
              {isTabDevice() && playerLabelIdData == 7 && (
                <SelectPlayerDeviceContainer />
              )}
              {isTabDevice() &&
                isEditMode &&
                !playerInfoUpdateLoading &&
                !PlayerInfoDataLoading && (
                  <>
                    <View
                      style={
                        isCoach
                          ? PlayerInfoScreenStyle.editModeView3
                          : PlayerInfoScreenStyle.editModeView2
                      }
                    >
                      {(!playerLabelIdData || playerLabelIdData == 1) && (
                        <Fragment>
                          <TouchableOpacity
                            onPress={() => SavePlayer()}
                            style={PlayerInfoScreenStyle.btn}
                          >
                            <Text style={PlayerInfoScreenStyle.btnTxt}>Save</Text>
                          </TouchableOpacity>

                          <TouchableOpacity
                            onPress={() => onCancel()}
                            style={PlayerInfoScreenStyle.btn2}
                          >
                            <Text style={PlayerInfoScreenStyle.btnTxt}>
                              Cancel
                            </Text>
                          </TouchableOpacity>
                        </Fragment>
                      )}
                      
                      <View>
                        {playerLabelIdData == 2 && (
                          <TouchableOpacity
                            onPress={() => setIsEditMode(false)}
                            style={
                              isCoach
                                ? PlayerInfoScreenStyle.btnPushed
                                : PlayerInfoScreenStyle.btn
                            }
                          >
                            <Text style={PlayerInfoScreenStyle.btnTxt}>Done</Text>
                          </TouchableOpacity>
                        )}
                        {playerLabelIdData == 2 && (
                          // <TouchableOpacity
                          //   onPress={() => setIsCommentsModalOpen(true)}
                          //   style={PlayerInfoScreenStyle.btnComments} 
                          // >
                          //   <Text style={PlayerInfoScreenStyle.btnCommentTxt}>
                          //     Comments
                          //   </Text>
                          // </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => setIsCommentsModalOpen(true)}
                            style={{...PlayerInfoScreenStyle.btnCommentsCoach, marginTop: hp('2%')}}
                          >
                            <Text style={PlayerInfoScreenStyle.btnCommentTxt}>
                              Comments
                            </Text>
                          </TouchableOpacity> 
                        )}
                      </View>
                    </View>
                  </>
                )}
              {isTabDevice() &&
                (!playerLabelIdData || playerLabelIdData == 1) && (
                  <View>
                    <TouchableOpacity
                      onPress={showUploadModal}
                      style={PlayerInfoScreenStyle.btnUpload}
                    >
                      <Text style={PlayerInfoScreenStyle.btnTxt}>
                        Documents
                      </Text>
                      <Image
                        style={PlayerInfoScreenStyle.btnUploadIcon}
                        source={require('../../../assets/icons/document-white.png')}
                      />
                    </TouchableOpacity>
                  </View>
                )}
            </View>
            <View style={PlayerInfoScreenStyle.rightView}>
              {(isPlayer || isParent) && isTabDevice() && (
                <TeamListOnly
                  setTeamID={setTeamID}
                />
              )}
              <View style={PlayerInfoScreenStyle.columnView}>
                <View style={PlayerInfoScreenStyle.ProfileLabelView}>
                  <ProfileLabel playerLabelIdData={playerLabelIdData} key={selectedChild?.id || 1} />
                </View>
                <View style={PlayerInfoScreenStyle.commonView}>
                  {(!playerLabelIdData || playerLabelIdData == 1) && (
                    <ProfileInfo
                      PlayerData={PlayerInfoData}
                      globalEdit={isEditMode}
                      TeamID={teamID}
                      savePlayerStatus={savePlayerInfo}
                      handlePlayerStatus={setSavePlayerInfo}
                      ToggleEnabled={isToggleEnabled}
                      profileID={profileID}
                      hideUploadModal={hideUploadModal}
                      hideEditModal={hideEditModal}
                      playerLabelIdData={playerLabelIdData}
                    />
                  )}
                  {playerLabelIdData == 2 && PlayerInfoData?.sportsProfileId && (
                    <View>
                      {!isTabDevice() && statGraphView && (
                        <TouchableOpacity onPress={() => setModalVisible(true)}>
                          <Text style={PlayerInfoScreenStyle.dateRange}>
                            Date Range
                          </Text>
                          <View
                            style={
                              PlayerInfoScreenStyle.dateRangeSelectorWrapper
                            }
                          >
                            <Text
                              style={PlayerInfoScreenStyle.dateRangeSelector}
                            >
                              {getDateRangeText()}
                            </Text>
                          </View>
                          <Image
                            style={PlayerInfoScreenStyle.dateRangeArrow}
                            source={require('../../../assets/buttons/whiteDownArrow.png')}
                          />
                        </TouchableOpacity>
                      )}
                      <IAP isCoach={isCoach} isEditMode={isCoach ? isEditMode : false} isCommentsModalOpen={isCommentsModalOpen} setIsCommentsModalOpen={setIsCommentsModalOpen} />
                    </View>
                  )}
                  {playerLabelIdData == 3 && PlayerInfoData?.sportsProfileId && (
                    <PlayerCommon
                      option
                      highlight
                      header
                      HeaderText="Current Updates"
                      message="Selected for School Team"
                      playerLabelIdData={3}
                    />
                  )}
                  {playerLabelIdData == 4 && PlayerInfoData?.sportsProfileId && (
                    <PlayerCommon
                      option
                      highlight
                      navHeader
                      navHeadertext1="Medical History"
                      navHeadertext2="Injuries"
                      message="Player has ashma Watch closely"
                      playerLabelIdData={4}
                    />
                  )}
                  {playerLabelIdData == 5 && PlayerInfoData?.sportsProfileId && (
                    <PlayerCommon
                      option={false}
                      highlight={false}
                      navHeader
                      navHeadertext1="Past Events"
                      navHeadertext2="Upcoming Events"
                      navHeadertext3="Sessions"
                      message=""
                      playerLabelIdData={5}
                    />
                  )}
                  {playerLabelIdData == 6 && PlayerInfoData?.sportsProfileId && (
                    <View>
                      {
                        <Stats
                          PlayerInfoData={PlayerInfoData}
                          teamID={teamID}
                          key={teamID}
                        />
                      }
                    </View>
                  )}
                  {playerLabelIdData == 7 && PlayerInfoData?.sportsProfileId && (
                    <DeviceStats PlayerInfoData={PlayerInfoData} />
                  )}
                </View>
              </View>
            </View>
          </View>
          {/* {modalVisible && ( */}
          <DateRangeModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            setModalResponse={setModalResponse}
            statGraphDateRange={statGraphDateRange}
            title={'IAP Graph'}
            subTitle={'Select Date Range'}
          />
          {/* )} */}
        </>
      </TouchableWithoutFeedback>
    </ScreenWrapper>
  );
}
