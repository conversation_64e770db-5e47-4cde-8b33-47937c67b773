import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const PlayerInfoScreenStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
        flex: 1,
      },
  textOnly: {
    color: '#fff',
  },
  leftView: isTabDevice()
    ? {
        width: wp('26%'),
        // justifyContent: 'center',
        marginTop: wp('1%'),
        paddingLeft: wp('3%'),
        height: hp('88%'),
      }
    : {
        width: wp('100%'),
        flexDirection: 'row',
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        // paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('100%'),
        marginBottom: wp('24%'),
      }
    : {
        width: wp('100%'),
        height: hp('65%'),
        marginBottom: wp('13%'),
      },
  playerInfoScreenScroll: isTabDevice()
    ? {}
    : {
        // backgroundColor: colors.green,
        height: hp('100%'),
      },
  ProfileLabelView: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  commonView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactsView: {
    flex: 1,
  },
  columnView: {
    flex: 1,
    flexDirection: 'column',
    marginBottom: 200,
  },
  dateRangePosition: isTabDevice()
    ? {
        // position: 'absolute',
        // top: wp('-7%'),
        // left: wp('2%'),
      }
    : {
        position: 'absolute',
        top: wp('-10%'),
        left: wp('2%'),
      },
      dateRangeSelectorText: isTabDevice()
    ? {
        color: colors.white,
      }
    : {
        fontSize: wp('3.5%'),
      },
  dateRange: isTabDevice()
    ? {
        marginTop: hp('2%'),
        marginBottom: hp('1%'),
        fontSize: wp('1.2%'),
        color: colors.white,
        position: 'relative',
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        marginTop: wp('2%'),
        marginBottom: wp('3%'),
        marginLeft: wp('5%'),
        fontFamily: 'Poppins-Regular',
      },
  dateRangeSelector: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        color: colors.white,
        padding: wp('1%'),
        width: wp('20%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  dateRangeSelectorWrapper: {
    backgroundColor: colors.green,
    color: colors.white,
    padding: wp('2%'),
    width: wp('90%'),
    marginLeft: wp('5%'),
    borderRadius: wp('4%'),
  },
  dateRangeArrow: isTabDevice()
    ? {
        width: wp('1%'),
        resizeMode: 'contain',
        position: 'absolute',
        bottom: wp('0.2%'),
        left: wp('18%'),
      }
    : {
        width: wp('3%'),
        resizeMode: 'contain',
        position: 'absolute',
        bottom: 0,
        right: wp('6%'),
      },
  btn: {
    backgroundColor: colors.green,
    width: wp('8%'),
    height: wp('4%'),
    borderRadius: wp('1%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp('2%'),
  },
  btnPushed: {
    backgroundColor: colors.green,
    width: wp('8%'),
    height: wp('4%'),
    borderRadius: wp('1%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: wp('7%'),
  },
  btnDisabled: {
    backgroundColor: colors.grey,
    width: wp('8%'),
    height: wp('5%'),
    borderRadius: wp('1%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp('2%'),
    opacity: 0.7,
  },
  btn2: {
    backgroundColor: colors.borderBlue,
    width: wp('10%'),
    height: wp('4%'),
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp('2%'),
  },
  btnTxt: {
    fontSize: wp('1%'),
    color: colors.white,
    fontFamily: 'Poppins-Regular',
  },
  shareIcon: {
    width: wp('2%'),
    resizeMode: 'contain',
    marginRight: wp('1%'),
  },
  btnCancel: {
    height: 40,
    width: 80,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  switchView: isTabDevice()
    ? {
        alignItems: 'flex-start',
        flexDirection: 'column',
        marginBottom: wp('1%'),
        marginTop: wp('4%'),
      }
    : {
        alignItems: 'flex-start',
        flexDirection: 'column',
        marginBottom: wp('3%'),
        marginTop: wp('-7%'),
      },
  switchText: isTabDevice()
    ? {
        fontSize: wp('1.7%'),
        color: colors.red,
        marginBottom: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.red,
        marginBottom: hp('2%'),
        fontFamily: 'Poppins-Bold',
      },
  switch: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('10%'),
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingLeft: wp('0.5%'),
        marginTop: wp('-1%'),
      }
    : {
        flexDirection: 'row',
        width: wp('10%'),
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingLeft: wp('0.5%'),
      },
  switchText2: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginLeft: wp('1%'),
      }
    : {
        fontSize: wp('3.4%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginLeft: wp('1%'),
      },
  unavailableSwitch: isTabDevice()
    ? {
        transform: [{ scaleX: 1.5 }, { scaleY: 1.5 }],
      }
    : {
        transform: [{ scaleX: 1.5 }, { scaleY: 1.5 }],
      },
  editModeView: isTabDevice()
    ? {
        flexDirection: 'row',
        marginTop: wp('5%'),
      }
    : {
        flexDirection: 'row',
      },
  editModeView2: isTabDevice()
    ? {
        flexDirection: 'row',
        marginTop: wp('5%'),
      }
    : {
        flexDirection: 'row',
      },
  editModeView3: isTabDevice()
    ? {
        flexDirection: 'row',
      }
    : {
        flexDirection: 'row',
      },
  editModeView4: isTabDevice()
  ? {
      flexDirection: 'row',
      marginTop: wp('5%'),
    }
  : {
      flexDirection: 'row',
    },
  graphScroll: isTabDevice()
    ? {}
    : {
        height: hp('53%'),
      },
      btnComments: isTabDevice() ? {
        backgroundColor: colors.aquaBlue,
        width: wp('10%'),
        height: wp('4%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        marginRight: hp('2%'),
      } : {
        backgroundColor: colors.aquaBlue,
        width: '100%',
        height: wp('10%'),
        padding: wp('2%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: wp('2%'),
        alignSelf: 'center'
      },
      btnCommentsCoach: isTabDevice() ? {
        backgroundColor: colors.aquaBlue,
        width: '95%',
        height: wp('4%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        marginRight: hp('2%')
      } : {
        backgroundColor: colors.aquaBlue,
        width: '100%',
        height: wp('10%'),
        padding: wp('2%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: wp('2%'),
        alignSelf: 'center'
      },
      btnCommentTxt: isTabDevice() ? {
        fontSize: wp('1%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      } : { 
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      },
      btnWrapper: {
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: 10,
        width: '100%'
      },
      btnWrapperRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%'
      },
  btnUpload: {
    backgroundColor: colors.aquaBlue,
    width: '80%',
    height: wp('5%'),
    padding: wp('1.2%'),
    borderRadius: wp('1%'),
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: hp('2%'),
    marginTop: wp('1%'),
  },
  btnUploadIcon: {
    width: wp('2%'),
    resizeMode: 'contain',
    marginLeft: wp('1.5%'),
  },
});
export default PlayerInfoScreenStyle;
