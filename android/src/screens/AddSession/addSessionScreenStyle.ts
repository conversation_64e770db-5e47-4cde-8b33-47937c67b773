import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const addSessionScreenStyle = (colors: any) => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
        paddingTop: wp('2%'),
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
        paddingTop: wp('2%'),
      },
});
export default addSessionScreenStyle;
