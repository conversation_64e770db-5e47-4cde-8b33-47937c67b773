import React from 'react';
import { View } from 'react-native';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import AddSessionContainer from '../../Container/AddSessionContainer/AddSessionContainer';
import useStyles from '../../hooks/useStyles';
import customAddSessionScreenStyle from './addSessionScreenStyle';

const AddSessionScreen = () => {
  const addSessionScreenStyle = useStyles(customAddSessionScreenStyle);
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <View style={addSessionScreenStyle.container}>
          <AddSessionContainer />
        </View>
      </View>
    </ScreenWrapper>
  );
};

export default AddSessionScreen;
