import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { View, Image, TouchableOpacity } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

import AddPlayerModal from '../../components/modal/AddPlayerModal/AddPlayerModal';
import { isTabDevice } from '../../config/appConfig';
import TeamLabel from '../../components/TeamLabel/TeamLabel';
import customTeamScreenStyle from './TeamScreenStyle';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import editIcon from '../../../assets/buttons/editIconPen.png';
import TeamWrapper from '../../components/Teams/TeamsWrapper';
import { userRoleType } from '../../constants/constants';
import useStyles from '../../hooks/useStyles';
import { SET_SELECTED_CHILD } from '../../store/actionTypes/common/commonActionTypes';

export default function TeamsScreen({ navigation }) {
  const dispatch = useDispatch();
  const TeamScreenStyle = useStyles(customTeamScreenStyle);
  const LayoutStyles = useStyles(customLayoutStyles);
  const [isEditMode, setIsEditMode] = useState(false);
  const { userRole, userData } = useSelector(state => state?.auth);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { playerData, playerDataLoading } = useSelector(state => state?.player);
  const { selectedChild } = useSelector(state => state?.common);
  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;
  const isCoach = userRoleType.COACH === userData?.type;

  const setSelectedChild = payload => {
    dispatch({
      type: SET_SELECTED_CHILD,
      payload,
    });
  };

  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <View style={TeamScreenStyle.container}>
          <View style={TeamScreenStyle.topContainer}>
            <TeamLabel
              selectedChild={selectedChild}
              setSelectedChild={setSelectedChild}
              handleDropdownOpen={isDropDownVisible =>
                setIsDropdownOpen(isDropDownVisible)
              }
            />
          </View>

          <View style={TeamScreenStyle.seperator} />

          <View style={TeamScreenStyle.bottomContainer}>
            <TeamWrapper
              navigation={navigation}
              isEditMode={isEditMode}
              userRole={userRole}
              selectedChild={selectedChild}
            />
            {isTabDevice() && !isPlayer && !isParent && !isCoach && (
              <View style={TeamScreenStyle.addPlayersView}>
                {playerData && !playerDataLoading && (
                  <TouchableOpacity
                    style={TeamScreenStyle.EditTouchableOpacity}
                    onPress={() => setIsEditMode(editMode => !editMode)}
                  >
                    {isEditMode ? (
                      <FontAwesome name="close" size={24} color="red" />
                    ) : (
                      <Image
                        source={editIcon}
                        style={TeamScreenStyle.EditIcon}
                      />
                    )}
                  </TouchableOpacity>
                )}

                <AddPlayerModal />
              </View>
            )}
          </View>
          {!isTabDevice() && !isPlayer && !isParent && !isCoach && (
            <View style={TeamScreenStyle.addPlayersView}>
              {playerData && !playerDataLoading && (
                <TouchableOpacity
                  style={TeamScreenStyle.EditTouchableOpacity}
                  onPress={() => setIsEditMode(editMode => !editMode)}
                >
                  {isEditMode ? (
                    <FontAwesome name="close" size={24} color="red" />
                  ) : (
                    <Image source={editIcon} style={TeamScreenStyle.EditIcon} />
                  )}
                </TouchableOpacity>
              )}
              <AddPlayerModal />
            </View>
          )}
        </View>
      </View>
    </ScreenWrapper>
  );
}
