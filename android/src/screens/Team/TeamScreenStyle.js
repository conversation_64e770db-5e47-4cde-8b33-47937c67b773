import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TeamScreenStyle = colors => ({
  container: {
    flex: 1,
  },
  topContainer: isTabDevice() ? {} : {},
  seperator: isTabDevice()
    ? {
        borderBottomColor: colors.borderBlue,
        borderBottomWidth: 1,
        marginLeft: hp('3.5%'),
        marginRight: hp('3.5%'),
      }
    : {
        display: 'none',
      },
  bottomContainer: isTabDevice()
    ? {
        height: hp('70'),
        marginTop: wp('3%'),
      }
    : {},
  EditTouchableOpacity: isTabDevice()
    ? {
        height: wp('5.5%'),
        width: wp('5.5%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.borderBlue,
        alignItems: 'center',
        justifyContent: 'center',
        bottom: wp('4%'),
        right: wp('-5.5%'),
        zIndex: 4,
      }
    : {
        height: wp('11%'),
        width: wp('11%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.borderBlue,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: wp('2%'),
        marginBottom: hp('2%'),
      },
  EditIcon: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'cover',
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'cover',
      },
  addPlayersView: isTabDevice()
    ? {
        flexDirection: 'column',
        width: wp('10%'),
        alignSelf: 'flex-end',
        position: 'absolute',
        bottom: wp('10%'),
        right: wp('3%'),
      }
    : {
        flexDirection: 'column',
        width: wp('10%'),
        alignSelf: 'flex-end',
        position: 'absolute',
        bottom: hp('15%'),
        right: wp('7%'),
      },
});

export default TeamScreenStyle;
