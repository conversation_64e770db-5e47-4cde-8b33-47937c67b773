import React from 'react';
import { View, Text } from 'react-native';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import ManageUserContainer from '../../Container/ManageUserContainer/ManageUserContainer';
import useStyles from '../../hooks/useStyles';

const UserManagementScreen = ({ navigation }) => {
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper isLoading={false}>
      <View style={LayoutStyles.contentWrapper}>
        <ManageUserContainer navigation={navigation} />
      </View>
    </ScreenWrapper>
  );
};

export default UserManagementScreen;
