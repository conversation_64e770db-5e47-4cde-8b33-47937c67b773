import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const addUserScreenStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
        paddingTop: wp('2%'),
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
        paddingTop: wp('2%'),
      },
  leftView: isTabDevice()
    ? {
        width: wp('26%'),
        paddingLeft: wp('2%'),
      }
    : {
        width: wp('96%'),
        paddingTop: wp('2%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('90%'),
        paddingLeft: wp('3%'),
        borderLeftWidth: 1,
        borderLeftColor: colors.borderBlue,
      }
    : {
        width: wp('100%'),
        // height: hp('90%'),
        paddingLeft: wp('2%'),
        borderTopWidth: 1,
        borderTopColor: colors.borderBlue,
      },
});
export default addUserScreenStyle;
