import React, { useEffect, useState } from 'react';
import { View, TouchableWithoutFeedback, Keyboard } from 'react-native';
import AddUser from '../../Container/AddUser/addUser';
import UserType from '../../Container/UserType/userType';
import customAddUserScreenStyle from './addUserScreenStyle';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import { useRoute } from '@react-navigation/native';
import useStyles from '../../hooks/useStyles';
import useColors from '../../hooks/useColors';
import UserCreationType from '../../Container/UserCreationType/userCreationType';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { isTabDevice } from '../../config/appConfig';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';

const AddUserScreen = () => {
  const addUserScreenStyle = useStyles(customAddUserScreenStyle);
  const colors = useColors();
  const LayoutStyles = useStyles(customLayoutStyles);
  const [userCreationMode, setUserCreationMode] = useState('COACH');
  const [userCreationTypeMode, setUserCreationTypeMode] = useState('New');
  const [isEditMode, setIsEditMode] = useState(false);

  const route = useRoute();

  useEffect(() => {
    if (route?.params?.userData) {
      setIsEditMode(true);
      setUserCreationMode(route.params.userData?.type);
    }
  }, [route]);

  const renderUserCreationType = () => (
    <UserCreationType
      userCreationTypeMode={userCreationTypeMode}
      handleUserCreationType={setUserCreationTypeMode}
    />
  );

  const renderUserCreationUI = () => (
    <>
      <View style={addUserScreenStyle.leftView}>
        <UserType
          userCreationMode={userCreationMode}
          handleUserCreation={setUserCreationMode}
          isEditMode={isEditMode}
        />
        {!isEditMode && isTabDevice() && renderUserCreationType()}
      </View>
      <View style={addUserScreenStyle.rightView}>
        <AddUser
          userCreationMode={userCreationMode}
          userCreationTypeMode={userCreationTypeMode}
          isEditMode={isEditMode}
          userDetails={route?.params?.userData}
          renderUserCreationType={renderUserCreationType}
        />
      </View>
    </>
  );
  return (
    // <ScreenWrapper>
    //   <TouchableWithoutFeedback
    //     onPress={Keyboard.dismiss}
    //     accessible={false}
    //     touchSoundDisabled={true}
    //   >
    //     <View style={LayoutStyles.contentWrapper}>
    //       <View
    //         style={addUserScreenStyle.container}
    //         onStartShouldSetResponder={() => true}
    //       >
    //         {isTabDevice() ? (
    //           renderUserCreationUI()
    //         ) : (
    //           <KeyboardAwareScrollView
    //             enableOnAndroid
    //             extraScrollHeight={hp('20%')}
    //           >
    //             {renderUserCreationUI()}
    //           </KeyboardAwareScrollView>
    //         )}
    //       </View>
    //     </View>
    //   </TouchableWithoutFeedback>
    // </ScreenWrapper>

    <ScreenWrapper>
      <View
        style={LayoutStyles.contentWrapper}
        onStartShouldSetResponder={() => true}
      >
        {renderUserCreationUI()}
      </View>
    </ScreenWrapper>
  );
};

export default AddUserScreen;
