import { FontAwesome, Ionicons } from '@expo/vector-icons';
import { Auth } from 'aws-amplify';
import { Video } from 'expo-av';
import React, { useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Checkbox } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import logo from '../../../assets/Aktive_logo.png';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import { isTabDevice } from '../../config/appConfig';
import url from '../../config/url';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import {
  FETCH_SPORTS_PROFILE_FAIL,
  FETCH_SPORTS_PROFILE_REQUEST,
  FETCH_SPORTS_PROFILE_SUCCESS,
  LOGIN_INITIALIZE,
  SET_CURRENT_ENVIRONMENT,
  SET_KEYBOARD_HEIGHT_ON_LOGIN,
  SET_THEME_INFORMATION,
} from '../../store/actionTypes/auth';
import customLoginStyles from './LoginScreenStyle';

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { userRoleType } from '../../constants/constants';
import {
  FOOTBALL_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import removeSpace from '../../helpers/removeSpace';
import { useGetAmplifyConfiguration } from '../../hooks/useConfigurationData';
import {
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import { LOGOUT_SUCCESS } from '../../store/actionTypes/auth';
import { HEADER_LOGO_CLICKED } from '../../store/actionTypes/common/commonActionTypes';
import {
  USER_TYPE_FAILED,
  USER_TYPE_REQUEST,
  USER_TYPE_SUCCESS,
} from '../../store/actionTypes/userType/userType';
import ForgotPassword from './ForgotPassword';

export default function LoginScreen({
  navigation,
  username,
  password,
  handleUserName,
  handlePassword,
}) {
  const [checked, setChecked] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  const [clubId, setClubId] = useState('');
  const [passwordVisibility, setPasswordVisibility] = useState(false);

  const dispatch = useDispatch();
  const {
    user,
    userData,
    sessionToken,
    error,
    loading,
    themInfo,
    userRole,
    userDataFetchError,
    configurationData,
  } = useSelector(state => state?.auth);
  const { teamTotalRecords, teamDataLoading } = useSelector(
    state => state.team
  );
  const [isUserDataFetchFail, setIsUserDataFetchFail] = useState(false);
  const [isInvalidCartId, setIsInvalidCartId] = useState(false);
  const [apiError, setApiError] = useState(false);
  const [isForgotPasswordView, setIsForgotPasswordView] = useState(false);
  const [theme, setTheme] = useState(null);
  const [fetchData] = useApi();
  const [fetchTeamData] = useApi();
  const LoginStyles = useStyles(customLoginStyles);
  const LayoutStyles = useStyles(customLayoutStyles);
  const [configLoading, setConfigLoading] = useState(false);
  const [fetchAmplifyConfigurationData] = useGetAmplifyConfiguration();

  const isPlayer = useMemo(() => {
    return userRoleType.PLAYER === userRole;
  }, [userRole]);

  const onKeyboardDidShow = e => {
    dispatch({
      type: SET_KEYBOARD_HEIGHT_ON_LOGIN,
      payload: e.endCoordinates.height,
    });
  };

  useEffect(() => {
    const showSubscription = Keyboard.addListener(
      'keyboardDidShow',
      onKeyboardDidShow
    );

    return () => {
      showSubscription.remove();
    };
  }, []);

  const handleSignIn = () => {
    setIsSubmit(true);
    setIsUserDataFetchFail(false);
    console.log('clubId', clubId, 'username', username, 'password', password);
    if (clubId && username && password) {
      dispatch({ type: SET_CURRENT_ENVIRONMENT, payload: clubId });
      setIsInvalidCartId(false);
      setApiError(false);
      getAmplifyConfiguration(clubId);
    }
  };

  const getAmplifyConfiguration = clubId => {
    setConfigLoading(true);
    fetchAmplifyConfigurationData(clubId)
      .then(data => {
        const { theme } = data?.data || {};
        setTheme(theme);
        setIsInvalidCartId(false);
      })
      .catch(error => {
        setIsInvalidCartId(true);
        setConfigLoading(false);
        console.log(error, 'error');
      });
  };

  useEffect(() => {
    if (configurationData && loading) {
      setConfigLoading(false);
    }
  }, [configurationData, loading]);

  const gotoForgotPassword = () => {
    setIsForgotPasswordView(true);
    handleUserName('');
    handlePassword('');
  };

  useEffect(() => {
    if (sessionToken) {
      if (!userData) {
        const email = user?.email;
        fetchData(
          `/api/v1/users?email=${email}`,
          USER_TYPE_REQUEST,
          USER_TYPE_SUCCESS,
          USER_TYPE_FAILED,
          null,
          '',
          'GET',
          null,
          USER_MANAGEMENT_SERVICE
        );
      } else if (isPlayer) {
        if (userData.id && !userData?.sportsProfileId) {
          //get player profile to get sportsProfileId
          fetchData(
            `/api/v1/sport-profiles?userIds=${userData.id}`,
            FETCH_SPORTS_PROFILE_REQUEST,
            FETCH_SPORTS_PROFILE_SUCCESS,
            FETCH_SPORTS_PROFILE_FAIL,
            null,
            '',
            'GET',
            null,
            FOOTBALL_SERVICE
          );
        } else if (userData?.sportsProfileId) {
          //get player team assignment details
          fetchTeamData(
            `/api/v1/sport-profiles/${userData.sportsProfileId}/teams?page=1&size=1`,
            TEAM_REQUEST,
            TEAM_SUCCESS,
            TEAM_FAIL,
            null,
            '',
            'GET',
            null,
            FOOTBALL_SERVICE
          );
        }
      }
    }
    if (
      sessionToken &&
      userData &&
      (!isPlayer || (isPlayer && teamTotalRecords))
    ) {
      handleUserName('');
      handlePassword('');
      dispatch({ type: HEADER_LOGO_CLICKED, payload: true });
      navigation.reset({
        index: 0,
        routes: [{ name: 'landing' }],
      });
      setTimeout(() => {
        dispatch({ type: HEADER_LOGO_CLICKED, payload: false });
      }, 5000);
      setTimeout(
        () =>
          dispatch({
            type: SET_THEME_INFORMATION,
            payload: theme,
          }),
        1000
      );
    }
  }, [sessionToken, userData, teamTotalRecords]);

  useEffect(() => {
    if (error) {
      setApiError(true);
    }
  }, [error]);

  useEffect(() => {
    setApiError(false);
    setIsSubmit(false);

    if (userData) {
      logoutUser();
    }
  }, [username, password, clubId]);

  useEffect(() => {
    if (loading) {
      dispatch({
        type: LOGIN_INITIALIZE,
      });
    }
  }, []);

  useEffect(() => {
    if (userDataFetchError) {
      setIsUserDataFetchFail(true);
      logoutUser();
    }
  }, [userDataFetchError]);

  const logoutUser = async () => {
    try {
      await Auth.signOut();
      dispatch({
        type: LOGOUT_SUCCESS,
      });
    } catch (error) {
      console.log('logout error', error);
    }
  };

  const isPlayerTeamsNotAvailable = useMemo(() => {
    return (
      isSubmit &&
      !loading &&
      isPlayer &&
      !teamDataLoading &&
      teamTotalRecords === 0
    );
  }, [teamDataLoading, teamTotalRecords, isPlayer, loading, isSubmit]);

  return (
    <KeyboardAwareScrollView enableOnAndroid={true}>
      <KeyboardAvoidingView behavior="position" keyboardVerticalOffset={-280}>
        <View style={LoginStyles.container}>
          <Image
            source={logo}
            style={isTabDevice() ? LayoutStyles.logo : LoginStyles.logo}
          />
          <View style={LoginStyles.leftView}>
            <Video
              source={{ uri: url.siginVideoUrl }}
              rate={1.0}
              volume={1.0}
              isMuted={false}
              resizeMode="cover"
              shouldPlay
              isLooping={true}
              style={LoginStyles.video}
            />
          </View>
          <View style={LoginStyles.rightView}>
            <View style={LoginStyles.overlay}></View>
            {isForgotPasswordView ? (
              <ForgotPassword
                goBack={() => setIsForgotPasswordView(false)}
                getAmplifyConfiguration={getAmplifyConfiguration}
                isInvalidClubId={isInvalidCartId}
              />
            ) : (
              <View style={LoginStyles.rightViewWrapper}>
                <View style={LoginStyles.rightViewSection1}>
                  <Text style={LoginStyles.rightText1}>Welcome to</Text>
                  <Text style={LoginStyles.rightText2}>Koach</Text>
                  <Text style={LoginStyles.rightText3}>
                    Manage your team anytime, anywhere!
                  </Text>
                </View>
                <View style={LoginStyles.rightViewSection2}>
                  <View style={LoginStyles.textInputMainView}>
                    <View style={LoginStyles.textInputView}>
                      <FontAwesome name="id-card" style={LoginStyles.icon} />
                      <View style={LoginStyles.textInputViewWrapper}>
                        <TextInput
                          style={LoginStyles.textInput}
                          autoCapitalize={'characters'}
                          onChangeText={e => setClubId(removeSpace(e))}
                          value={clubId}
                          placeholder="Club ID"
                          placeholderTextColor="#FFF"
                        />
                      </View>
                    </View>
                    <View style={LoginStyles.textInputView}>
                      <Ionicons name="mail" style={LoginStyles.icon} />
                      <View style={LoginStyles.textInputViewWrapper}>
                        <TextInput
                          style={LoginStyles.textInput}
                          autoCapitalize="none"
                          onChangeText={e => handleUserName(removeSpace(e))}
                          value={username}
                          placeholder="Email Address"
                          placeholderTextColor="#FFF"
                        />
                      </View>
                    </View>
                    <View style={LoginStyles.textInputView}>
                      <Ionicons
                        name="lock-closed"
                        style={LoginStyles.icon}
                      />
                      <View style={LoginStyles.textInputViewWrapper}>
                        <TextInput
                          style={LoginStyles.textInput}
                          onChangeText={handlePassword}
                          value={password}
                          secureTextEntry={!passwordVisibility}
                          placeholder="Password"
                          placeholderTextColor="#FFF"
                        />
                      </View>
                      <TouchableOpacity
                        onPress={() => {
                          setPasswordVisibility(!passwordVisibility);
                        }}
                      >
                        {passwordVisibility ? (
                          <Ionicons
                            name="eye-off-sharp"
                            style={LoginStyles.icon}
                          />
                        ) : (
                          <Ionicons name="eye-sharp" style={LoginStyles.icon} />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View style={LoginStyles.checkboxView}>
                    <View style={LoginStyles.checkboxTextView}>
                      <Checkbox.Android
                        style={LoginStyles.checkbox}
                        uncheckedColor="white"
                        status={checked ? 'checked' : 'unchecked'}
                        onPress={() => {
                          setChecked(!checked);
                        }}
                      />
                      <Text style={LoginStyles.checkboxText}>Remember me</Text>
                    </View>
                    <TouchableOpacity
                      onPress={gotoForgotPassword}
                      style={LoginStyles.forgotPassword}
                    >
                      <Text style={LoginStyles.checkboxText1}>
                        Forgot Password?
                      </Text>
                    </TouchableOpacity>
                  </View>
                  {isSubmit && (!clubId || !username || !password) && (
                    <View>
                      <Text style={LoginStyles.errorText}>
                        Please provide club id, email address and password
                      </Text>
                    </View>
                  )}
                  {isInvalidCartId && (
                    <View>
                      <Text style={LoginStyles.errorText}>
                        Please provide valid club id
                      </Text>
                    </View>
                  )}
                  {isSubmit && apiError && (
                    <View>
                      <Text style={LoginStyles.errorText}>
                        Your password or username is incorrect. Please try again
                      </Text>
                    </View>
                  )}
                  {isSubmit && isUserDataFetchFail && (
                    <View>
                      <Text style={LoginStyles.errorText}>
                        Unauthorized user
                      </Text>
                    </View>
                  )}
                  {isPlayerTeamsNotAvailable && (
                    <View>
                      <Text style={LoginStyles.errorText}>
                        Sorry, you have not been assigned to a team yet. Contact
                        your coach before proceeding.
                      </Text>
                    </View>
                  )}

                  {(isSubmit && (loading || configLoading)) ||
                  (isSubmit && isPlayer && teamDataLoading) ? (
                    <View style={LoginStyles.btnSignin}>
                      <ActivityIndicator size="small" color="#0000ff" />
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={LoginStyles.btnSignin}
                      onPress={handleSignIn}
                    >
                      <Text style={LoginStyles.btnSigninText}>Sign in</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            )}
          </View>
        </View>
      </KeyboardAvoidingView>
    </KeyboardAwareScrollView>
  );
}
