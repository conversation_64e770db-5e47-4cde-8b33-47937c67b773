import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons, FontAwesome } from '@expo/vector-icons';
import customLoginStyles from './LoginScreenStyle';
import { useState } from 'react';
import { Amplify } from 'aws-amplify';
import { useDispatch, useSelector } from 'react-redux';
import useStyles from '../../hooks/useStyles';
import removeSpace from '../../helpers/removeSpace';
import useApi from '../../hooks/useApi';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';

import {
  USER_EXIST_REQUEST,
  USER_EXIST_SUCCESS,
  USER_EXIST_FAILED,
  VERIFY_EMAIL_SEND_REQUEST,
  VERIFY_EMAIL_SEND_SUCCESS,
  VERIFY_EMAIL_SEND_FAILED,
  <PERSON>AN<PERSON>_PASSWORD_SUCCESS,
  CHANGE_PASSWORD_FAILED,
  CHANGE_PASSWORD_REQUEST,
  VERIFY_EMAIL_SEND_RESET,
  CHANGE_PASSWORD_RESET,
} from '../../store/actionTypes/auth';
import {
  forcedModalType,
  userExistStatusType,
} from '../../constants/constants';
import ForceModal from '../../components/modal/ForceModal/ForceModal';

const ForgotPassword = ({
  goBack,
  getAmplifyConfiguration,
  isInvalidClubId,
}) => {
  const dispatch = useDispatch();
  const LoginStyles = useStyles(customLoginStyles);
  const [email, setEmail] = useState('');
  const [clubId, setClubId] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const {
    configurationData,
    userExistStatus,
    verifyEmailSent,
    verifyEmailErrorCode,
    changePasswordSuccess,
    changePasswordErrorCode,
  } = useSelector(state => state?.auth);
  const [errorMessage, setErrorMessage] = useState('');

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [userExistCheck] = useApi();
  const [sendVerifyEmail] = useApi();
  const [changePasswordApi] = useApi();

  const [showErrorModalForgot, setShowErrorModalForgot] = useState(false);
  const [passwordVisibility, setPasswordVisibility] = useState(false);
  const [confirmPasswordVisibility, setConfirmPasswordVisibility] =
    useState(false);

  useEffect(() => {
    dispatch({
      type: VERIFY_EMAIL_SEND_RESET,
    });
    dispatch({
      type: CHANGE_PASSWORD_RESET,
    });
    setErrorMessage('');
  }, []);

  useEffect(() => {
    if (configurationData && Amplify) {
      Amplify.configure(configurationData);
    } else {
      setLoading(false);
    }
  }, [configurationData, Amplify]);

  useEffect(() => {
    if (loading) {
      if (userExistStatus === userExistStatusType.userExistTrue) {
        sendVerificationEmail();
      } else if (
        userExistStatus === userExistStatusType.userExistFalse ||
        userExistStatus === userExistStatusType.userExistFail
      ) {
        setLoading(false);
        setShowErrorModalForgot(true);
      }
    }
  }, [userExistStatus]);

  useEffect(() => {
    if (configurationData && email) {
      loading && checkUserExistCheck(email);
    } else {
      setLoading(false);
    }
  }, [configurationData, email]);

  useEffect(() => {
    if (verifyEmailSent) {
      setLoading(false);
      setEmailSent(true);
    }
  }, [verifyEmailSent]);

  useEffect(() => {
    if (changePasswordSuccess) {
      setLoading(false);
      goBack();
    }
  }, [changePasswordSuccess]);

  useEffect(() => {
    if (changePasswordErrorCode) {
      generateError(changePasswordErrorCode);
    }
    if (verifyEmailErrorCode) {
      generateError(verifyEmailErrorCode);
    }
    setLoading(false);
  }, [
    JSON.stringify(changePasswordErrorCode),
    JSON.stringify(verifyEmailErrorCode),
  ]);

  const checkUserExistCheck = email => {
    if (email) {
      setLoading(true);
      userExistCheck(
        `/api/v1/users/${email}/id`,
        USER_EXIST_REQUEST,
        USER_EXIST_SUCCESS,
        USER_EXIST_FAILED,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
    }
  };

  useEffect(() => {
    if (isInvalidClubId) {
      setErrorMessage('Please enter a valid Club ID');
      setLoading(false);
    }
  }, [isInvalidClubId]);

  const sendVerificationEmail = () => {
    if (email) {
      setLoading(true);
      sendVerifyEmail(
        `/api/v1/users/${email}/password/reset`,
        VERIFY_EMAIL_SEND_REQUEST,
        VERIFY_EMAIL_SEND_SUCCESS,
        VERIFY_EMAIL_SEND_FAILED,
        null,
        '',
        'POST',
        false,
        USER_MANAGEMENT_SERVICE
      );
    } else {
      setLoading(false);
    }
  };

  const resetPassword = () => {
    if (!verificationCode || !password || !confirmPassword) {
      setErrorMessage(
        'Please enter verification code, new password and password confirmation.'
      );
      return;
    } else if (password !== confirmPassword) {
      setErrorMessage('New password and Confirm password does not match.');
      return;
    }

    if (
      verificationCode &&
      password &&
      confirmPassword &&
      password === confirmPassword
    ) {
      const verifyPasswordPayload = {
        code: verificationCode,
        password: password,
      };

      setLoading(true);
      changePasswordApi(
        `/api/v1/users/${email}/password/change`,
        CHANGE_PASSWORD_REQUEST,
        CHANGE_PASSWORD_SUCCESS,
        CHANGE_PASSWORD_FAILED,
        verifyPasswordPayload,
        '',
        'PUT',
        false,
        USER_MANAGEMENT_SERVICE
      );
    }
  };

  const handleBackButton = () => {
    if (emailSent) {
      setEmailSent(false);
      setErrorMessage('');
    } else {
      goBack();
    }
  };

  useEffect(() => {
    errorMessage && setErrorMessage('');
  }, [clubId, email, verificationCode, password, confirmPassword]);

  const generateError = passwordError => {
    switch (passwordError) {
      case 'WRONG_USER_EMAIL':
        setErrorMessage('Please enter a valid email address');
        break;
      case 'INVALID_PASSWORD_RESET_CODE':
        setErrorMessage(
          'The verification code you have entered is incorrect, please enter the correct verification code.'
        );
        break;
      case 'PASSWORD_NOT_UP_TO_STANDARD':
        setErrorMessage(`Entered password does not match the criteria. The password should include 
        ● At least eight characters 
        ● One special character
        ● A numeric value
        ● A capital letter and a simple letter`);
        break;
      default:
        setErrorMessage('Something went wrong. Please try again.');
        break;
    }
  };

  const handlePasswordReset = () => {
    if (!clubId || !email) return;
    setLoading(true);
    setErrorMessage('');
    getAmplifyConfiguration(clubId);
  };

  return (
    <View style={LoginStyles.rightViewWrapper}>
      {showErrorModalForgot && (
        <ForceModal
          onClose={setShowErrorModalForgot}
          type={forcedModalType.FORGOT_MODAL}
        />
      )}
      <View style={LoginStyles.rightViewSection1}>
        <Text style={LoginStyles.rightText1}>Welcome to</Text>
        <Text style={LoginStyles.rightText2}>Koach</Text>
        <Text style={LoginStyles.rightText4}>Forgot your password? </Text>
        {!emailSent && (
          <Text style={LoginStyles.rightText5}>
            No worries. Just enter the email you used to sign up and we'll send
            you a link to reset it.
          </Text>
        )}
      </View>
      <View style={LoginStyles.rightViewSection2}>
        {emailSent ? (
          <View style={LoginStyles.textInputMainView}>
            <View style={LoginStyles.textInputView}>
              <FontAwesome name="id-card" style={LoginStyles.icon} />
              <View style={LoginStyles.textInputViewWrapper}>
                <TextInput
                  style={LoginStyles.textInput}
                  onChangeText={setVerificationCode}
                  value={verificationCode}
                  placeholder="Verification Code"
                  placeholderTextColor="#FFF"
                />
              </View>
            </View>
            <View style={LoginStyles.textInputView}>
              <Ionicons name="lock-closed" style={LoginStyles.icon} />
              <View style={LoginStyles.textInputViewWrapper}>
                <TextInput
                  style={LoginStyles.textInput}
                  onChangeText={setPassword}
                  value={password}
                  secureTextEntry={!passwordVisibility}
                  placeholder="New Password"
                  placeholderTextColor="#FFF"
                  textContentType={'oneTimeCode'}
                />
              </View>
              <TouchableOpacity
                onPress={() => {
                  setPasswordVisibility(!passwordVisibility);
                }}
              >
                {passwordVisibility ? (
                  <Ionicons name="eye-off-sharp" style={LoginStyles.icon} />
                ) : (
                  <Ionicons name="eye-sharp" style={LoginStyles.icon} />
                )}
              </TouchableOpacity>
            </View>
            <View style={LoginStyles.textInputView}>
              <Ionicons name="lock-closed" style={LoginStyles.icon} />
              <View style={LoginStyles.textInputViewWrapper}>
                <TextInput
                  style={LoginStyles.textInput}
                  onChangeText={setConfirmPassword}
                  value={confirmPassword}
                  secureTextEntry={!confirmPasswordVisibility}
                  placeholder="Confirm Password"
                  placeholderTextColor="#FFF"
                  textContentType={'oneTimeCode'}
                />
              </View>
              <TouchableOpacity
                onPress={() => {
                  setConfirmPasswordVisibility(!confirmPasswordVisibility);
                }}
              >
                {confirmPasswordVisibility ? (
                  <Ionicons name="eye-off-sharp" style={LoginStyles.icon} />
                ) : (
                  <Ionicons name="eye-sharp" style={LoginStyles.icon} />
                )}
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={LoginStyles.textInputMainView}>
            <View style={LoginStyles.textInputView}>
              <FontAwesome name="id-card" style={LoginStyles.icon} />
              <View style={LoginStyles.textInputViewWrapper}>
                <TextInput
                  autoCapitalize={'characters'}
                  style={LoginStyles.textInput}
                  onChangeText={e => setClubId(removeSpace(e))}
                  value={clubId}
                  placeholder="Club ID"
                  placeholderTextColor="#FFF"
                />
              </View>
            </View>
            <View style={LoginStyles.textInputView}>
              <Ionicons name="mail" style={LoginStyles.icon} />
              <View style={LoginStyles.textInputViewWrapper}>
                <TextInput
                  autoCapitalize="none"
                  style={LoginStyles.textInput}
                  onChangeText={e => setEmail(removeSpace(e))}
                  value={email}
                  placeholder="Email Address"
                  placeholderTextColor="#FFF"
                />
              </View>
            </View>
          </View>
        )}
        <View>
          <Text style={LoginStyles.errorText}>{errorMessage}</Text>
          {loading ? (
            <View style={[LoginStyles.btnSignin, LoginStyles.btnReset]}>
              <ActivityIndicator size="small" color="#0000ff" />
            </View>
          ) : (
            <TouchableOpacity
              style={[LoginStyles.btnSignin, LoginStyles.btnReset]}
              onPress={emailSent ? resetPassword : handlePasswordReset}
            >
              <Text style={LoginStyles.btnSigninText}>
                {emailSent ? 'Submit' : 'Reset Password'}
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={LoginStyles.btnBack}
            onPress={handleBackButton}
            disabled={loading}
          >
            <Text style={LoginStyles.btnBackText}>Go back</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default ForgotPassword;
