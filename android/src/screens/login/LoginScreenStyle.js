//
import { isTabDevice } from '../../config/appConfig';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { Platform } from 'react-native';

const LoginStyles = colors => ({
  logo: {
    position: 'absolute',
    top: 0,
    left: wp('30%'),
    width: wp('40%'),
    height: hp('15%'),
    zIndex: 10,
    resizeMode: 'contain',
  },
  container: isTabDevice()
    ? {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        position: 'relative',
      }
    : {
        flex: 1,
        flexDirection: 'column',
        width: wp('100%'),
        height: '100%',
      },
  video: isTabDevice()
    ? {
        flex: 1,
      }
    : {
        flex: 1,
        width: wp('100%'),
        height: hp('100%'),
      },
  leftView: isTabDevice()
    ? {
        width: wp('60%'),
        position: 'relative',
      }
    : {
        width: wp('100%'),
        height: hp('100%'),
        position: 'relative',
      },
  rightView: isTabDevice()
    ? {
        width: wp('40%'),
        // backgroundColor: colors.blue,
        paddingLeft: hp('9%'),
        paddingRight: hp('9%'),
        justifyContent: 'center',
      }
    : {
        width: wp('100%'),
        height: hp('100%'),
        paddingLeft: hp('3%'),
        paddingRight: hp('3%'),
        justifyContent: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      },
  overlay: isTabDevice()
    ? {
        backgroundColor: colors.blue,
        width: wp('100%'),
        height: hp('100%'),
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }
    : {
        backgroundColor: colors.blue,
        width: wp('100%'),
        height: hp('100%'),
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.9,
      },
  rightViewWrapper: isTabDevice()
    ? {
        height: hp('100%'),
        paddingTop: hp('17%'),
        paddingBottom: hp('5%'),
        flexDirection: 'column',
      }
    : {
        height: hp('100%'),
        paddingTop: hp('17%'),
        paddingBottom: hp('5%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
      },
  rightText1: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      },
  rightText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        lineHeight: wp('5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('20%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
      },
  rightText3: isTabDevice()
    ? {
        color: colors.grey,
        paddingTop: 5,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.grey,
        paddingTop: hp('0%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        textAlign: 'center',
      },

  rightText4: isTabDevice()
    ? {
        color: colors.white,
        paddingTop: 5,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        paddingTop: hp('0%'),
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'left',
      },
  rightText5: isTabDevice()
    ? {
        color: colors.grey,
        paddingTop: 5,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.grey,
        paddingTop: hp('0%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        textAlign: 'left',
      },
  icon: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        width: wp('3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5,5%'),
        width: wp('6%'),
        marginRight: wp('3%'),
      },
  textInputMainView: {
    marginTop: hp('2%'),
  },
  textInputView: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('1%'),
        marginTop: hp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 20,
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('4%'),
        marginTop: hp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: wp('7%'),
        height: wp('15%'),
      },
  textInputViewWrapper: isTabDevice()
    ? {
        paddingLeft: 2,
        width: '80%',
      }
    : {
        paddingLeft: 2,
        width: '80%',
        padding: Platform.OS === 'android' ? wp('2%') : 0,
      },
  textInput: isTabDevice()
    ? {
        height: wp('5%'),
        borderColor: colors.tileBackground,
        borderWidth: 1,
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        height: Platform.OS === 'android' ? wp('9%') : wp('6%'),
        borderColor: colors.tileBackground,
        borderWidth: 1,
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Regular',
        paddingTop: Platform.OS === 'android' ? wp('1%') : 0,
      },
  checkbox: {
    opacity: 1,
  },
  checkboxView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: hp('2%'),
  },
  checkboxTextView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  forgotPassword: {
    padding: wp('1%'),
  },
  checkboxText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Regular',
      },
  checkboxText1: isTabDevice()
    ? {
        color: colors.grey,
        fontSize: wp('1.2%'),
      }
    : {
        color: colors.grey,
        fontSize: wp('4%'),
      },
  btnSignin: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('4%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: wp('1%'),
        marginTop: hp('2%'),
      }
    : {
        backgroundColor: colors.green,
        height: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: wp('4%'),
        marginTop: hp('2%'),
      },
  btnSigninText: isTabDevice()
    ? {
        color: colors.white,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Regular',
      },
  errorText: {
    color: colors.red,
  },
  btnBackText: isTabDevice()
    ? {
        fontFamily: 'Poppins-Regular',
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
        color: colors.semiLightGrey,
      }
    : {
        color: colors.semiLightGrey,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Regular',
      },
  btnBack: isTabDevice()
    ? {
        height: wp('4%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: wp('1%'),
      }
    : {
        height: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: wp('4%'),
      },
  btnReset: isTabDevice()
    ? {
        marginTop: hp('10%'),
      }
    : {
        marginTop: hp('10%'),
      },
});

export default LoginStyles;
