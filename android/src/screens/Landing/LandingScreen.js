import * as WebBrowser from 'expo-web-browser';
import { useFocusEffect } from '@react-navigation/native';
import { getCalendars } from 'expo-localization';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { ScrollView, Text, View, Platform } from 'react-native';
import Swiper from 'react-native-swiper/src';
import { useDispatch, useSelector } from 'react-redux';
import SeasonUpdateContainer from '../../Container/SeasonUpdateContainer/SeasonUpdateContainer';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import Tile from '../../components/Tile/Tile';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import { isTabDevice } from '../../config/appConfig';
import {
  CALENDER,
  ROUTE_PATH,
  TilesData,
  messageTypes,
  notificationTypes,
  userRoleType,
} from '../../constants/constants';
import {
  FOOTBALL_SERVICE,
  MESSAGING_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import useApi from '../../hooks/useApi';
import useApiPromise from '../../hooks/useApiPromise';
import useCalender from '../../hooks/useCalender';
import useStyles from '../../hooks/useStyles';
import {
  GET_SELECTED_NOTFICATION_CHAT_MEMBER_INFO_FAIL,
  GET_SELECTED_NOTFICATION_CHAT_MEMBER_INFO_REQUEST,
  GET_SELECTED_NOTFICATION_FAIL,
  GET_SELECTED_NOTFICATION_MEMBER_INFO_SUCCESS,
  GET_SELECTED_NOTFICATION_REQUEST,
  GET_SELECTED_NOTFICATION_SUCCESS,
  RESET_MESSAGE_TYPES,
  SELECTED_USER_FOR_MESSAGE,
  SET_PREV_MESSAGE_NOTIFICATION,
  SET_SELECTED_MESSAGE_NOTIFICATION,
} from '../../store/actionTypes/Message/MessageAction';
import { SET_NOTIFCATION_EVENT_ID } from '../../store/actionTypes/Planner/PlannerAction';
import {
  GET_CHILD_TEAM_DATA_FAILED,
  GET_CHILD_TEAM_DATA_REQUEST,
  GET_CHILD_TEAM_DATA_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import {
  FETCH_SPORTS_PROFILE_FAIL,
  FETCH_SPORTS_PROFILE_REQUEST,
  FETCH_SPORTS_PROFILE_SUCCESS,
  SEND_PUSH_NOTIFICATION_TOKEN_FAIL,
  SEND_PUSH_NOTIFICATION_TOKEN_REQUEST,
  SEND_PUSH_NOTIFICATION_TOKEN_SUCCESS,
  SET_CURRENT_PUSH_NOTIFICATION_TOKEN,
} from '../../store/actionTypes/auth';
import {
  GET_CHILD_DATA_FAIL,
  GET_CHILD_DATA_REQUEST,
  GET_CHILD_DATA_SUCCESS,
  GET_CHILD_INFORMATION_FAILED,
  GET_CHILD_INFORMATION_REQUEST,
  GET_CHILD_INFORMATION_SUCCESS,
  GET_CLUB_SETTINGS_FAIL,
  GET_CLUB_SETTINGS_REQUEST,
  GET_CLUB_SETTINGS_SUCCESS,
  SET_SELECTED_CHILD,
} from '../../store/actionTypes/common/commonActionTypes';
import customLandingStyles from './LandingScreenStyle';
import { useTermAndConditionHook } from '../../hooks/TermAndConditionAPIHook/useTermAndConditionHook';
import { SET_TERM_AND_CONDITION } from '../../store/actionTypes/TermAndCondition/termAndCondtionAction';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {USER_TYPE_SUCCESS } from '../../store/actionTypes/User/User';
import { USER_TYPE_FAILED } from '../../store/actionTypes/userType/userType';
export default function LandingScreen({ navigation }) {
  const LandingStyles = useStyles(customLandingStyles);
  const LayoutStyles = useStyles(customLayoutStyles);
  const [tileData, setTileData] = useState(TilesData);
  const {
    sessionToken,
    userData,
    expoPushNotificationToken,
    userRole,
    calenderSyncTimeDuration,
    selectedCalenderID,
    isCalenderSyncEnabled,
  } = useSelector(state => state?.auth);

  const {
    selectedChild,
    children,
    childInformation,
    currentRoute,
    headerClicked,
    clubSettingsLoading,
    clubSettings,
  } = useSelector(state => state?.common);
  const { notificationPrevMessageChatInfo } = useSelector(
    state => state?.message
  );
  const isSuperAdmin = useMemo(() => {
    return userRoleType.HEAD_COACH === userData?.type;
  }, [userData]);
  const isPlayer = useMemo(() => {
    return userRoleType.PLAYER === userData?.type;
  }, [userData]);
  const isCoach = useMemo(() => {
    return userRoleType.COACH === userData?.type;
  }, [userData]);
  const { teamIdData, teamData } = useSelector(state => state?.team);

  const lastNotificationResponse = Notifications.useLastNotificationResponse();

  const [fetchData] = useApi();
  const [fetchClubSettingsData] = useApi();
  const [sendNotificationToken] = useApi();
  const dispatch = useDispatch();
  const [fetchChildren] = useApi();
  const [fetchChildTeam] = useApi();
  const [fetchChildInformation] = useApi();
  const [fetchSelectedNotificationDetails] = useApiPromise();
  const [getPastMsgChatMemberInfo] = useApiPromise();
  const [{ automaticSync }] = useCalender();
  const notificationListener = useRef();

  useEffect(() => {
    notificationListener.current =
      Notifications.addNotificationReceivedListener(notification => {
        if (
          notification?.request?.content?.title === CALENDER.matchTypeAdd ||
          notification?.request?.content?.title === CALENDER.trainingTypeAdd ||
          notification?.request?.content?.title === CALENDER.matchTypeEdit ||
          notification?.request?.content?.title === CALENDER.trainingTypeEdit ||
          notification?.request?.content?.title === CALENDER.eventCancelled
        ) {
          automaticSync();
        }
      });

    return () => {
      Notifications.removeNotificationSubscription(
        notificationListener.current
      );
    };
  }, [userData, selectedCalenderID, isCalenderSyncEnabled]);

  const isParent = userRole === userRoleType.PARENT;

  const { termAndConditionData, setTermAndCondition } = useSelector(
    state => state.TermAndCondition
  );
  const { getTermAndConditionDetails } = useTermAndConditionHook();

  const getTermData = async () => {
    getTermAndConditionDetails(userData.id);
    const value = await AsyncStorage.getItem('TermAndConditionData');
    const newData = JSON.parse(value);

    if (newData?.userType === 'ExistingUser') {
    } else {
      if (newData.termActionType !== 'ACCEPTED') {
        setTimeout(() => {
          dispatch({ type: SET_TERM_AND_CONDITION, payload: true });
        }, 0);
      }
    }
  };

  useEffect(() => {
    userData && getTermData();
  }, [termAndConditionData]);
  useEffect(() => {
    const handleSelectedMessageDetails = async () => {
      try {
        const lastNotificationResponseType =
          lastNotificationResponse?.notification?.request?.content?.data
            ?.notificationInfo?.notificationType;

        if (lastNotificationResponseType == notificationTypes.MESSAGING) {
          const currentNotificationResponse =
            lastNotificationResponse?.notification?.request?.content?.data
              ?.notificationInfo;

          if (
            JSON.stringify(notificationPrevMessageChatInfo) ==
            JSON.stringify(currentNotificationResponse)
          ) {
            return;
          }

          dispatch({
            type: SET_PREV_MESSAGE_NOTIFICATION,
            payload: {
              customInput: currentNotificationResponse,
            },
          });

          /**
           * Reseting the redux before Navigatoin
           */
          dispatch({ type: RESET_MESSAGE_TYPES });
          if (currentRoute === ROUTE_PATH.MESSAGING_CHAT) {
            dispatch({
              type: SELECTED_USER_FOR_MESSAGE,
              payload: { customInput: null },
            });
          }
          const chatId =
            lastNotificationResponse?.notification?.request?.content?.data
              ?.notificationInfo?.chatId;

          const { data } = await fetchSelectedNotificationDetails(
            `/api/v1/chats?page=1&size=1&chatIds=${chatId}`,
            GET_SELECTED_NOTFICATION_REQUEST,
            GET_SELECTED_NOTFICATION_SUCCESS,
            GET_SELECTED_NOTFICATION_FAIL,
            null,
            '',
            'GET',
            null,
            MESSAGING_SERVICE
          );

          const chatType = data?.data[0]?.type;
          const selectedChatDetails = data?.data?.[0];

          if (chatType === messageTypes.PERSONAL) {
            onNavigatePersonalMessage(selectedChatDetails);
          }

          if (chatType === messageTypes.TEAMS) {
            onNavigatehandleTeamsMessage(selectedChatDetails);
          }
        }
      } catch (error) {
        console.log(error);
      }
    };
    if (!headerClicked) {
      const notificationResponseType =
        lastNotificationResponse?.notification?.request?.content?.data
          ?.notificationInfo?.notificationType;

      switch (notificationResponseType) {
        case notificationTypes.MESSAGING:
          handleSelectedMessageDetails();
          break;
        case notificationTypes.EVENT:
          onNavigateEventScreen(lastNotificationResponse);
          break;
        default:
          return;
      }
    }
  }, [JSON.stringify(lastNotificationResponse)]);

  const onNavigateEventScreen = lastNotificationResponseForEvent => {
    const eventId =
      lastNotificationResponseForEvent?.notification?.request?.content?.data
        ?.notificationInfo?._id;
    if (eventId) {
      dispatch({
        type: SET_NOTIFCATION_EVENT_ID,
        payload: eventId,
      });
      navigation.navigate('PlannerScreen');
    }
  };

  const onNavigatePersonalMessage = async selectedChatDetails => {
    try {
      const selectedUserid = selectedChatDetails?.memberUserIds?.filter(
        userId => userId != userData?.id
      );

      const { data: userDetails } = await getPastMsgChatMemberInfo(
        `/api/v1/users?page=1&size=${selectedUserid?.length}&userIds=${selectedUserid}`,
        GET_SELECTED_NOTFICATION_CHAT_MEMBER_INFO_REQUEST,
        GET_SELECTED_NOTFICATION_MEMBER_INFO_SUCCESS,
        GET_SELECTED_NOTFICATION_CHAT_MEMBER_INFO_FAIL,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
      /**
       * Alway be for one
       */
      const selectedUserDetails = userDetails?.data[0];

      const selectedUserChatInfo = {
        ...selectedUserDetails,
        creatorUserId: selectedUserDetails.id,
        firstName: selectedUserDetails.firstName,
        id: selectedUserDetails.id,
        img: selectedUserDetails?.profileImageUrl,
        lastMessage: '',
        lastName: selectedUserDetails.lastName,
        memberUserIds: selectedChatDetails.memberUserIds,
        opponentUserIds: selectedUserid,
        type: 'USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE',
        unReadMessage: 0,
        unreadMessageCount: undefined,
        _id: selectedChatDetails?._id,
      };

      dispatch({
        type: SET_SELECTED_MESSAGE_NOTIFICATION,
        payload: { customInput: selectedUserChatInfo },
      });

      currentRoute !== ROUTE_PATH.MESSAGING &&
        currentRoute !== ROUTE_PATH.MESSAGING_CHAT &&
        navigation.navigate('Messaging');
    } catch (error) {
      console.log(error);
    }
  };

  const onNavigatehandleTeamsMessage = selectedChatDetails => {
    dispatch({
      type: SET_SELECTED_MESSAGE_NOTIFICATION,
      payload: {
        customInput: {
          id: selectedChatDetails?._id,
          value: selectedChatDetails,
        },
      },
    });

    currentRoute !== ROUTE_PATH.MESSAGING &&
      currentRoute !== ROUTE_PATH.MESSAGING_CHAT &&
      navigation.navigate('Messaging');
  };

  const registerForPushNotificationsAsync = async () => {
    if (Device.isDevice) {
      let token;
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        return;
      }
      try {
        const projectId =
          Constants?.expoConfig?.extra?.eas?.projectId ??
          Constants?.easConfig?.projectId;

        token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
      } catch (error) {
        console.log(error);
      }
      dispatch({
        type: SET_CURRENT_PUSH_NOTIFICATION_TOKEN,
        payload: token,
      });
      const tokenObj = {
        token: token || '',
        preferredTimeZone: getCalendars()[0]?.timeZone,
        platform: Platform.OS,
      };
      sendNotificationToken(
        `/api/v1/users/${userData.id}/expoTokens`,
        SEND_PUSH_NOTIFICATION_TOKEN_REQUEST,
        SEND_PUSH_NOTIFICATION_TOKEN_SUCCESS,
        SEND_PUSH_NOTIFICATION_TOKEN_FAIL,
        { ...tokenObj },
        '',
        'PUT',
        null,
        USER_MANAGEMENT_SERVICE
      );
    }
  };

  useEffect(() => {
    // Get settings
    fetchClubSettingsData(
      `/api/v1/app-setting`,
      GET_CLUB_SETTINGS_REQUEST,
      GET_CLUB_SETTINGS_SUCCESS,
      GET_CLUB_SETTINGS_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );

    setTileData(TilesData);
  }, []);

  useEffect(() => {
    if(userData?.emailId){
     fetchData(
       `/api/v1/users?email=${userData.emailId}`,
       "_",
       USER_TYPE_SUCCESS,
       USER_TYPE_FAILED,
       null,
       '',
       'GET',
       null,
       USER_MANAGEMENT_SERVICE
     );
    }
   }, [userData?.emailId])

  useEffect(() => {
    if (sessionToken && userData && !userData.userId && isPlayer) {
      fetchData(
        `/api/v1/sport-profiles?userIds=${userData.id}`,
        FETCH_SPORTS_PROFILE_REQUEST,
        FETCH_SPORTS_PROFILE_SUCCESS,
        FETCH_SPORTS_PROFILE_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [sessionToken, userData]);

  useEffect(() => {
    userData?.id &&
      !expoPushNotificationToken &&
      registerForPushNotificationsAsync();
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (
        sessionToken &&
        userData?.sportsProfileId &&
        teamData?.data?.length &&
        !userData?.givenPosition
      ) {
        fetchData(
          `/api/v1/teams/${teamData.data[0]?._id}/players/${userData.sportsProfileId}`,
          FETCH_SPORTS_PROFILE_REQUEST,
          FETCH_SPORTS_PROFILE_SUCCESS,
          FETCH_SPORTS_PROFILE_FAIL,
          null,
          '',
          'GET',
          null,
          FOOTBALL_SERVICE
        );
      }
    }, [userData])
  );

  const setSelectedChild = payload => {
    dispatch({
      type: SET_SELECTED_CHILD,
      payload,
    });
  };

  const getChildData = userId => {
    fetchChildren(
      `/api/v1/users?userIds=${userId}&page=1&size=${userId.length}`,
      GET_CHILD_DATA_REQUEST,
      GET_CHILD_DATA_SUCCESS,
      GET_CHILD_DATA_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  const getChildTeamData = userId => {
    fetchChildTeam(
      `/api/v1/teams?page=1&size=15&playerIds=${userId}`,
      GET_CHILD_TEAM_DATA_REQUEST,
      GET_CHILD_TEAM_DATA_SUCCESS,
      GET_CHILD_TEAM_DATA_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const getChildInformation = playerId => {
    fetchChildInformation(
      `/api/v1/sport-profiles?userIds=${playerId}`,
      GET_CHILD_INFORMATION_REQUEST,
      GET_CHILD_INFORMATION_SUCCESS,
      GET_CHILD_INFORMATION_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    selectedChild?.id && getChildInformation(selectedChild.id);
  }, [selectedChild]);

  useEffect(() => {
    if (isParent && selectedChild) {
      getChildTeamData(selectedChild?.id);
    }
  }, [selectedChild]);

  //Auto select child
  useEffect(() => {
    if (children?.length && !selectedChild) {
      setSelectedChild(children[0]);
    }
  }, [children, selectedChild]);

  useEffect(() => {
    if (isParent && userData?.childrenIds?.length) {
      getChildData(userData.childrenIds);
    }
  }, [userData]);

  const filterdTileData = useMemo(() => {
    let filterTileData;
    if (isParent || isPlayer) {
      filterTileData = tileData.filter(({ key }) => key !== 'userManagement');
    } else if (isSuperAdmin) {
      filterTileData = tileData.filter(({ key }) => key !== 'payment');
    } else if (isCoach) {
      filterTileData = tileData.filter(
        ({ key }) => key !== 'payment' && key !== 'userManagement'
      );
    } else {
      filterTileData = [];
    }
    return filterTileData;
  }, [tileData]);

  const getProperUrlForUserIAPNavigation = useCallback(() => {
    const { iapWebUrl } = clubSettings || {};
    return `${iapWebUrl}`;
  }, [clubSettings]);

  const handlePaymentPressButtonAsync = async () => {
    let result = await WebBrowser.openBrowserAsync(
      getProperUrlForUserIAPNavigation()
    );
  };

  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <View style={LandingStyles.wrapper}>
          <View style={LandingStyles.content}>
            <Swiper
              style={LandingStyles.wrapper}
              showsButtons={false}
              showsPagination={false}
              loop={false}
            >
              <View style={LandingStyles.slide}>
                <ScrollView
                  style={LandingStyles.scroll}
                  contentContainerStyle={LandingStyles.contentContainer}
                  scrollEnabled={!isTabDevice()}
                >
                  <View style={LandingStyles.slideView1}>
                    <View style={LandingStyles.leftView}>
                      <Text style={LandingStyles.leftViewText1}>
                        Welcome to
                      </Text>
                      <Text style={LandingStyles.leftViewText2}>Koach</Text>
                      <Text style={LandingStyles.leftViewText3}>
                        Manage your team anytime, anywhere!
                      </Text>
                    </View>
                    <View style={LandingStyles.rightView}>
                      <View style={LandingStyles.tileView}>
                        {filterdTileData.map((data, key) => {
                          return (
                            <Tile
                              key={key}
                              data={data}
                              navigation={navigation}
                              active={data.active}
                              loginUserRole={userData?.type}
                              onPressPayment={handlePaymentPressButtonAsync}
                              teamID={
                                userData?.type === userRoleType.PARENT ||
                                userData?.type === userRoleType.PLAYER
                                  ? teamData?.data && teamData?.data[0]?._id
                                  : ''
                              }
                              profileID={
                                userData?.type === userRoleType.PARENT
                                  ? childInformation?.sportsProfileId
                                  : userData?.type === userRoleType.PLAYER
                                  ? userData?.sportsProfileId
                                  : ''
                              }
                              isNotAvailable={userData?.isAvailable}
                            />
                          );
                        })}
                      </View>
                    </View>
                  </View>
                </ScrollView>
                {/* ) : (
                  <ActivitySpinner />
                )} */}
              </View>
              <View style={LandingStyles.slide}>
                <SeasonUpdateContainer />
              </View>
            </Swiper>
          </View>
        </View>
      </View>
    </ScreenWrapper>
  );
}
