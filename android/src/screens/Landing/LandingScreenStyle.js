//
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const LandingStyles = colors => ({
  scroll: isTabDevice()
    ? {}
    : {
        width: wp('100%'),
        // height: hp('40%'),
      },
  wrapper: isTabDevice()
    ? {
        position: 'relative',
      }
    : {
        position: 'relative',
        height: hp('100%'),
      },
  content: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: wp('100%'),
    height: hp('100%'),
    zIndex: 2,
  },
  slide: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        width: wp('100%'),
        // height: '100%',
        // marginBottom: hp('40%'),
        height: hp('80%'),
      },
  text: {
    color: colors.textColor1,
    fontSize: 30,
    fontWeight: 'bold',
  },
  slideView1: isTabDevice()
    ? {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        width: wp('100%'),
        height: '100%',
      }
    : {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'center',
        height: '100%',
        // paddingBottom: wp('75%'),
      },
  leftView: isTabDevice()
    ? {
        flex: 1.5,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: hp('3%'),
        height: hp('90%'),
      }
    : {
        display: 'none',
      },
  leftViewText1: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'left',
        width: '80%',
      }
    : {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'left',
        width: '80%',
      },
  leftViewText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'left',
        width: '80%',
        lineHeight: wp('5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'left',
        width: '80%',
      },
  leftViewText3: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        textAlign: 'left',
        width: '80%',
      }
    : {
        color: colors.white,
        paddingTop: 5,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        textAlign: 'left',
        width: '80%',
      },
  rightView: isTabDevice()
    ? {
        flex: 1.5,
        padding: wp('5%'),
        justifyContent: 'center',
        flexDirection: 'column',
        height: hp('90%'),
      }
    : {
        flex: 1,
        padding: wp('2%'),
        flexDirection: 'column',
        // height: hp('90%'),
        height: '100%',
      },
  tileView: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // height: '100%',
    // justifyContent: 'center',
    alignItems: 'center',
  },
  tileViewWrapper: {
    width: wp('20%'),
    height: wp('30%'),
    backgroundColor: colors.green,
  },
});

export default LandingStyles;
