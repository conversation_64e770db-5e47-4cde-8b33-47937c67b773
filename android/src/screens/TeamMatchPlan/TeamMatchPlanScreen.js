import React from 'react';
import { View, Text } from 'react-native';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import MatchPlanContainer from '../../Container/MatchPlanContainer/MatchPlanContainer';
import useStyles from '../../hooks/useStyles';

const TeamMatchPlanScreen = () => {
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <MatchPlanContainer />
      </View>
    </ScreenWrapper>
  );
};

export default TeamMatchPlanScreen;
