import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const MessageMainStyles = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
      },
  leftView: isTabDevice()
    ? {
        height: hp('80%'),
        width: wp('26%'),
        marginTop: wp('5%'),
        paddingLeft: wp('2%'),
      }
    : {
        paddingLeft: wp('2.5%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('90%'),
        paddingLeft: wp('3%'),
        borderLeftWidth: 1,
        borderLeftColor: colors.borderBlue,
      }
    : {
        width: wp('100%'),
        height: hp('77%'),
        paddingLeft: wp('3%'),
      },
});

export default MessageMainStyles;
