import React from 'react';
import { View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import MessageLeft from '../../Container/Message/MessageLeftContainer/MessageLeft';
import MessageRight from '../../Container/Message/MessageRightContainer/MessageRight';
import MessageCommon from '../../Container/MessageCommon/MessageCommon';
import customTeamMatchesContainerStyle from '../../Container/TeamMatchesContainer/TeamMatchesContainerStyle';
import useStyles from '../../hooks/useStyles';

const MessageScreenTabView = (navigation: any) => {
  const LayoutStyles = useStyles(customLayoutStyles);
  const TeamMatchesContainerStyle = useStyles(customTeamMatchesContainerStyle);

  return (
    <>
      <MessageCommon />
      <ScreenWrapper>
        <View style={LayoutStyles.contentWrapper}>
          <View style={TeamMatchesContainerStyle.container}>
            <View style={TeamMatchesContainerStyle.leftView}>
              <MessageLeft />
            </View>
            <View style={TeamMatchesContainerStyle.rightView}>
              <MessageRight />
            </View>
          </View>
        </View>
      </ScreenWrapper>
    </>
  );
};

export default MessageScreenTabView;
