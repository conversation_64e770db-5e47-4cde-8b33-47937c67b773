import React, { useEffect } from 'react';
import { View, KeyboardAvoidingView } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useDispatch } from 'react-redux';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import MessageCommon from '../../Container/MessageCommon/MessageCommon';
import MessageRight from '../../Container/Message/MessageRightContainer/MessageRight';
import customMessagesMainStyles from './MessagesMainStyles';
import useStyles from '../../hooks/useStyles';
import { RESET_MESSAGE_TYPES } from '../../store/actionTypes/Message/MessageAction';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { ScrollView } from 'react-native-gesture-handler';

const MessageScreenMobileMain = (navigation: any) => {
  const LayoutStyles = useStyles(customLayoutStyles);
  const MessagesMainStyles = useStyles(customMessagesMainStyles);
  const dispatch = useDispatch();

  return (
    <>
      <ScreenWrapper>
        <View style={LayoutStyles.contentWrapper}>
          <View style={MessagesMainStyles.rightView}>
            <MessageRight />
          </View>
        </View>
      </ScreenWrapper>
    </>
  );
};

export default MessageScreenMobileMain;
