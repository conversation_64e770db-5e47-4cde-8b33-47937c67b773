import React from 'react';
import { View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import MessageLeft from '../../Container/Message/MessageLeftContainer/MessageLeft';
import MessageCommon from '../../Container/MessageCommon/MessageCommon';
import customMessagesMainStyles from './MessagesMainStyles';
import useStyles from '../../hooks/useStyles';

const MessageScreenMobileMain = (navigation: any) => {
  const LayoutStyles = useStyles(customLayoutStyles);
  const MessagesMainStyles = useStyles(customMessagesMainStyles);

  return (
    <>
      <MessageCommon />
      <ScreenWrapper>
        <View style={LayoutStyles.contentWrapper}>
          <KeyboardAwareScrollView enableOnAndroid>
            <View style={MessagesMainStyles.container}>
              <View style={MessagesMainStyles.leftView}>
                <MessageLeft />
              </View>
            </View>
          </KeyboardAwareScrollView>
        </View>
      </ScreenWrapper>
    </>
  );
};

export default MessageScreenMobileMain;
