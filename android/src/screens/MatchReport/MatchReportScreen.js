import React from 'react';
import { View } from 'react-native';

import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import MatchReport from '../../Container/MatchReportContainer/MatchReport';
import useStyles from '../../hooks/useStyles';

const MatchReportScreen = () => {
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <MatchReport />
      </View>
    </ScreenWrapper>
  );
};

export default MatchReportScreen;
