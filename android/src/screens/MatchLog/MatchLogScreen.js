import React from 'react';
import { View } from 'react-native';
import customLayoutStyles from '../../components/Layout/layoutStyles';
import ScreenWrapper from '../../components/screenWrapper/screenWrapper';
import MatchLogWrapper from '../../Container/MatchLogContainer/MatchLogWrapper';
import useStyles from '../../hooks/useStyles';

const MatchLogScreen = () => {
  const LayoutStyles = useStyles(customLayoutStyles);
  return (
    <ScreenWrapper>
      <View style={LayoutStyles.contentWrapper}>
        <MatchLogWrapper />
      </View>
    </ScreenWrapper>
  );
};

export default MatchLogScreen;
