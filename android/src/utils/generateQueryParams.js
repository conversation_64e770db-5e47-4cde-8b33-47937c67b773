const generateQueryParams = (
  pageNo,
  pageSize,
  searchText,
  selectedUserType
) => {
  const queryParams = [
    pageNo && `page=${pageNo}`,
    pageSize && `size=${pageSize}`,
    searchText && `search=${searchText}`,
    selectedUserType && `type=${selectedUserType}`,
  ]
    .filter(Boolean)
    .join('&');

  return `${queryParams ? `?${queryParams}` : ''}`;
};

export default generateQueryParams;
