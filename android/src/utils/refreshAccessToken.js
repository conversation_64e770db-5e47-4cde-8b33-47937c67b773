import { Auth } from 'aws-amplify';
import { LOGOUT_SUCCESS, UPDATE_ACCESS_TOKEN } from '../store/actionTypes/auth';
import { store } from '../store/store';

const refreshAccessToken = async () => {
  try {
    const session = await Auth.currentSession();
    const accessToken = session.getAccessToken().getJwtToken();
    store.dispatch({
      type: UPDATE_ACCESS_TOKEN,
      payload: accessToken,
    });
    return accessToken;
  } catch (error) {
    store.dispatch({
      type: LOGOUT_SUCCESS,
    });
    return error;
  }
};

export default refreshAccessToken;
