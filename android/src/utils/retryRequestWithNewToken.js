import refreshAccessToken from './refreshAccessToken';
import axios from 'axios';

let isAlreadyFetchingAccessToken = false;
let unauthorizedRequestsQueue = [];

const retryRequestWithNewToken = async error => {
  try {
    const { response: errorResponse } = error;
    const retryOriginalRequest = new Promise(resolve => {
      addRequest(access_token => {
        errorResponse.config.headers.Authorization = 'Bearer ' + access_token;
        resolve(axios(errorResponse.config));
      });
    });
    if (!isAlreadyFetchingAccessToken) {
      isAlreadyFetchingAccessToken = true;
      const newToken = await refreshAccessToken();
      isAlreadyFetchingAccessToken = false;
      onAccessTokenFetched(newToken);
    }
    return retryOriginalRequest;
  } catch (err) {
    return Promise.reject(err);
  }
};

const onAccessTokenFetched = access_token => {
  unauthorizedRequestsQueue.forEach(callback => callback(access_token));
  unauthorizedRequestsQueue = [];
};

const addRequest = callback => {
  unauthorizedRequestsQueue.push(callback);
};

export default retryRequestWithNewToken;
