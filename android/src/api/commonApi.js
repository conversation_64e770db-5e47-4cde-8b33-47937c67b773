import { API, Auth } from 'aws-amplify';
const apiName = 'execute-api';

export const get = async (url, serviceType) => {
  let session;
  let accessToken;
  try {
    session = await Auth?.currentSession();
    accessToken = session?.getAccessToken()?.getJwtToken();
  } catch (error) {
    console.log(error);
  }
  const path = `/${serviceType}${url}`;
  const config = {
    headers: {
      accessToken: accessToken || '',
    },
    response: true,
    timeout: 120000,
  };
  return API.get(apiName, path, config)
    .then(res => res)
    .catch(error => error.response);
};

export const post = async (
  url,
  data,
  isKeyPropertyNoAvailable,
  serviceType
) => {
  let session;
  let accessToken;
  try {
    session = await Auth?.currentSession();
    accessToken = session?.getAccessToken()?.getJwtToken();
  } catch (error) {
    console.log(error);
  }
  const path = `/${serviceType}${url}`;

  const config = {
    headers: {
      'Content-Type': 'application/json',
      accessToken: accessToken || '',
    },
    timeout: 120000,
    response: true,
    body: data,
  };
  return API.post(apiName, path, config)
    .then(res => res)
    .catch(error => error.response);
};

export const put = async (url, data, isKeyPropertyNoAvailable, serviceType) => {
  let session;
  let accessToken;
  try {
    session = await Auth?.currentSession();
    accessToken = session?.getAccessToken()?.getJwtToken();
  } catch (error) {
    console.log(error);
  }
  const path = `/${serviceType}${url}`;
  const config = {
    headers: {
      accessToken: accessToken || '',
    },
    timeout: 120000,
    response: true,
    body: data,
  };
  return API.put(apiName, path, config)
    .then(res => res)
    .catch(error => error.response);
};

export const deleteRequest = async (url, data, serviceType, hasHeader) => {
  session = await Auth?.currentSession();
  accessToken = session?.getAccessToken()?.getJwtToken();

  const path = `/${serviceType}${url}`;
  const config = {
    headers: {
      accessToken: accessToken || '',
    },
    response: true,
    timeout: 120000,
    body: data,
  };
  return API.del(apiName, path, config)
    .then(res => res)
    .catch(error => error.response);
};
