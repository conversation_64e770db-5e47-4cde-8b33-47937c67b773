export const StateApiStrings = {
  Aggregate: 'AGGREGATE',
  Average: 'AVG',
  matchWise: 'MATCH',
};

export const statsCategoryList = [
  { _id: '1', name: 'Aggregate' },
  { _id: '2', name: 'Average' },
  { _id: '3', name: 'Match-Wise' },
];

export const statsCategory = {
  AGGREGATE: 'Aggregate',
  AVERAGE: 'Average',
  MATCH_WISE: 'Match-Wise',
};

export const filterType = {
  SEASON: 'Season',
  TOURNAMENT: 'Tournament',
  TEAM: 'Team',
  OPPONENT: 'Opponent',
  DATE_RANGE: 'Date Range',
};

export const DEVICE_STATS_CATEGORY_LIST = [
  { key: 'SESSIONS', title: 'Sessions' },
  { key: 'SUMMARY', title: 'Summary' },
];

export const DeviceStatsSessionType = 'SESSIONS';
export const DeviceStatsSummaryType = 'SUMMARY';
export type TSelectedType = 'Summary' | 'Sessions';

export const DELETE_RECURRING_EVENT_OPTIONS = [
  { key: 'SELECTED_EVENT', title: 'This event', id: '1' },
  {
    key: 'SELECTED_AND_FOLLOWING_EVENTS',
    title: 'This and following events',
    id: '2',
  },
  { key: 'ALL_EVENTS', title: 'All events', id: '3' },
];
