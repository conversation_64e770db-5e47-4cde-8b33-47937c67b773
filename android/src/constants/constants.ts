// Player Reducer

export const playerConstant = {
  playerMedicalHistoryPageInitial: 1,
  playerMedicalHistoryPageSizeInitial: 15,
  playerUpdatePageInitial: 1,
  playerUpdatePageSizeInitial: 15,
  playerInjuriesPageInitial: 1,
  playerInjuriesPageSizeInitial: 15,
  playerUpcomingEventsPageSize: 15,
  playerPastEventsPageSize: 15,
  playerUpcomingEventsInitialPage: 1,
  playerPastEventsInitialPage: 1,
};

export const IAPStatsPageSize = 10;
export const IAPCommentsPageSize = 10 ;
export const IAPGraphPageSize = 10000;

export const teamInitialSize = 15;

export const days = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];
export const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
export const eventType = {
  MATCH: 'MATCH',
  TRAINING: 'TRAINING',
};

export const addEventInfo = {
  TOURNAMENTS: 'tournaments',
  OPPONENTS: 'opponents',
  SEASONS: 'seasons',
};

export const filterByAssign = [
  {
    label: 'All',
    value: '',
  },
  {
    label: 'Assigned',
    value: 'ASSIGNED',
  },
  {
    label: 'Not Assigned',
    value: 'NOT_ASSIGNED',
  },
];
export const jerseyErrorList = {
  JERSEY_NO_ALREADY_ASSIGNED: 'JERSEY_NO_ALREADY_ASSIGNED',
  INVALID_JERSEY_NO: 'INVALID_JERSEY_NO',
  JERSEY_NO_ALREADY_ASSIGNED_MESSAGE: 'Already joined',
  INVALID_JERSEY_NO_MESSAGE: 'Invalid jersey number',
};
export const userTypes = [
  {
    id: 'COACH',
    typeName: 'Coaches',
  },
  {
    id: 'PLAYER',
    typeName: 'Players',
  },
];

export const colorPalette = [
  '#FF2A2A',
  '#89C829',
  '#ECDE11',
  '#FF9011',
  '#089CDE',
  '#133EEB',
  '#4317D8',
  '#680AE2',
  '#BD2DF9',
  '#EF08EF',
  '#E80B79',
  '#B5EC76',
  '#07E69A',
  '#5784FF',
  '#15C1DC',
  '#6CFF0C',
  '#F27D7D',
  '#E2F388',
];

// please don't change this userRoleType value ---------------------
export const userRoleType = {
  SUPERADMIN: 'SUPERADMIN',
  COACH: 'COACH',
  PLAYER: 'PLAYER',
  HEAD_COACH: 'HEAD_COACH',
  PARENT: 'PARENT',
};
//-------------------------------------------------

export const userCreateTypes = {
  KOACH: 'Koach',
  NEW: 'New',
};

export const repeatEventTypes = [
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Bi-weekly', value: 'bi-weekly' },
  { label: 'Monthly', value: 'monthly' },
];
export const messageTypes = {
  PERSONAL: 'PERSONAL',
  TEAMS: 'TEAM',
};

export const messageOptions = {
  REPLY: 'Reply',
  FORWARD: 'Forward',
  COPY: 'Copy',
  DELETE: 'Delete',
};

export const singleMessageTypes = {
  USER_CREATE_MESSAGE: 'USER_CREATE_MESSAGE',
  USER_PAST_MESSAGE_VIEW: 'USER_PAST_MESSAGE_VIEW',
  USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE:
    'USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE',
};

export const socketMessageTypes = {
  CHAT_MESSAGE: 'CHAT_MESSAGE',
  IMAGE_MESSAGE_COMPRESSED: 'IMAGE_MESSAGE_COMPRESSED',
  VIDEO_MESSAGE_PREVIEW_IMAGE_GENERATED:
    'VIDEO_MESSAGE_PREVIEW_IMAGE_GENERATED',
};

export const repeatEventTypesValues = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  BI_WEEKLY: 'bi-weekly',
  MONTHLY: 'monthly',
};

export const playerMakerTexts = {
  SYNC_FAIL_PLAYER_MAKER_ALREADY_ID_TAKEN:
    'Sorry, this player ID has already been synced to another player.',
  PLAYER_MAKER_USER_IS_ALREADY_MAPPED: 'PLAYER_MAKER_USER_IS_ALREADY_MAPPED',
};
export const playerStatDevices = {
  PLAYER_MAKER_DEVICE: 'PlayerMaker',
};
export const playerStatDevicesArray = [
  { label: 'PlayerMaker', value: 'PlayerMaker' },
];

export const rsvpTotalCountText = {
  TOTAL_YES_COUNTS: 'Yes',
  TOTAL_NO_COUNTS: 'No',
  TOTAL_NO_RESPONSE: 'No Response',
};

export const manageUsersteamInitialSize = 10;
export const manageUsersListInitialPageSize = 20;
export const manageUsersListInitialPageNo = 1;

export const rsvpListPageSize = 10;

export const MatchLogActions = {
  START_GAME: 'START_GAME',
  FIRST_HALF_END: 'FIRST_HALF_END',
  SECOND_HALF_START: 'SECOND_HALF_START',
  END_GAME: 'END_GAME',
  PENALTY_WON: 'PENALTY_WON',
  PENALTY_CONCEDED: 'PENALTY_CONCEDED',
  PENALTY_MISSED: 'PENALTY_MISSED',
  SUB_IN: 'SUB_IN',
  SUB_OUT: 'SUB_OUT',
  RED_CARD: 'RED_CARD',
  YELLOW_CARD: 'YELLOW_CARD',
  CORNER: 'CORNER',
  ASSIST: 'ASSIST',
  GOAL_SCORED: 'GOAL_SCORED',
  GOAL_CONCEDED: 'GOAL_CONCEDED',
  ANONYMOUS_GOAL_SCORED: 'ANONYMOUS_GOAL_SCORED',
  GOAL_REDUCED: 'GOAL_REDUCED',
  MOVE_PLAYER: 'MOVE_PLAYER',
  PLAYER_MOVE_ACTIVITY: 'PLAYER_MOVE_ACTIVITY',
  SYSTEM_END_GAME: 'SYSTEM_END_GAME',
};

export const MatchLogText = {
  START_GAME: 'Kick-Off',
  FIRST_HALF_END: 'End of 1st Half',
  SECOND_HALF_START: 'Start of 2nd Half',
  END_GAME: 'Final Whistle',
  PENALTY_WON: 'Penalty Won by {PLAYER}',
  PENALTY_CONCEDED: 'Penalty Conceded by {PLAYER}',
  PENALTY_MISSED: 'Penalty Missed by {PLAYER}',
  SUB_IN: '{PLAYER} Subbed In',
  SUB_OUT: '{PLAYER} Subbed Out',
  RED_CARD: '{PLAYER} - Red Card',
  YELLOW_CARD: '{PLAYER} - Yellow Card',
  CORNER: 'Corner by {PLAYER}',
  ASSIST: 'Assist by {PLAYER}',
  GOAL_SCORED: 'Goal Scored by {PLAYER}',
  GOAL_CONCEDED: 'Goal Conceded by {PLAYER}',
  ANONYMOUS_GOAL_SCORED: 'Goal Scored',
  MOVE_PLAYER: '{PLAYER} moved from {POSITION1} to {POSITION2}',
};

export const MatchStatus = {
  WON: 'WON',
  LOOSE: 'LOST',
};

export const EventDateType = {
  END: 'END',
  START: 'START',
};

export const Activity = {
  PLAYER_ACTIVITY: 'PLAYER_ACTIVITY',
  ACTIVITY: 'ACTIVITY',
};

export const summeryTitle = [
  {
    key: 'PENALTY_WON',
    title: 'Penalty Won',
  },
  {
    key: 'PENALTY_CONCEDED',
    title: 'Penalty Conceded',
  },
  {
    key: 'GOAL_SCORED',
    title: 'Goal Scored',
  },
  {
    key: 'PENALTY_MISSED',
    title: 'Penalty Missed',
  },
  {
    key: 'SUB_IN_OUT',
    title: 'Sub Out/Sub In',
  },
  {
    key: 'RED_CARD',
    title: 'R Card',
  },
  {
    key: 'YELLOW_CARD',
    title: 'Y Card',
  },

  {
    key: 'ASSIST',
    title: 'Assist',
  },

  {
    key: 'CORNER',
    title: 'Corner',
  },
];

export const MATCH_LOG_SYNC_TIME_INTERVAL = 30;

export const DEFAULT_MAP_COUNTRY = 'us';

export const MatchLogStages = {
  NOT_STARTED: 0,
  GAME_STARTED: 1,
  FIRST_HALF_ENDED: 2,
  SECOND_HALF_STARTED: 3,
  GAME_ENDED: 4,
  GAME_CONCLUDED: 5,
  SYSTEM_END_GAME: 4,
};

export const emailRegex =
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export const videoFileTypes = [
  'mp4',
  'avi',
  'mkv',
  'flv',
  'mov',
  'mpeg',
  'MP4',
  'AVI',
  'MKV',
  'FLV',
  'MOV',
  'MPEG',
];

export const PlayerInfoLabel = [
  {
    id: '1',
    title: 'Info',
  },
  {
    id: '2',
    title: 'IAP',
  },
  {
    id: '3',
    title: 'Updates',
  },
  {
    id: '4',
    title: 'Medical',
  },
  {
    id: '5',
    title: 'Activity',
  },
  {
    id: '6',
    title: 'Stats',
  },
  {
    id: '7',
    title: 'Device Stats',
  },
];

export const TilesData = [
  {
    active: true,
    key: 'teams',
    name: 'Teams',
    path: 'Teams',
    isTesting: false,
    tile: 'TEAMS',
  },
  {
    active: true,
    key: 'planner',
    name: 'Planner',
    path: 'PlannerScreen',
    isTesting: false,
    tile: 'PLANNER',
  },
  {
    active: true,
    key: 'training',
    name: 'Training',
    path: 'Training',
    isTesting: false,
    tile: 'TRAINING',
  },
  {
    active: true,
    key: 'matches',
    name: 'Matches',
    path: 'Matches',
    isTesting: false,
    tile: 'MATCHES',
  },

  {
    active: true,
    key: 'userManagement',
    name: 'User Management',
    path: 'UserManagement',
    isTesting: false,
    tile: 'USER_MANAGEMENT',
  },
  {
    active: true,
    key: 'messaging',
    name: 'Messaging',
    path: 'Messaging',
    isTesting: false,
    tile: 'MESSAGING',
  },
  {
    active: true,
    key: 'payment',
    name: 'Payment',
    path: 'Payment',
    isTesting: false,
    tile: 'PAYMENT',
  },
];
export const forcedModalType = {
  FORCED_UPDATE: 'forceUpdate',
  UNAUTHORIZED_PARENT: 'unauthorizedParent',
  UNAUTHORIZED_PARENT_WITH_NO_TEAMS: 'unauthorizedParentWithNoTeams',
  RE_ESTABLISH_WS: 'reEstablishWs',
  FORGOT_MODAL: 'forgotModal',
  MATCH_PLANER_OFFLINE: 'MATCH_PLANER_OFFLINE',
  INVALID_ACTION: 'INVALID_ACTION',
  APP_OFFLINE: 'APP_OFFLINE',
};

export const changePasswordConst = {
  PASSWORD_ERROR_MESSAGE:
    'Password must contain at least eight characters, one special character, a numeric value,a capital letter,and a simple letter.',
  NetWorkError: 'Something went wrong try again later!',
};

export const passwordRegex =
  /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/;

export const matchLogValidErrorMessage = {
  INVALID_MATCH_ACTION: 'INVALID_MATCH_ACTION',
};
export const invalidActionMessages = {
  REFRESH: 'Refresh',
  message: 'This action is not valid please refresh.',
  actionFailedMsg: 'Action failed, please refresh and try again',
};

export const onlineBarStatus = {
  ONLINE: 'online',
};

export const messageContentType = {
  TEXT: 'TEXT',
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  FILE: 'FILE',
};
export const messageFileTypes = [
  'ppt',
  'pdf',
  'jpg',
  'jpeg',
  'png',
  'mp4',
  'mov',
  'doc',
  'docx',
  'xls',
  'xlsx',
];

export const imageError = {
  MAX_FILE_SIZE_ERROR: 'Maximum upload image size: 3mb',
};

export const RSVP_RESPONSES = {
  GOING: 'GOING',
  NOT_GOING: 'NOT_GOING',
  NO_RESPONSE: 'NO_RESPONSE',
};

export const FileContentType = {
  PPT: 'ppt',
  PDF: 'pdf',
  DOC: 'doc',
  DOCX: 'docx',
  XLS: 'xls',
  XLSX: 'xlsx',
};

export const teamProfilePropNames = {
  GIVEN_POSITION: 'givenPosition',
  PREFERRED_POSITION: 'preferredPosition',
  FOOT: 'foot',
  GENDER: 'gender',
};

export const gameType = {
  PRACTICE: 'Practice',
  MATCH: 'Match',
};

export const messagingForceModalStatus = {
  RETRY: 'Retry',
  RECONNECTING: 'Reconnecting',
  WS_CONNECTIONFAILED:
    "Oops, we couldn't establish an internet connection. Check internet / Wifi settings or try again",
  APP_OFFLINE:
    'Oops, we couldn’t establish an internet connection. Please check your internet connection and try again.',
};

export const forgotPasswordModalWording = {
  EMAIL: '<EMAIL>',
  ERROR_MESSAGE:
    'The email address you have entered does not exist for this Club ID. Please click on the email below to contact support.',
};

export const notificationTypes = {
  MESSAGING: 'MESSAGING',
  EVENT: 'EVENT',
};

export const APP_STATE_BACKGROUND = 'background';

export const ROUTE_PATH = {
  TEAMS: 'Teams',
  PLANNNER_SCREEN: 'PlannerScreen',
  TRAINING: 'Training',
  MATCHES: 'Matches',
  USER_MANAGEMENT: 'UserManagement',
  MESSAGING: 'Messaging',
  MESSAGING_CHAT: 'MessagingChat',
  MATCH_PLAN: 'MatchPlan',
};

export const userExistStatusType = {
  userExistTrue: 'userExistTrue',
  userExistFalse: 'userExistFalse',
  userExistFail: 'userExistFail',
};

export const CALENDER = {
  uniqueKeyString: 'KoachHubMeta',
  appID: 'KoachHub',
  calendarPermissions: 'granted',
  ios: 'ios',
  accessLevelRead: 'read',
  userIDKey: 'u',
  eventIDKey: 'e',
  emailID: 'emailID',
  matchTypeAdd: 'Match scheduled',
  trainingTypeAdd: 'Training scheduled',
  matchTypeEdit: 'Match Updated',
  trainingTypeEdit: 'Training Updated',
  eventCancelled: 'Event Cancelled',
};

export const S3_BUCKET_LOCATION = {
  documentRepository: 'document-repository',
  chat: 'chat',
  trainingDocuments: 'training-documents',
  medicalDocuments: 'medical-documents',
  injuryDocuments: 'injury-documents',
  profileImages: 'profile-images',
};

export const FILE_UPLOAD_STATUS_TYPE = {
  SUCCESS: 'success',
  CANCEL: 'cancel',
};

export const statsErrorMessage = {
  END_DATE_ERROR: 'Please pick an end date',
  START_DATE_ERROR: 'Please pick a start date',
};

export const WebSocketParams = {
  MESSAGE_WS: 'message',
  MATCH_WS: 'match',
};

export const matchSocketTypes = {
  MATCH_ACTIVITY_CREATE: 'MATCH_ACTIVITY_CREATE',
  MATCH_PLAN_UPDATE: 'MATCH_PLAN_UPDATE',
  MATCH_ACTIVITY_DELETE: 'MATCH_ACTIVITY_DELETE',
  MATCH_CONCLUDE: 'MATCH_CONCLUDE',
};

export const deviceStatfilterList = [
  {
    label: 'Technical',
    id: 'technical-stat',
    summaryId: 'technical-summary',
  },
  {
    label: 'Physical',
    id: 'physical-stat',
    summaryId: 'physical-summary',
  },
];

export const deviceStatsEventType = {
  MATCH: 'Match',
  TRAINING: 'Training',
};

export const NOT_RATED = 'NR';

interface IChartDataTemplate {
  label: string;
  value: string;
  isDiff?: boolean;
  option?: any;
  legends: string[];
}

export const color = ['#41C4D2', '#41D982'];
export const technicalTypeList: IChartDataTemplate[] = [
  {
    label: 'Total Touches',
    value: 'totalTouchesCount',
    legends: ['Total touches'],
  },
  {
    label: 'Leg Use (%)',
    value: 'legUser',
    isDiff: true,
    option: ['leftLegUse', 'rightLegUse'],
    legends: ['Left Leg', 'Right Leg'],
  },
];
export const physicalTypeList: IChartDataTemplate[] = [
  {
    label: 'Distance Covered (km)',
    value: 'distanceCoveredByPlayer',
    legends: ['Distance covered (km)'],
  },
  { label: 'HID (m)', value: 'hidOfPlayer', legends: ['HID (m)'] },
  {
    label: 'Intense Speed Changes',
    value: 'intenseSpeedChangesByPlayer',
    legends: ['Intense speed changes'],
  },
  {
    label: 'Work Rate (m/min)',
    value: 'workRateByPlayer',
    legends: ['Work Rate (m/min)'],
  },
];

export const EventsErrorMessages = {
  MATCH_ALREADY_STARTED: 'Match Already Started',
};

export const deleteRecurringEventsModalTexts = {
  MATCH_ALREADY_STARTED: 'MATCH_ALREADY_STARTED',
  Title: ' Delete Recurring Event ',
  ErrorMessage: 'There is an ongoing match. The event cannot be deleted.',
};

export const deleteEventMessages = {
  confirmationMessage: 'Are you sure you want to delete this event?',
  deleteAllMessage: 'Are you sure you want to Delete all events?',
  currentAndFollowing: `Are you sure you want to delete this event and the following `,
};

export const deleteRecurringEventTypes = {
  singleEvent: 'SELECTED_EVENT',
  currentAndFollowing: 'SELECTED_AND_FOLLOWING_EVENTS',
  allEvents: 'ALL_EVENTS',
};

export const genders = [
  { value: 'MALE', label: 'Male' },
  { value: 'FEMALE', label: 'Female' }
]

export const gendersX = [
  { value: 'MALE', label: 'MALE' },
  { value: 'FEMALE', label: 'FEMALE' },
];


export const trainingRatings = [{
  id: 1,
  label: "Very Easy",
  value: 'Daily actitivies, besides sleeping',
  color: '#00C2CB',
}, {
  id: 2,
  label: "Easy",
  value: 'Walking with purpose to a light jog',
  color: '#74BFAF',
}, {
  id: 3,
  label: "Moderate",
  value: 'Running, biking, swimming can carry on a conversation',
  color: '#7ED957',
}, {
  id: 4,
  label: "Comfortable Pace",
  value: 'Breathing heavily, but can have a short conversation',
  color: '#C9E265',
}, {
  id: 5,
  label: "Hard",
  value: 'Can say short sentences',
  color: '#FDE59D',
}, {
  id: 6,
  label: "Very Hard",
  value: 'Almost feels uncomfortable',
  color: '#FFBD59',
}, {
  id: 7,
  label: "Vigorous",
  value: 'Feels uncomfortable, can say 3 words',
  color: '#FF914D',
}, {
  id: 8,
  label: "Extremely Difficult",
  value: 'Feels uncomfortable, can barely speak a sentence',
  color: '#FF5757',
}, {
  id: 9,
  label: "Very Hard Intensity",
  value: 'Difficult to maintain intensity, can barely breath and speak a word',
  color: '#FF1616',
}, {
  id: 10,
  label: "Max Effort",
  value: 'Out of breath, Unable to talk',
  color: '#B61C12',
}]