import { userRoleType } from './constants';
const getFormattedDate = () => {
  const today = new Date();

  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const day = String(today.getDate()).padStart(2, '0');

  const formattedDate = `${year}/${month}/${day}`;
  return formattedDate;
};

const todayDate = getFormattedDate();

export const generateTermAndConditionData = (
  userData: any,
  parentDetails: any,
  children: any
) => {
  return [
    {
      id: 1,
      title: 'Data Usage Agreement',
      content: [
        userData?.type === userRoleType.COACH
          ? `This Data Usage Agreement (the "Agreement") is entered into as of ${todayDate} (the "Effective Date"), by and between the Koach Hub and the signatory, with its principal place of business at Singapore (the "Provider"). Collectively referred to as the "Parties."`
          : `This Data Usage Agreement (the "Agreement") is entered into as of ${todayDate} (the "Effective Date"), by and between ${
              userData?.type === userRoleType.PARENT
                ? userData?.firstName + ' ' + userData?.lastName || ''
                : userData?.type === userRoleType.PLAYER && parentDetails[0]
                ? parentDetails[0]?.firstName + ' ' + parentDetails[0]?.lastName
                : userData?.guardianName
            }, as the legal guardian of ${
              userData?.type === userRoleType.PLAYER
                ? userData?.firstName + ' ' + userData.lastName || ''
                : userData?.type === userRoleType.PARENT
                ? children.map(
                    (child: any) =>
                      child.firstName +
                        ' ' +
                        (child.lastName ? child.lastName + ' ' : ' ') || ''
                  )
                : ''
            }, and KoachHub, with its principal place of business at Singapore (the "Provider"). Collectively referred to as the "Parties."`,

        `WHEREAS, the Provider offers the KoachHub application (the "Application") that collects, analyzes, and derives insights from data provided by users.`,
        `WHEREAS, the Customer desires to allow the Provider to use the data gathered from the Customer's use of the Application to drive product enhancements and develop new product offerings.`,
        ` NOW, THEREFORE, in consideration of the mutual promises and covenants contained herein, the Parties agree as follows:`,
      ],
    },
    {
      id: 2,
      title: 'Definitions',
      content: [
        '1.1 "Data" refers to any information or material provided by the Customer or generated as a result of the Customer\'s use of the Application.',
        '1.2 "Insights" refer to any knowledge, analysis, or findings derived from the Data by the Provider and its associated companies.',
      ],
    },
    {
      id: 3,
      title: 'Data usage rights',
      content: [
        '2.1 The Customer acknowledges and agrees that by using the Application, they grant the Provider and its associated, affiliated companies, or service providers who act on behalf of the provider a non-exclusive, royalty-free, worldwide license to access, use, analyze, and process the Data for the purpose of improving the Application, driving product enhancements, and developing new product offerings.',
        '2.2 The Customer acknowledges and agrees, the Provider and its associated, affiliated companies, or service providers who act on behalf of the provider shall have the right to create Insights based on the Data.',
        '2.3 The Customer acknowledges and agrees that the Provider and its associated, affiliated companies, or service providers who act on behalf of the provider may aggregate and anonymize the Data to create aggregated statistical information as required by the Provider and its associated companies to suit its requirements and needs over time.',
      ],
    },
    {
      id: 4,
      title: 'Confidentiality and data security',
      content: [
        '3.1 The Provider shall maintain the confidentiality of the Data and shall use commercially reasonable efforts to protect the Data from unauthorized access, use, or disclosure.',
        '3.2 The Provider shall implement appropriate technical and organizational measures to ensure the security and integrity of the Data.',
        '3.3 The Provider shall not disclose the Data to any third party not covered under this agreement without the prior written consent of the Customer, except as required by law or as necessary to provide the Application and fulfill the purposes stated in this Agreement.',
      ],
    },
    {
      id: 5,
      title: 'Ownership of data',
      content: [
        '4.1 The Customer hereby transfers all rights, title, and interest in the Data inputted into the system to the Provider, associated, affiliated companies, or service providers who act on behalf of the provider. The Provider, associated, affiliated companies, or service providers who act on behalf of the provider acknowledges that it acquires full ownership and rights to the Data, except as expressly provided herein.',
        '4.2 The Customer grants the Provider, associated, affiliated companies, or service providers who act on behalf of the provider a non-exclusive, royalty-free license to use the Data for the purposes specified in this Agreement. The Customer shall hold the Provider free from any obligation to pay any loyalty fee or compensation for such use.',
        '4.3 The Insights created by the Provider and its associated, affiliated companies, or service providers who act on behalf of the provider shall be owned by the Provider, and the Customer shall have no ownership rights or claims to such Insights.',
      ],
    },
    {
      id: 6,
      title: 'Term and termination',
      content: [
        '5.1 This Agreement shall commence on the Effective Date and shall continue until the Customer ceases to use the Application or until terminated by either Party in accordance with this Section 5.',
        '5.2 Either Party may terminate this Agreement at any time by providing written notice to the other Party.',
        '5.3 The cession of this contract shall not affect the rights and permissions granted by the Customer to the Provider regarding the use of already captured data and insights. The Provider shall retain the right to continue using and analyzing the data and insights derived from the data collected during the term of this contract, even after the termination or cession of the contract. The Customer acknowledges and agrees that the Provider may continue to utilize the data and insights for the purposes specified in this agreement, subject to the terms and conditions outlined herein.',
      ],
    },
    {
      id: 7,
      title: 'Limitation of liability',
      content: [
        '6.1 TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL EITHER PARTY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, CONSEQUENTIAL, SPECIAL, EXEMPLARY, OR PUNITIVE DAMAGES ARISING OUT OF OR IN CONNECTION WITH THIS AGREEMENT, REGARDLESS OF THE FORM OF ACTION, WHETHER IN CONTRACT, TORT, OR OTHERWISE.',
      ],
    },
    {
      id: 8,
      title: 'general provisions',
      content: [
        '7.1 Entire Agreement: This Agreement, including any attached exhibits, constitutes the entire agreement between the Parties concerning the subject matter hereof and supersedes all prior or contemporaneous oral or written agreements, understandings, or representations.',
        '7.2 Governing Law: This Agreement shall be governed by and construed in accordance with the laws of Singapore without giving effect to any conflict of law principles.',
        '7.3 Severability: If any provision of this Agreement is held to be invalid, illegal, or unenforceable, the validity, legality, or enforceability of the remaining provisions shall in no way be affected or impaired.',
        'By clicking the "Accept" button or otherwise indicating acceptance of this agreement, you acknowledge and agree to be bound by all the terms and conditions set forth in this agreement. Your acceptance signifies your understanding and agreement to comply with all obligations, restrictions, and responsibilities outlined herein. This agreement constitutes a legally binding contract between you and the Provider. If you do not agree to these terms, please refrain from clicking "Accept" and do not proceed with using the services.',
      ],
    },
  ];
};
export const TermAndConditionVersion = 'V1.0';
