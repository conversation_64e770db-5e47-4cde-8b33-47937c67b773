interface ICountry {
    name_ar: string;
    name_en: string;
    native_name: string;
    capital: string;
    en_capital: string;
    alpha_2: string;
    alpha_3: string;
    phone_code: string;
    nationality_en: string;
    nationality_ar: string;
  }
  
  export const countryList: ICountry[] = [
    {
      name_ar: "أفغانستان",
      name_en: "Afghanistan",
      native_name: "افغانستان",
      capital: "كابول",
      en_capital: "Kabul",
      alpha_2: "AF",
      alpha_3: "AFG",
      phone_code: "93",
      nationality_en: "Afghan",
      nationality_ar: "أفغاني"
    },
    {
      name_ar: "جزر آلاند",
      name_en: "Åland Islands",
      native_name: "Åland",
      capital: "ماريهامن",
      en_capital: "Mariehamn",
      alpha_2: "AX",
      alpha_3: "ALA",
      phone_code: "358",
      nationality_en: "Åland Island",
      nationality_ar: "جزر آلاند"
    },
    {
      name_ar: "ألبانيا",
      name_en: "Albania",
      native_name: "Shqipëria",
      capital: "تيرانا",
      en_capital: "Tirana",
      alpha_2: "AL",
      alpha_3: "ALB",
      phone_code: "355",
      nationality_en: "Albanian",
      nationality_ar: "ألباني"
    },
    {
      name_ar: "الجزائر",
      name_en: "Algeria",
      native_name: "الجزائر",
      capital: "الجزائر",
      en_capital: "Algiers",
      alpha_2: "DZ",
      alpha_3: "DZA",
      phone_code: "213",
      nationality_en: "Algerian",
      nationality_ar: "جزائري"
    },
    {
      name_ar: "ساموا الأمريكية",
      name_en: "American Samoa",
      native_name: "American Samoa",
      capital: "باجو باجو",
      en_capital: "Pago Pago",
      alpha_2: "AS",
      alpha_3: "ASM",
      phone_code: "1684",
      nationality_en: "American Samoan",
      nationality_ar: "ساموا الأمريكية"
    },
    {
      name_ar: "أندورا",
      name_en: "Andorra",
      native_name: "Andorra",
      capital: "أندورا لا فيلا",
      en_capital: "Andorra la Vella",
      alpha_2: "AD",
      alpha_3: "AND",
      phone_code: "376",
      nationality_en: "Andorran",
      nationality_ar: "أندوري"
    },
    {
      name_ar: "أنغولا",
      name_en: "Angola",
      native_name: "Angola",
      capital: "لواندا",
      en_capital: "Luanda",
      alpha_2: "AO",
      alpha_3: "AGO",
      phone_code: "244",
      nationality_en: "Angolan",
      nationality_ar: "أنغولي"
    },
    {
      name_ar: "أنغيلا",
      name_en: "Anguilla",
      native_name: "Anguilla",
      capital: "وادي",
      en_capital: "The Valley",
      alpha_2: "AI",
      alpha_3: "AIA",
      phone_code: "1264",
      nationality_en: "Anguillan",
      nationality_ar: "أنغيلا"
    },
    {
      name_ar: "أنتيغوا وبربودا",
      name_en: "Antigua and Barbuda",
      native_name: "Antigua and Barbuda",
      capital: "سانت جون",
      en_capital: "Saint John's",
      alpha_2: "AG",
      alpha_3: "ATG",
      phone_code: "1268",
      nationality_en: "Antiguan or Barbudan",
      nationality_ar: "أنتيغوا وبربودا"
    },
    {
      name_ar: "الأرجنتين",
      name_en: "Argentina",
      native_name: "Argentina",
      capital: "بوينس آيرس",
      en_capital: "Buenos Aires",
      alpha_2: "AR",
      alpha_3: "ARG",
      phone_code: "54",
      nationality_en: "Argentine",
      nationality_ar: "الأرجنتين"
    },
    {
      name_ar: "أرمينيا",
      name_en: "Armenia",
      native_name: "Հայաստան",
      capital: "يريفان",
      en_capital: "Yerevan",
      alpha_2: "AM",
      alpha_3: "ARM",
      phone_code: "374",
      nationality_en: "Armenian",
      nationality_ar: "أرميني"
    },
    {
      name_ar: "أروبا",
      name_en: "Aruba",
      native_name: "Aruba",
      capital: "اورانجستاد",
      en_capital: "Oranjestad",
      alpha_2: "AW",
      alpha_3: "ABW",
      phone_code: "297",
      nationality_en: "Aruban",
      nationality_ar: "أروبا"
    },
    {
      name_ar: "أستراليا",
      name_en: "Australia",
      native_name: "Australia",
      capital: "كانبيرا",
      en_capital: "Canberra",
      alpha_2: "AU",
      alpha_3: "AUS",
      phone_code: "61",
      nationality_en: "Australian",
      nationality_ar: "أسترالي"
    },
    {
      name_ar: "النمسا",
      name_en: "Austria",
      native_name: "Österreich",
      capital: "فيينا",
      en_capital: "Vienna",
      alpha_2: "AT",
      alpha_3: "AUT",
      phone_code: "43",
      nationality_en: "Austrian",
      nationality_ar: "نمساوي"
    },
    {
      name_ar: "أذربيجان",
      name_en: "Azerbaijan",
      native_name: "Azərbaycan",
      capital: "باكو",
      en_capital: "Baku",
      alpha_2: "AZ",
      alpha_3: "AZE",
      phone_code: "994",
      nationality_en: "Azerbaijani",
      nationality_ar: "أذربيجاني"
    },
    {
      name_ar: "جزر البهاما",
      name_en: "The Bahamas",
      native_name: "Bahamas",
      capital: "ناسو",
      en_capital: "Nassau",
      alpha_2: "BS",
      alpha_3: "BHS",
      phone_code: "1242",
      nationality_en: "Bahamian",
      nationality_ar: "باهامى"
    },
    {
      name_ar: "البحرين",
      name_en: "Bahrain",
      native_name: "‏البحرين",
      capital: "المنامة",
      en_capital: "Manama",
      alpha_2: "BH",
      alpha_3: "BHR",
      phone_code: "973",
      nationality_en: "Bahraini",
      nationality_ar: "بحريني"
    },
    {
      name_ar: "بنغلاديش",
      name_en: "Bangladesh",
      native_name: "Bangladesh",
      capital: "دكا",
      en_capital: "Dhaka",
      alpha_2: "BD",
      alpha_3: "BGD",
      phone_code: "880",
      nationality_en: "Bangladeshi",
      nationality_ar: "بنجلاديشي"
    },
    {
      name_ar: "بربادوس",
      name_en: "Barbados",
      native_name: "Barbados",
      capital: "بريدجتاون",
      en_capital: "Bridgetown",
      alpha_2: "BB",
      alpha_3: "BRB",
      phone_code: "1246",
      nationality_en: "Barbadian",
      nationality_ar: "باربادوسي"
    },
    {
      name_ar: "بيلاروس",
      name_en: "Belarus",
      native_name: "Белару́сь",
      capital: "مينسك",
      en_capital: "Minsk",
      alpha_2: "BY",
      alpha_3: "BLR",
      phone_code: "375",
      nationality_en: "Belarusian",
      nationality_ar: "بيلاروسي"
    },
    {
      name_ar: "بلجيكا",
      name_en: "Belgium",
      native_name: "België",
      capital: "بروكسل",
      en_capital: "Brussels",
      alpha_2: "BE",
      alpha_3: "BEL",
      phone_code: "32",
      nationality_en: "Belgian",
      nationality_ar: "بلجيكي"
    },
    {
      name_ar: "بليز",
      name_en: "Belize",
      native_name: "Belize",
      capital: "بلموبان",
      en_capital: "Belmopan",
      alpha_2: "BZ",
      alpha_3: "BLZ",
      phone_code: "501",
      nationality_en: "Belizean",
      nationality_ar: "بليزي"
    },
    {
      name_ar: "بنن",
      name_en: "Benin",
      native_name: "Bénin",
      capital: "بورتو نوفو",
      en_capital: "Porto-Novo",
      alpha_2: "BJ",
      alpha_3: "BEN",
      phone_code: "229",
      nationality_en: "Beninese",
      nationality_ar: "بنيني"
    },
    {
      name_ar: "برمودا",
      name_en: "Bermuda",
      native_name: "Bermuda",
      capital: "هاميلتون",
      en_capital: "Hamilton",
      alpha_2: "BM",
      alpha_3: "BMU",
      phone_code: "1441",
      nationality_en: "Bermudian",
      nationality_ar: "برمودا"
    },
    {
      name_ar: "بوتان",
      name_en: "Bhutan",
      native_name: "ʼbrug-yul",
      capital: "تيمفو",
      en_capital: "Thimphu",
      alpha_2: "BT",
      alpha_3: "BTN",
      phone_code: "975",
      nationality_en: "Bhutanese",
      nationality_ar: "بوتاني"
    },
    {
      name_ar: "بوليفيا",
      name_en: "Bolivia",
      native_name: "Bolivia",
      capital: "سوكري",
      en_capital: "Sucre",
      alpha_2: "BO",
      alpha_3: "BOL",
      phone_code: "591",
      nationality_en: "Bolivian",
      nationality_ar: "بوليفي"
    },
    {
      name_ar: "بونير",
      name_en: "Bonaire",
      native_name: "Bonaire",
      capital: "كرالنديجك",
      en_capital: "Kralendijk",
      alpha_2: "BQ",
      alpha_3: "BES",
      phone_code: "5997",
      nationality_en: "Bonaire",
      nationality_ar: "بونير"
    },
    {
      name_ar: "بوتسوانا",
      name_en: "Botswana",
      native_name: "Botswana",
      capital: "غابورون",
      en_capital: "Gaborone",
      alpha_2: "BW",
      alpha_3: "BWA",
      phone_code: "267",
      nationality_en: "Motswana",
      nationality_ar: "لتسواني"
    },
    {
      name_ar: "البرازيل",
      name_en: "Brazil",
      native_name: "Brasil",
      capital: "برازيليا",
      en_capital: "Brasília",
      alpha_2: "BR",
      alpha_3: "BRA",
      phone_code: "55",
      nationality_en: "Brazilian",
      nationality_ar: "برازيلي"
    },
    {
      name_ar: "إقليم المحيط الهندي البريطاني",
      name_en: "British Indian Ocean Territory",
      native_name: "British Indian Ocean Territory",
      capital: "دييغو غارسيا",
      en_capital: "Diego Garcia",
      alpha_2: "IO",
      alpha_3: "IOT",
      phone_code: "246",
      nationality_en: "BIOT",
      nationality_ar: "إقليم المحيط الهندي البريطاني"
    },
    {
      name_ar: "جزر فيرجن البريطانية",
      name_en: "British Virgin Islands",
      native_name: "British Virgin Islands",
      capital: "رود تاون",
      en_capital: "Road Town",
      alpha_2: "VG",
      alpha_3: "VGB",
      phone_code: "1284",
      nationality_en: "British Virgin Island",
      nationality_ar: "جزر فيرجن البريطانية"
    },
    {
      name_ar: "بروناي",
      name_en: "Brunei",
      native_name: "Negara Brunei Darussalam",
      capital: "بندر سيري بيغاوان",
      en_capital: "Bandar Seri Begawan",
      alpha_2: "BN",
      alpha_3: "BRN",
      phone_code: "673",
      nationality_en: "Bruneian",
      nationality_ar: "بروناى"
    },
    {
      name_ar: "بلغاريا",
      name_en: "Bulgaria",
      native_name: "България",
      capital: "صوفيا",
      en_capital: "Sofia",
      alpha_2: "BG",
      alpha_3: "BGR",
      phone_code: "359",
      nationality_en: "Bulgarian",
      nationality_ar: "بلغاري"
    },
    {
      name_ar: "بوركينا فاسو",
      name_en: "Burkina Faso",
      native_name: "Burkina Faso",
      capital: "واغادوغو",
      en_capital: "Ouagadougou",
      alpha_2: "BF",
      alpha_3: "BFA",
      phone_code: "226",
      nationality_en: "Burkinabé",
      nationality_ar: "بوركينا فاسو"
    },
    {
      name_ar: "بوروندي",
      name_en: "Burundi",
      native_name: "Burundi",
      capital: "بوجومبورا",
      en_capital: "Bujumbura",
      alpha_2: "BI",
      alpha_3: "BDI",
      phone_code: "257",
      nationality_en: "Burundian",
      nationality_ar: "بوروندي"
    },
    {
      name_ar: "كمبوديا",
      name_en: "Cambodia",
      native_name: "Kâmpŭchéa",
      capital: "بنوم بنه",
      en_capital: "Phnom Penh",
      alpha_2: "KH",
      alpha_3: "KHM",
      phone_code: "855",
      nationality_en: "Cambodian",
      nationality_ar: "كمبودي"
    },
    {
      name_ar: "الكاميرون",
      name_en: "Cameroon",
      native_name: "Cameroon",
      capital: "ياوندي",
      en_capital: "Yaoundé",
      alpha_2: "CM",
      alpha_3: "CMR",
      phone_code: "237",
      nationality_en: "Cameroonian",
      nationality_ar: "كاميروني"
    },
    {
      name_ar: "كندا",
      name_en: "Canada",
      native_name: "Canada",
      capital: "أوتاوا",
      en_capital: "Ottawa",
      alpha_2: "CA",
      alpha_3: "CAN",
      phone_code: "1",
      nationality_en: "Canadian",
      nationality_ar: "كندي"
    },
    {
      name_ar: "الرأس الأخضر",
      name_en: "Cape Verde",
      native_name: "Cabo Verde",
      capital: "برايا",
      en_capital: "Praia",
      alpha_2: "CV",
      alpha_3: "CPV",
      phone_code: "238",
      nationality_en: "Cabo Verdean",
      nationality_ar: "الرأس الأخضر"
    },
    {
      name_ar: "جزر كايمان",
      name_en: "Cayman Islands",
      native_name: "Cayman Islands",
      capital: "جورج تاون",
      en_capital: "George Town",
      alpha_2: "KY",
      alpha_3: "CYM",
      phone_code: "1345",
      nationality_en: "Caymanian",
      nationality_ar: "جزر كايمان"
    },
    {
      name_ar: "جمهورية أفريقيا الوسطى",
      name_en: "Central African Republic",
      native_name: "Ködörösêse tî Bêafrîka",
      capital: "بانغي",
      en_capital: "Bangui",
      alpha_2: "CF",
      alpha_3: "CAF",
      phone_code: "236",
      nationality_en: "Central African",
      nationality_ar: "وسط أفريقيا"
    },
    {
      name_ar: "تشاد",
      name_en: "Chad",
      native_name: "Tchad",
      capital: "نجامينا",
      en_capital: "N'Djamena",
      alpha_2: "TD",
      alpha_3: "TCD",
      phone_code: "235",
      nationality_en: "Chadian",
      nationality_ar: "تشادي"
    },
    {
      name_ar: "شيلي",
      name_en: "Chile",
      native_name: "Chile",
      capital: "سانتياغو",
      en_capital: "Santiago",
      alpha_2: "CL",
      alpha_3: "CHL",
      phone_code: "56",
      nationality_en: "Chilean",
      nationality_ar: "شيلي"
    },
    {
      name_ar: "الصين",
      name_en: "China",
      native_name: "中国",
      capital: "بكين",
      en_capital: "Beijing",
      alpha_2: "CN",
      alpha_3: "CHN",
      phone_code: "86",
      nationality_en: "Chinese",
      nationality_ar: "صينى"
    },
    {
      name_ar: "جزيرة عيد الميلاد",
      name_en: "Christmas Island",
      native_name: "Christmas Island",
      capital: "ذي سيتلمنت",
      en_capital: "Flying Fish Cove",
      alpha_2: "CX",
      alpha_3: "CXR",
      phone_code: "61",
      nationality_en: "Christmas Island",
      nationality_ar: "جزيرة عيد الميلاد"
    },
    {
      name_ar: "كوكوس (كيلينغ جزر",
      name_en: "Cocos (Keeling) Islands",
      native_name: "Cocos (Keeling) Islands",
      capital: "غرب الجزيرة",
      en_capital: "West Island",
      alpha_2: "CC",
      alpha_3: "CCK",
      phone_code: "61",
      nationality_en: "Cocos Island",
      nationality_ar: "كوكوس (كيلينغ جزر"
    },
    {
      name_ar: "كولومبيا",
      name_en: "Colombia",
      native_name: "Colombia",
      capital: "بوغوتا",
      en_capital: "Bogotá",
      alpha_2: "CO",
      alpha_3: "COL",
      phone_code: "57",
      nationality_en: "Colombian",
      nationality_ar: "كولومبي"
    },
    {
      name_ar: "جزر القمر",
      name_en: "Comoros",
      native_name: "Komori",
      capital: "موروني",
      en_capital: "Moroni",
      alpha_2: "KM",
      alpha_3: "COM",
      phone_code: "269",
      nationality_en: "Comoran",
      nationality_ar: "جزر القمر"
    },
    {
      name_ar: "جمهورية الكونغو",
      name_en: "Republic of the Congo",
      native_name: "République du Congo",
      capital: "برازافيل",
      en_capital: "Brazzaville",
      alpha_2: "CG",
      alpha_3: "COG",
      phone_code: "242",
      nationality_en: "Congolese",
      nationality_ar: "كونغولي"
    },
    {
      name_ar: "جمهورية الكونغو الديمقراطية",
      name_en: "Democratic Republic of the Congo",
      native_name: "République démocratique du Congo",
      capital: "كينشاسا",
      en_capital: "Kinshasa",
      alpha_2: "CD",
      alpha_3: "COD",
      phone_code: "243",
      nationality_en: "Congolese",
      nationality_ar: "كونغولي"
    },
    {
      name_ar: "جزر كوك",
      name_en: "Cook Islands",
      native_name: "Cook Islands",
      capital: "أفاروا",
      en_capital: "Avarua",
      alpha_2: "CK",
      alpha_3: "COK",
      phone_code: "682",
      nationality_en: "Cook Island",
      nationality_ar: "جزر كوك"
    },
    {
      name_ar: "كوستاريكا",
      name_en: "Costa Rica",
      native_name: "Costa Rica",
      capital: "سان خوسيه",
      en_capital: "San José",
      alpha_2: "CR",
      alpha_3: "CRI",
      phone_code: "506",
      nationality_en: "Costa Rican",
      nationality_ar: "كوستاريكي"
    },
    {
      name_ar: "كرواتيا",
      name_en: "Croatia",
      native_name: "Hrvatska",
      capital: "زغرب",
      en_capital: "Zagreb",
      alpha_2: "HR",
      alpha_3: "HRV",
      phone_code: "385",
      nationality_en: "Croatian",
      nationality_ar: "كرواتية"
    },
    {
      name_ar: "كوبا",
      name_en: "Cuba",
      native_name: "Cuba",
      capital: "هافانا",
      en_capital: "Havana",
      alpha_2: "CU",
      alpha_3: "CUB",
      phone_code: "53",
      nationality_en: "Cuban",
      nationality_ar: "كوبي"
    },
    {
      name_ar: "كوراساو",
      name_en: "Curaçao",
      native_name: "Curaçao",
      capital: "وليمستاد",
      en_capital: "Willemstad",
      alpha_2: "CW",
      alpha_3: "CUW",
      phone_code: "5999",
      nationality_en: "Curaçaoan",
      nationality_ar: "كوراساو"
    },
    {
      name_ar: "قبرص",
      name_en: "Cyprus",
      native_name: "Κύπρος",
      capital: "نيقوسيا",
      en_capital: "Nicosia",
      alpha_2: "CY",
      alpha_3: "CYP",
      phone_code: "357",
      nationality_en: "Cypriot",
      nationality_ar: "قبرصي"
    },
    {
      name_ar: "الجمهورية التشيكية",
      name_en: "Czech Republic",
      native_name: "Česká republika",
      capital: "براغ",
      en_capital: "Prague",
      alpha_2: "CZ",
      alpha_3: "CZE",
      phone_code: "420",
      nationality_en: "Czech",
      nationality_ar: "تشيكي"
    },
    {
      name_ar: "الدانمرك",
      name_en: "Denmark",
      native_name: "Danmark",
      capital: "كوبنهاغن",
      en_capital: "Copenhagen",
      alpha_2: "DK",
      alpha_3: "DNK",
      phone_code: "45",
      nationality_en: "Danish",
      nationality_ar: "دانماركي"
    },
    {
      name_ar: "جيبوتي",
      name_en: "Djibouti",
      native_name: "Djibouti",
      capital: "جيبوتي",
      en_capital: "Djibouti",
      alpha_2: "DJ",
      alpha_3: "DJI",
      phone_code: "253",
      nationality_en: "Djiboutian",
      nationality_ar: "جيبوتي"
    },
    {
      name_ar: "دومينيكا",
      name_en: "Dominica",
      native_name: "Dominica",
      capital: "روزيو",
      en_capital: "Roseau",
      alpha_2: "DM",
      alpha_3: "DMA",
      phone_code: "1767",
      nationality_en: "Dominican",
      nationality_ar: "دومينيكاني"
    },
    {
      name_ar: "الجمهورية الدومينيكية",
      name_en: "Dominican Republic",
      native_name: "República Dominicana",
      capital: "سانتو دومينغو",
      en_capital: "Santo Domingo",
      alpha_2: "DO",
      alpha_3: "DOM",
      phone_code: "1809",
      nationality_en: "Dominican",
      nationality_ar: "دومينيكاني"
    },
    {
      name_ar: "إكوادور",
      name_en: "Ecuador",
      native_name: "Ecuador",
      capital: "كيتو",
      en_capital: "Quito",
      alpha_2: "EC",
      alpha_3: "ECU",
      phone_code: "593",
      nationality_en: "Ecuadorian",
      nationality_ar: "إكوادور"
    },
    {
      name_ar: "مصر",
      name_en: "Egypt",
      native_name: "مصر‎",
      capital: "القاهرة",
      en_capital: "Cairo",
      alpha_2: "EG",
      alpha_3: "EGY",
      phone_code: "20",
      nationality_en: "Egyptian",
      nationality_ar: "مصري"
    },
    {
      name_ar: "السلفادور",
      name_en: "El Salvador",
      native_name: "El Salvador",
      capital: "سان سلفادور",
      en_capital: "San Salvador",
      alpha_2: "SV",
      alpha_3: "SLV",
      phone_code: "503",
      nationality_en: "Salvadoran",
      nationality_ar: "سلفادوري"
    },
    {
      name_ar: "غينيا الاستوائية",
      name_en: "Equatorial Guinea",
      native_name: "Guinea Ecuatorial",
      capital: "مالابو",
      en_capital: "Malabo",
      alpha_2: "GQ",
      alpha_3: "GNQ",
      phone_code: "240",
      nationality_en: "Equatorial Guinean",
      nationality_ar: "غيني  استوائي"
    },
    {
      name_ar: "إريتريا",
      name_en: "Eritrea",
      native_name: "ኤርትራ",
      capital: "أسمرة",
      en_capital: "Asmara",
      alpha_2: "ER",
      alpha_3: "ERI",
      phone_code: "291",
      nationality_en: "Eritrean",
      nationality_ar: "إريتري"
    },
    {
      name_ar: "إستونيا",
      name_en: "Estonia",
      native_name: "Eesti",
      capital: "تالين",
      en_capital: "Tallinn",
      alpha_2: "EE",
      alpha_3: "EST",
      phone_code: "372",
      nationality_en: "Estonian",
      nationality_ar: "إستوني"
    },
    {
      name_ar: "إثيوبيا",
      name_en: "Ethiopia",
      native_name: "ኢትዮጵያ",
      capital: "أديس أبابا",
      en_capital: "Addis Ababa",
      alpha_2: "ET",
      alpha_3: "ETH",
      phone_code: "251",
      nationality_en: "Ethiopian",
      nationality_ar: "حبشي"
    },
    {
      name_ar: "جزر فوكلاند",
      name_en: "Falkland Islands",
      native_name: "Falkland Islands",
      capital: "ستانلي",
      en_capital: "Stanley",
      alpha_2: "FK",
      alpha_3: "FLK",
      phone_code: "500",
      nationality_en: "Falkland Island",
      nationality_ar: "جزر فوكلاند"
    },
    {
      name_ar: "جزر فارو",
      name_en: "Faroe Islands",
      native_name: "Føroyar",
      capital: "تورشافن",
      en_capital: "Tórshavn",
      alpha_2: "FO",
      alpha_3: "FRO",
      phone_code: "298",
      nationality_en: "Faroese",
      nationality_ar: "جزر فارو"
    },
    {
      name_ar: "فيجي",
      name_en: "Fiji",
      native_name: "Fiji",
      capital: "سوفا",
      en_capital: "Suva",
      alpha_2: "FJ",
      alpha_3: "FJI",
      phone_code: "679",
      nationality_en: "Fijian",
      nationality_ar: "فيجي"
    },
    {
      name_ar: "فنلندا",
      name_en: "Finland",
      native_name: "Suomi",
      capital: "هلسنكي",
      en_capital: "Helsinki",
      alpha_2: "FI",
      alpha_3: "FIN",
      phone_code: "358",
      nationality_en: "Finnish",
      nationality_ar: "فنلندي"
    },
    {
      name_ar: "فرنسا",
      name_en: "France",
      native_name: "France",
      capital: "باريس",
      en_capital: "Paris",
      alpha_2: "FR",
      alpha_3: "FRA",
      phone_code: "33",
      nationality_en: "French",
      nationality_ar: "فرنسي"
    },
    {
      name_ar: "غويانا الفرنسية",
      name_en: "French Guiana",
      native_name: "Guyane française",
      capital: "كايين",
      en_capital: "Cayenne",
      alpha_2: "GF",
      alpha_3: "GUF",
      phone_code: "594",
      nationality_en: "French Guianese",
      nationality_ar: "غويانا الفرنسية"
    },
    {
      name_ar: "بولينيزيا الفرنسية",
      name_en: "French Polynesia",
      native_name: "Polynésie française",
      capital: "Papeetē",
      en_capital: "Papeetē",
      alpha_2: "PF",
      alpha_3: "PYF",
      phone_code: "689",
      nationality_en: "French Polynesian",
      nationality_ar: "بولينيزيا الفرنسية"
    },
    {
      name_ar: "الأراضي الفرنسية الجنوبية والقطبية الجنوبية",
      name_en: "French Southern and Antarctic Lands",
      native_name: "Territoire des Terres australes et antarctiques françaises",
      capital: "بورت-اوكس-فرانكايس",
      en_capital: "Port-aux-Français",
      alpha_2: "TF",
      alpha_3: "ATF",
      phone_code: "262",
      nationality_en: "French Southern Territories",
      nationality_ar: "الأراضي الفرنسية الجنوبية والقطبية الجنوبية"
    },
    {
      name_ar: "غابون",
      name_en: "Gabon",
      native_name: "Gabon",
      capital: "ليبرفيل",
      en_capital: "Libreville",
      alpha_2: "GA",
      alpha_3: "GAB",
      phone_code: "241",
      nationality_en: "Gabonese",
      nationality_ar: "جابوني"
    },
    {
      name_ar: "غامبيا",
      name_en: "The Gambia",
      native_name: "Gambia",
      capital: "بانجول",
      en_capital: "Banjul",
      alpha_2: "GM",
      alpha_3: "GMB",
      phone_code: "220",
      nationality_en: "Gambian",
      nationality_ar: "غامبيي"
    },
    {
      name_ar: "جورجيا",
      name_en: "Georgia",
      native_name: "საქართველო",
      capital: "تبليسي",
      en_capital: "Tbilisi",
      alpha_2: "GE",
      alpha_3: "GEO",
      phone_code: "995",
      nationality_en: "Georgian",
      nationality_ar: "جورجي"
    },
    {
      name_ar: "ألمانيا",
      name_en: "Germany",
      native_name: "Deutschland",
      capital: "برلين",
      en_capital: "Berlin",
      alpha_2: "DE",
      alpha_3: "DEU",
      phone_code: "49",
      nationality_en: "German",
      nationality_ar: "ألماني"
    },
    {
      name_ar: "غانا",
      name_en: "Ghana",
      native_name: "Ghana",
      capital: "أكرا",
      en_capital: "Accra",
      alpha_2: "GH",
      alpha_3: "GHA",
      phone_code: "233",
      nationality_en: "Ghanaian",
      nationality_ar: "غاني"
    },
    {
      name_ar: "طارق",
      name_en: "Gibraltar",
      native_name: "Gibraltar",
      capital: "طارق",
      en_capital: "Gibraltar",
      alpha_2: "GI",
      alpha_3: "GIB",
      phone_code: "350",
      nationality_en: "Gibraltar",
      nationality_ar: "طارق"
    },
    {
      name_ar: "اليونان",
      name_en: "Greece",
      native_name: "Ελλάδα",
      capital: "أثينا",
      en_capital: "Athens",
      alpha_2: "GR",
      alpha_3: "GRC",
      phone_code: "30",
      nationality_en: "Greek",
      nationality_ar: "إغريقي"
    },
    {
      name_ar: "غرينلاند",
      name_en: "Greenland",
      native_name: "Kalaallit Nunaat",
      capital: "نوك",
      en_capital: "Nuuk",
      alpha_2: "GL",
      alpha_3: "GRL",
      phone_code: "299",
      nationality_en: "Greenlandic",
      nationality_ar: "غرينلاند"
    },
    {
      name_ar: "غرينادا",
      name_en: "Grenada",
      native_name: "Grenada",
      capital: "سانت جورج",
      en_capital: "St. George's",
      alpha_2: "GD",
      alpha_3: "GRD",
      phone_code: "1473",
      nationality_en: "Grenadian",
      nationality_ar: "جرينادي"
    },
    {
      name_ar: "غوادلوب",
      name_en: "Guadeloupe",
      native_name: "Guadeloupe",
      capital: "باس-تير",
      en_capital: "Basse-Terre",
      alpha_2: "GP",
      alpha_3: "GLP",
      phone_code: "590",
      nationality_en: "Guadeloupe",
      nationality_ar: "غوادلوب"
    },
    {
      name_ar: "غوام",
      name_en: "Guam",
      native_name: "Guam",
      capital: "هاغاتنا",
      en_capital: "Hagåtña",
      alpha_2: "GU",
      alpha_3: "GUM",
      phone_code: "1671",
      nationality_en: "Guamanian",
      nationality_ar: "غوام"
    },
    {
      name_ar: "غواتيمالا",
      name_en: "Guatemala",
      native_name: "Guatemala",
      capital: "مدينة غواتيمالا",
      en_capital: "Guatemala City",
      alpha_2: "GT",
      alpha_3: "GTM",
      phone_code: "502",
      nationality_en: "Guatemalan",
      nationality_ar: "غواتيمالي"
    },
    {
      name_ar: "غيرنسي",
      name_en: "Guernsey",
      native_name: "Guernsey",
      capital: "سانت بيتر بورت",
      en_capital: "St. Peter Port",
      alpha_2: "GG",
      alpha_3: "GGY",
      phone_code: "44",
      nationality_en: "Channel Island",
      nationality_ar: "غيرنسي"
    },
    {
      name_ar: "غينيا",
      name_en: "Guinea",
      native_name: "Guinée",
      capital: "كوناكري",
      en_capital: "Conakry",
      alpha_2: "GN",
      alpha_3: "GIN",
      phone_code: "224",
      nationality_en: "Guinean",
      nationality_ar: "غيني"
    },
    {
      name_ar: "غينيا-بيساو",
      name_en: "Guinea-Bissau",
      native_name: "Guiné-Bissau",
      capital: "بيساو",
      en_capital: "Bissau",
      alpha_2: "GW",
      alpha_3: "GNB",
      phone_code: "245",
      nationality_en: "Bissau-Guinean",
      nationality_ar: "غينيا-بيساو"
    },
    {
      name_ar: "غيانا",
      name_en: "Guyana",
      native_name: "Guyana",
      capital: "جورج تاون",
      en_capital: "Georgetown",
      alpha_2: "GY",
      alpha_3: "GUY",
      phone_code: "592",
      nationality_en: "Guyanese",
      nationality_ar: "جوياني"
    },
    {
      name_ar: "هايتي",
      name_en: "Haiti",
      native_name: "Haïti",
      capital: "Port-au-Prince",
      en_capital: "Port-au-Prince",
      alpha_2: "HT",
      alpha_3: "HTI",
      phone_code: "509",
      nationality_en: "Haitian",
      nationality_ar: "هايتي"
    },
    {
      name_ar: "هندوراس",
      name_en: "Honduras",
      native_name: "Honduras",
      capital: "تيغوسيغالبا",
      en_capital: "Tegucigalpa",
      alpha_2: "HN",
      alpha_3: "HND",
      phone_code: "504",
      nationality_en: "Honduran",
      nationality_ar: "هندوراسي"
    },
    {
      name_ar: "هونغ كونغ",
      name_en: "Hong Kong",
      native_name: "香港",
      capital: "مدينة فيكتوريا",
      en_capital: "City of Victoria",
      alpha_2: "HK",
      alpha_3: "HKG",
      phone_code: "852",
      nationality_en: "Hong Kong",
      nationality_ar: "هونغ كونغ"
    },
    {
      name_ar: "هنغاريا",
      name_en: "Hungary",
      native_name: "Magyarország",
      capital: "بودابست",
      en_capital: "Budapest",
      alpha_2: "HU",
      alpha_3: "HUN",
      phone_code: "36",
      nationality_en: "Hungarian",
      nationality_ar: "هنغاري"
    },
    {
      name_ar: "أيسلندا",
      name_en: "Iceland",
      native_name: "Ísland",
      capital: "ريكيافيك",
      en_capital: "Reykjavik",
      alpha_2: "IS",
      alpha_3: "ISL",
      phone_code: "354",
      nationality_en: "Icelandic",
      nationality_ar: "أيسلندا"
    },
    {
      name_ar: "الهند",
      name_en: "India",
      native_name: "भारत",
      capital: "نيودلهي",
      en_capital: "New Delhi",
      alpha_2: "IN",
      alpha_3: "IND",
      phone_code: "91",
      nationality_en: "Indian",
      nationality_ar: "هندي"
    },
    {
      name_ar: "إندونيسيا",
      name_en: "Indonesia",
      native_name: "Indonesia",
      capital: "جاكرتا",
      en_capital: "Jakarta",
      alpha_2: "ID",
      alpha_3: "IDN",
      phone_code: "62",
      nationality_en: "Indonesian",
      nationality_ar: "إندونيسي"
    },
    {
      name_ar: "ساحل العاج",
      name_en: "Ivory Coast",
      native_name: "Côte d'Ivoire",
      capital: "ياموسوكرو",
      en_capital: "Yamoussoukro",
      alpha_2: "CI",
      alpha_3: "CIV",
      phone_code: "225",
      nationality_en: "Ivorian",
      nationality_ar: "إفواري"
    },
    {
      name_ar: "إيران",
      name_en: "Iran",
      native_name: "Irān",
      capital: "طهران",
      en_capital: "Tehran",
      alpha_2: "IR",
      alpha_3: "IRN",
      phone_code: "98",
      nationality_en: "Iranian",
      nationality_ar: "إيراني"
    },
    {
      name_ar: "العراق",
      name_en: "Iraq",
      native_name: "العراق",
      capital: "بغداد",
      en_capital: "Baghdad",
      alpha_2: "IQ",
      alpha_3: "IRQ",
      phone_code: "964",
      nationality_en: "Iraqi",
      nationality_ar: "عراقي"
    },
    {
      name_ar: "جمهورية أيرلندا",
      name_en: "Republic of Ireland",
      native_name: "Éire",
      capital: "دبلن",
      en_capital: "Dublin",
      alpha_2: "IE",
      alpha_3: "IRL",
      phone_code: "353",
      nationality_en: "Irish",
      nationality_ar: "إيرلندي"
    },
    {
      name_ar: "مان",
      name_en: "Isle of Man",
      native_name: "Isle of Man",
      capital: "دوغلاس",
      en_capital: "Douglas",
      alpha_2: "IM",
      alpha_3: "IMN",
      phone_code: "44",
      nationality_en: "Manx",
      nationality_ar: "مان"
    },
    {
      name_ar: "إسرائيل",
      name_en: "Israel",
      native_name: "יִשְׂרָאֵל",
      capital: "تل أبيب",
      en_capital: "Tel Aviv",
      alpha_2: "IL",
      alpha_3: "ISR",
      phone_code: "972",
      nationality_en: "Israeli",
      nationality_ar: "إسرائيل"
    },
    {
      name_ar: "إيطاليا",
      name_en: "Italy",
      native_name: "Italia",
      capital: "روما",
      en_capital: "Rome",
      alpha_2: "IT",
      alpha_3: "ITA",
      phone_code: "39",
      nationality_en: "Italian",
      nationality_ar: "إيطالي"
    },
    {
      name_ar: "جامايكا",
      name_en: "Jamaica",
      native_name: "Jamaica",
      capital: "كينغستون",
      en_capital: "Kingston",
      alpha_2: "JM",
      alpha_3: "JAM",
      phone_code: "1876",
      nationality_en: "Jamaican",
      nationality_ar: "جامايكي"
    },
    {
      name_ar: "اليابان",
      name_en: "Japan",
      native_name: "日本",
      capital: "طوكيو",
      en_capital: "Tokyo",
      alpha_2: "JP",
      alpha_3: "JPN",
      phone_code: "81",
      nationality_en: "Japanese",
      nationality_ar: "ياباني"
    },
    {
      name_ar: "الأردن",
      name_en: "Jordan",
      native_name: "الأردن",
      capital: "عمان",
      en_capital: "Amman",
      alpha_2: "JO",
      alpha_3: "JOR",
      phone_code: "962",
      nationality_en: "Jordanian",
      nationality_ar: "أردني"
    },
    {
      name_ar: "كازاخستان",
      name_en: "Kazakhstan",
      native_name: "Қазақстан",
      capital: "أستانا",
      en_capital: "Astana",
      alpha_2: "KZ",
      alpha_3: "KAZ",
      phone_code: "76",
      nationality_en: "Kazakhstani",
      nationality_ar: "كازاخستاني"
    },
    {
      name_ar: "كينيا",
      name_en: "Kenya",
      native_name: "Kenya",
      capital: "نيروبي",
      en_capital: "Nairobi",
      alpha_2: "KE",
      alpha_3: "KEN",
      phone_code: "254",
      nationality_en: "Kenyan",
      nationality_ar: "كيني"
    },
    {
      name_ar: "كيريباس",
      name_en: "Kiribati",
      native_name: "Kiribati",
      capital: "جنوب تاراوا",
      en_capital: "South Tarawa",
      alpha_2: "KI",
      alpha_3: "KIR",
      phone_code: "686",
      nationality_en: "I-Kiribati",
      nationality_ar: "كيريباس"
    },
    {
      name_ar: "الكويت",
      name_en: "Kuwait",
      native_name: "الكويت",
      capital: "مدينة الكويت",
      en_capital: "Kuwait City",
      alpha_2: "KW",
      alpha_3: "KWT",
      phone_code: "965",
      nationality_en: "Kuwaiti",
      nationality_ar: "كويتي"
    },
    {
      name_ar: "قيرغيزستان",
      name_en: "Kyrgyzstan",
      native_name: "Кыргызстан",
      capital: "بيشكيك",
      en_capital: "Bishkek",
      alpha_2: "KG",
      alpha_3: "KGZ",
      phone_code: "996",
      nationality_en: "Kyrgyzstani",
      nationality_ar: "قيرغيزستان"
    },
    {
      name_ar: "لاوس",
      name_en: "Laos",
      native_name: "ສປປລາວ",
      capital: "فينتيان",
      en_capital: "Vientiane",
      alpha_2: "LA",
      alpha_3: "LAO",
      phone_code: "856",
      nationality_en: "Lao",
      nationality_ar: "لاوس"
    },
    {
      name_ar: "لاتفيا",
      name_en: "Latvia",
      native_name: "Latvija",
      capital: "ريغا",
      en_capital: "Riga",
      alpha_2: "LV",
      alpha_3: "LVA",
      phone_code: "371",
      nationality_en: "Latvian",
      nationality_ar: "لاتفي"
    },
    {
      name_ar: "لبنان",
      name_en: "Lebanon",
      native_name: "لبنان",
      capital: "بيروت",
      en_capital: "Beirut",
      alpha_2: "LB",
      alpha_3: "LBN",
      phone_code: "961",
      nationality_en: "Lebanese",
      nationality_ar: "لبناني"
    },
    {
      name_ar: "ليسوتو",
      name_en: "Lesotho",
      native_name: "Lesotho",
      capital: "ماسيرو",
      en_capital: "Maseru",
      alpha_2: "LS",
      alpha_3: "LSO",
      phone_code: "266",
      nationality_en: "Basotho",
      nationality_ar: "ليسوتو"
    },
    {
      name_ar: "ليبريا",
      name_en: "Liberia",
      native_name: "Liberia",
      capital: "مونروفيا",
      en_capital: "Monrovia",
      alpha_2: "LR",
      alpha_3: "LBR",
      phone_code: "231",
      nationality_en: "Liberian",
      nationality_ar: "ليبيري"
    },
    {
      name_ar: "ليبيا",
      name_en: "Libya",
      native_name: "‏ليبيا",
      capital: "طرابلس",
      en_capital: "Tripoli",
      alpha_2: "LY",
      alpha_3: "LBY",
      phone_code: "218",
      nationality_en: "Libyan",
      nationality_ar: "ليبي"
    },
    {
      name_ar: "ليختنشتاين",
      name_en: "Liechtenstein",
      native_name: "Liechtenstein",
      capital: "فادوز",
      en_capital: "Vaduz",
      alpha_2: "LI",
      alpha_3: "LIE",
      phone_code: "423",
      nationality_en: "Liechtenstein",
      nationality_ar: "ليختنشتاين"
    },
    {
      name_ar: "ليتوانيا",
      name_en: "Lithuania",
      native_name: "Lietuva",
      capital: "فيلنيوس",
      en_capital: "Vilnius",
      alpha_2: "LT",
      alpha_3: "LTU",
      phone_code: "370",
      nationality_en: "Lithuanian",
      nationality_ar: "لتواني"
    },
    {
      name_ar: "لكسمبرغ",
      name_en: "Luxembourg",
      native_name: "Luxembourg",
      capital: "لكسمبرغ",
      en_capital: "Luxembourg",
      alpha_2: "LU",
      alpha_3: "LUX",
      phone_code: "352",
      nationality_en: "Luxembourg",
      nationality_ar: "لكسمبرغ"
    },
    {
      name_ar: "جمهورية مقدونيا",
      name_en: "Republic of Macedonia",
      native_name: "Македонија",
      capital: "سكوبي",
      en_capital: "Skopje",
      alpha_2: "MK",
      alpha_3: "MKD",
      phone_code: "389",
      nationality_en: "Macedonian",
      nationality_ar: "مقدوني"
    },
    {
      name_ar: "مدغشقر",
      name_en: "Madagascar",
      native_name: "Madagasikara",
      capital: "أنتاناناريفو",
      en_capital: "Antananarivo",
      alpha_2: "MG",
      alpha_3: "MDG",
      phone_code: "261",
      nationality_en: "Malagasy",
      nationality_ar: "مدغشقري"
    },
    {
      name_ar: "ملاوي",
      name_en: "Malawi",
      native_name: "Malawi",
      capital: "ليلونغوي",
      en_capital: "Lilongwe",
      alpha_2: "MW",
      alpha_3: "MWI",
      phone_code: "265",
      nationality_en: "Malawian",
      nationality_ar: "مالاوى"
    },
    {
      name_ar: "ماليزيا",
      name_en: "Malaysia",
      native_name: "Malaysia",
      capital: "كوالالمبور",
      en_capital: "Kuala Lumpur",
      alpha_2: "MY",
      alpha_3: "MYS",
      phone_code: "60",
      nationality_en: "Malaysian",
      nationality_ar: "ماليزي"
    },
    {
      name_ar: "جزر المالديف",
      name_en: "Maldives",
      native_name: "Maldives",
      capital: "ماليه",
      en_capital: "Malé",
      alpha_2: "MV",
      alpha_3: "MDV",
      phone_code: "960",
      nationality_en: "Maldivian",
      nationality_ar: "جزر المالديف"
    },
    {
      name_ar: "مالي",
      name_en: "Mali",
      native_name: "Mali",
      capital: "باماكو",
      en_capital: "Bamako",
      alpha_2: "ML",
      alpha_3: "MLI",
      phone_code: "223",
      nationality_en: "Malian",
      nationality_ar: "مالي"
    },
    {
      name_ar: "مالطة",
      name_en: "Malta",
      native_name: "Malta",
      capital: "فاليتا",
      en_capital: "Valletta",
      alpha_2: "MT",
      alpha_3: "MLT",
      phone_code: "356",
      nationality_en: "Maltese",
      nationality_ar: "مالطي"
    },
    {
      name_ar: "جزر مارشال",
      name_en: "Marshall Islands",
      native_name: "M̧ajeļ",
      capital: "ماجورو",
      en_capital: "Majuro",
      alpha_2: "MH",
      alpha_3: "MHL",
      phone_code: "692",
      nationality_en: "Marshallese",
      nationality_ar: "مارشالي"
    },
    {
      name_ar: "مارتينيك",
      name_en: "Martinique",
      native_name: "Martinique",
      capital: "Fort-de-France",
      en_capital: "Fort-de-France",
      alpha_2: "MQ",
      alpha_3: "MTQ",
      phone_code: "596",
      nationality_en: "Martiniquais",
      nationality_ar: "مارتينيك"
    },
    {
      name_ar: "موريتانيا",
      name_en: "Mauritania",
      native_name: "موريتانيا",
      capital: "نواكشوط",
      en_capital: "Nouakchott",
      alpha_2: "MR",
      alpha_3: "MRT",
      phone_code: "222",
      nationality_en: "Mauritanian",
      nationality_ar: "موريتاني"
    },
    {
      name_ar: "موريشيوس",
      name_en: "Mauritius",
      native_name: "Maurice",
      capital: "بورت لويس",
      en_capital: "Port Louis",
      alpha_2: "MU",
      alpha_3: "MUS",
      phone_code: "230",
      nationality_en: "Mauritian",
      nationality_ar: "موريشيوسي"
    },
    {
      name_ar: "مايوت",
      name_en: "Mayotte",
      native_name: "Mayotte",
      capital: "مامودزو",
      en_capital: "Mamoudzou",
      alpha_2: "YT",
      alpha_3: "MYT",
      phone_code: "262",
      nationality_en: "Mahoran",
      nationality_ar: "مايوت"
    },
    {
      name_ar: "المكسيك",
      name_en: "Mexico",
      native_name: "México",
      capital: "مكسيكو سيتي",
      en_capital: "Mexico City",
      alpha_2: "MX",
      alpha_3: "MEX",
      phone_code: "52",
      nationality_en: "Mexican",
      nationality_ar: "مكسيكي"
    },
    {
      name_ar: "ميكرونيزيا (ولايات-الموحدة",
      name_en: "Federated States of Micronesia",
      native_name: "Micronesia",
      capital: "Palikir",
      en_capital: "Palikir",
      alpha_2: "FM",
      alpha_3: "FSM",
      phone_code: "691",
      nationality_en: "Micronesian",
      nationality_ar: "ميكرونيزي"
    },
    {
      name_ar: "مولدوفا",
      name_en: "Moldova",
      native_name: "Moldova",
      capital: "Chișinău",
      en_capital: "Chișinău",
      alpha_2: "MD",
      alpha_3: "MDA",
      phone_code: "373",
      nationality_en: "Moldovan",
      nationality_ar: "مولدوفي"
    },
    {
      name_ar: "موناكو",
      name_en: "Monaco",
      native_name: "Monaco",
      capital: "موناكو",
      en_capital: "Monaco",
      alpha_2: "MC",
      alpha_3: "MCO",
      phone_code: "377",
      nationality_en: "Monégasque",
      nationality_ar: "موناكو"
    },
    {
      name_ar: "منغوليا",
      name_en: "Mongolia",
      native_name: "Монгол улс",
      capital: "أولان باتور",
      en_capital: "Ulan Bator",
      alpha_2: "MN",
      alpha_3: "MNG",
      phone_code: "976",
      nationality_en: "Mongolian",
      nationality_ar: "منغولي"
    },
    {
      name_ar: "الأسود",
      name_en: "Montenegro",
      native_name: "Црна Гора",
      capital: "بودغوريتسا",
      en_capital: "Podgorica",
      alpha_2: "ME",
      alpha_3: "MNE",
      phone_code: "382",
      nationality_en: "Montenegrin",
      nationality_ar: "الأسود"
    },
    {
      name_ar: "مونتسيرات",
      name_en: "Montserrat",
      native_name: "Montserrat",
      capital: "بليموث",
      en_capital: "Plymouth",
      alpha_2: "MS",
      alpha_3: "MSR",
      phone_code: "1664",
      nationality_en: "Montserratian",
      nationality_ar: "مونتسيرات"
    },
    {
      name_ar: "المغرب",
      name_en: "Morocco",
      native_name: "المغرب",
      capital: "الرباط",
      en_capital: "Rabat",
      alpha_2: "MA",
      alpha_3: "MAR",
      phone_code: "212",
      nationality_en: "Moroccan",
      nationality_ar: "مغربي"
    },
    {
      name_ar: "موزامبيق",
      name_en: "Mozambique",
      native_name: "Moçambique",
      capital: "مابوتو",
      en_capital: "Maputo",
      alpha_2: "MZ",
      alpha_3: "MOZ",
      phone_code: "258",
      nationality_en: "Mozambican",
      nationality_ar: "موزمبيقي"
    },
    {
      name_ar: "ميانمار",
      name_en: "Myanmar",
      native_name: "Myanma",
      capital: "نايبيداو",
      en_capital: "Naypyidaw",
      alpha_2: "MM",
      alpha_3: "MMR",
      phone_code: "95",
      nationality_en: "Burmese",
      nationality_ar: "بورمي"
    },
    {
      name_ar: "ناميبيا",
      name_en: "Namibia",
      native_name: "Namibia",
      capital: "ويندهوك",
      en_capital: "Windhoek",
      alpha_2: "NA",
      alpha_3: "NAM",
      phone_code: "264",
      nationality_en: "Namibian",
      nationality_ar: "ناميبي"
    },
    {
      name_ar: "ناورو",
      name_en: "Nauru",
      native_name: "Nauru",
      capital: "يارن",
      en_capital: "Yaren",
      alpha_2: "NR",
      alpha_3: "NRU",
      phone_code: "674",
      nationality_en: "Nauruan",
      nationality_ar: "ناورو"
    },
    {
      name_ar: "نيبال",
      name_en: "Nepal",
      native_name: "नपल",
      capital: "كاتماندو",
      en_capital: "Kathmandu",
      alpha_2: "NP",
      alpha_3: "NPL",
      phone_code: "977",
      nationality_en: "Nepali",
      nationality_ar: "نيبال"
    },
    {
      name_ar: "هولندا",
      name_en: "Netherlands",
      native_name: "Nederland",
      capital: "أمستردام",
      en_capital: "Amsterdam",
      alpha_2: "NL",
      alpha_3: "NLD",
      phone_code: "31",
      nationality_en: "Dutch",
      nationality_ar: "هولندي"
    },
    {
      name_ar: "كاليدونيا الجديدة",
      name_en: "New Caledonia",
      native_name: "Nouvelle-Calédonie",
      capital: "نوميا",
      en_capital: "Nouméa",
      alpha_2: "NC",
      alpha_3: "NCL",
      phone_code: "687",
      nationality_en: "New Caledonian",
      nationality_ar: "كاليدونيا الجديدة"
    },
    {
      name_ar: "نيوزيلندا",
      name_en: "New Zealand",
      native_name: "New Zealand",
      capital: "ولينغتون",
      en_capital: "Wellington",
      alpha_2: "NZ",
      alpha_3: "NZL",
      phone_code: "64",
      nationality_en: "New Zealand",
      nationality_ar: "نيوزيلندا"
    },
    {
      name_ar: "نيكاراغوا",
      name_en: "Nicaragua",
      native_name: "Nicaragua",
      capital: "ماناغوا",
      en_capital: "Managua",
      alpha_2: "NI",
      alpha_3: "NIC",
      phone_code: "505",
      nationality_en: "Nicaraguan",
      nationality_ar: "نيكاراغوا"
    },
    {
      name_ar: "النيجر",
      name_en: "Niger",
      native_name: "Niger",
      capital: "نيامي",
      en_capital: "Niamey",
      alpha_2: "NE",
      alpha_3: "NER",
      phone_code: "227",
      nationality_en: "Nigerien",
      nationality_ar: "نيجري"
    },
    {
      name_ar: "نيجيريا",
      name_en: "Nigeria",
      native_name: "Nigeria",
      capital: "أبوجا",
      en_capital: "Abuja",
      alpha_2: "NG",
      alpha_3: "NGA",
      phone_code: "234",
      nationality_en: "Nigerian",
      nationality_ar: "نيجيريا"
    },
    {
      name_ar: "نيوي",
      name_en: "Niue",
      native_name: "Niuē",
      capital: "العوفي",
      en_capital: "Alofi",
      alpha_2: "NU",
      alpha_3: "NIU",
      phone_code: "683",
      nationality_en: "Niuean",
      nationality_ar: "نيوي"
    },
    {
      name_ar: "جزيرة نورفولك",
      name_en: "Norfolk Island",
      native_name: "Norfolk Island",
      capital: "كينغستون",
      en_capital: "Kingston",
      alpha_2: "NF",
      alpha_3: "NFK",
      phone_code: "672",
      nationality_en: "Norfolk Island",
      nationality_ar: "جزيرة نورفولك"
    },
    {
      name_ar: "كوريا الشمالية",
      name_en: "North Korea",
      native_name: "북한",
      capital: "بيونغ يانغ",
      en_capital: "Pyongyang",
      alpha_2: "KP",
      alpha_3: "PRK",
      phone_code: "850",
      nationality_en: "North Korean",
      nationality_ar: "كوري شمالي"
    },
    {
      name_ar: "جزر ماريانا الشمالية",
      name_en: "Northern Mariana Islands",
      native_name: "Northern Mariana Islands",
      capital: "سايبان",
      en_capital: "Saipan",
      alpha_2: "MP",
      alpha_3: "MNP",
      phone_code: "1670",
      nationality_en: "Northern Marianan",
      nationality_ar: "جزر ماريانا الشمالية"
    },
    {
      name_ar: "النرويج",
      name_en: "Norway",
      native_name: "Norge",
      capital: "أوسلو",
      en_capital: "Oslo",
      alpha_2: "NO",
      alpha_3: "NOR",
      phone_code: "47",
      nationality_en: "Norwegian",
      nationality_ar: "نرويجي"
    },
    {
      name_ar: "عمان",
      name_en: "Oman",
      native_name: "عمان",
      capital: "مسقط",
      en_capital: "Muscat",
      alpha_2: "OM",
      alpha_3: "OMN",
      phone_code: "968",
      nationality_en: "Omani",
      nationality_ar: "عماني"
    },
    {
      name_ar: "باكستان",
      name_en: "Pakistan",
      native_name: "Pakistan",
      capital: "إسلام أباد",
      en_capital: "Islamabad",
      alpha_2: "PK",
      alpha_3: "PAK",
      phone_code: "92",
      nationality_en: "Pakistani",
      nationality_ar: "باكستاني"
    },
    {
      name_ar: "بالاو",
      name_en: "Palau",
      native_name: "Palau",
      capital: "Ngerulmud",
      en_capital: "Ngerulmud",
      alpha_2: "PW",
      alpha_3: "PLW",
      phone_code: "680",
      nationality_en: "Palauan",
      nationality_ar: "بالاوي"
    },
    {
      name_ar: "فلسطين",
      name_en: "Palestine",
      native_name: "فلسطين",
      capital: "القدس",
      en_capital: "Jerusalem",
      alpha_2: "PS",
      alpha_3: "PSE",
      phone_code: "970",
      nationality_en: "Palestinian",
      nationality_ar: "فلسطيني"
    },
    {
      name_ar: "بنما",
      name_en: "Panama",
      native_name: "Panamá",
      capital: "مدينة بنما",
      en_capital: "Panama City",
      alpha_2: "PA",
      alpha_3: "PAN",
      phone_code: "507",
      nationality_en: "Panamanian",
      nationality_ar: "بنمي"
    },
    {
      name_ar: "بابوا غينيا الجديدة",
      name_en: "Papua New Guinea",
      native_name: "Papua Niugini",
      capital: "بورت مورسبي",
      en_capital: "Port Moresby",
      alpha_2: "PG",
      alpha_3: "PNG",
      phone_code: "675",
      nationality_en: "Papua New Guinean",
      nationality_ar: "بابوا غينيا الجديدة"
    },
    {
      name_ar: "باراغواي",
      name_en: "Paraguay",
      native_name: "Paraguay",
      capital: "أسونسيون",
      en_capital: "Asunción",
      alpha_2: "PY",
      alpha_3: "PRY",
      phone_code: "595",
      nationality_en: "Paraguayan",
      nationality_ar: "باراغواياني"
    },
    {
      name_ar: "بيرو",
      name_en: "Peru",
      native_name: "Perú",
      capital: "ليما",
      en_capital: "Lima",
      alpha_2: "PE",
      alpha_3: "PER",
      phone_code: "51",
      nationality_en: "Peruvian",
      nationality_ar: "بيروفي"
    },
    {
      name_ar: "الفلبين",
      name_en: "Philippines",
      native_name: "Pilipinas",
      capital: "مانيلا",
      en_capital: "Manila",
      alpha_2: "PH",
      alpha_3: "PHL",
      phone_code: "63",
      nationality_en: "Philippine",
      nationality_ar: "الفلبين"
    },
    {
      name_ar: "جزر بيتكيرن",
      name_en: "Pitcairn Islands",
      native_name: "Pitcairn Islands",
      capital: "آدمزتاون",
      en_capital: "Adamstown",
      alpha_2: "PN",
      alpha_3: "PCN",
      phone_code: "64",
      nationality_en: "Pitcairn Island",
      nationality_ar: "جزر بيتكيرن"
    },
    {
      name_ar: "بولندا",
      name_en: "Poland",
      native_name: "Polska",
      capital: "وارسو",
      en_capital: "Warsaw",
      alpha_2: "PL",
      alpha_3: "POL",
      phone_code: "48",
      nationality_en: "Polish",
      nationality_ar: "بولندي"
    },
    {
      name_ar: "البرتغال",
      name_en: "Portugal",
      native_name: "Portugal",
      capital: "لشبونة",
      en_capital: "Lisbon",
      alpha_2: "PT",
      alpha_3: "PRT",
      phone_code: "351",
      nationality_en: "Portuguese",
      nationality_ar: "برتغالي"
    },
    {
      name_ar: "بورتوريكو",
      name_en: "Puerto Rico",
      native_name: "Puerto Rico",
      capital: "سان خوان",
      en_capital: "San Juan",
      alpha_2: "PR",
      alpha_3: "PRI",
      phone_code: "1787",
      nationality_en: "Puerto Rican",
      nationality_ar: "بورتوريكو"
    },
    {
      name_ar: "قطر",
      name_en: "Qatar",
      native_name: "قطر",
      capital: "الدوحة",
      en_capital: "Doha",
      alpha_2: "QA",
      alpha_3: "QAT",
      phone_code: "974",
      nationality_en: "Qatari",
      nationality_ar: "قطري"
    },
    {
      name_ar: "جمهورية كوسوفو",
      name_en: "Republic of Kosovo",
      native_name: "Republika e Kosovës",
      capital: "بريشتينا",
      en_capital: "Pristina",
      alpha_2: "XK",
      alpha_3: "KOS",
      phone_code: "383",
      nationality_en: "XXX",
      nationality_ar: "جمهورية كوسوفو"
    },
    {
      name_ar: "ريونيون",
      name_en: "Réunion",
      native_name: "La Réunion",
      capital: "Saint-Denis",
      en_capital: "Saint-Denis",
      alpha_2: "RE",
      alpha_3: "REU",
      phone_code: "262",
      nationality_en: "Réunionese",
      nationality_ar: "ريونيون"
    },
    {
      name_ar: "رومانيا",
      name_en: "Romania",
      native_name: "România",
      capital: "بوخارست",
      en_capital: "Bucharest",
      alpha_2: "RO",
      alpha_3: "ROU",
      phone_code: "40",
      nationality_en: "Romanian",
      nationality_ar: "روماني"
    },
    {
      name_ar: "روسيا",
      name_en: "Russia",
      native_name: "Россия",
      capital: "موسكو",
      en_capital: "Moscow",
      alpha_2: "RU",
      alpha_3: "RUS",
      phone_code: "7",
      nationality_en: "Russian",
      nationality_ar: "روسي"
    },
    {
      name_ar: "رواندا",
      name_en: "Rwanda",
      native_name: "Rwanda",
      capital: "كيغالي",
      en_capital: "Kigali",
      alpha_2: "RW",
      alpha_3: "RWA",
      phone_code: "250",
      nationality_en: "Rwandan",
      nationality_ar: "رواندي"
    },
    {
      name_ar: "سانت بارتيليمي",
      name_en: "Saint Barthélemy",
      native_name: "Saint-Barthélemy",
      capital: "جوستافيا",
      en_capital: "Gustavia",
      alpha_2: "BL",
      alpha_3: "BLM",
      phone_code: "590",
      nationality_en: "Barthélemois",
      nationality_ar: "سانت بارتيليمي"
    },
    {
      name_ar: "سانت كيتس ونيفيس",
      name_en: "Saint Kitts and Nevis",
      native_name: "Saint Kitts and Nevis",
      capital: "باستير",
      en_capital: "Basseterre",
      alpha_2: "KN",
      alpha_3: "KNA",
      phone_code: "1869",
      nationality_en: "Kittitian or Nevisian",
      nationality_ar: "سانت كيتس ونيفيس"
    },
    {
      name_ar: "سانت لوسيا",
      name_en: "Saint Lucia",
      native_name: "Saint Lucia",
      capital: "كاستريس",
      en_capital: "Castries",
      alpha_2: "LC",
      alpha_3: "LCA",
      phone_code: "1758",
      nationality_en: "Saint Lucian",
      nationality_ar: "لوسياني"
    },
    {
      name_ar: "سانت مارتن",
      name_en: "Saint Martin",
      native_name: "Saint-Martin",
      capital: "ماريغوت",
      en_capital: "Marigot",
      alpha_2: "MF",
      alpha_3: "MAF",
      phone_code: "590",
      nationality_en: "Saint-Martinoise",
      nationality_ar: "سانت مارتن"
    },
    {
      name_ar: "سانت بيير وميكلون",
      name_en: "Saint Pierre and Miquelon",
      native_name: "Saint-Pierre-et-Miquelon",
      capital: "Saint-Pierre",
      en_capital: "Saint-Pierre",
      alpha_2: "PM",
      alpha_3: "SPM",
      phone_code: "508",
      nationality_en: "Saint-Pierrais or Miquelonnais",
      nationality_ar: "سانت بيير وميكلون"
    },
    {
      name_ar: "سانت فنسنت وجزر غرينادين",
      name_en: "Saint Vincent and the Grenadines",
      native_name: "Saint Vincent and the Grenadines",
      capital: "كينغستاون",
      en_capital: "Kingstown",
      alpha_2: "VC",
      alpha_3: "VCT",
      phone_code: "1784",
      nationality_en: "Saint Vincentian",
      nationality_ar: "سانت فنسنت وجزر غرينادين"
    },
    {
      name_ar: "ساموا",
      name_en: "Samoa",
      native_name: "Samoa",
      capital: "آبيا",
      en_capital: "Apia",
      alpha_2: "WS",
      alpha_3: "WSM",
      phone_code: "685",
      nationality_en: "Samoan",
      nationality_ar: "ساموايان"
    },
    {
      name_ar: "سان مارينو",
      name_en: "San Marino",
      native_name: "San Marino",
      capital: "مدينة سان مارينو",
      en_capital: "City of San Marino",
      alpha_2: "SM",
      alpha_3: "SMR",
      phone_code: "378",
      nationality_en: "Sammarinese",
      nationality_ar: "سان مارينو"
    },
    {
      name_ar: "ساو تومي وبرينسيبي",
      name_en: "São Tomé and Príncipe",
      native_name: "São Tomé e Príncipe",
      capital: "ساو تومي",
      en_capital: "São Tomé",
      alpha_2: "ST",
      alpha_3: "STP",
      phone_code: "239",
      nationality_en: "São Toméan",
      nationality_ar: "ساو تومي وبرينسيبي"
    },
    {
      name_ar: "المملكة العربية السعودية",
      name_en: "Saudi Arabia",
      native_name: "العربية السعودية",
      capital: "الرياض",
      en_capital: "Riyadh",
      alpha_2: "SA",
      alpha_3: "SAU",
      phone_code: "966",
      nationality_en: "Saudi",
      nationality_ar: "سعودي"
    },
    {
      name_ar: "السنغال",
      name_en: "Senegal",
      native_name: "Sénégal",
      capital: "داكار",
      en_capital: "Dakar",
      alpha_2: "SN",
      alpha_3: "SEN",
      phone_code: "221",
      nationality_en: "Senegalese",
      nationality_ar: "سنغالي"
    },
    {
      name_ar: "صربيا",
      name_en: "Serbia",
      native_name: "Србија",
      capital: "بلغراد",
      en_capital: "Belgrade",
      alpha_2: "RS",
      alpha_3: "SRB",
      phone_code: "381",
      nationality_en: "Serbian",
      nationality_ar: "صربي"
    },
    {
      name_ar: "سيشيل",
      name_en: "Seychelles",
      native_name: "Seychelles",
      capital: "فيكتوريا",
      en_capital: "Victoria",
      alpha_2: "SC",
      alpha_3: "SYC",
      phone_code: "248",
      nationality_en: "Seychellois",
      nationality_ar: "سيشلي"
    },
    {
      name_ar: "سيراليون",
      name_en: "Sierra Leone",
      native_name: "Sierra Leone",
      capital: "فريتاون",
      en_capital: "Freetown",
      alpha_2: "SL",
      alpha_3: "SLE",
      phone_code: "232",
      nationality_en: "Sierra Leonean",
      nationality_ar: "سيرا ليوني"
    },
    {
      name_ar: "سنغافورة",
      name_en: "Singapore",
      native_name: "Singapore",
      capital: "سنغافورة",
      en_capital: "Singapore",
      alpha_2: "SG",
      alpha_3: "SGP",
      phone_code: "65",
      nationality_en: "Singaporean",
      nationality_ar: "سنغافوري"
    },
    {
      name_ar: "سان مارتن",
      name_en: "Sint Maarten",
      native_name: "Sint Maarten",
      capital: "فلسبرغ",
      en_capital: "Philipsburg",
      alpha_2: "SX",
      alpha_3: "SXM",
      phone_code: "1721",
      nationality_en: "Sint Maarten",
      nationality_ar: "سان مارتن"
    },
    {
      name_ar: "سلوفاكيا",
      name_en: "Slovakia",
      native_name: "Slovensko",
      capital: "براتيسلافا",
      en_capital: "Bratislava",
      alpha_2: "SK",
      alpha_3: "SVK",
      phone_code: "421",
      nationality_en: "Slovak",
      nationality_ar: "سلوفاكيا"
    },
    {
      name_ar: "سلوفينيا",
      name_en: "Slovenia",
      native_name: "Slovenija",
      capital: "ليوبليانا",
      en_capital: "Ljubljana",
      alpha_2: "SI",
      alpha_3: "SVN",
      phone_code: "386",
      nationality_en: "Slovenian",
      nationality_ar: "سلوفيني"
    },
    {
      name_ar: "جزر سليمان",
      name_en: "Solomon Islands",
      native_name: "Solomon Islands",
      capital: "هونيارا",
      en_capital: "Honiara",
      alpha_2: "SB",
      alpha_3: "SLB",
      phone_code: "677",
      nationality_en: "Solomon Island",
      nationality_ar: "جزر سليمان"
    },
    {
      name_ar: "الصومال",
      name_en: "Somalia",
      native_name: "Soomaaliya",
      capital: "مقديشو",
      en_capital: "Mogadishu",
      alpha_2: "SO",
      alpha_3: "SOM",
      phone_code: "252",
      nationality_en: "Somali",
      nationality_ar: "صومالي"
    },
    {
      name_ar: "جنوب أفريقيا",
      name_en: "South Africa",
      native_name: "South Africa",
      capital: "بريتوريا",
      en_capital: "Pretoria",
      alpha_2: "ZA",
      alpha_3: "ZAF",
      phone_code: "27",
      nationality_en: "South African",
      nationality_ar: "جنوب افريقيي"
    },
    {
      name_ar: "جورجيا الجنوبية",
      name_en: "South Georgia",
      native_name: "South Georgia",
      capital: "الملك إدوارد نقطة",
      en_capital: "King Edward Point",
      alpha_2: "GS",
      alpha_3: "SGS",
      phone_code: "500",
      nationality_en: "South Georgia or South Sandwich Islands",
      nationality_ar: "جورجيا الجنوبية"
    },
    {
      name_ar: "كوريا الجنوبية",
      name_en: "South Korea",
      native_name: "대한민국",
      capital: "سيول",
      en_capital: "Seoul",
      alpha_2: "KR",
      alpha_3: "KOR",
      phone_code: "82",
      nationality_en: "South Korean",
      nationality_ar: "كوري جنوبي"
    },
    {
      name_ar: "جنوب السودان",
      name_en: "South Sudan",
      native_name: "South Sudan",
      capital: "جوبا",
      en_capital: "Juba",
      alpha_2: "SS",
      alpha_3: "SSD",
      phone_code: "211",
      nationality_en: "South Sudanese",
      nationality_ar: "جنوب السودان"
    },
    {
      name_ar: "إسبانيا",
      name_en: "Spain",
      native_name: "España",
      capital: "مدريد",
      en_capital: "Madrid",
      alpha_2: "ES",
      alpha_3: "ESP",
      phone_code: "34",
      nationality_en: "Spanish",
      nationality_ar: "إسباني"
    },
    {
      name_ar: "سري لانكا",
      name_en: "Sri Lanka",
      native_name: "śrī laṃkāva",
      capital: "كولومبو",
      en_capital: "Colombo",
      alpha_2: "LK",
      alpha_3: "LKA",
      phone_code: "94",
      nationality_en: "Sri Lankan",
      nationality_ar: "سري لانكي"
    },
    {
      name_ar: "السودان",
      name_en: "Sudan",
      native_name: "السودان",
      capital: "الخرطوم",
      en_capital: "Khartoum",
      alpha_2: "SD",
      alpha_3: "SDN",
      phone_code: "249",
      nationality_en: "Sudanese",
      nationality_ar: "سوداني"
    },
    {
      name_ar: "سورينام",
      name_en: "Suriname",
      native_name: "Suriname",
      capital: "باراماريبو",
      en_capital: "Paramaribo",
      alpha_2: "SR",
      alpha_3: "SUR",
      phone_code: "597",
      nationality_en: "Surinamese",
      nationality_ar: "سورينام"
    },
    {
      name_ar: "سفالبارد وجان مايان",
      name_en: "Svalbard and Jan Mayen",
      native_name: "Svalbard og Jan Mayen",
      capital: "لونجييربن",
      en_capital: "Longyearbyen",
      alpha_2: "SJ",
      alpha_3: "SJM",
      phone_code: "4779",
      nationality_en: "Svalbard",
      nationality_ar: "سفالبارد وجان مايان"
    },
    {
      name_ar: "سوازيلند",
      name_en: "Swaziland",
      native_name: "Swaziland",
      capital: "Lobamba",
      en_capital: "Lobamba",
      alpha_2: "SZ",
      alpha_3: "SWZ",
      phone_code: "268",
      nationality_en: "Swazi",
      nationality_ar: "سوازي"
    },
    {
      name_ar: "السويد",
      name_en: "Sweden",
      native_name: "Sverige",
      capital: "ستوكهولم",
      en_capital: "Stockholm",
      alpha_2: "SE",
      alpha_3: "SWE",
      phone_code: "46",
      nationality_en: "Swedish",
      nationality_ar: "سويدي"
    },
    {
      name_ar: "سويسرا",
      name_en: "Switzerland",
      native_name: "Schweiz",
      capital: "برن",
      en_capital: "Bern",
      alpha_2: "CH",
      alpha_3: "CHE",
      phone_code: "41",
      nationality_en: "Swiss",
      nationality_ar: "سويسري"
    },
    {
      name_ar: "سوريا",
      name_en: "Syria",
      native_name: "سوريا",
      capital: "دمشق",
      en_capital: "Damascus",
      alpha_2: "SY",
      alpha_3: "SYR",
      phone_code: "963",
      nationality_en: "Syrian",
      nationality_ar: "سوري"
    },
    {
      name_ar: "تايوان",
      name_en: "Taiwan",
      native_name: "臺灣",
      capital: "تايبيه",
      en_capital: "Taipei",
      alpha_2: "TW",
      alpha_3: "TWN",
      phone_code: "886",
      nationality_en: "Chinese",
      nationality_ar: "صينى"
    },
    {
      name_ar: "طاجيكستان",
      name_en: "Tajikistan",
      native_name: "Тоҷикистон",
      capital: "دوشانبي",
      en_capital: "Dushanbe",
      alpha_2: "TJ",
      alpha_3: "TJK",
      phone_code: "992",
      nationality_en: "Tajikistani",
      nationality_ar: "طاجيكستان"
    },
    {
      name_ar: "تنزانيا",
      name_en: "Tanzania",
      native_name: "Tanzania",
      capital: "دودوما",
      en_capital: "Dodoma",
      alpha_2: "TZ",
      alpha_3: "TZA",
      phone_code: "255",
      nationality_en: "Tanzanian",
      nationality_ar: "تنزاني"
    },
    {
      name_ar: "تايلند",
      name_en: "Thailand",
      native_name: "ประเทศไทย",
      capital: "بانكوك",
      en_capital: "Bangkok",
      alpha_2: "TH",
      alpha_3: "THA",
      phone_code: "66",
      nationality_en: "Thai",
      nationality_ar: "التايلاندي"
    },
    {
      name_ar: "تيمور الشرقية",
      name_en: "East Timor",
      native_name: "Timor-Leste",
      capital: "ديلي",
      en_capital: "Dili",
      alpha_2: "TL",
      alpha_3: "TLS",
      phone_code: "670",
      nationality_en: "Timorese",
      nationality_ar: "تيمور الشرقية"
    },
    {
      name_ar: "توغو",
      name_en: "Togo",
      native_name: "Togo",
      capital: "لومي",
      en_capital: "Lomé",
      alpha_2: "TG",
      alpha_3: "TGO",
      phone_code: "228",
      nationality_en: "Togolese",
      nationality_ar: "توغواني"
    },
    {
      name_ar: "توكيلاو",
      name_en: "Tokelau",
      native_name: "Tokelau",
      capital: "فاكاوفو",
      en_capital: "Fakaofo",
      alpha_2: "TK",
      alpha_3: "TKL",
      phone_code: "690",
      nationality_en: "Tokelauan",
      nationality_ar: "توكيلاو"
    },
    {
      name_ar: "تونغا",
      name_en: "Tonga",
      native_name: "Tonga",
      capital: "Nuku'alofa",
      en_capital: "Nuku'alofa",
      alpha_2: "TO",
      alpha_3: "TON",
      phone_code: "676",
      nationality_en: "Tongan",
      nationality_ar: "تونجاني"
    },
    {
      name_ar: "ترينيداد وتوباغو",
      name_en: "Trinidad and Tobago",
      native_name: "Trinidad and Tobago",
      capital: "ميناء أسبانيا",
      en_capital: "Port of Spain",
      alpha_2: "TT",
      alpha_3: "TTO",
      phone_code: "1868",
      nationality_en: "Trinidadian or Tobagonian",
      nationality_ar: "ترينيدادي أو توباغوني"
    },
    {
      name_ar: "تونس",
      name_en: "Tunisia",
      native_name: "تونس",
      capital: "تونس",
      en_capital: "Tunis",
      alpha_2: "TN",
      alpha_3: "TUN",
      phone_code: "216",
      nationality_en: "Tunisian",
      nationality_ar: "تونسي"
    },
    {
      name_ar: "تركيا",
      name_en: "Turkey",
      native_name: "Türkiye",
      capital: "أنقرة",
      en_capital: "Ankara",
      alpha_2: "TR",
      alpha_3: "TUR",
      phone_code: "90",
      nationality_en: "Turkish",
      nationality_ar: "تركي"
    },
    {
      name_ar: "تركمانستان",
      name_en: "Turkmenistan",
      native_name: "Türkmenistan",
      capital: "عشق أباد",
      en_capital: "Ashgabat",
      alpha_2: "TM",
      alpha_3: "TKM",
      phone_code: "993",
      nationality_en: "Turkmen",
      nationality_ar: "تركمانستان"
    },
    {
      name_ar: "جزر تركس وكايكوس",
      name_en: "Turks and Caicos Islands",
      native_name: "Turks and Caicos Islands",
      capital: "كوكبرن تاون",
      en_capital: "Cockburn Town",
      alpha_2: "TC",
      alpha_3: "TCA",
      phone_code: "1649",
      nationality_en: "Turks and Caicos Island",
      nationality_ar: "جزر تركس وكايكوس"
    },
    {
      name_ar: "توفالو",
      name_en: "Tuvalu",
      native_name: "Tuvalu",
      capital: "فونافوتي",
      en_capital: "Funafuti",
      alpha_2: "TV",
      alpha_3: "TUV",
      phone_code: "688",
      nationality_en: "Tuvaluan",
      nationality_ar: "توفالي"
    },
    {
      name_ar: "أوغندا",
      name_en: "Uganda",
      native_name: "Uganda",
      capital: "كمبالا",
      en_capital: "Kampala",
      alpha_2: "UG",
      alpha_3: "UGA",
      phone_code: "256",
      nationality_en: "Ugandan",
      nationality_ar: "أوغندي"
    },
    {
      name_ar: "أوكرانيا",
      name_en: "Ukraine",
      native_name: "Україна",
      capital: "كييف",
      en_capital: "Kiev",
      alpha_2: "UA",
      alpha_3: "UKR",
      phone_code: "380",
      nationality_en: "Ukrainian",
      nationality_ar: "أوكراني"
    },
    {
      name_ar: "الإمارات العربية المتحدة",
      name_en: "United Arab Emirates",
      native_name: "دولة الإمارات العربية المتحدة",
      capital: "أبو ظبي",
      en_capital: "Abu Dhabi",
      alpha_2: "AE",
      alpha_3: "ARE",
      phone_code: "971",
      nationality_en: "Emirati",
      nationality_ar: "الإمارات العربية المتحدة"
    },
    {
      name_ar: "المملكة المتحدة",
      name_en: "United Kingdom",
      native_name: "United Kingdom",
      capital: "لندن",
      en_capital: "London",
      alpha_2: "GB",
      alpha_3: "GBR",
      phone_code: "44",
      nationality_en: "British",
      nationality_ar: "بريطاني"
    },
    {
      name_ar: "الولايات المتحدة",
      name_en: "United States",
      native_name: "United States",
      capital: "واشنطن العاصمة",
      en_capital: "Washington D.C.",
      alpha_2: "US",
      alpha_3: "USA",
      phone_code: "1",
      nationality_en: "American",
      nationality_ar: "أمريكي"
    },
    {
      name_ar: "أوروغواي",
      name_en: "Uruguay",
      native_name: "Uruguay",
      capital: "مونتيفيديو",
      en_capital: "Montevideo",
      alpha_2: "UY",
      alpha_3: "URY",
      phone_code: "598",
      nationality_en: "Uruguayan",
      nationality_ar: "أوروجواي"
    },
    {
      name_ar: "أوزبكستان",
      name_en: "Uzbekistan",
      native_name: "O‘zbekiston",
      capital: "طشقند",
      en_capital: "Tashkent",
      alpha_2: "UZ",
      alpha_3: "UZB",
      phone_code: "998",
      nationality_en: "Uzbekistani",
      nationality_ar: "أوزبكستاني"
    },
    {
      name_ar: "فانواتو",
      name_en: "Vanuatu",
      native_name: "Vanuatu",
      capital: "بورت فيلا",
      en_capital: "Port Vila",
      alpha_2: "VU",
      alpha_3: "VUT",
      phone_code: "678",
      nationality_en: "Ni-Vanuatu",
      nationality_ar: "ني فانواتي"
    },
    {
      name_ar: "فنزويلا",
      name_en: "Venezuela",
      native_name: "Venezuela",
      capital: "كاراكاس",
      en_capital: "Caracas",
      alpha_2: "VE",
      alpha_3: "VEN",
      phone_code: "58",
      nationality_en: "Venezuelan",
      nationality_ar: "فنزويلي"
    },
    {
      name_ar: "فيتنام",
      name_en: "Vietnam",
      native_name: "Việt Nam",
      capital: "هانوي",
      en_capital: "Hanoi",
      alpha_2: "VN",
      alpha_3: "VNM",
      phone_code: "84",
      nationality_en: "Vietnamese",
      nationality_ar: "فيتنامي"
    },
    {
      name_ar: "واليس وفوتونا",
      name_en: "Wallis and Futuna",
      native_name: "Wallis et Futuna",
      capital: "ماتايوتو",
      en_capital: "Mata-Utu",
      alpha_2: "WF",
      alpha_3: "WLF",
      phone_code: "681",
      nationality_en: "Wallis and Futuna",
      nationality_ar: "واليس وفوتونا"
    },
    {
      name_ar: "الصحراء الغربية",
      name_en: "Western Sahara",
      native_name: "الصحراء الغربية",
      capital: "العيون",
      en_capital: "El Aaiún",
      alpha_2: "EH",
      alpha_3: "ESH",
      phone_code: "212",
      nationality_en: "Sahrawi",
      nationality_ar: "الصحراء الغربية"
    },
    {
      name_ar: "اليمن",
      name_en: "Yemen",
      native_name: "اليَمَن",
      capital: "صنعاء",
      en_capital: "Sana'a",
      alpha_2: "YE",
      alpha_3: "YEM",
      phone_code: "967",
      nationality_en: "Yemeni",
      nationality_ar: "اليمن"
    },
    {
      name_ar: "زامبيا",
      name_en: "Zambia",
      native_name: "Zambia",
      capital: "لوساكا",
      en_capital: "Lusaka",
      alpha_2: "ZM",
      alpha_3: "ZMB",
      phone_code: "260",
      nationality_en: "Zambian",
      nationality_ar: "زامبي"
    },
    {
      name_ar: "زمبابوي",
      name_en: "Zimbabwe",
      native_name: "Zimbabwe",
      capital: "هراري",
      en_capital: "Harare",
      alpha_2: "ZW",
      alpha_3: "ZWE",
      phone_code: "263",
      nationality_en: "Zimbabwean",
      nationality_ar: "زيمبابوي"
    }
  ]