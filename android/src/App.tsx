import { useFonts } from '@expo-google-fonts/inter';
import React from 'react';
import { LogBox, View } from 'react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { persistor, store } from '../src/store/store';
import AppNavigator from './navigation/AppNavigator';

const App = () => {
  let [fontsLoaded] = useFonts({
    'Poppins-Regular': require('../assets/fonts/Poppins-Regular.ttf'),
    'Poppins-Bold': require('../assets/fonts/Poppins-Bold.ttf'),
    'Poppins-Medium': require('../assets/fonts/Poppins-Medium.ttf'),
    'Poppins-Italic': require('../assets/fonts/Poppins-SemiBoldItalic.ttf'),
  });
  LogBox.ignoreAllLogs();
  if (fontsLoaded) {
    return (
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <AppNavigator />
        </PersistGate>
      </Provider>
    );
  } else {
    return <View></View>;
  }
};
export default App;
