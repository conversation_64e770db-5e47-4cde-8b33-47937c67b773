{"main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "build": "eas build -p android", "apk": "eas build -p android --profile preview"}, "dependencies": {"@expo-google-fonts/inter": "^0.1.0", "@expo/vector-icons": "14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/masked-view": "0.1.10", "@react-native-community/netinfo": "11.3.1", "@react-navigation/native": "^5.2.3", "amazon-cognito-identity-js": "^5.2.10", "aws-amplify": "^5.3.11", "axios": "^0.21.0", "expo": "~51.0.14", "expo-av": "~14.0.5", "expo-calendar": "~13.0.5", "expo-checkbox": "~3.0.0", "expo-clipboard": "~6.0.3", "expo-constants": "~16.0.2", "expo-device": "~6.0.2", "expo-document-picker": "~12.0.2", "expo-image": "~1.12.12", "expo-image-picker": "~15.0.5", "expo-localization": "~15.0.3", "expo-media-library": "~16.0.3", "expo-notifications": "~0.28.9", "expo-screen-orientation": "~7.0.5", "expo-sharing": "~12.0.1", "expo-status-bar": "~1.12.1", "expo-updates": "~0.25.17", "expo-web-browser": "~13.0.3", "i18n-js": "^3.8.0", "lodash.debounce": "^4.0.8", "moment": "^2.29.4", "patch-package": "^6.5.1", "prop-types": "^15.7.2", "qs": "^6.10.1", "react": "18.2.0", "react-compound-timer": "^1.2.0", "react-dom": "18.2.0", "react-native": "0.74.2", "react-native-autocomplete-input": "^5.4.0", "react-native-calendars": "^1.846.0", "react-native-chart-kit": "^6.7.0", "react-native-dropdown-picker": "^4.0.2", "react-native-echarts-pro": "^1.8.5", "react-native-gesture-handler": "~2.16.1", "react-native-keyboard-aware-scroll-view": "^0.9.3", "react-native-material-buttons": "^0.5.0", "react-native-material-ripple": "^0.9.1", "react-native-mime-types": "^2.3.0", "react-native-modal-datetime-picker": "^10.0.0", "react-native-paper": "^4.4.0", "react-native-reanimated": "~3.10.1", "react-native-responsive-screen": "^1.4.1", "react-native-safe-area-context": "4.10.1", "react-native-screens": "3.31.1", "react-native-svg": "15.2.0", "react-native-swiper": "^1.6.0", "react-native-uuid": "^2.0.1", "react-native-web": "~0.19.6", "react-native-webview": "13.8.6", "react-navigation": "^4.4.3", "react-navigation-stack": "^2.3.11", "react-redux": "^7.2.2", "redux": "^4.0.5", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "shorthash": "^0.0.2", "websocket": "^1.0.34"}, "devDependencies": {"@babel/core": "^7.24.0", "@react-navigation/stack": "^5.12.6", "@types/react": "~18.2.14", "@types/react-redux": "^7.1.23", "@types/websocket": "^1.0.5", "react-native-tab-view": "^2.15.2", "redux-logger": "^3.0.6", "typescript": "~5.3.3"}, "private": true}