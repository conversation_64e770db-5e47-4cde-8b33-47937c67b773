{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "Aktive", "version": "2.1.8", "icon": "./assets/icon.png", "backgroundColor": "#000000", "jsEngine": "hermes", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "updates": {"enabled": false, "fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.knod.Aktive", "config": {"usesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#053C29"}, "package": "com.knod.AktiveApp", "googleServicesFile": "./google-services.json", "versionCode": 55, "permissions": ["READ_CALENDAR", "WRITE_CALENDAR"], "softwareKeyboardLayoutMode": "pan"}, "androidStatusBar": {"barStyle": "light-content", "backgroundColor": "#000000", "hidden": true, "translucent": false}, "web": {"favicon": "./assets/favicon.png"}, "notification": {"iosDisplayInForeground": true}, "plugins": [["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": "true"}], "expo-localization"], "extra": {"STORE_URL": "market://details?id=com.knod.AktiveApp", "APP_VALIDATION_URL": "https://iwsgs3lks3.execute-api.us-east-1.amazonaws.com/versions/", "eas": {"projectId": "438aae58-700b-43d3-ae1a-9236b09a29b5"}}}}