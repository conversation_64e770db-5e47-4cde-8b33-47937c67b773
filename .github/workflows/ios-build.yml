name: IOS cicd
on:
  workflow_dispatch:

jobs:
  build-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 10
      - uses: expo/expo-github-action@v4
        env:
          ACTIONS_ALLOW_UNSECURE_COMMANDS: 'true'
        with:
          expo-username: ${{secrets.EXPO_CLI_USERNAME}}
          expo-password: ${{secrets.EXPO_CLI_PASSWORD}}
          expo-packager: yarn
      - name: Install deps
        run: yarn install
      - name: Build iOS app
        run: expo build:ios
        env:
          EXPO_APPLE_ID: ${{secrets.EXPO_APPLE_ID}}
          EXPO_APPLE_ID_PASSWORD: ${{secrets.EXPO_APPLE_PASSWORD}}
