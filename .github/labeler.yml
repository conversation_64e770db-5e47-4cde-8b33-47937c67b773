github_action:
  - any: ['.github/**/*']

ios:
  - any: ['ios/**/*']

android:
  - any: ['android/**/*']

ios_components:
  - any: ['ios/src/components/**/*']

android_components:
  - any: ['android/src/components/**/*']

ios_Container:
  - any: ['ios/src/Container/**/*']

android_Container:
  - any: ['android/src/Container/**/*']

ios_assets:
  - any: ['ios/assets/**/*']

android_assets:
  - any: ['android/assets/**/*']

ios_patches:
  - any: ['ios/patches/**/*']

android_patches:
  - any: ['android/patches/**/*']

ios_api:
  - any: ['ios/src/api/**/*']

android_api:
  - any: ['android/src/api/**/*']

ios_config:
  - any: ['ios/src/config/**/*']

android_config:
  - any: ['android/src/config/**/*']

ios_constants:
  - any: ['ios/src/constants/**/*']

android_constants:
  - any: ['android/src/constants/**/*']

ios_helpers:
  - any: ['ios/src/helpers/**/*']

android_helpers:
  - any: ['android/src/helpers/**/*']

ios_hooks:
  - any: ['ios/src/hooks/**/*']

android_hooks:
  - any: ['android/src/hooks/**/*']

ios_i18n:
  - any: ['ios/src/i18n/**/*']

android_i18n:
  - any: ['android/src/i18n/**/*']

ios_navigation:
  - any: ['ios/src/navigation/**/*']

android_navigation:
  - any: ['android/src/navigation/**/*']

ios_screens:
  - any: ['ios/src/screens/**/*']

android_screens:
  - any: ['android/src/screens/**/*']

ios_store:
  - any: ['ios/src/store/**/*']

android_store:
  - any: ['android/src/store/**/*']

ios_utils:
  - any: ['ios/src/utils/**/*']

android_utils:
  - any: ['android/src/utils/**/*']

ios_validation:
  - any: ['ios/src/validation/**/*']

android_validation:
  - any: ['android/src/validation/**/*']

ios_src_App.tsx:
  - any: ['ios/src/App.tsx']

android_src_App.tsx:
  - any: ['android/src/App.tsx']

ios_types:
  - any: ['ios/types/**/*']

android_types:
  - any: ['android/types/**/*']

ios_package.json:
  - any: ['ios/package.json']

android_package.json:
  - any: ['android/package.json']

ios_App.tsx:
  - any: ['ios/App.tsx']

android_App.tsx:
  - any: ['android/App.tsx']

ios_babel.config.js:
  - any: ['ios/babel.config.js']

android_babel.config.js:
  - any: ['android/babel.config.js']

ios_eas.json:
  - any: ['ios/eas.json']

android_eas.json:
  - any: ['android/eas.json']

ios_tsconfig.json:
  - any: ['ios/tsconfig.json']

android_tsconfig.json:
  - any: ['android/tsconfig.json']

ios_yarn.lock:
  - any: ['ios/yarn.lock']

android_yarn.lock:
  - any: ['android/yarn.lock']

.gitignore:
  - any: ['.gitignore']
