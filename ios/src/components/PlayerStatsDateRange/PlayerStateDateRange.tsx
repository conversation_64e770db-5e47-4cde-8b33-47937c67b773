import React, { useState, FC } from 'react';
import { Appearance, Text, TouchableOpacity, View, Image } from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { dateToString } from '../../helpers/DateHelper';
import useStyles from '../../hooks/useStyles';
import customDateRangeModalStyle from './PlayerStateDateRangeStyle';
import { statsErrorMessage } from '../../constants/constants';

const START_DATE = 'Start Date';
const END_DATE = 'End Date';

interface IPlayerStateDateRange {
  title: string;
  selectedDateRange: string;
  setSelectedDateRange: Function;
  isEndDateError: boolean;
  isStartDateError: boolean;
}

const PlayerStateDateRange: FC<IPlayerStateDateRange> = ({
  title,
  selectedDateRange,
  setSelectedDateRange,
  isEndDateError,
  isStartDateError,
}: any) => {
  const { startDate, endDate } = selectedDateRange || {};
  const DateRangeStyle = useStyles(customDateRangeModalStyle);
  const [showStartDatePicker, setshowStartPicker] = useState<boolean>(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState<boolean>(false);

  const resetTimeToNearestMidnight = (date: Date) => {
    return new Date(date.setHours(0, 0, 0, 0));
  };

  const resetTimeToNearesrMidnightInFuture = (date: Date) => {
    return new Date(date.setHours(23, 59, 59, 0));
  };

  const onStartDateSelect = (date: Date) => {
    const dateWithTimeReset = resetTimeToNearestMidnight(date);
    setShowEndDatePicker(false);
    date &&
      setSelectedDateRange({
        ...selectedDateRange,
        startDate: dateWithTimeReset,
      });
  };

  const onEndDateSelect = (date: Date) => {
    const dateWithTimeReset = resetTimeToNearesrMidnightInFuture(date);
    setshowStartPicker(false);
    date &&
      setSelectedDateRange({
        ...selectedDateRange,
        endDate: dateWithTimeReset,
      });
  };

  const renderDateLable = (label: string) => (
    <>
      <Text style={DateRangeStyle.listItemText}>{label}</Text>
      <Image
        style={DateRangeStyle.calendarIcon}
        source={require('../../../assets/icons/calendarIcon.png')}
      />
    </>
  );

  const renderDateString = (label: string) => (
    <Text style={DateRangeStyle.listItemText}>{label}</Text>
  );

  return (
    <View>
      <View>
        <Text style={DateRangeStyle.listTitle}>{title}</Text>
        <View style={DateRangeStyle.listContainer}>
          <View style={DateRangeStyle.list}>
            <TouchableOpacity
              style={DateRangeStyle.listItem}
              onPress={() => setShowEndDatePicker(true)}
            >
              <View style={DateRangeStyle.listItemContent}>
                {startDate
                  ? renderDateString(dateToString(startDate))
                  : renderDateLable(START_DATE)}
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={DateRangeStyle.listItem}
              onPress={() => setshowStartPicker(true)}
            >
              <View style={DateRangeStyle.listItemContent}>
                {endDate
                  ? renderDateString(dateToString(endDate))
                  : renderDateLable(END_DATE)}
              </View>
            </TouchableOpacity>
            {isEndDateError || isStartDateError ? (
              <Text style={DateRangeStyle.error}>
                {isEndDateError
                  ? statsErrorMessage?.END_DATE_ERROR
                  : statsErrorMessage.START_DATE_ERROR}
              </Text>
            ) : (
              <></>
            )}
          </View>
        </View>
      </View>
      {showEndDatePicker && (
        <View style={DateRangeStyle.datePickerWrapper}>
          <DateTimePickerModal
            isVisible
            mode="date"
            date={startDate || undefined}
            style={DateRangeStyle.datePicker}
            onConfirm={onStartDateSelect}
            display="spinner"
            onCancel={() => setShowEndDatePicker(false)}
            modalStyleIOS={DateRangeStyle.datePickerSelector}
            pickerContainerStyleIOS={DateRangeStyle.datePickerWrapper}
            maximumDate={endDate ? endDate : new Date()}
            isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
          />
        </View>
      )}

      {showStartDatePicker && (
        <View style={DateRangeStyle.datePickerWrapper}>
          <DateTimePickerModal
            isVisible
            mode="date"
            style={DateRangeStyle.datePicker}
            onConfirm={onEndDateSelect}
            display="spinner"
            date={endDate || undefined}
            onCancel={() => setshowStartPicker(false)}
            modalStyleIOS={DateRangeStyle.datePickerSelector}
            pickerContainerStyleIOS={DateRangeStyle.datePickerWrapper}
            maximumDate={new Date()}
            minimumDate={startDate || undefined}
            isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
          />
        </View>
      )}
    </View>
  );
};

export default PlayerStateDateRange;
