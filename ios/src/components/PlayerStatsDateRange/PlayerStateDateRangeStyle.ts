import { useColorScheme } from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const DateRangeModalStyle = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    // backgroundColor: colors.darkBlue,
    // opacity: 0.8,
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 20,
        padding: 35,
        alignItems: 'center',
        shadowColor: colors.black,
        width: wp('35%'),
        height: wp('34%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 20,
        padding: wp('4%'),
        alignItems: 'center',
        shadowColor: colors.black,
        width: wp('70%'),
        height: wp('50%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  dateSelectorWrapper: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-around',
        padding: wp('2%'),
        width: wp('30%'),
        height: wp('15%'),
        marginBottom: wp('1.5%'),
        borderRadius: wp('1.5%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        backgroundColor: colors.darkBlue,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        padding: wp('2%'),
        width: '90%',
        height: wp('20%'),
        marginBottom: wp('1.5%'),
        borderRadius: wp('1.5%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  title: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('2%'),
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('5%'),
        textAlign: 'center',
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        // padding: wp('1%'),
        width: wp('11%'),
        height: hp('7%'),
        marginTop: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        width: wp('20%'),
        height: hp('4%'),
        marginTop: wp('2%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  button2: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        // padding: wp('1%'),
        width: wp('11%'),
        height: hp('7%'),
        marginTop: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        width: wp('20%'),
        height: hp('4%'),
        marginTop: wp('2%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  textStyle: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        fontSize: wp('3%'),
      },
  modalText: isTabDevice()
    ? {
        // marginBottom: 15,
        textAlign: 'center',
        color: colors.green,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1%'),
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.green,
        fontSize: wp('4%'),
        fontWeight: 'bold',
        marginRight: wp('1%'),
      },
  dateString: isTabDevice()
    ? {
        // marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-Bold',
        paddingHorizontal: 10,
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('4%'),
        fontWeight: 'bold',
        paddingHorizontal: 10,
      },
  addView: {
    alignItems: 'flex-end',
    alignContent: 'flex-end',
    paddingHorizontal: 20,
  },
  AddTouchableOpacity: {
    height: 60,
    width: 60,
    paddingBottom: 5,
    borderRadius: 60,
    backgroundColor: '#1dc4d2',
    alignItems: 'center',
  },
  AddIcon: {
    paddingTop: 12,
  },
  showDatePickerButton: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginBottom: wp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginTop: wp('3%'),
        marginBottom: wp('1%'),
      },
  row: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
  },
  datePickerContainer: {},
  datePickerSelector: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'absolute',
        left: wp('27%'),
        top: wp('22%'),
      }
    : {
        // width: wp('40%'),
        position: 'absolute',
        left: wp('2%'),
        top: wp('40%'),
      },
  datePickerWrapper: isTabDevice()
    ? {
        zIndex: 3,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      }
    : {
        position: 'absolute',
        borderRadius: wp('3%'),
        top: 0,
        left: 0,
        zIndex: 3,
        width: wp('90%'),
        borderWidth: 10,
        borderColor: colors.semiDarkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },
  datePicker: {},
  listTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        marginBottom: 15,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
        marginBottom: 15,
      },
  list: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 15,
        padding: wp('1%'),
        width: wp('22%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 15,
        padding: wp('2%'),
        padding: wp('2%'),
        width: wp('60%'),
      },
  listContainer: isTabDevice()
    ? {
        width: '30%',
      }
    : {
        width: '100%',
      },
  listItem: isTabDevice()
    ? {
        textAlign: 'center',
        backgroundColor: colors.midDarkBlue,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: wp('5%'),
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 5,
        paddingRight: 5,
        marginBottom: wp('1%'),
        width: '100%',
      }
    : {
        textAlign: 'center',
        backgroundColor: colors.midDarkBlue,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: wp('10%'),
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 5,
        paddingRight: 5,
        marginBottom: wp('1%'),
        width: '100%',
      },
  listItemText: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: wp('1%'),
        marginRight: wp('4%'),
        fontSize: wp('1.6%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        paddingLeft: wp('3%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  listItemContent: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  calendarIcon: isTabDevice()
    ? {
        height: wp('1.5%'),
        marginRight: 10,
        resizeMode: 'contain',
        width: wp('1.5%'),
      }
    : {
        height: wp('4%'),
        marginRight: 10,
        resizeMode: 'contain',
        width: wp('4%'),
      },
  error: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.red,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
});
export default DateRangeModalStyle;
