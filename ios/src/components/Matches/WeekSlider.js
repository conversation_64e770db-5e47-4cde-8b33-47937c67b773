import React, { useEffect, useState } from 'react';
import { Text, View, FlatList, TouchableHighlight } from 'react-native';
import WeekContainer from '../../Container/WeekContainer/WeekContainer';
import customWeekSliderStyles from './WeekSliderStyles';
import useStyles from '../../hooks/useStyles';

const WeekSlider = ({
  selectedMonthCalendar,
  setSelectedWeek,
  selectedWeekIndex,
  loadMoreMatches,
  currentWeekIndex,
  year,
  month,
}) => {
  const weekSliderStyles = useStyles(customWeekSliderStyles);
  const { calendar } = selectedMonthCalendar;
  const addLeadingZeros = value => {
    return value < 10 ? '0' + value : value;
  };
  const week = ({ item, index }) => {
    return (
      <View style={weekSliderStyles.weeksWrapper}>
        <TouchableHighlight onPress={() => setSelectedWeek(item)}>
          <View style={weekSliderStyles.weeks}>
            <Text
              style={
                selectedWeekIndex ==
                Number(`${year}${addLeadingZeros(month)}${item.week}`)
                  ? weekSliderStyles.weeksTextSelected
                  : weekSliderStyles.weeksText
              }
            >
              Week {item.week + 1}
            </Text>
          </View>
        </TouchableHighlight>
      </View>
    );
  };

  return (
    <View>
      <View style={weekSliderStyles.weeksContainer}>
        <FlatList
          horizontal={true}
          data={calendar}
          renderItem={week}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
      <WeekContainer
        loadMoreMatches={loadMoreMatches}
        currentWeekIndex={currentWeekIndex}
        selectedWeekIndex={selectedWeekIndex}
      />
    </View>
  );
};

export default WeekSlider;
