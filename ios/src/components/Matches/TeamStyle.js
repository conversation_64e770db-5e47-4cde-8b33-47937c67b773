import { isTabDevice } from '../../config/appConfig';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { colorPalette } from '../../constants/constants';

const TeamStyle = colors => ({
  teamWrapper: isTabDevice() ? {} : {},
  tile: isTabDevice()
    ? {}
    : {
        width: '100%',
        height: wp('15%'),
        marginRight: wp('2%'),
        marginBottom: wp('2%'),
      },
  team: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: '100%',
        height: hp('7%'),
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        paddingTop: wp('0.5%'),
        paddingBottom: wp('0.5%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        flexDirection: 'row',
        width: '100%',
        height: '100%',
        alignItems: 'center',
        padding: wp('2%'),
      },
  teamSelected: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        width: '100%',
        height: hp('7%'),
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        paddingTop: wp('0.5%'),
        paddingBottom: wp('0.5%'),
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('2%'),
        width: '100%',
        height: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        padding: wp('2%'),
      },
  teamText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      },
  teamColor: isTabDevice()
    ? {
        width: wp('0.5%'),
        height: '100%',
        borderRadius: 10,
        marginRight: wp('1%'),
      }
    : {
        width: wp('1%'),
        height: '100%',
        borderRadius: 30,
        marginRight: wp('2%'),
      },
});
export default TeamStyle;
