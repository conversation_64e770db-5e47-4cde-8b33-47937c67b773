import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import customTeamStyle from './TeamStyle';
import useStyles from '../../hooks/useStyles';
import { stringLength } from '../../helpers';
import { isTabDevice } from '../../config/appConfig';

const Team = ({
  setSelectedTeam,
  selectedTeamID,
  item,
  setShowTeamData,
  setMatchScreen,
}) => {
  const TeamStyle = useStyles(customTeamStyle);
  let { teamName, _id, colour } = item;

  return (
    <>
      <View style={TeamStyle.teamWrapper}>
        <TouchableOpacity
          onPress={() => {
            setSelectedTeam(item),
              isTabDevice()
                ? null
                : setShowTeamData === undefined
                ? null
                : (setShowTeamData(true), setMatchScreen(true));
          }}
          style={TeamStyle.tile}
        >
          <View
            style={
              selectedTeamID == _id ? TeamStyle.teamSelected : TeamStyle.team
            }
          >
            <View
              style={{ ...TeamStyle.teamColor, backgroundColor: colour }}
            ></View>
            <Text style={TeamStyle.teamText}>
              {stringLength(teamName, isTabDevice() ? 23 : 32)}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </>
  );
};

export default Team;
