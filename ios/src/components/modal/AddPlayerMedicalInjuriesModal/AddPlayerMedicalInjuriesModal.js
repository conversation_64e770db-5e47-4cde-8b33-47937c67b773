import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';

import { Ionicons } from '@expo/vector-icons';
import ModalWrapper from '../ModalWrapper/ModalWrapper';

import PlayerInfoMedicalInjuriesAddContainer from '../../../Container/PlayerInfoMedicalInjuriesAddContainer/PlayerInfoMedicalInjuriesAddContainer';
import customAddPlayerMedicalInjuriesModalStyle from './AddPlayerMedicalInjuriesModalStyle';
import useStyles from '../../../hooks/useStyles';

const AddPlayerMedicalInjuriesModal = ({ addUpdate, isAddModal }) => {
  const AddPlayerMedicalInjuriesModalStyle = useStyles(
    customAddPlayerMedicalInjuriesModalStyle
  );
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <View style={AddPlayerMedicalInjuriesModalStyle.centeredView}>
      {modalVisible && (
        <ModalWrapper>
          <PlayerInfoMedicalInjuriesAddContainer
            addUpdate={addUpdate}
            setModalVisible={setModalVisible}
            isAddModal={isAddModal}
          />
        </ModalWrapper>
      )}

      <View style={AddPlayerMedicalInjuriesModalStyle.addView}>
        <TouchableOpacity
          style={AddPlayerMedicalInjuriesModalStyle.AddTouchableOpacity}
          onPress={() => setModalVisible(true)}
        >
          <Ionicons
            color="#fff"
            name="add"
            size={35}
            style={AddPlayerMedicalInjuriesModalStyle.AddIcon}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddPlayerMedicalInjuriesModal;
