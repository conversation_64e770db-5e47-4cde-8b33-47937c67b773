import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const AddPlayerMedicalInjuriesModalStyle = colors => ({
  addView: isTabDevice()
    ? {
        position: 'absolute',
        bottom: hp('-5%'),
        right: 0,
      }
    : {
        position: 'absolute',
        bottom: wp('45%'),
        right: 0,
      },
  AddTouchableOpacity: isTabDevice()
    ? {
        height: wp('7%'),
        width: wp('7%'),
        borderRadius: 60,
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('13%'),
        width: wp('13%'),
        borderRadius: 60,
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
      },
  AddIcon: isTabDevice()
    ? {
        fontSize: wp('3.5%'),
      }
    : {
        fontSize: wp('7%'),
      },
});
export default AddPlayerMedicalInjuriesModalStyle;
