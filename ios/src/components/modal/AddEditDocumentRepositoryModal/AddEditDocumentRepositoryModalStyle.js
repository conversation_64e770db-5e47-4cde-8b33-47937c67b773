import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ChangePasswordModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        // width: wp('40%'),
        // height: hp('20%'),
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('3%'),
        width:wp('72%'),
        maxHeight:hp('90%')
      }
    : {
        width: wp('90%'),
        flexDirection: 'column',
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('4%'),
        height: hp('65%')
      },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 5,
    fontSize: wp('2%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  content: isTabDevice()
    ? {
      width: wp('70%'),     
     }
    : {
      width: '100%',
    },
  title: isTabDevice()
  ? {
    color: colors.white,
    fontSize: wp('2.5%'),
    fontFamily: 'Poppins-Bold',
    marginBottom: hp('2%'),
  }
: {
    color: colors.white,
    fontSize: wp('5%'),
    fontFamily: 'Poppins-Bold',
    marginBottom: wp('2%'),
  },
  inputFieldWrapper: isTabDevice() ? {} : {
    marginBottom: wp('2%')
  },
  inputFieldWrapper2: isTabDevice()
  ? {
    marginBottom: wp('1%'),
    width : wp('30%')
  }
  : {
    marginBottom: wp('3.5%'),
  },
  inputFieldContainer: isTabDevice() ? {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '95%',
    marginBottom: wp('2%')
  } : {
  },  
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      },
  inputField: {
    width: '100%',
    height: hp('6%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  inputView: isTabDevice()
    ? {
        borderRadius: wp('0.7%'),
        paddingRight: wp('1.5%'),
        width: wp('38%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
      }
    : {
        borderRadius: wp('0.7%'),
        padding: wp('1.5%'),
        paddingRight: wp('1.5%'),
        width: wp('60%'),
        flexDirection: 'row',
        position: 'relative',
        alignItems: 'center',
      },
  textInput: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2 %'),
        width: wp('30%'),
        padding: wp('1.2%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        width: '100%',
        height: wp('10%'),
        padding: wp('1.2%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('2%'),
        paddingLeft: wp('2%'),
        marginLeft: wp('-1%'),
      },
  errorText: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.5%'),
        width: wp('24%'),
        padding: wp('1.5%'),
      }
    : {
        color: colors.red,
        fontSize: wp('3%'),
        width: '100%',
        padding: wp('1.5%'),
      },
  submitButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        height: wp('7%'),
        width: wp('20%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  cancelButton: isTabDevice()
    ? {
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.darkBlue,
        marginRight: wp('1%'),
      }
    : {
        height: wp('7%'),
        width: wp('20%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.darkBlue,
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  submitRow: isTabDevice()
    ? {
        width: '95%',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
      },
  upload: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        padding: wp('0.5%'),
        paddingTop: wp('0.3%'),
        paddingBottom: wp('0.3%'),
        width: wp('66.5%'),
        display: 'flex',
        flexDirection: 'row',
        marginRight: wp('1%'),
        // marginTop: wp('-1%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('2%'),
        padding: wp('0.5%'),
        paddingTop: wp('0.3%'),
        paddingBottom: wp('0.3%'),
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        marginRight: wp('1%'),
        // marginTop: wp('-1%'),
      },
  uploadIconImageWrapper: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    padding: 10,
    paddingRight: 30,
  },
  uploadIconImage: isTabDevice()
    ? {
        height: wp('2.5%'),
        width: wp('2.5%'),
        resizeMode: 'contain',
        marginRight: wp('0.5%'),
      }
    : {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        height: wp('7%'),
        width: wp('7%'),
        resizeMode: 'contain',
      },
  uploaded: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        padding: wp('1.2%'),
        width: '95%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: wp('1%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('2%'),
        padding: wp('2%'),
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: wp('1%'),
      },
  uploadWrapperClose: {
    fontSize: wp('1.5%'),
    position: 'absolute',
    top: '50%',
    right: 10,
    zIndex: 2,
  },
  iconImage: isTabDevice()
    ? {
        height: wp('2.5%'),
        width: wp('2.5%'),
        resizeMode: 'contain',
      }
    : {
        height: wp('7%'),
        width: wp('7%'),
        resizeMode: 'contain',
      },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  btnText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        width: '70%',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        width: '70%',
      },
  error: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.red,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      },
  picker: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        color: 'white',
      }
    : {
        backgroundColor: colors.darkBlue,
        color: 'white',
        borderRadius: wp('1%'),
        fontSize: wp('3%'),
        padding: wp('0.3%'),
        width: wp('60%'),
      },
  dropdownTopArea: {
    backgroundColor: colors.transparent,
    borderColor: colors.transparent,
    position: 'absolute',
    zIndex: 5,
  },
  dropdown: {
    backgroundColor: colors.darkBlue,
    justifyContent: 'flex-start',
  },
  dropdownSelectedContainer: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        height: wp('4.8%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        height: wp('10%'),
        width: wp('60%'),
        marginBottom: wp('3%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        marginTop: wp('0.3%'),
        marginBottom: wp('0.3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: 20,
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('0.5%'),
        marginBottom: wp('0.5%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('-1%'),
        position: 'absolute',
        zIndex: 100,
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
      },

  requiredAstric: isTabDevice()
    ? {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: hp('0.7%'),
      }
    : {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1%'),
      },
});

export default ChangePasswordModalStyles;
