import { AntDesign } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import React from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { dateTimeConversion } from '../../../helpers';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import { SET_SELECTED_MATCH_DETAILS } from '../../../store/actionTypes/MatchPlan/MatchPlanActions';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customOngoingMatchesStyles from './OngoingMatchesStyles';
import { userRoleType } from '../../../constants/constants';

const OngoingMatchesModal = ({ showModal, closeModal }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const { ongoingMatchList } = useSelector(state => state.matchLog);
  const OngoingMatchesStyles = useStyles(customOngoingMatchesStyles);
  const colors = useColors();
  const { userRole } = useSelector(state => state?.auth);

  const navigateToOngoingMatch = event => {
    dispatch({
      type: SET_SELECTED_MATCH_DETAILS,
      payload: event,
    });

    const matchLogRoute = 'MatchLog';
    const isParentOrPlayer =
      userRole === userRoleType.PLAYER || userRole === userRoleType.PARENT;
    if (isParentOrPlayer) {
      return;
    } else {
      if (route?.name === matchLogRoute) {
        navigation.replace(matchLogRoute);
      } else {
        navigation.navigate(matchLogRoute);
      }
    }
    closeModal();
  };

  const EventItem = ({ item }) => {
    const { name, startTime, tournament, matchScorecard } = item;
    const {
      monthString,
      dateReadable,
      dateString,
      hours12,
      minutesString,
      amPm,
    } = dateTimeConversion(startTime);

    const date = `${monthString.slice(0, 3)} ${dateReadable}, ${dateString}`;
    const time = `${hours12}:${minutesString} ${amPm}`;

    return (
      <TouchableOpacity
        onPress={() => navigateToOngoingMatch(item)}
        style={OngoingMatchesStyles.eventItem}
      >
        <View style={OngoingMatchesStyles.eventLeft}>
          <Text style={OngoingMatchesStyles.eventLabel1}>{name || ''}</Text>
          <Text style={OngoingMatchesStyles.eventLabel2}>
            {tournament?.name || ''}
          </Text>
          <View style={OngoingMatchesStyles.eventDateTimeContainer}>
            <Text style={OngoingMatchesStyles.eventLabelTime}>{date} </Text>
            <Text style={OngoingMatchesStyles.eventLabelTime}>{time}</Text>
          </View>
        </View>
        <View style={OngoingMatchesStyles.eventRight}>
          <Text style={OngoingMatchesStyles.eventScore}>
            {matchScorecard?.score || 0}
          </Text>
          <View style={OngoingMatchesStyles.eventScoreSeperator} />
          <Text style={OngoingMatchesStyles.eventScore}>
            {matchScorecard?.opponentScore || 0}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <View style={OngoingMatchesStyles.overlay} />
        <View style={OngoingMatchesStyles.centeredView}>
          <View style={OngoingMatchesStyles.modalArea}>
            <TouchableOpacity
              style={OngoingMatchesStyles.closeButton}
              onPress={() => closeModal()}
            >
              <AntDesign name="close" size={20} color={colors.white} />
            </TouchableOpacity>
            <View style={OngoingMatchesStyles.header}>
              <Text style={OngoingMatchesStyles.headerTitle}>
                Ongoing Matches
              </Text>
            </View>
            <View style={OngoingMatchesStyles.messageContainer}>
              <FlatList
                data={ongoingMatchList}
                renderItem={EventItem}
                keyExtractor={item => item?._id}
              />
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default OngoingMatchesModal;
