import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const Message = colors => ({
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        paddingTop: hp('5%'),
        paddingRight: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('70%'),
        paddingTop: wp('6%'),
        paddingRight: wp('6%'),
      },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('-0.5'),
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('4%'),
        top: wp('4%'),
        zIndex: 9,
      },
  closeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        position: 'absolute',
        right: -6,
        top: -7,
      },
  modalTitle: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  selectionWrapper: isTabDevice()
    ? {
        width: wp('30%'),
      }
    : {
        width: '100%',
      },
  selectionTextWrapper: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        marginTop: wp('0.5%'),
        marginBottom: wp('0.5%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
      },
  selectionText: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        padding: wp('1%'),
        marginTop: wp('0.5%'),
        marginBottom: wp('0.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3%'),
        padding: wp('2%'),
        marginTop: wp('0.5%'),
        marginBottom: wp('0.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
export default Message;
