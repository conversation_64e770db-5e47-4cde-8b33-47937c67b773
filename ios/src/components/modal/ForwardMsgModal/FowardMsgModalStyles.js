import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const Message = colors => ({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        width: wp('27%'),
        height: hp('46%'),
        alignItems: 'center',
        paddingTop: hp('5%'),
        paddingRight: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('90%'),
        height: hp('49%'),
        paddingTop: wp('6%'),
        paddingRight: wp('6%'),
      },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('-1%'),
        top: wp('-1%'),
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('4%'),
        top: wp('4%'),
        zIndex: 9,
      },
  closeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        position: 'absolute',
        right: -6,
        top: -7,
      },
  modalTitle: {
    fontSize: wp('1.5%'),
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },
  searchContainer: {
    width: '100%',
  },
  selectionWrapper: {
    width: wp('30%'),
  },
  selectionText: {
    backgroundColor: colors.darkBlue,
    fontSize: wp('1.2%'),
    padding: wp('1%'),
    marginTop: wp('0.5%'),
    marginBottom: wp('0.5%'),
    borderRadius: 10,
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },
  searchContainer: isTabDevice()
    ? {
        width: wp('21.5%'),
        position: 'relative',
        alignItems: 'flex-end',
        justifyContent: 'center',
      }
    : {
        width: wp('72%'),
        position: 'relative',
        alignItems: 'flex-end',
        justifyContent: 'center',
      },
  chatList: isTabDevice()
    ? {
        height: wp('15%'),
        width: wp('21.5%'),
      }
    : {
        height: wp('60%'),
        width: wp('72%'),
      },
  listItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.darkBlue,
        marginBottom: wp('0.5%'),
        padding: wp('0.5%'),
        borderRadius: 10,
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.darkBlue,
        marginBottom: wp('1.5%'),
        padding: wp('0.5%'),
        borderRadius: 10,
      },
  listItemCheckbox: {},
  listItemText: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        marginTop: wp('0.2%'),
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        marginTop: wp('1%'),
      },
  message: isTabDevice()
    ? {
        color: colors.white,
        textAlign: 'center',
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1.2%'),
        padding: wp('2%'),
      }
    : {
        color: colors.white,
        textAlign: 'center',
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
        padding: wp('2%'),
      },
  errorMessage: isTabDevice()
    ? {
        color: colors.red,
        textAlign: 'center',
        fontFamily: 'Poppins-Bold',
        fontSize: wp('0.8%'),
      }
    : {
        color: colors.red,
        textAlign: 'center',
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2.5%'),
      },
  errorMessageWrapper: {
    padding: wp('0.5%'),
  },
  selectionText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        backgroundColor: colors.tileBackground,
        width: wp('10.8%'),
        textAlign: 'center',
        padding: wp('0.2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        backgroundColor: colors.tileBackground,
        width: wp('35%'),
        textAlign: 'center',
        padding: wp('2%'),
      },
  selectedText: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1%'),
        backgroundColor: colors.lightBlue,
        width: wp('10.8%'),
        textAlign: 'center',
        padding: wp('0.2%'),
        position: 'relative',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        backgroundColor: colors.lightBlue,
        width: wp('35%'),
        textAlign: 'center',
        padding: wp('2%'),
        position: 'relative',
      },
  submitWrapper: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('0.5%'),
        padding: wp('0.5%'),
        width: wp('10%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('2%'),
        padding: wp('2%'),
        marginTop: wp('2%'),
        width: wp('30%'),
      },
  submit: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        textAlign: 'center',
      },
  submitLoader: isTabDevice()
    ? {
        width: wp('27%'),
        height: hp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        width: wp('90%'),
        height: hp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
});
export default Message;
