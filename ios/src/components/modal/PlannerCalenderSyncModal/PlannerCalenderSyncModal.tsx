import { AntDesign, EvilIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  FlatList,
  Image,
  Modal,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import useCalender from '../../../hooks/useCalender';
import useStyles from '../../../hooks/useStyles';
import customPlannerCalenderSyncModalStyle from './PlannerCalenderSyncModalStyle';

const PlannerCalenderSyncModal = () => {
  const [
    {
      deviceCalendarList,
      setCalender,
      syncCalender,
      getLastSyncDateTime,
      selectedCalenderID,
      isCalenderSyncEnabled,
      updateSyncStatus,
      unSyncCalender,
    },
  ] = useCalender();

  const PlannerCalenderSyncModalStyle = useStyles(
    customPlannerCalenderSyncModalStyle
  );
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [unSyncCalenderEnable, setUnSyncCalenderEnable] =
    useState<boolean>(false);
  const renderChildData = ({ item }: any) => {
    return (
      <TouchableOpacity onPress={() => setCalender(item?.id || '')}>
        <View style={PlannerCalenderSyncModalStyle.selectAccountItem}>
          <View style={PlannerCalenderSyncModalStyle.selectAccountRight}>
            <Text style={PlannerCalenderSyncModalStyle.selectAccountName}>
              {item?.source?.name || ''}
            </Text>
            <Text style={PlannerCalenderSyncModalStyle.selectAccountEmail}>
              {item?.title || ''}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const SwitchComponent = () => {
    return (
      <>
        <Switch
          trackColor={{ false: '#c3c5c8', true: '#c3c5c8' }}
          thumbColor={isCalenderSyncEnabled ? '#36d982' : '#071324'}
          ios_backgroundColor="#3e3e3e"
          onValueChange={updateSyncStatus}
          value={isCalenderSyncEnabled}
        />
        <Text style={PlannerCalenderSyncModalStyle.toggleText}>
          {isCalenderSyncEnabled ? 'On' : 'Off'}
        </Text>
      </>
    );
  };

  const RenderCalendarSync = () => {
    return (
      <View style={PlannerCalenderSyncModalStyle.PlannerCalenderWrapper}>
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderHeader}>
          <Image
            style={PlannerCalenderSyncModalStyle.calendarIcon}
            source={require('../../../../assets/icons/calendarIcon.png')}
          />
          <Text style={PlannerCalenderSyncModalStyle.heading}>
            Calender Sync
          </Text>
        </View>
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderBody}>
          <View style={PlannerCalenderSyncModalStyle.toggleWrapper}>
            {SwitchComponent()}
          </View>
        </View>
      </View>
    );
  };

  const renderSelectAccount = () => {
    return (
      <View
        style={[
          PlannerCalenderSyncModalStyle.PlannerCalenderWrapper,
          PlannerCalenderSyncModalStyle.selectAccountWrapper,
        ]}
      >
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderHeader}>
          <Image
            style={PlannerCalenderSyncModalStyle.calendarIcon}
            source={require('../../../../assets/icons/calendarIcon.png')}
          />
          <Text style={PlannerCalenderSyncModalStyle.heading}>
            Select Account
          </Text>
        </View>
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderBody}>
          <FlatList
            data={deviceCalendarList}
            renderItem={renderChildData}
            initialNumToRender={3}
            style={PlannerCalenderSyncModalStyle.selectAccountList}
          />
        </View>
      </View>
    );
  };

  const RenderFinalCalendarSync = () => {
    return (
      <View style={PlannerCalenderSyncModalStyle.PlannerCalenderWrapper}>
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderHeader}>
          <Image
            style={PlannerCalenderSyncModalStyle.calendarIcon}
            source={require('../../../../assets/icons/calendarIcon.png')}
          />
          <Text style={PlannerCalenderSyncModalStyle.heading}>
            Calender Sync
          </Text>
          <Text style={PlannerCalenderSyncModalStyle.subText}>Last Synced</Text>
          <Text style={PlannerCalenderSyncModalStyle.subText}>
            {getLastSyncDateTime()}
          </Text>
        </View>
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderBody}>
          <View style={PlannerCalenderSyncModalStyle.toggleWrapper}>
            {SwitchComponent()}
          </View>
          <TouchableOpacity onPress={syncCalender}>
            <View style={PlannerCalenderSyncModalStyle.syncNowFeatureWrapper2}>
              <View style={PlannerCalenderSyncModalStyle.syncNowFeatureIcon2}>
                <EvilIcons
                  name="refresh"
                  size={isTabDevice() ? 33 : 44}
                  color="white"
                />
              </View>
              <Text style={PlannerCalenderSyncModalStyle.syncNowFeatureText2}>
                Sync Now
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        <View style={PlannerCalenderSyncModalStyle.PlannerCalenderFooter}>
          <TouchableOpacity onPress={() => setUnSyncCalenderEnable(true)}>
            <Text style={PlannerCalenderSyncModalStyle.unsyncNowFeatureText}>
              Unsync calender
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderUnSyncAlert = () => (
    <View style={PlannerCalenderSyncModalStyle.PlannerCalenderWrapper}>
      <View style={PlannerCalenderSyncModalStyle.PlannerCalenderHeader}>
        <Image
          style={PlannerCalenderSyncModalStyle.calendarIcon}
          source={require('../../../../assets/icons/calendarIcon.png')}
        />
        <Text style={PlannerCalenderSyncModalStyle.heading}>
          Are you sure you want to unsync the Calender
        </Text>
      </View>
      <View style={PlannerCalenderSyncModalStyle.PlannerCalenderBody}>
        <View style={PlannerCalenderSyncModalStyle.buttonsWrapper}>
          <TouchableOpacity
            style={[
              PlannerCalenderSyncModalStyle.btnView,
              PlannerCalenderSyncModalStyle.greenBg,
            ]}
            onPress={() => {
              unSyncCalender();
              setUnSyncCalenderEnable(false);
            }}
          >
            <Text style={PlannerCalenderSyncModalStyle.btnTxt}>Yes</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              PlannerCalenderSyncModalStyle.btnView,
              PlannerCalenderSyncModalStyle.darkBlueBg,
            ]}
            onPress={() => {
              setUnSyncCalenderEnable(false);
            }}
          >
            <Text style={PlannerCalenderSyncModalStyle.btnTxt}>No</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderModalContainer = () => {
    if (!isCalenderSyncEnabled) {
      return RenderCalendarSync();
    } else if (!selectedCalenderID) {
      return renderSelectAccount();
    } else if (unSyncCalenderEnable) {
      return renderUnSyncAlert();
    } else {
      return RenderFinalCalendarSync();
    }
  };

  const getStyles = () => {
    if (!isCalenderSyncEnabled) {
      return PlannerCalenderSyncModalStyle.modalView2;
    } else if (!selectedCalenderID) {
      return PlannerCalenderSyncModalStyle.modalView;
    } else if (unSyncCalenderEnable) {
      return PlannerCalenderSyncModalStyle.modalView3;
    } else {
      return PlannerCalenderSyncModalStyle.modalView;
    }
  };

  return (
    <View style={PlannerCalenderSyncModalStyle.container}>
      {modalVisible && (
        <Modal animationType="slide" transparent={true}>
          <View style={PlannerCalenderSyncModalStyle.centeredView}>
            <View style={PlannerCalenderSyncModalStyle.overlay}></View>
            <View style={getStyles()}>
              <TouchableOpacity
                style={PlannerCalenderSyncModalStyle.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <AntDesign
                  name="close"
                  size={20}
                  color="#FFFFFF"
                  style={PlannerCalenderSyncModalStyle.closeText}
                />
              </TouchableOpacity>
              <View style={PlannerCalenderSyncModalStyle.modalContainer}>
                {renderModalContainer()}
              </View>
            </View>
          </View>
        </Modal>
      )}
      <TouchableOpacity onPress={() => setModalVisible(true)}>
        <View style={PlannerCalenderSyncModalStyle.syncNowFeatureWrapper}>
          <View style={PlannerCalenderSyncModalStyle.syncNowFeatureIcon}>
            <EvilIcons
              name="refresh"
              size={isTabDevice() ? 26 : 30}
              color="white"
            />
          </View>
          <Text style={PlannerCalenderSyncModalStyle.syncNowFeatureText}>
            {isTabDevice() ? 'Sync Now' : 'Sync'}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default PlannerCalenderSyncModal;
