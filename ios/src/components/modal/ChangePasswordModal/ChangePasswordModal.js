import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, Image } from 'react-native';
import { AntDesign, Ionicons } from '@expo/vector-icons';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customChangePasswordModalStyles from './ChangePasswordModalStyles';

import { Auth } from 'aws-amplify';
import { isTabDevice } from '../../../config/appConfig';
import { useSelector } from 'react-redux';
import ProfileImage from '../../ProfileImage/ProfileImage';
import { ActivityIndicator } from 'react-native-paper';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

const ChangePasswordModal = ({ showModal, closeModal }) => {
  const ChangePasswordModalStyles = useStyles(customChangePasswordModalStyles);
  const colors = useColors();
  const { userData } = useSelector(state => state.auth);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [passwordChangeSuccess, setPasswordChangeSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPasswordVisibility, setCurrentPasswordVisibility] =
    useState(false);
  const [newPasswordVisibility, setNewPasswordVisibility] = useState(false);
  const [confirmPasswordVisibility, setConfirmPasswordVisibility] =
    useState(false);

  const changePassword = () => {
    setLoading(true);
    if (!validate()) {
      setLoading(false);
      return;
    }
    Auth.currentAuthenticatedUser()
      .then(user => {
        return Auth.changePassword(user, currentPassword, newPassword);
      })
      .then(data => {
        setPasswordChangeSuccess(true);
        setLoading(false);
      })
      .catch(err => {
        console.log('error', err);
        setPasswordError(err);
        setPasswordChangeSuccess(false);
        setLoading(false);
      });
  };

  const validate = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      setErrorMessage('Please fill the required fields');
      return false;
    }
    if (newPassword !== confirmPassword) {
      setErrorMessage('New password and Confirm password does not match.');
      return false;
    }

    setErrorMessage('');
    return true;
  };

  useEffect(() => {
    if (passwordError?.code) {
      switch (passwordError?.code) {
        case 'NotAuthorizedException':
          setErrorMessage('Current password is incorrect');
          break;
        case 'InvalidPasswordException':
          setErrorMessage(passwordError?.message);
          break;
        case 'InvalidParameterException':
          setErrorMessage('Enter a valid password');
          break;
        case 'LimitExceededException':
          setErrorMessage(passwordError?.message);
          break;
        default:
          setErrorMessage('Something went wrong. Try again later');
          break;
      }
    }
  }, [passwordError]);

  useEffect(() => {
    if (errorMessage) {
      setErrorMessage('');
      setPasswordError(null);
    }
  }, [currentPassword, newPassword, confirmPassword]);

  useEffect(() => {
    if (passwordChangeSuccess) {
      resetAndClose();
    }
  }, [passwordChangeSuccess]);

  const resetAndClose = () => {
    setErrorMessage('');
    setPasswordError(null);
    setCurrentPassword('');
    setNewPassword('');
    closeModal();
  };

  return (
    <View>
      <ModalWrapper visible={showModal} transparent>
        <KeyboardAwareScrollView>
          <View style={ChangePasswordModalStyles.overlay} />
          <View style={ChangePasswordModalStyles.centeredView}>
            <View style={ChangePasswordModalStyles.modalArea}>
              {!isTabDevice() && (
                <Text style={ChangePasswordModalStyles.title}>
                  Change Password
                </Text>
              )}
              <View style={ChangePasswordModalStyles.leftContent}>
                <View style={ChangePasswordModalStyles.userProfile}>
                  <View style={ChangePasswordModalStyles.userImage}>
                    <ProfileImage
                      imageStyles={ChangePasswordModalStyles.image}
                      profileImageUrl={userData?.profileImageUrl}
                    />
                  </View>
                  <View style={ChangePasswordModalStyles.userName}>
                    <Text style={ChangePasswordModalStyles.name}>
                      {userData?.firstName} {userData?.lastName}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={ChangePasswordModalStyles.rightContent}>
                {isTabDevice() && (
                  <Text style={ChangePasswordModalStyles.title}>
                    Change Password
                  </Text>
                )}
                <View style={ChangePasswordModalStyles.inputFieldWrapper}>
                  <View
                    style={ChangePasswordModalStyles.inputFieldLabelWrapper}
                  >
                    <Text style={ChangePasswordModalStyles.inputFieldLabel}>
                      Current Password
                    </Text>
                    <AntDesign
                      name="star"
                      size={10}
                      color="black"
                      style={ChangePasswordModalStyles.requiredAstric}
                    />
                  </View>
                  <View style={ChangePasswordModalStyles.inputView}>
                    <TextInput
                      style={ChangePasswordModalStyles.textInput}
                      onChangeText={setCurrentPassword}
                      placeholder="Enter Current Password"
                      placeholderTextColor="#595959"
                      secureTextEntry={!currentPasswordVisibility}
                      textContentType={'password'}
                    />
                    <TouchableOpacity
                      onPress={() => {
                        setCurrentPasswordVisibility(
                          !currentPasswordVisibility
                        );
                      }}
                    >
                      {currentPasswordVisibility ? (
                        <Ionicons
                          name="eye-off-sharp"
                          style={ChangePasswordModalStyles.eyeIcon}
                        />
                      ) : (
                        <Ionicons
                          name="eye-sharp"
                          style={ChangePasswordModalStyles.eyeIcon}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={ChangePasswordModalStyles.inputFieldWrapper}>
                  <View
                    style={ChangePasswordModalStyles.inputFieldLabelWrapper}
                  >
                    <Text style={ChangePasswordModalStyles.inputFieldLabel}>
                      New Password
                    </Text>
                    <AntDesign
                      name="star"
                      size={10}
                      color="black"
                      style={ChangePasswordModalStyles.requiredAstric}
                    />
                  </View>
                  <View style={ChangePasswordModalStyles.inputView}>
                    <TextInput
                      style={ChangePasswordModalStyles.textInput}
                      onChangeText={setNewPassword}
                      placeholder="Enter New Password"
                      placeholderTextColor="#595959"
                      secureTextEntry={!newPasswordVisibility}
                      textContentType={'newPassword'}
                    />
                    <TouchableOpacity
                      onPress={() => {
                        setNewPasswordVisibility(!newPasswordVisibility);
                      }}
                    >
                      {newPasswordVisibility ? (
                        <Ionicons
                          name="eye-off-sharp"
                          style={ChangePasswordModalStyles.eyeIcon}
                        />
                      ) : (
                        <Ionicons
                          name="eye-sharp"
                          style={ChangePasswordModalStyles.eyeIcon}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={ChangePasswordModalStyles.inputFieldWrapper}>
                  <View
                    style={ChangePasswordModalStyles.inputFieldLabelWrapper}
                  >
                    <Text style={ChangePasswordModalStyles.inputFieldLabel}>
                      Verify Password
                    </Text>
                    <AntDesign
                      name="star"
                      size={10}
                      color="black"
                      style={ChangePasswordModalStyles.requiredAstric}
                    />
                  </View>
                  <View style={ChangePasswordModalStyles.inputView}>
                    <TextInput
                      style={ChangePasswordModalStyles.textInput}
                      onChangeText={setConfirmPassword}
                      placeholder="Enter Verify Password"
                      placeholderTextColor="#595959"
                      secureTextEntry={!confirmPasswordVisibility}
                      textContentType={'newPassword'}
                    />
                    <TouchableOpacity
                      onPress={() => {
                        setConfirmPasswordVisibility(
                          !confirmPasswordVisibility
                        );
                      }}
                    >
                      {confirmPasswordVisibility ? (
                        <Ionicons
                          name="eye-off-sharp"
                          style={ChangePasswordModalStyles.eyeIcon}
                        />
                      ) : (
                        <Ionicons
                          name="eye-sharp"
                          style={ChangePasswordModalStyles.eyeIcon}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                  {errorMessage !== '' && (
                    <Text style={ChangePasswordModalStyles.errorText}>
                      {errorMessage}
                    </Text>
                  )}
                </View>
                <View style={ChangePasswordModalStyles.submitRow}>
                  <TouchableOpacity
                    onPress={() => changePassword()}
                    style={ChangePasswordModalStyles.submitButton}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator color={colors.white} />
                    ) : (
                      <Text style={ChangePasswordModalStyles.buttonText}>
                        Submit
                      </Text>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => resetAndClose()}
                    style={{
                      ...ChangePasswordModalStyles.submitButton2,
                      opacity: loading ? 0.5 : 1,
                    }}
                    disabled={loading}
                  >
                    <Text style={ChangePasswordModalStyles.buttonText}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>
      </ModalWrapper>
    </View>
  );
};

export default ChangePasswordModal;
