import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ScrollView,
  Share,
  Text,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';
import { useDispatch } from 'react-redux';
import useColors from '../../../hooks/useColors';
import useGeneratedFileUrl from '../../../hooks/useGeneratedFileUrl';
import useStyles from '../../../hooks/useStyles';
import { REPORT_RESET } from '../../../store/actionTypes/UploadReport/uploadreportAction';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customDownloadDocumentModalStyles from './DownloadDocumentModalStyles';

const DownloadDocumentModal = ({
  item,
  setOpenDownloadModal,
  contentType,
  isDownloadTileVisible,
}) => {
  const DownloadDocumentModalStyles = useStyles(
    customDownloadDocumentModalStyles
  );
  const colors = useColors();
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  const [selectedFile, setSelectedFile] = useState();
  const dispatch = useDispatch();
  useEffect(() => {
    return () => {
      dispatch({
        type: REPORT_RESET,
      });
    };
  }, []);

  const downloadFile = url => {
    if (selectedFile) {
      const { fileName = '' } = selectedFile || {};

      FileSystem.downloadAsync(
        url,
        FileSystem.documentDirectory + `${fileName?.replace(/ /g, '') || ''}`
      )
        .then(({ uri, status }) => {
          Share.share({
            ...Platform.select({
              ios: {
                url: uri,
              },
              android: {
                message: uri,
              },
            }),
          });
        })
        .catch(error => {
          console.log(error, 'error');
        });
    }
  };

  useEffect(() => {
    url && downloadFile(url);
  }, [url]);

  const [selectedFileIndex, setSelectedFileIndex] = useState(0);

  const { createdDate, title, _id, document } = item;
  return (
    <View style={DownloadDocumentModalStyles.container}>
      <ModalWrapper transparent>
        <View style={DownloadDocumentModalStyles.centeredView}>
          <View style={DownloadDocumentModalStyles.overlay}></View>
          <View style={DownloadDocumentModalStyles.content}>
            <TouchableOpacity
              style={DownloadDocumentModalStyles.close}
              onPress={() => setOpenDownloadModal(false)}
            >
              <Ionicons name="close" style={{ fontSize: 25, color: 'white' }} />
            </TouchableOpacity>

            <Text style={DownloadDocumentModalStyles.message}>
              {contentType}
            </Text>

            <ScrollView style={DownloadDocumentModalStyles.scrollText}>
              <Text style={DownloadDocumentModalStyles.text}>{title}</Text>
            </ScrollView>

            {isDownloadTileVisible && document?.length && (
              <Text style={DownloadDocumentModalStyles.description}>
                Download Reports
              </Text>
            )}
            <ScrollView style={DownloadDocumentModalStyles.scroll}>
              <View style={DownloadDocumentModalStyles.reports}>
                {document?.fileKey && (
                  <View style={DownloadDocumentModalStyles.report}>
                    <TouchableOpacity
                      onPress={() => {
                        setSelectedFile(document);
                        s3FileObject(document);
                        setSelectedFileIndex(0);
                      }}
                    >
                      <View style={DownloadDocumentModalStyles.downloadWrapper}>
                        <View
                          style={
                            DownloadDocumentModalStyles.downloadInnerWrapper
                          }
                        >
                          <Image
                            style={DownloadDocumentModalStyles.iconImage}
                            source={require('../../../../assets/icons/medical-icon.png')}
                          />
                          <Text
                            style={DownloadDocumentModalStyles.downloadFile}
                          >
                            {document?.fileName || ''}
                          </Text>
                        </View>

                        {isDownloading ? (
                          <ActivityIndicator color="green" />
                        ) : (
                          <Image
                            style={DownloadDocumentModalStyles.iconImage}
                            source={require('../../../../assets/icons/download-icon.png')}
                          />
                        )}
                      </View>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            </ScrollView>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default DownloadDocumentModal;
