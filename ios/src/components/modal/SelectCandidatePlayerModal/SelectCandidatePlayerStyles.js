import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const SelectCandidatePlayerStyles = colors => ({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 22,
  },
  modalView: isTabDevice()
    ? {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 20,
        padding: 35,
        alignItems: 'center',
        shadowColor: '#000',
        width: wp('45%'),
        height: hp('50%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 20,
        padding: 35,
        alignItems: 'center',
        shadowColor: '#000',
        width: wp('80%'),
        height: hp('40%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  header: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginBottom: wp('1%'),
  },
  closeButton: {
    padding: 5,
    position: 'absolute',
    right: 10,
    top: 15,
  },
  title: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      }
    : {
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      },
  listItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        padding: 5,
        marginBottom: wp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        height: wp('13%'),
        padding: 5,
        paddingRight: 40,
        marginBottom: wp('1%'),
      },
  listItemImageContainer: isTabDevice()
    ? {
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        width: wp('10%'),
        height: wp('10%'),
      },
  listItemImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 100,
  },
  listItemText: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: wp('1%'),
        fontSize: wp('1.4%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        paddingLeft: wp('3%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  noPlayerWrapper: {
    height: '100%',
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: colors.white,
  },
  list: {
    width: '100%',
  },
  exclamationIcon: isTabDevice()
    ? {
        position: 'absolute',
        top: 0,
        right: 0,
        width: wp('1.5%'),
        height: wp('1.5%'),
      }
    : {
        position: 'absolute',
        top: 0,
        right: 0,
        width: wp('4.5%'),
        height: wp('4.5%'),
      },
  searchText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('3%'),
      },
  search: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        width: '100%',
        borderRadius: wp('1%'),
        shadowOpacity: 0,
      }
    : {
        backgroundColor: colors.darkBlue,
        width: wp('60%'),
        height: wp('11%'),
        marginRight: wp('2%'),
        borderRadius: wp('1.5%'),
        shadowOpacity: 0,
      },
});

export default SelectCandidatePlayerStyles;
