import React from 'react';
import {
  Text,
  View,
  TouchableHighlight,
  ActivityIndicator,
} from 'react-native';
import customDeleteModalStyles from './DeleteModalStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';

import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';
import { deleteRecurringEventsModalTexts } from '../../../constants/constants';

const DeleteModal = ({
  submitAction,
  cancelAction,
  message,
  subMessage,
  isTeamModal,
  loading,
  errorMessage = '',
}) => {
  const DeleteModalStyles = useStyles(customDeleteModalStyles);
  const colors = useColors();
  return (
    <View style={DeleteModalStyles.container}>
      <ModalWrapper transparent>
        <View style={DeleteModalStyles.centeredView}>
          <View style={DeleteModalStyles.overlay}></View>
          <View
            style={[
              DeleteModalStyles.modalView,
              isTeamModal && DeleteModalStyles.deleteTeamModalView,
            ]}
          >
            <Text style={DeleteModalStyles.modalText}>{message}</Text>
            {subMessage ? (
            <Text
              style={[
                DeleteModalStyles.modalText,
                DeleteModalStyles.modalSubText,
              ]}
            >
              {subMessage}
            </Text>
            ) : null}
            {errorMessage ? (
              <Text
                style={[
                  DeleteModalStyles.modalText,
                  DeleteModalStyles.modalSubText,
                  DeleteModalStyles.errorText,
                ]}
                numberOfLines={2}
              >
               {errorMessage}
              </Text>
            ) : null}
            <View style={DeleteModalStyles.modalContent}>
              {loading ? (
                <View
                  style={{
                    ...DeleteModalStyles.openButton,
                    ...DeleteModalStyles.yesButton,
                  }}
                >
                  <ActivityIndicator color={colors.white} />
                </View>
              ) : (
                <TouchableHighlight
                  style={{
                    ...DeleteModalStyles.openButton,
                    ...DeleteModalStyles.yesButton,
                  }}
                  onPress={() => submitAction()}
                >
                  <Text style={DeleteModalStyles.textStyle}>Yes</Text>
                </TouchableHighlight>
              )}

              <TouchableHighlight
                style={{
                  ...DeleteModalStyles.openButton,
                  ...DeleteModalStyles.noButton,
                }}
                onPress={() => cancelAction()}
                disabled={loading}
              >
                <Text style={DeleteModalStyles.textStyle}>No</Text>
              </TouchableHighlight>
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default DeleteModal;
