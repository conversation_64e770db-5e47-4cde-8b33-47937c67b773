import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  Modal,
  Text,
  TouchableOpacity,
  View,
  TouchableHighlight,
  FlatList,
  Image,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { Ionicons, AntDesign } from '@expo/vector-icons';
import customAddPlayerModalStyle from './AddPlayerModalStyle';
import useApi from '../../../hooks/useApi';
import {
  PLAYER_FETCH_REQUEST,
  PLAYER_FETCH_SUCCESS,
  PLAYER_FETCH_FAIL,
  PLAYER_ADD_REQUEST,
  PLAYER_ADD_SUCCESS,
  PLAYER_ADD_FAIL,
  RESET_PLAYER_FETCH,
  PLAYER_FETCH_MORE_REQUEST,
  PLAYER_FETCH_MORE_SUCCESS,
  PLAYER_FETCH_MORE_FAIL,
} from '../../../store/actionTypes/player/playerAction';
import { FOOTBALL_SERVICE } from '../../../constants/services';
import { useDispatch, useSelector } from 'react-redux';
import ProfileImage from '../../../components/ProfileImage/ProfileImage';
import ExclamationIcon from '../../../../assets/buttons/exclamation.png';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import { Searchbar } from 'react-native-paper';
import useStyles from '../../../hooks/useStyles';
import { isTabDevice } from '../../../config/appConfig';

const AddPlayerModal = () => {
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);
  const [modalVisible, setModalVisible] = useState(false);
  const [apiCall] = useApi();
  const dispatch = useDispatch();

  const { playerAddData, playerAddTeamData, playerAddDataLoading } =
    useSelector(state => state?.player);
  const { playerRemoveSuccess } = useSelector(state => state?.teamID);
  const { teamData, teamIdData } = useSelector(state => state?.team);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    //clean up method for player fetch iin dropdown
    return () =>
      dispatch({
        type: RESET_PLAYER_FETCH,
      });
  }, []);

  const fetchPlayers = searchString => {
    apiCall(
      `/api/v1/teams/${
        teamIdData || teamData?.data?.[0]?._id
      }/candidate-players?page=1&size=20${
        searchString ? '&searchNameLike=' + searchString : ''
      }`,
      PLAYER_FETCH_REQUEST,
      PLAYER_FETCH_SUCCESS,
      PLAYER_FETCH_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    if (teamIdData || teamData?.data) {
      fetchPlayers();
    }
  }, [modalVisible]);

  const loadMoreCandidatePlayers = () => {
    if (!playerAddData?.page) return;
    apiCall(
      `/api/v1/teams/${
        teamIdData || teamData?.data?.[0]?._id
      }/candidate-players?page=${Number(playerAddData.page) + 1}&size=20${
        searchText ? '&searchNameLike=' + searchText : ''
      }`,
      PLAYER_FETCH_MORE_REQUEST,
      PLAYER_FETCH_MORE_SUCCESS,
      PLAYER_FETCH_MORE_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const AddPlayer = item => {
    setSearchText('');
    const arrayIndex = playerAddData?.data.findIndex(
      element => element.sportsProfileId === item.sportsProfileId
    );
    if (item?.sportsProfileId && playerAddData?.data) {
      const SportsProfileId = item.sportsProfileId;
      apiCall(
        `/api/v1/teams/${
          teamIdData ? teamIdData : teamData.data[0]._id
        }/team-member-profile`,
        PLAYER_ADD_REQUEST,
        PLAYER_ADD_SUCCESS,
        PLAYER_ADD_FAIL,
        { sportsProfileId: SportsProfileId },
        '',
        'PUT',
        null,
        FOOTBALL_SERVICE,
        playerAddData.data[arrayIndex]
      );
    }
    setModalVisible(false);
  };

  const searchUsers = () => {
    fetchPlayers(searchText);
  };

  const PlayerItem = ({ item }) => {
    const {
      profileImageUrl,
      firstName,
      lastName,
      sportsProfileId,
      isAvailable,
    } = item;
    return (
      <TouchableOpacity onPress={() => AddPlayer(item)}>
        <View style={AddPlayerModalStyle.listItem}>
          <View style={AddPlayerModalStyle.listItemImageContainer}>
            <ProfileImage
              style={AddPlayerModalStyle.listItemImage}
              imageStyles={AddPlayerModalStyle.listItemImage}
              profileImageUrl={profileImageUrl}
            />
            {!isAvailable && (
              <Image
                style={AddPlayerModalStyle.exclamationIcon}
                source={ExclamationIcon}
              />
            )}
          </View>
          <Text
            style={AddPlayerModalStyle.listItemText}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {firstName || ''} {lastName || ''}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={AddPlayerModalStyle.container}>
      {modalVisible && (
        <Modal animationType="slide" transparent={true}>
          <TouchableWithoutFeedback
            onPress={Keyboard.dismiss}
            accessible={false}
            touchSoundDisabled={true}
          >
            <View style={AddPlayerModalStyle.centeredView}>
              <View style={AddPlayerModalStyle.overlay}></View>
              <KeyboardAvoidingView
                behavior="padding"
                // keyboardVerticalOffset={100}
              >
                <View style={AddPlayerModalStyle.modalView}>
                  <TouchableOpacity
                    style={AddPlayerModalStyle.closeButton}
                    onPress={() => {
                      setModalVisible(false);
                      setSearchText('');
                    }}
                  >
                    <AntDesign
                      name="close"
                      size={isTabDevice() ? 25 : 20}
                      color="#FFFFFF"
                      style={AddPlayerModalStyle.closeButton}
                    />
                  </TouchableOpacity>
                  <View style={AddPlayerModalStyle.playerListContainer}>
                    <View>
                      <Searchbar
                        placeholder="Search User"
                        placeholderTextColor="#595959"
                        onChangeText={text => setSearchText(text)}
                        value={searchText}
                        iconColor="#FFF"
                        inputStyle={AddPlayerModalStyle.searchText}
                        style={AddPlayerModalStyle.search}
                        onSubmitEditing={() => searchUsers()}
                        onIconPress={() => searchUsers()}
                      />
                    </View>
                    {playerAddDataLoading ? (
                      <ActivitySpinner />
                    ) : (
                      <FlatList
                        style={AddPlayerModalStyle.playerList}
                        data={playerAddData?.data || []}
                        renderItem={PlayerItem}
                        keyExtractor={item => item.sportsProfileId}
                        onEndReached={loadMoreCandidatePlayers}
                        onEndReachedThreshold={3}
                      />
                    )}
                  </View>
                </View>
              </KeyboardAvoidingView>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}

      <View style={AddPlayerModalStyle.addView}>
        <TouchableHighlight
          style={AddPlayerModalStyle.AddTouchableOpacity}
          onPress={() => setModalVisible(true)}
        >
          <Ionicons
            color="#fff"
            name="add"
            size={35}
            style={AddPlayerModalStyle.AddIcon}
          />
        </TouchableHighlight>
      </View>
    </View>
  );
};

export default AddPlayerModal;
