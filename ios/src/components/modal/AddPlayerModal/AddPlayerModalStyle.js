import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const AddPlayerModalStyle = colors => ({
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('3%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        height: wp('33%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('70%'),
        height: hp('30%'),
      },
  openButton: {
    backgroundColor: '#F194FF',
    borderRadius: 20,
    padding: 10,
    elevation: 2,
  },
  textStyle: {
    color: colors.black,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: isTabDevice()
    ? {
        marginBottom: hp('2%'),
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
      }
    : {
        marginBottom: hp('2%'),
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
      },
  playerListContainer: isTabDevice()
    ? {
        width: wp('30%'),
        height: '100%',
      }
    : {
        width: wp('57%'),
        height: '100%',
      },
  searchText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        fontSize: wp('3%'),
      },
  search: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        width: wp('30%'),
        marginRight: wp('2%'),
        borderRadius: wp('1%'),
        shadowOpacity: 0,
      }
    : {
        backgroundColor: colors.tileBackground,
        width: wp('57%'),
        height: wp('11%'),
        marginRight: wp('2%'),
        borderRadius: wp('1.5%'),
        shadowOpacity: 0,
      },
  /*----------------- DropDown styles ----------------*/
  dropdownView: isTabDevice()
    ? {
        width: wp('30%'),
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        height: wp('5%'),
        // borderTopLeftRadius: wp('1%'),
        // borderTopRightRadius: wp('1%'),
        // marginTop: wp('-1%')
      }
    : {
        width: wp('57%'),
        // marginTop: wp('-1%'),
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        height: wp('15%'),
        // backgroundColor: colors.red,
      },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        borderColor: colors.darkBlue,
        height: hp('5%'),
        width: '100%',
      }
    : {
        borderColor: colors.darkBlue,
        height: hp('5%'),
        width: '100%',
        // backgroundColor: colors.red,
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        // height: wp('20%'),
        minHeight: wp('25%'),
        marginTop: wp('-1%'),
        // padding: wp('1.5%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,
        elevation: 10,
      }
    : {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        // height: wp('20%'),
        minHeight: wp('25%'),
        marginTop: wp('-1%'),
        // padding: wp('1.5%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,
        elevation: 10,
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  dropdownTopArea: isTabDevice()
    ? {
        backgroundColor: colors.transparent,
        borderColor: colors.transparent,
        width: '100%',
        height: wp('5%'),
        position: 'absolute',
        zIndex: 5,
      }
    : {
        backgroundColor: colors.transparent,
        borderColor: colors.transparent,
        width: '100%',
        height: wp('15%'),
        position: 'absolute',
        zIndex: 5,
      },
  /*----------------- DropDown styles End----------------*/

  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('-1.5'),
        top: 5,
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 10,
        top: 10,
        zIndex: 9,
      },
  closeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        position: 'absolute',
        right: -6,
        top: -7,
      },
  addView: isTabDevice()
    ? {
        position: 'absolute',
        bottom: hp('-10%'),
        right: wp('3%'),
      }
    : {
        marginRight: hp('3%'),
      },
  AddTouchableOpacity: isTabDevice()
    ? {
        height: wp('7%'),
        width: wp('7%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('11%'),
        width: wp('11%'),
        borderRadius: wp('2%'),
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: wp('2%'),
      },
  AddIcon: isTabDevice()
    ? {
        fontSize: wp('3.5%'),
      }
    : {
        fontSize: wp('6%'),
      },
  ProfileImage: {
    width: 50,
    height: 50,
    borderRadius: 100,
  },

  // player list
  listItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        padding: 5,
        marginBottom: wp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        height: wp('13%'),
        padding: 5,
        marginBottom: wp('1%'),
      },
  listItemImageContainer: isTabDevice()
    ? {
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        width: wp('10%'),
        height: wp('10%'),
      },
  listItemImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 100,
  },
  listItemText: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: wp('1%'),
        marginRight: wp('4%'),
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        paddingLeft: wp('3%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  exclamationIcon: isTabDevice()
    ? {
        position: 'absolute',
        top: 0,
        right: 0,
        width: wp('1.5%'),
        height: wp('1.5%'),
      }
    : {
        position: 'absolute',
        top: 0,
        right: 0,
        width: wp('4.5%'),
        height: wp('4.5%'),
      },

  playerList: isTabDevice()
    ? {
        marginTop: 5,
      }
    : {
        marginBottom: 7,
        marginTop: 5,
      },
});
export default AddPlayerModalStyle;
