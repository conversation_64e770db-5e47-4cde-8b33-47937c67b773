import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const AddTeamStyles = colors => ({
  overlay: {
    backgroundColor: colors.darkBlue,
    width: wp('100%'),
    height: hp('100%'),
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: 0.98,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
  },
  modalView: {
    margin: 20,
    backgroundColor: '#263b58',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    width: 380,
    // height: 250,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  teamNameInput: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    padding: 15,
    backgroundColor: colors.darkBlue,
    borderRadius: 15,
    width: '100%',
  },
  colorPaletteContainer: {
    backgroundColor: colors.darkBlue,
    padding: 10,
    borderRadius: 15,
    marginTop: 10,
  },
  paletteHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 5,
    paddingRight: 5,
    marginBottom: 15,
  },
  paletteHeaderText: {
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  paletteSelectedColor: {
    width: 27,
    height: 27,
    borderRadius: 8,
    margin: 2,
  },
  palette: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonTouchable: {
    backgroundColor: colors.aquaBlue,
    borderRadius: 20,
    marginTop: 10,
    width: '100%',
    height: 60,
    justifyContent: 'center',
  },
  createButtonText: {
    alignItems: 'center',
    color: colors.white,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 15,
    color: colors.red,
    fontWeight: 'bold',
  },
  closeButton: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 15,
    top: 15,
    padding: 5,
  },
});

export default AddTeamStyles;
