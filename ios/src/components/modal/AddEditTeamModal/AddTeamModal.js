import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import { colorPalette } from '../../../constants/constants';
import customAddTeamStyles from './AddTeamStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';

import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

const AddTeamModal = ({
  showModal,
  closeModal,
  onTeamCreate,
  teamData,
  message = '',
  loading = false,
}) => {
  const AddTeamStyles = useStyles(customAddTeamStyles);
  const colors = useColors();
  const [teamName, setTeamName] = useState('');
  const [selectedColor, setSelectedColor] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const onCreate = () => {
    if (teamName && selectedColor) {
      onTeamCreate({
        teamName,
        colour: selectedColor,
      });
      resetData();
    } else if (!teamName) {
      setErrorMessage('Please enter a team name');
    } else {
      setErrorMessage('Please select a team color');
    }
  };

  useEffect(() => {
    if (teamData) {
      setTeamName(teamData.teamName);
      setSelectedColor(teamData.colour);
    }
  }, [teamData]);

  useEffect(() => {
    if (teamName || selectedColor) {
      setErrorMessage('');
    }
  }, [teamName, selectedColor]);

  useEffect(() => {
    if (message) {
      setErrorMessage(message);
    }
  }, [message]);

  const onCloseButtonClicked = () => {
    resetData();
    closeModal();
  };

  const resetData = () => {
    setErrorMessage('');
  };

  return (
    <View style={AddTeamStyles.centeredView}>
      {showModal && (
        <ModalWrapper transparent visible>
          <TouchableWithoutFeedback
            onPress={Keyboard.dismiss}
            accessible={false}
            touchSoundDisabled={true}
          >
            <View style={AddTeamStyles.centeredView}>
              <View style={AddTeamStyles.overlay}></View>
              <KeyboardAvoidingView
                behavior="padding"
                // keyboardVerticalOffset={100}
              >
                <View style={AddTeamStyles.modalView}>
                  <TouchableOpacity
                    onPress={() => onCloseButtonClicked()}
                    style={AddTeamStyles.closeButton}
                  >
                    <AntDesign name="close" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                  <Text style={AddTeamStyles.modalText}>
                    {teamData ? 'Edit Team' : 'Create Team'}
                  </Text>
                  <TextInput
                    placeholder={'Team Name'}
                    style={AddTeamStyles.teamNameInput}
                    multiline={false}
                    placeholderTextColor={colors.lightGrey}
                    value={teamName}
                    onChangeText={text => setTeamName(text)}
                  />
                  <View style={AddTeamStyles.colorPaletteContainer}>
                    <View style={AddTeamStyles.paletteHeader}>
                      <Text style={AddTeamStyles.paletteHeaderText}>
                        Team Color
                      </Text>
                      <View
                        style={[
                          AddTeamStyles.paletteSelectedColor,
                          { backgroundColor: selectedColor || colors.darkBlue },
                        ]}
                      />
                    </View>
                    <View style={AddTeamStyles.palette}>
                      {colorPalette.map(color => (
                        <TouchableOpacity
                          onPress={() => setSelectedColor(color)}
                          key={color}
                        >
                          <View
                            style={[
                              AddTeamStyles.paletteSelectedColor,
                              { backgroundColor: color },
                            ]}
                          />
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                  <Text style={AddTeamStyles.errorMessage}>{errorMessage}</Text>
                  {loading ? (
                    <View style={AddTeamStyles.createButtonTouchable}>
                      <ActivityIndicator size="small" color="#0000ff" />
                    </View>
                  ) : (
                    <TouchableOpacity
                      onPress={() => onCreate()}
                      style={AddTeamStyles.createButtonTouchable}
                    >
                      <Text style={AddTeamStyles.createButtonText}>
                        {teamData ? 'Save Team' : 'Create Team'}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </KeyboardAvoidingView>
            </View>
          </TouchableWithoutFeedback>
        </ModalWrapper>
      )}
    </View>
  );
};

export default AddTeamModal;
