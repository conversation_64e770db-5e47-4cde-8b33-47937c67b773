import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const RsvpNoteModalStyles = colors => ({
  container: {},
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        paddingTop: wp('1%'),
        borderRadius: wp('2%'),
        paddingRight: wp('0.5%'),
        paddingBottom: wp('2%'),
        alignItems: 'center',
        width: wp('35%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        paddingTop: wp('2%'),
        paddingRight: wp('0.5%'),
        paddingBottom: wp('3%'),
        borderRadius: wp('4%'),
        alignItems: 'center',
        width: wp('80%'),
      },
  modalTitleContainer: isTabDevice()
    ? {
        marginLeft: wp('-12%'),
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
      }
    : {
        marginLeft: wp('-23%'),
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
      },
  backIcon: isTabDevice()
    ? {
        marginRight: wp('1%'),
      }
    : {
        marginRight: wp('3%'),
      },
  modalTitle: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-regular',
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-regular',
      },
  playerContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginBottom: hp('2%'),
        marginTop: hp('2%'),
        width: '83%',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'flex-start',
        marginBottom: hp('2%'),
        marginTop: hp('2%'),
        width: '85%',
      },
  playerProfilePicture: {
    width: 40,
    height: 40,
    marginRight: wp('1%'),
    borderRadius: wp('1%'),
  },
  playerName: isTabDevice()
    ? { fontSize: wp('2%'), fontFamily: 'Poppins-bold', color: colors.white }
    : { fontSize: wp('5%'), fontFamily: 'Poppins-bold', color: colors.white },

  TextInputNote: isTabDevice()
    ? {
        padding: wp('2%'),
        backgroundColor: colors.midDarkBlue,
        height: hp('13%'),
        width: wp('30%'),
        marginLeft: wp('1%'),
        marginBottom: wp('2%'),
        color: colors.white,
        borderRadius: wp('2%'),
      }
    : {
        padding: wp('2%'),
        backgroundColor: colors.midDarkBlue,
        height: hp('13%'),
        width: wp('70%'),
        marginLeft: wp('1%'),
        marginBottom: wp('4%'),
        color: colors.white,
        borderRadius: wp('2%'),
      },
  modalContent: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        marginRight: wp('-10%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        marginBottom: wp('2%'),
      },
  confirmButton: {
    backgroundColor: colors.aquaBlue,
    marginHorizontal: 0,
    borderRadius: 10,
  },
  cancelButton: {
    backgroundColor: colors.darkBlue,
    marginLeft: 10,
    borderRadius: 10,
  },
  buttonContainer: isTabDevice()
    ? {
        backgroundColor: '#F194FF',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: wp('10%'),
        height: 45,
      }
    : {
        backgroundColor: '#F194FF',
        borderRadius: 10,
        padding: 10,
        elevation: 2,
        width: wp('22%'),
        height: 45,
      },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 17,
  },
});

export default RsvpNoteModalStyles;
