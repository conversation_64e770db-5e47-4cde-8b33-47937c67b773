import React, { useEffect, useState } from 'react';
import { Text, View, Modal, TouchableHighlight } from 'react-native';
import customRemovePlayerModalStyle from './RemovePlayerModalStyles';
import useStyles from '../../../hooks/useStyles';

export default function RemovePlayersModal({ keyVal }, { visible }) {
  const RemovePlayerModalStyle = useStyles(customRemovePlayerModalStyle);
  const [modalVisible, setModalVisible] = useState(true);

  return (
    <View style={RemovePlayerModalStyle.container}>
      <View style={RemovePlayerModalStyle.innerContaier}>
        <Modal animationType="slide" transparent={true} visible={modalVisible}>
          <View style={RemovePlayerModalStyle.centeredView}>
            <View style={RemovePlayerModalStyle.modalView}>
              <Text style={RemovePlayerModalStyle.modalText}>
                Are you sure you want to remove this player ?
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  alignContent: 'center',
                }}
              >
                <TouchableHighlight
                  style={{ ...RemovePlayerModalStyle.buttonYes }}
                  onPress={() => {
                    setModalVisible(!modalVisible);
                  }}
                >
                  <Text style={RemovePlayerModalStyle.textStyle}>Yes</Text>
                </TouchableHighlight>

                <TouchableHighlight
                  style={{ ...RemovePlayerModalStyle.buttonNo }}
                  onPress={() => {
                    setModalVisible(!modalVisible);
                  }}
                >
                  <Text style={RemovePlayerModalStyle.textStyle}>No</Text>
                </TouchableHighlight>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </View>
  );
}
