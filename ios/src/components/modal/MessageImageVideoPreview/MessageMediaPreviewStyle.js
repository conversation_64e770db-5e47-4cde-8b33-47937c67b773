import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const AddTeamStyles = colors => ({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
  },
  modalView: {
    backgroundColor: colors.darkBlue,
    padding: wp('5%'),
    shadowColor: '#000',
    width: wp('100%'),
    height: hp('100%'),
    opacity: 0.98,
  },
  modalViewTop: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: hp('10%'),
        marginBottom: hp('5%'),
        width: '100%',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: hp('10%'),
        marginBottom: hp('5%'),
        width: '100%',
      },
  modalViewTop1: isTabDevice()
    ? {
        width: wp('5%'),
      }
    : {
        width: wp('10%'),
      },
  modalViewTop2: isTabDevice()
    ? {
        width: wp('90%'),
        flexDirection: 'row',
      }
    : {
        width: wp('90%'),
        flexDirection: 'row',
      },
  modalViewTop3: isTabDevice()
    ? {}
    : {
        width: wp('15%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      },
  modalViewTopPropic: isTabDevice()
    ? {
        marginRight: wp('1%'),
      }
    : {
        marginRight: wp('2%'),
      },
  modalViewTopDetails: {},
  modalViewTopDetails1: {},
  modalViewTopDetails2: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalViewBottomLoader: {
    position: 'absolute',
    left: '50%',
    top: '20%',
  },
  modalViewBottom: isTabDevice()
    ? {
        height: hp('70%'),
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      }
    : {
        height: hp('75%'),
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      },
  modalViewBottomDownload: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: hp('20%'),
        width: wp('100%'),
        left: wp('-5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingBottom: wp('8%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        height: hp('15%'),
        width: wp('100%'),
        left: wp('-5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingBottom: wp('12%'),
      },
  modalViewBottomImage: isTabDevice()
    ? {
        width: '100%',
        height: wp('40%'),
        resizeMode: 'contain',
      }
    : {
        width: '100%',
        height: wp('80%'),
        resizeMode: 'contain',
      },
  modalText: isTabDevice()
    ? {
        color: '#fff',
        fontSize: 20,
      }
    : {
        color: '#fff',
        fontSize: wp('3%'),
      },
  messageRightTopProImage: isTabDevice()
    ? {
        width: wp('5%'),
        height: wp('5%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      }
    : {
        width: wp('10%'),
        height: wp('10%'),
        borderRadius: wp('100%'),
        resizeMode: 'cover',
      },
});

export default AddTeamStyles;
