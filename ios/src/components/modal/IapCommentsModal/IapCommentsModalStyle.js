
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IapCommentsModalStyle = colors => ({
  centeredView: isTabDevice()
    ? {
      width: wp('100%'),
      height: hp('100%'),
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    }
    : {
      width: wp('100%'),
      height: hp('100%'),
      justifyContent: 'center',
      alignItems: 'center',
    },
  overlay: isTabDevice()
    ? {
      backgroundColor: colors.darkBlue,
      width: '100%',
      height: '100%',
      position: 'absolute',
      top: 0,
      left: 0,
      opacity: 0.9,
    }
    : {
      backgroundColor: colors.darkBlue,
      width: '100%',
      height: '100%',
      position: 'absolute',
      top: 0,
      left: 0,
      opacity: 0.9,
    },
  modalArea: isTabDevice()
    ? {
      // width: wp('40%'),
      // height: hp('20%'),
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.tileBackground,
      borderRadius: 30,
      padding: wp('3%'),
    }
    : {
      width: wp('95%'),
      flexDirection: 'column',
      alignItems: 'center',
      backgroundColor: colors.tileBackground,
      borderRadius: 20,
      padding: wp('4%'),
    },
  closeButton: {
    position: 'absolute',
    top: wp('2 %'),
    right: wp('2%'),
    zIndex: 10,
  },
  content: isTabDevice()
    ? {
      width: wp('85%'),
    }
    : {
      width: '100%',
    },
  title: isTabDevice()
    ? {
      fontSize: wp('2%'),
      fontFamily: 'Poppins-Bold',
      color: colors.white,
      marginBottom: wp('2%'),
      textAlign: 'center'
    }
    : {
      fontSize: wp('4%'),
      fontFamily: 'Poppins-Bold',
      color: colors.white,
      textAlign: 'center'
    },
  contentArea: isTabDevice() ? {
    flexDirection: 'row'
  } : {
    flexDirection: 'column'
  },
  leftContent: isTabDevice() ? {
    gap: 5,
    borderRightWidth: 1,
    borderColor: colors.lightGrey
  } : {
    borderBottomWidth: 1,
    borderColor: colors.lightGrey,
    paddingBottom: wp('2%'),
  },
  rightContent: isTabDevice() ? {
    paddingLeft: wp('2%'),
  } : {
    paddingTop: wp('2%'),
  },
  statsWrapper: {},
  catName: isTabDevice() ? {
    fontSize: wp('1.5%'),
    fontFamily: 'Poppins-Medium',
    color: colors.white,
    marginBottom: wp('2%'),
  } : {
    fontSize: wp('3%'),
    fontFamily: 'Poppins-Medium',
    color: colors.white,
    marginBottom: wp('4%'),
    marginTop: wp('4%'),
  },
  lastComment: {
    flexDirection: 'row'},
  lastCommentText: isTabDevice() ? {
    fontSize: wp('0.9%'),
    fontFamily: 'Poppins-Medium',
    color: colors.white,
  } : {
    fontSize: wp('2.5%'),
    fontFamily: 'Poppins-Medium',
    color: colors.white,
  },
  lastCommentTextBlue: isTabDevice() ? {
    fontSize: wp('0.9%'),
    fontFamily: 'Poppins-Medium',
    color: colors.aquaBlue,
  } : {
    fontSize: wp('2.5%'),
    fontFamily: 'Poppins-Medium',
    color: colors.aquaBlue,
  },
  commentContainer: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginBottom: hp('1%'),
        width: '100%'
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        marginBottom: hp('1%'),
      },
  dateWrapper: {
    flexDirection: 'row',
  },
  date: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.aquaBlue,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
  day: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        marginLeft: wp('0.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        marginLeft: wp('0.5%'),
        fontFamily: 'Poppins-Regular',
      },
  comment: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3'),
        marginTop: hp('0.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5'),
        marginTop: hp('0.5%'),
      },
  commentsList: isTabDevice() ? {
    height: hp('30%'),
    width: wp("22%"),
  } : {
    height: hp('20%')
  }
});
export default IapCommentsModalStyle;
