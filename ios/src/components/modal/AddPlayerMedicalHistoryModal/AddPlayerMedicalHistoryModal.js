import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';

import { Ionicons } from '@expo/vector-icons';

import ModalWrapper from '../ModalWrapper/ModalWrapper';

import PlayerInfoMedicalHistoryAddContainer from '../../../Container/PlayerInfoMedicalHistoryAddContainer/PlayerInfoMedicalHistoryAddContainer';
import customAddPlayerMedicalHistoryModalStyle from './AddPlayerMedicalHistoryModalStyle';
import useStyles from '../../../hooks/useStyles';

const AddPlayerMedicalHistoryModal = ({
  addUpdate,
  modalVisible,
  setModalVisible,
  isAddModal,
}) => {
  const AddPlayerMedicalHistoryModalStyle = useStyles(
    customAddPlayerMedicalHistoryModalStyle
  );

  return (
    <View style={AddPlayerMedicalHistoryModalStyle.centeredView}>
      {modalVisible && (
        <ModalWrapper>
          <PlayerInfoMedicalHistoryAddContainer
            addUpdate={addUpdate}
            setModalVisible={setModalVisible}
            isAddModal={isAddModal}
          />
        </ModalWrapper>
      )}

      <View style={AddPlayerMedicalHistoryModalStyle.addView}>
        <TouchableOpacity
          style={AddPlayerMedicalHistoryModalStyle.AddTouchableOpacity}
          onPress={() => setModalVisible(true)}
        >
          <Ionicons
            color="#fff"
            name="add"
            size={35}
            style={AddPlayerMedicalHistoryModalStyle.AddIcon}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AddPlayerMedicalHistoryModal;
