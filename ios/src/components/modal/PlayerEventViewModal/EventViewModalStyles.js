import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

import { colorPalette } from '../../../constants/constants';
import { isTabDevice } from '../../../config/appConfig';

const EventViewModalStyles = colors => ({
  repeatEventDetailsContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: wp('1%'),
      }
    : { flexDirection: 'row', justifyContent: 'space-between' },

  repeatEventEndDateContainer: isTabDevice()
    ? {
        alignItems: 'flex-end',
        marginRight: wp('1%'),
      }
    : {},
  repeatEventDetailsFieldWrapper: isTabDevice()
    ? {
        width: '40%',
        flexDirection: 'row',
        alignItems: 'center',
        textAlign: 'center',
        justifyContent: 'flex-start',
      }
    : {
        width: '80%',
      },

  repeatEventDetailsInputField: isTabDevice()
    ? {
        width: '80%',
        height: hp('6%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        width: '100%',
        height: wp('9%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },

  repeatEventDetailsDateField: isTabDevice()
    ? {
        width: '90%',
        height: hp('6%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        width: '100%',
        height: wp('9%'),
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },
  repeatEventDetailsLabelWrapper: isTabDevice()
    ? {
        marginRight: wp('1%'),
      }
    : {},
  repeatEventDetailsLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        marginTop: hp('1%'),

        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
      },
  repeatEventDetailsText: isTabDevice()
    ? {
        textAlign: 'center',
        width: '100%',
        fontSize: hp('2%'),
        color: '#ffff',
        justifyContent: 'center',
        fontFamily: 'Poppins-Regular',
      }
    : {
        textAlign: 'left',
        width: '70%',
        fontSize: wp('3%'),
        color: '#ffff',
        paddingLeft: wp('3%'),
        fontFamily: 'Poppins-Regular',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('60%'),
        height: wp('63%'),
        flexDirection: 'column',
      }
    : {
        width: wp('90%'),
        height: hp('87%'),
        flexDirection: 'column',
      },
  row: {
    width: '45%',
  },
  row2: isTabDevice() ? {
    width: wp('64%'),
    height: '13%',
  } : {
    width: '100%',
    height: '20%',
  },
  row3: {
    width: '100%',
    marginTop: wp('2%'),
  },
  form: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
  },
  column: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    width: wp('60%'),
    height: hp('60%'),
  },
  label: isTabDevice()
    ? {
        fontSize: wp('1.4%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        width: '94%',
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        width: '85%',
      },
  label2: isTabDevice()
    ? {
        fontSize: wp('1.4%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      }
    : {
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      },
  textContainer: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: wp('1%'),
        padding: wp('1.3%'),
        // height: hp('6%'),
        marginBottom: wp('2.5%'),
        width: '100%',
      }
    : {
        backgroundColor: colors.tileBackground,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderRadius: wp('2%'),
        padding: wp('2%'),
        // height: hp('6%'),
        marginBottom: wp('4%'),
        width: '100%',
      },
  notesContainer: isTabDevice()
    ? {
        height: '100%',
        backgroundColor: colors.tileBackground,
        borderRadius: wp('1%'),
        padding: wp('1.5%'),
      }
    : {
        height: '100%',
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        padding: wp('1.5%'),
      },
  rsvpContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  rsvpButtons: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        paddingBottom: 8,
        paddingTop: 8,
        borderRadius: wp('1%'),
        height: wp('4%'),
        width: wp('8%'),
        alignItems: 'center',
        marginLeft: 15,
        flexDirection: 'row',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.tileBackground,
        paddingBottom: 8,
        paddingTop: 8,
        borderRadius: wp('1%'),
        height: wp('8%'),
        width: wp('14%'),
        alignItems: 'center',
        marginLeft: 15,
        flexDirection: 'row',
        justifyContent: 'center',
      },
  rsvpButtonSelected: {
    backgroundColor: colors.green,
  },
  rsvpButtonText: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Bold',
      },
  rsvpButtonTextSelected: {
    color: colors.white,
  },
  backArrow: {
    marginTop: wp('0.2%'),
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  headerTitle: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginTop: wp('0.5%'),
        marginLeft: wp('2%'),
        marginBottom: wp('3%'),
      },
  requiredAstric: isTabDevice()
    ? {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('0.7%'),
      }
    : {
        color: colors.red,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1.5%'),
      },
  inputFieldWrapper: isTabDevice(0) ? {
    width: wp('31%')
  } : {
    width: '100%',
  },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('1%'),
      },
  inputField: {
    width: '100%',
    height: hp('6%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  locationIcon: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'contain',
        position: 'absolute',
        right: wp('1%'),
        // top: wp('1%'),
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'contain',
        position: 'absolute',
        right: wp('2%'),
        // top: wp('1%'),
      },
  timerIcon: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'contain',
        position: 'absolute',
        right: wp('1%'),
        top: wp('1.2%'),
      }
    : {
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'contain',
        position: 'absolute',
        right: wp('2%'),
        top: wp('2%'),
      },
  rsvpAbsenceContainer: isTabDevice()
    ? {
        marginLeft: wp('1%'),
      }
    : {
        marginLeft: wp('2%'),
      },
  responseLink: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ActivitySpinner: isTabDevice()
    ? {
        marginLeft: wp('8%'),
      }
    : { marginLeft: wp('18%') },

  rsvpContainer1: isTabDevice()
    ? { flexDirection: 'row' }
    : {
        marginTop: wp('5%'),
      },
  rsvpContainer2: isTabDevice()
    ? { flexDirection: 'row' }
    : { flexDirection: 'row' },
  rsvpAbsenceTitle: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        color: colors.green,
        marginRight: wp('0.5%'),
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
        marginRight: wp('1.5%'),
      },
  rsvpAbsenceReason: isTabDevice()
    ? { fontSize: wp('1.5%'), color: colors.white }
    : { fontSize: wp('3.5%'), color: colors.white },
});

export default EventViewModalStyles;
