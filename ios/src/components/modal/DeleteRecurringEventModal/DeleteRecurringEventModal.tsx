import React, { useEffect, useState, FC } from 'react';
import { Ionicons, FontAwesome } from '@expo/vector-icons';
import {
  View,
  Text,
  TouchableHighlight,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import CustomDeleteRecurringEventModalStyles from './DeleteRecurringEventModalStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import { DELETE_RECURRING_EVENT_OPTIONS } from '../../../constants/data';
import { deleteRecurringEventsModalTexts } from '../../../constants/constants';
import useStyles from '../../../hooks/useStyles';
import useColors from '../../../hooks/useColors';

type DeleteRecurringEventModalType = {
  loading: boolean;
  cancelAction: Function;
  submitAction: Function;
  selectedOptionMethod: string;
  setSelectedOptionMethod: Function;
  errorMessage: any;
};

const DeleteRecurringEventModal: FC<DeleteRecurringEventModalType> = ({
  loading,
  cancelAction,
  submitAction,
  selectedOptionMethod,
  setSelectedOptionMethod,
  errorMessage,
}) => {
  const DeleteRecurringEventModalStyles = useStyles(
    CustomDeleteRecurringEventModalStyles
  );
  const colors = useColors();

  const renderItem = ({ item }: any) => {
    const { key, title } = item;
    return (
      <TouchableOpacity onPress={() => setSelectedOptionMethod(key)}>
        <View style={DeleteRecurringEventModalStyles.flatListDataContainer}>
          {selectedOptionMethod === key ? (
            <Ionicons
              name="radio-button-on"
              size={20}
              color={colors.aquaBlue}
            />
          ) : (
            <FontAwesome name="circle" size={20} color={colors.darkBlue} />
          )}
          <Text style={DeleteRecurringEventModalStyles.flatListDataOptionText}>
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <View style={DeleteRecurringEventModalStyles.container}>
        <ModalWrapper transparent>
          <View style={DeleteRecurringEventModalStyles.centeredView}>
            <View style={DeleteRecurringEventModalStyles.overlay}></View>
            <View style={DeleteRecurringEventModalStyles.modalView}>
              <View style={DeleteRecurringEventModalStyles.modalTitleContainer}>
                <Text style={DeleteRecurringEventModalStyles.modalTitle}>
                  {deleteRecurringEventsModalTexts.Title}
                </Text>
              </View>
              <View
                style={DeleteRecurringEventModalStyles.modalOptionsContainer}
              >
                <FlatList
                  data={DELETE_RECURRING_EVENT_OPTIONS}
                  renderItem={renderItem}
                  keyExtractor={item => item.id}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                  showsHorizontalScrollIndicator={false}
                />
              </View>
              {errorMessage ? (
                <Text style={DeleteRecurringEventModalStyles.errorMessage}>
                  {errorMessage.includes(
                    deleteRecurringEventsModalTexts.MATCH_ALREADY_STARTED
                  )
                    ? deleteRecurringEventsModalTexts.ErrorMessage
                    : errorMessage}
                </Text>
              ) : null}
              <View style={DeleteRecurringEventModalStyles.modalContent}>
                {loading ? (
                  <View
                    style={{
                      ...DeleteRecurringEventModalStyles.buttonContainer,
                      ...DeleteRecurringEventModalStyles.confirmButton,
                      opacity: selectedOptionMethod ? null : 0.5,
                    }}
                  >
                    <ActivityIndicator color={colors.white} />
                  </View>
                ) : (
                  <TouchableHighlight
                    style={{
                      ...DeleteRecurringEventModalStyles.buttonContainer,
                      ...DeleteRecurringEventModalStyles.confirmButton,
                    }}
                    onPress={() => submitAction()}
                  >
                    <Text style={DeleteRecurringEventModalStyles.textStyle}>
                      Confirm
                    </Text>
                  </TouchableHighlight>
                )}

                <TouchableOpacity
                  style={{
                    ...DeleteRecurringEventModalStyles.buttonContainer,
                    ...DeleteRecurringEventModalStyles.cancelButton,
                    elevation: 0,
                    borderRadius: 0,
                    backgroundColor: 'transparent',
                  }}
                  onPress={() => cancelAction()}
                  disabled={loading}
                >
                  <Text style={DeleteRecurringEventModalStyles.textStyle}>
                    Cancel
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ModalWrapper>
      </View>
    </View>
  );
};

export default DeleteRecurringEventModal;
