import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MatchPlanNotReadyModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('40%'),
        height: hp('20%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      }
    : {
        width: wp('80%'),
        height: hp('20%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
      },
  closeButton: isTabDevice()
    ? {
        position: 'absolute',
        right: 20,
        top: 20,
      }
    : {
        position: 'absolute',
        right: 10,
        top: 10,
      },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
  },
  headerTitle: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontWeight: 'bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('4%'),
        fontWeight: 'bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  messageContainer: {
    display: 'flex',
    marginLeft: 30,
    marginRight: 30,
    flex: 1,
    marginBottom: 20,
    alignItems: 'center',
  },
  message: {
    color: colors.red,
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default MatchPlanNotReadyModalStyles;
