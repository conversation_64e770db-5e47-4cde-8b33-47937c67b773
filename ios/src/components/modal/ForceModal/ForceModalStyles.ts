import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ForceModalStyles = (colors: any) => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    margin: 'auto',
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'relative',
      }
    : {},
  heading: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: hp('2.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      },
  forgotHeading: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        textAlign: 'center',
        lineHeight: wp('3.5%'),
      }
    : {
        color: colors.white,
        fontSize: hp('2.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
        lineHeight: wp('7%'),
      },
  descriptionWrapper: isTabDevice()
    ? {
        width: wp('30%'),
        marginTop: wp('1.5%'),
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        marginTop: hp('2.5%'),
      },
  description: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        textAlign: 'center',
        fontFamily: 'Poppins-medium',
        marginTop: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: hp('2%'),
        textAlign: 'center',
        fontFamily: 'Poppins-medium',
        marginTop: wp('3%'),
      },
  descriptionRed: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.5%'),
        textAlign: 'center',
        fontWeight: 'bold',
      }
    : {
        color: colors.red,
        fontSize: hp('2%'),
        textAlign: 'center',
        fontWeight: 'bold',
      },
  buttonWrapper: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        marginTop: wp('2%'),
        padding: wp('1.3%'),
        paddingRight: wp('2%'),
        paddingLeft: wp('2%'),
        borderRadius: wp('1.5%'),
        width: '50%',
      }
    : {
        backgroundColor: colors.aquaBlue,
        marginTop: hp('2.5%'),
        padding: hp('1.5%'),
        paddingRight: hp('2.5%'),
        paddingLeft: hp('2.5%'),
        borderRadius: hp('1.5%'),
      },
  linkWrapper: isTabDevice()
    ? {
        padding: wp('1.3%'),
        paddingRight: wp('2%'),
        paddingLeft: wp('2%'),
        borderRadius: wp('1.5%'),
        width: '70%',
      }
    : {
        padding: hp('1.5%'),
        paddingRight: hp('2.5%'),
        paddingLeft: hp('2.5%'),
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontWeight: 'bold',
        textAlign: 'center',
      },
  linkText: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      }
    : {
        color: colors.aquaBlue,
        fontSize: wp('6%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  closeIcon: isTabDevice()
    ? {
        position: 'absolute',
        top: wp('2%'),
        right: wp('1.5%'),
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
      }
    : {
        position: 'absolute',
        top: wp('3%'),
        right: wp('5.5%'),
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
      },

  loaderSpinner: isTabDevice()
    ? {
        justifyContent: 'center',
        width: wp('20%'),
        height: wp('20%'),
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('3%'),
        color: colors.white,
        position: 'relative',
      }
    : {
        justifyContent: 'center',
        width: wp('50%'),
        height: wp('50%'),
        alignItems: 'center',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        padding: wp('3%'),
        color: colors.white,
        position: 'relative',
      },
  loaderErrorMsg: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.tileBackground,
        width: wp('30%'),
        height: wp('22%'),
        borderRadius: 30,
        padding: wp('3%'),
        position: 'relative',
      }
    : {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.tileBackground,
        width: wp('85%'),
        height: wp('60%'),
        borderRadius: 30,
        padding: wp('3%'),
        position: 'relative',
      },
  forgotErrorMsg: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.tileBackground,
        width: wp('45%'),
        height: wp('22%'),
        borderRadius: 30,
        padding: wp('3%'),
        position: 'relative',
      }
    : {
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.tileBackground,
        width: wp('85%'),
        height: wp('60%'),
        borderRadius: 30,
        padding: wp('3%'),
        position: 'relative',
      },
  forceModalWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loading: {
    width: '50%',
    height: '50%',
    resizeMode: 'contain',
  },

  content: isTabDevice()
    ? {}
    : {
        marginTop: wp('10%'),
      },
});

export default ForceModalStyles;
