import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

import { colorPalette } from '../../../constants/constants';
import { isTabDevice } from '../../../config/appConfig';

const EventRSVPModalStyles = colors => ({
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  modalArea: isTabDevice()
    ? {
        width: wp('35%'),
        height: hp('70%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        position: 'relative',
      }
    : {
        width: wp('85%'),
        height: hp('70%'),
        flexDirection: 'column',
        backgroundColor: colors.tileBackground,
        borderRadius: 30,
        position: 'relative',
      },
  closeButtonWrapper: {
    position: 'absolute',
    right: 20,
    top: 0,
    width: '5%',
    height: '50%',
  },
  closeButton: isTabDevice()
    ? {
        fontSize: wp('2%'),
      }
    : {
        fontSize: wp('5%'),
      },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
  },
  headerTitle: isTabDevice()
    ? {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginLeft: wp('2%'),
        marginBottom: wp('2%'),
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  userListContainer: isTabDevice()
    ? {
        display: 'flex',
        marginLeft: 30,
        marginRight: 30,
        flex: 1,
        marginBottom: 20,
      }
    : {
        display: 'flex',
        marginLeft: 10,
        marginRight: 10,
        flex: 1,
        marginBottom: 20,
      },
  userListItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  profileImage: {
    width: 50,
    height: 50,
    resizeMode: 'cover',
    borderRadius: 10,
  },
  left: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flex: 1,
  },
  userName: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        marginLeft: 20,
        flexShrink: 1,
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        marginLeft: 20,
        width: '68%',
      },
  responseText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('0.9%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
      },
  responseContainer: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('7%'),
        height: wp('3%'),
        borderRadius: 40,
        marginTop: 5,
        marginBottom: 5,
        marginRight: 5,
      }
    : {
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('20%'),
        height: wp('7.5%'),
        borderRadius: wp('10%'),
        marginTop: 5,
        marginBottom: 5,
        marginRight: 5,
      },
  responseYes: {
    backgroundColor: colors.green,
  },
  responseNo: {
    backgroundColor: colors.red,
  },
  responseNotAvailable: {
    backgroundColor: colors.lightGrey,
  },
  exclamationIcon: isTabDevice()
    ? {
        position: 'absolute',
        top: 0,
        right: -3,
        width: wp('1.5%'),
        height: wp('1.5%'),
      }
    : {
        position: 'absolute',
        top: 0,
        right: -3,
        width: wp('4.5%'),
        height: wp('4.5%'),
      },
  rsvpCountWrapper: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        padding: wp('1%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 15,
        width: '100%',
      }
    : {
        backgroundColor: colors.darkBlue,
        padding: wp('1.5%'),
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 15,
      },
  rsvpCountText: isTabDevice()
    ? {
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      }
    : {
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      },
  rsvpCount: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: wp('1%'),
      }
    : {
        minWidth: wp('7%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  noData: {
    height: hp('30%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  rsvpCountTab: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: wp('0.6%'),
        paddingRight: wp('0.6%'),
        paddingTop: wp('0.4%'),
        paddingBottom: wp('0.4%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingLeft: wp('1.5%'),
        paddingRight: wp('1.5%'),
        paddingTop: wp('0.4%'),
        paddingBottom: wp('0.4%'),
      },
  rsvpCountTabWrapper: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: 5,
        width: '32.5%',
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 5,
        width: '31.5%',
      },
  rsvpCountDivider: isTabDevice()
    ? {
        borderWidth: 1,
        height: '100%',
        borderColor: colors.borderBlue,
        marginLeft: 5,
        marginRight: 5,
      }
    : {
        borderWidth: 1,
        height: '100%',
        borderColor: colors.borderBlue,
        marginLeft: 5,
        marginRight: 5,
      },
  rsvpResponseContentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    width: '100%',
  },
  responseNoteIcon: {
    opacity: 0.5,
    position: 'absolute',
    right: 2,
  },
  responseNoText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.8%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
      },
});

export default EventRSVPModalStyles;
