import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const syncDeviceModal = colors => ({
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        // height: wp('33%'),
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        paddingTop: wp('6%'),
      },
  modalTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        maxWidth: wp('40%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        maxWidth: wp('80%'),
        textAlign: 'center',
        marginTop: wp('2%'),
      },
  modalTitle2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        width: '20%',
        backgroundColor: colors.red,
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        marginTop: wp('2%'),
        textAlign: 'center',
      },
  playerName: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.green,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  bodyText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontWeight: 'bold',
      },
  modalHeader: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
  deviceItem: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        marginBottom: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1%'),
        padding: wp('1%'),
        paddingTop: wp('0.5%'),
        paddingBottom: wp('0.5%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        marginBottom: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('2%'),
        padding: wp('1%'),
        paddingTop: wp('0.5%'),
        paddingBottom: wp('0.5%'),
      },
  listItem: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputField: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        marginBottom: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1%'),
        padding: wp('1%'),
        marginTop: wp('0.5%'),
        color: colors.white,
      }
    : {
        backgroundColor: colors.darkBlue,
        marginBottom: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('2%'),
        padding: wp('2%'),
        marginTop: wp('2%'),
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        padding: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        marginTop: wp('2%'),
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      },
  buttonWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp('3%'),
  },
  buttonYes: isTabDevice()
    ? {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.aquaBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('7%'),
        textAlign: 'center',
        marginRight: wp('2%'),
      }
    : {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.aquaBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('15%'),
        textAlign: 'center',
        marginRight: wp('2%'),
      },
  buttonNo: isTabDevice()
    ? {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.darkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('7%'),
        textAlign: 'center',
      }
    : {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.darkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('15%'),
        textAlign: 'center',
      },
  buttonSuccess: isTabDevice()
    ? {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.green,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        // width: '100%',
        textAlign: 'center',
        marginTop: wp('3%'),
      }
    : {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.green,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        textAlign: 'center',
        marginTop: wp('3%'),
      },
  playerListContainer: isTabDevice()
    ? {
        width: wp('30%'),
      }
    : {
        width: wp('65%'),
        marginTop: wp('2%'),
      },
  centerAlign: {
    alignItems: 'center',
  },
  icon: {
    marginBottom: wp('3%'),
    alignItems: 'center',
  },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 0,
        top: 0,
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 10,
        top: 10,
        zIndex: 9,
      },
});
export default syncDeviceModal;
