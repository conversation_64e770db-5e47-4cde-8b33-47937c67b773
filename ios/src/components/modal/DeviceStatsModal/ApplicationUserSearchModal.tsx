import { AntDesign } from '@expo/vector-icons';
import React, { FC, useState } from 'react';

import {
  KeyboardAvoidingView,
  Modal,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import { userDetailsFromPMType } from '../../../store/reducers/DeviceStats/DeviceStatsReducer';
import customAddPlayerModalStyle from '../AddPlayerModal/AddPlayerModalStyle';
import customSyncDeviceModalStyle from './SyncDeviceModalStyles';

type ApplicationUserSearchModalType = {
  setIsApplicationUserSearchModalOpen: Function;
  isPlayerDetailsScreen: boolean;
  onUserSearch: Function;
  userDetailsFromPM: userDetailsFromPMType | null;
  userDetailsFromPMLoading: boolean;
  onSyncDataClick: Function;
};

const ApplicationUserSearchModal: FC<ApplicationUserSearchModalType> = ({
  setIsApplicationUserSearchModalOpen,
  isPlayerDetailsScreen,
  onUserSearch,
  userDetailsFromPM,
  userDetailsFromPMLoading,
  onSyncDataClick,
}) => {
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);
  const SyncDeviceModalStyle = useStyles(customSyncDeviceModalStyle);
  const [playerMakerId, setPlayerMakerId] = useState('');

  return (
    <View style={AddPlayerModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <TouchableWithoutFeedback
          onPress={Keyboard.dismiss}
          accessible={false}
          touchSoundDisabled={true}
        >
          <View style={AddPlayerModalStyle.centeredView}>
            <View style={AddPlayerModalStyle.overlay}></View>
            <KeyboardAvoidingView
              behavior="position"
              keyboardVerticalOffset={30}
            >
              <View style={SyncDeviceModalStyle.modalView}>
                <TouchableOpacity
                  style={SyncDeviceModalStyle.closeButton}
                  onPress={() => {
                    setIsApplicationUserSearchModalOpen(false);
                  }}
                >
                  <AntDesign name="close" size={20} color="#FFFFFF" />
                </TouchableOpacity>
                <Text style={SyncDeviceModalStyle.modalTitle}>
                  PlayerMaker Data
                </Text>

                <View style={SyncDeviceModalStyle.playerListContainer}>
                  <Text style={SyncDeviceModalStyle.modalHeader}>
                    Player's ID
                  </Text>
                  <TextInput
                    placeholder={`Enter Player ID`}
                    style={SyncDeviceModalStyle.inputField}
                    multiline={false}
                    placeholderTextColor="#595959"
                    value={playerMakerId}
                    onChangeText={text =>
                      setPlayerMakerId(text.replace(/\s/g, ''))
                    }
                    editable={!isPlayerDetailsScreen}
                  />
                  {isPlayerDetailsScreen ? (
                    <>
                      <Text style={SyncDeviceModalStyle.modalHeader}>
                        Player's Name
                      </Text>
                      <TextInput
                        style={SyncDeviceModalStyle.inputField}
                        multiline={false}
                        placeholderTextColor="#595959"
                        value={userDetailsFromPM?.pmUserName || ''}
                        editable={false}
                      />
                      <TouchableOpacity
                        onPress={() => onSyncDataClick(playerMakerId)}
                        style={SyncDeviceModalStyle.button}
                      >
                        <Text style={SyncDeviceModalStyle.buttonText}>
                          Sync data
                        </Text>
                      </TouchableOpacity>
                    </>
                  ) : playerMakerId ? (
                    <TouchableOpacity
                      onPress={() => onUserSearch(playerMakerId)}
                      disabled={userDetailsFromPMLoading}
                      style={SyncDeviceModalStyle.button}
                    >
                      <Text style={SyncDeviceModalStyle.buttonText}>
                        {userDetailsFromPMLoading ? 'Search...' : 'Search'}
                      </Text>
                    </TouchableOpacity>
                  ) : null}
                </View>
              </View>
            </KeyboardAvoidingView>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default ApplicationUserSearchModal;
