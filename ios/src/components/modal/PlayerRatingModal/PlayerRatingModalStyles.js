import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

import { colorPalette } from '../../../constants/constants';

const PlayerRatingModalStyles = colors => ({
  container: {
    // backgroundColor: colors.red,
    height: hp('100%'),
    width: wp('100%'),
    position: 'relative',
  },
  overlay: {
    backgroundColor: colors.darkBlue,
    height: hp('100%'),
    width: wp('100%'),
    position: 'absolute',
    top: 0,
    left: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: hp('80%'),
        width: wp('95%'),
        borderRadius: wp('2%'),
        padding: wp('2%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        width: wp('95%'),
        borderRadius: wp('2%'),
        padding: wp('2%'),
        marginBottom: wp('10%'),
        maxHeight: hp('80%'),
      },
  titleRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: wp('2%'),
        width: '100%',
        paddingLeft: wp('1%'),
        paddingRight: wp('2%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('3%'),
        width: '100%',
        paddingLeft: wp('1%'),
        paddingRight: wp('2%'),
      },
  title: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('2%'),
        color: colors.white,
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('3.5%'),
        color: colors.white,
      },
  buttons: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: wp('22%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: wp('30%'),
      },
  button: {
    backgroundColor: colors.aquaBlue,
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonTransparent: {
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  button2: {
    backgroundColor: colors.borderBlue,
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
      },
  errorText: {
    color: colors.red,
    fontSize: wp('1%'),
    fontWeight: 'bold',
  },
});

export default PlayerRatingModalStyles;
