import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const DownloadReportModalStyles = colors => ({
  container: {},
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  content: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        width: wp('40%'),
        padding: 50,
        color: 'white',
        borderRadius: 30,
        position: 'relative',
      }
    : {
        backgroundColor: colors.borderBlue,
        width: wp('80%'),
        padding: wp('5%'),
        color: 'white',
        borderRadius: 30,
        position: 'relative',
      },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      },
  deleteTeamModalView: {
    width: 500,
  },
  openButton: {
    backgroundColor: '#F194FF',
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    width: 80,
    height: 45,
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 17,
  },
  modalText: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  modalSubText: {
    fontSize: wp('1.3%'),
  },
  errorText: {
    color: colors.red,
  },
  modalContent: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
  },
  yesButton: {
    backgroundColor: colors.aquaBlue,
    marginHorizontal: 0,
  },
  noButton: {
    backgroundColor: colors.darkBlue,
    marginLeft: 10,
  },
  message: isTabDevice()
    ? {
        fontSize: wp('2.6%'),
        color: colors.white,
        marginBottom: 10,
        fontWeight: 'bold',
      }
    : {
        fontSize: wp('7%'),
        color: colors.white,
        marginBottom: 10,
        fontWeight: 'bold',
      },
  text: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        marginBottom: 30,
        lineHeight: 30,
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        marginBottom: 30,
        lineHeight: 30,
      },
  description: {
    color: 'green',
    paddingVertical: 15,
    fontSize: 18,
  },
  reports: {
    display: 'flex',
    flexDirection: 'column',
  },
  report: {
    marginBottom: 10,
    backgroundColor: colors.darkBlue,
    padding: 15,
    borderRadius: 5,
  },
  iconImage: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        resizeMode: 'contain',
      },
  downloadWrapper: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 36,
  },
  downloadInnerWrapper: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    width: '85%',
  },
  downloadFile: isTabDevice()
    ? {
        color: colors.white,
        marginLeft: wp('0.5%'),
      }
    : {
        color: colors.white,
        marginLeft: wp('2%'),
      },
  close: isTabDevice()
    ? {
        fontSize: 30,
        position: 'absolute',
        top: 20,
        right: 30,
        padding: wp('1%'),
      }
    : {
        fontSize: 30,
        position: 'absolute',
        top: 10,
        right: 20,
        padding: wp('1%'),
        zIndex: 2,
      },
  scroll: {
    maxHeight: 190,
    minHeight: 20,
  },
  scrollText: {
    maxHeight: 150,
    minHeight: 20,
  },
});

export default DownloadReportModalStyles;
