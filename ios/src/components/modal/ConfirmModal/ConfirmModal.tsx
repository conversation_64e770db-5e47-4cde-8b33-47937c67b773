import { AntDesign } from '@expo/vector-icons';
import React, { FC } from 'react';
import { Modal, Text, TouchableOpacity, View } from 'react-native';
import useStyles from '../../../hooks/useStyles';
import customAddPlayerModalStyle from '../AddPlayerModal/AddPlayerModalStyle';
import customConfirmModalStyle from './ConfirmModalStyle';

type ConfirmModalType = {
  isConfirmModalOpen: boolean;
  setIsConfirmModalOpen: (isConfirmModalOpen: boolean) => void;
  isDeleting: boolean;
  handleDeleteMessage: () => void;
};
const ConfirmModal: FC<ConfirmModalType> = ({
  isConfirmModalOpen,
  setIsConfirmModalOpen,
  isDeleting,
  handleDeleteMessage
}) => {
  const ConfirmModalStyle = useStyles(customConfirmModalStyle);
  const AddPlayerModalStyle = useStyles(customAddPlayerModalStyle);

  return (
    <View style={AddPlayerModalStyle.container}>
      <Modal animationType="slide" transparent={true}>
        <View style={AddPlayerModalStyle.centeredView}>
          <View style={AddPlayerModalStyle.overlay}></View>
          <View
            style={{ ...ConfirmModalStyle.modalView, alignItems: 'center' }}
          >
            <TouchableOpacity
              style={ConfirmModalStyle.closeButton} 
              onPress={() => setIsConfirmModalOpen(false)}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={ConfirmModalStyle.modalTitle}>
              Do you want to delete this message
            </Text>
            <View style={ConfirmModalStyle.buttonWrapper}>
              <TouchableOpacity
                onPress={() => handleDeleteMessage()}
                disabled={isDeleting}
                style={ConfirmModalStyle.buttonYes}
              >
                <Text style={ConfirmModalStyle.buttonText}>
                  {isDeleting ? 'deleting...' : 'Yes'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => setIsConfirmModalOpen(false)}
                disabled={isDeleting}
                style={ConfirmModalStyle.buttonNo}
              >
                <Text style={ConfirmModalStyle.buttonText}>No</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ConfirmModal;
