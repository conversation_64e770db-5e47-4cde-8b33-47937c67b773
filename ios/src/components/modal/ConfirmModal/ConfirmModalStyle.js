import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ConfirmModalStyle = colors => ({
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        // height: wp('33%'),
        paddingTop: hp('5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        paddingTop: wp('6%'),
      },
  modalTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Bold',
        maxWidth: wp('40%'),
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        maxWidth: wp('80%'),
        textAlign: 'center',
        marginTop: wp('2%'),
      },
  modalTitle2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        width: '20%',
        backgroundColor: colors.red,
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        marginTop: wp('2%'),
        textAlign: 'center',
      },
      closeButton: isTabDevice()
      ? {
          width: wp('6%'),
          height: wp('5%'),
          alignItems: 'center',
          justifyContent: 'center',
          position: 'absolute',
          right: wp('-0.5'),
          zIndex: 9,
        }
      : {
          width: wp('5%'),
          height: wp('4%'),
          alignItems: 'center',
          justifyContent: 'center',
          position: 'absolute',
          right: wp('2%'),
          top: wp('2%'),
          zIndex: 9,
        },
  button: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        padding: wp('1%'),
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'center',
        marginTop: wp('2%'),
      },
  buttonWrapper: isTabDevice() ? {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp('1.5%'),
  } : {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: wp('3%'),
  },
  buttonYes: isTabDevice()
    ? {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.aquaBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('7%'),
        textAlign: 'center',
        marginRight: wp('2%'),
      }
    : {
        padding: wp('2%'),
        borderRadius: wp('2%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.aquaBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('15%'),
        textAlign: 'center',
        marginRight: wp('2%'),
      },
  buttonNo: isTabDevice()
    ? {
        padding: wp('1%'),
        borderRadius: wp('1%'),
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.darkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('7%'),
        textAlign: 'center',
      }
    : {
        padding: wp('2%'),
        borderRadius: wp('2%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        backgroundColor: colors.darkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        minWidth: wp('15%'),
        textAlign: 'center',
      },
      buttonText: isTabDevice()
      ? {
          color: colors.white,
          fontSize: wp('1.5%'),
          fontWeight: 'bold',
          textAlign: 'center',
        }
      : {
          color: colors.white,
          fontSize: wp('3%'),
          fontWeight: 'bold',
          textAlign: 'center',
        },
});
export default ConfirmModalStyle;
