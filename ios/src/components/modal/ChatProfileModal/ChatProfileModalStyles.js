import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ChatProfileModalStyle = colors => ({
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        height: wp('50%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('2%'),
        paddingTop: wp('5%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('70%'),
        height: hp('57%'),
      },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 0,
        top: 0,
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 10,
        top: 10,
        zIndex: 9,
      },
  playerListContainer: isTabDevice()
    ? {
        width: wp('30%'),
        height: '100%',
      }
    : {
        width: wp('57%'),
        height: '100%',
      },
  playerListContainerTop: {
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: colors.white,
    paddingBottom: 20,
    marginBottom: 10,
  },
  playerListContainerTopTitle: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('2%'),
        marginTop: 10,
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('4%'),
        marginTop: 10,
      },
  playerListContainerBottomTitle: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.5%'),
        marginTop: 10,
        marginBottom: 10,
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3%'),
        marginTop: 10,
        marginBottom: 10,
      },
  playerListContainerBottom: {
    paddingBottom: 10,
  },

  listItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        padding: 5,
        marginBottom: wp('1%'),
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        height: wp('13%'),
        padding: 5,
        paddingLeft: 10,
        paddingRight: 40,
        marginBottom: wp('1%'),
        backgroundColor: colors.darkBlue,
        borderRadius: wp('3%'),
      },
  listItemImageContainer: isTabDevice()
    ? {
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        width: wp('8%'),
        height: wp('8%'),
      },
  listItemImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 100,
  },
  listItemText: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: wp('1%'),
        marginRight: wp('4%'),
        fontSize: wp('1.4%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        paddingLeft: wp('3%'),
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  uploadImageWrapper: isTabDevice()
    ? {
        height: 100,
        width: 100,
        borderRadius: wp('100%'),
        borderWidth: 5,
        borderColor: colors.white,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.green,
      }
    : {
        height: 100,
        width: 100,
        borderRadius: wp('100%'),
        borderWidth: 5,
        borderColor: colors.white,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.green,
      },
  uploadImage: {
    resizeMode: 'cover',
    width: '100%',
    height: '100%',
  },
  playerList: isTabDevice()
    ? {
        height: hp('28%'),
      }
    : {
        height: hp('28%'),
      },
  error: {
    paddingTop: 10,
    color: colors.red,
    fontSize: 12,
  },
});

export default ChatProfileModalStyle;
