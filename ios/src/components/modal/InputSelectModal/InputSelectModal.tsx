import { AntDesign } from '@expo/vector-icons';
import React, { FC, useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  KeyboardAvoidingView,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customInputSelectModal from './InputSelectModalStyles';
import { Feather } from '@expo/vector-icons';
import { isTabDevice } from '../../../config/appConfig';
import { convertCamelCaseToStandardString } from '../../../helpers/common';
import useDebounce from '../../../hooks/useDebounce';

interface InputSelectModal {
  title?: string;
  items: { value: string; label: string }[];
  openModal?: () => void;
  multiple?: boolean;
  enableDoneBtn?: boolean;
  defaultValues?: string[] | null;
  isCheckedIconShown?: boolean[] | null;
  isEnableAutoComplete?: boolean;
  onAutoCompleteChange?: (search: string) => void;
  isEnableAddNew?: boolean;
  isNewDataAdding?: boolean;
  addNewAction?: (search: string) => void;
  isSearchByFetch?: boolean;
  onCloseHook: (data: any) => void;
  onSelectItemHook: (data: any[]) => void;
  isUnselectedAllowed?: boolean;
  extendLabelText?: string;
  onReachEndHandler?: Function;
  newAddedItem?: any[];
}

type SelectedItems = {
  value: string;
  label: string;
}[];

const InputSelectModal: FC<InputSelectModal> = ({
  items,
  title,
  onCloseHook,
  multiple,
  defaultValues,
  enableDoneBtn = false,
  isEnableAutoComplete,
  isEnableAddNew,
  isNewDataAdding,
  addNewAction,
  onAutoCompleteChange,
  isSearchByFetch,
  onSelectItemHook,
  isUnselectedAllowed,
  extendLabelText,
  onReachEndHandler,
  newAddedItem,
  isCheckedIconShown,
}) => {
  const colors = useColors();
  const InputSelectModal = useStyles(customInputSelectModal);

  const [errorMessage, setErrorMessage] = useState('');

  const [selectedItems, setSelectedItems] = useState<SelectedItems | null>(
    null
  );

  const [listItems, setListItems] = useState(items);
  const [searchText, setSearchText] = useState('');
  const debouncedSearchText = useDebounce(searchText, 500);
  const [isComponentInit, setIsComponentInit] = useState(true);

  useEffect(() => {
    onAutoCompleteChange?.(debouncedSearchText);
  }, [debouncedSearchText]);

  const filterItems = (searchText: string) => {
    const filtered = items.filter(item =>
      item.label.toLowerCase().includes(searchText.toLowerCase())
    );
    setListItems([...filtered]);
  };
  useEffect(() => {
    setSearchText('');
  }, []);
  useEffect(() => {
    filterItems(searchText);
  }, [JSON.stringify(items), searchText]);

  useEffect(() => {
    if (defaultValues?.length && items?.length) {
      const mappedItems = defaultValues?.map(selectedValue => {
        const selectedItemValueAndLabel = items.find(
          item => item.value === selectedValue
        );

        return {
          ...selectedItemValueAndLabel,
        };
      });

      isComponentInit && setSelectedItems(mappedItems as SelectedItems);
    }
  }, [defaultValues, JSON.stringify(items)]);

  const onSelectItem = useCallback(
    (selectedValue: string) => {
      const isItemExistInSelectedItems = selectedItems?.find(
        item => item.value === selectedValue
      );

      if (isItemExistInSelectedItems && (isUnselectedAllowed || multiple)) {
        setSelectedItems(
          selectedItems?.filter(item => item.value !== selectedValue) || []
        );
      } else {
        const selectedItem = items?.find(item => item.value === selectedValue);

        selectedItem &&
          setSelectedItems((items: SelectedItems | null) => {
            return multiple
              ? [...(items || []), { ...selectedItem }]
              : [{ ...selectedItem }];
          });
      }
      setIsComponentInit(false);
    },
    [JSON.stringify(items), multiple, JSON.stringify(selectedItems)]
  );

  useEffect(() => {
    if (
      (!multiple || !isUnselectedAllowed) &&
      selectedItems?.length &&
      selectedItems?.[0]?.value &&
      !isComponentInit
    ) {
      onModalClose();
    }
  }, [
    JSON.stringify(selectedItems),
    multiple,
    isUnselectedAllowed,
    isComponentInit,
  ]);

  const isItemSelected = useCallback(
    (selectedValue: string) => {
      return selectedItems?.find((item, index) => item.value === selectedValue);
    },
    [JSON.stringify(selectedItems)]
  );

  const renderItem = ({
    item,
    index,
  }: {
    item: { value: string; label: string };
    index: number;
  }) => {
    return (
      <TouchableOpacity
        onPress={() => onSelectItem(item.value)}
        style={
          isItemSelected(item.value)
            ? InputSelectModal.select
            : InputSelectModal.unselect
        }
      >
        <Text style={InputSelectModal.buttonText}>
          {item.label}
          {extendLabelText?.length ? extendLabelText : ''}
        </Text>
        {isCheckedIconShown?.[index] && (
          <MaterialCommunityIcons
            name="checkbox-marked-circle"
            size={24}
            color={colors.white}
          />
        )}
        {multiple && (
          <View style={InputSelectModal.selectButton}>
            {isItemSelected(item.value) && (
              <View style={InputSelectModal.selectButtonSelected}></View>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const onModalClose = useCallback(() => {
    onSelectItemHook(
      enableDoneBtn || !selectedItems?.length ? [] : selectedItems
    );
    onCloseHook(false);
    enableDoneBtn;
  }, [JSON.stringify(selectedItems)]);

  const onModalSave = () => {
    onCloseHook(false);
    onSelectItemHook(selectedItems?.length ? selectedItems : []);
  };

  const onSearchItem = (searchText: string) => {
    const searchTextWithoutWhiteSpace = searchText?.trim();
    setSearchText(searchTextWithoutWhiteSpace);
    setIsAddNewItemClicked(false);
    filterItems(searchTextWithoutWhiteSpace);
  };

  const [isAddNewItemClicked, setIsAddNewItemClicked] = useState(false);

  useEffect(() => {
    isEnableAddNew &&
      isNewDataAdding &&
      isAddNewItemClicked &&
      onAutoCompleteChange?.(searchText);
  }, [isNewDataAdding, isAddNewItemClicked]);

  useEffect(() => {
    isEnableAddNew &&
      isNewDataAdding &&
      searchText !== '' &&
      listItems.length &&
      setSelectedItems([...listItems]);
  }, [isNewDataAdding, searchText, listItems]);

  const AddNewItem = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          setIsAddNewItemClicked(true);
          addNewAction?.(searchText);
        }}
        style={InputSelectModal.unselect}
      >
        <Text style={InputSelectModal.buttonText}>{searchText} (Add New)</Text>

        {isAddNewItemClicked &&
          (!isNewDataAdding ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <AntDesign name="checkcircle" size={20} color={'green'} />
          ))}
      </TouchableOpacity>
    );
  };

  const renderTitle = () => {
    return convertCamelCaseToStandardString(title || '');
  };

  const renderSearchLabel = () => {
    return isEnableAddNew
      ? `Search ${convertCamelCaseToStandardString(
          title?.replace('Select', '').trim()
        )} or Add New`
      : `Search ${convertCamelCaseToStandardString(
          title?.replace('Select', '').trim()
        )}` || '';
  };

  return (
    <ModalWrapper transparent>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={InputSelectModal.overlay} />
      </TouchableWithoutFeedback>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={InputSelectModal.centeredView}>
          <KeyboardAvoidingView behavior="padding">
            <View style={InputSelectModal.modalView}>
              <TouchableOpacity
                style={InputSelectModal.closeButton}
                onPress={onModalClose}
              >
                <AntDesign
                  name="close"
                  size={isTabDevice() ? 25 : 20}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
              <Text style={InputSelectModal.modalText}>{renderTitle()}</Text>
              {isEnableAutoComplete && (
                <View style={InputSelectModal.searchInputContainer}>
                  <TextInput
                    placeholder={renderSearchLabel()}
                    style={InputSelectModal.searchInput}
                    multiline={false}
                    placeholderTextColor={colors.white}
                    onChangeText={text => onSearchItem(text)}
                    autoCorrect={false}
                  />
                  <Feather name="search" size={24} color="white" />
                </View>
              )}

              {listItems.length > 0 ? (
                <FlatList
                  data={listItems || []}
                  renderItem={renderItem}
                  keyExtractor={item => item?.value}
                  onEndReached={() =>
                    onReachEndHandler ? onReachEndHandler() : null
                  }
                  keyboardShouldPersistTaps="handled"
                />
              ) : isEnableAddNew ? (
                <AddNewItem />
              ) : (
                <View>
                  <Text style={InputSelectModal.buttonText}>
                    No {searchText?.length ? searchText : 'data'} found
                  </Text>
                </View>
              )}

              {listItems.length &&
              selectedItems?.length &&
              selectedItems.length > 0 &&
              enableDoneBtn ? (
                <View style={InputSelectModal.saveButtonContainer}>
                  <TouchableOpacity
                    onPress={onModalSave}
                    style={InputSelectModal.saveButton}
                  >
                    <Text style={InputSelectModal.saveButtonText}>Done</Text>
                  </TouchableOpacity>
                </View>
              ) : null}

              <Text style={InputSelectModal.errorMessage}>{errorMessage}</Text>
            </View>
          </KeyboardAvoidingView>
        </View>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
};

export default InputSelectModal;
