import React from 'react';
import { View, Text } from 'react-native';
import { S3_BUCKET_LOCATION } from '../../../constants/constants';
import PlayerInfoUpdateFormReport from '../../PlayerInfoUpdateForm/PlayerInfoUpdateFormReport';
import ModalWrapper from '../ModalWrapper/ModalWrapper';

const EditPlayerMedicalHistoryModal = ({
  setIsEditModeOpen,
  selectedUpdateData,
  editUpdate,
  playerUploadedReport,
  deleteReportAction,
  playerUploadedReportLoading,
}) => {
  return (
    <ModalWrapper>
      <PlayerInfoUpdateFormReport
        modalAction={setIsEditModeOpen}
        selectedUpdateData={selectedUpdateData}
        action={editUpdate}
        playerUploadedReport={playerUploadedReport}
        deleteReportAction={deleteReportAction}
        fileType={S3_BUCKET_LOCATION.medicalDocuments}
        playerUploadedReportLoading={playerUploadedReportLoading}
      />
    </ModalWrapper>
  );
};

export default EditPlayerMedicalHistoryModal;
