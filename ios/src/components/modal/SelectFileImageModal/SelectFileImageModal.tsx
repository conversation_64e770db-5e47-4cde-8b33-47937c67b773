import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import React, { FC, useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import {
  messageFileTypes,
  S3_BUCKET_LOCATION,
} from '../../../constants/constants';
import { dateTimeConversion, get_url_extension } from '../../../helpers';
import useFileMetaData from '../../../hooks/useFileMetaData';
import useFileUpload from '../../../hooks/useFileUpload';
import useStyles from '../../../hooks/useStyles';
import customDownloadReportModalStyles from '../DownloadReportModal/DownloadReportModalStyles';
import ModalWrapper from '../ModalWrapper/ModalWrapper';
import customSelectFileImageModalStyles from './SelectFileImageModalStyles';

import * as FileSystem from 'expo-file-system';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import useS3bucketLocation from '../../../hooks/useS3bucketLocation';
import { MESSAGING_SERVICE } from '../../../constants/services';
interface selectImageFileProps {
  onClose: (type: boolean) => void;
  callback: (type: object) => void;
  mainSelectedChatId: string | null;
}

const SelectFileImageModal: FC<selectImageFileProps> = ({
  onClose,
  callback,
  mainSelectedChatId,
}) => {
  const [getBucketLocation, bucketLocation] = useS3bucketLocation();

  const [upload, uploadedContent, uploadProgress] = useFileUpload();

  const [getFileMetaData, fileMetaData] = useFileMetaData();
  const DownloadReportModalStyles = useStyles(customDownloadReportModalStyles);
  const SelectFileImageModalStyles = useStyles(
    customSelectFileImageModalStyles
  );
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: 'Maximum upload file size: 10mb',
  });

  const [selectedFileName, setSelectedFileName] = useState<string | null>(null);
  const [isFileUploading, setIsFileUploading] = useState(false);
  const [startFileUpload, setStartFileUpload] = useState(false);
  const [filePath, setFilePath] = useState<string | null>(null);

  useEffect(() => {
    getBucketLocation({
      path: S3_BUCKET_LOCATION.chat,
      service: MESSAGING_SERVICE,
    });
  }, []);

  useEffect(() => {
    if (uploadProgress < 100) {
      setIsFileUploading(true);
    }
  }, [uploadProgress]);

  const [s3FilePath, setS3FilePath] = useState<string | null>(null);

  useEffect(() => {
    if (bucketLocation?.filePath && mainSelectedChatId?.length) {
      setS3FilePath(`/${bucketLocation.filePath}` + mainSelectedChatId + '/');
    }
  }, [mainSelectedChatId, bucketLocation]);

  const changeFileName = (selectedFileName: string) => {
    const sentDate = new Date();
    const {
      monthNumberString,
      dateNumberString,
      hours24String,
      minutesString,
      year,
    } = dateTimeConversion(sentDate);

    const fileNameExtension = selectedFileName.split('.')[1];

    return `Koach_${year}_${monthNumberString}_${dateNumberString}_at_${hours24String}_${minutesString}.${fileNameExtension}`;
  };

  const pickDocument = async () => {
    try {
      const response: any = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: false,
        copyToCacheDirectory: true,
      });
      if (!response.canceled) {
        setIsFileError({
          status: false,
          error: '',
        });

        const result = response.assets[0];

        const fileExtension = get_url_extension(result.uri);

        if (messageFileTypes.includes(fileExtension.toLowerCase())) {
          if (10 > Number(result.size / 1048576)) {
            setSelectedFileName(changeFileName(result.name));
            getFileMetaData(result.uri);
            setFilePath(result.uri);
          } else {
            setIsFileError({
              status: true,
              error: 'The attachement should not exceed 10mb',
            });
          }
        } else {
          setIsFileError({
            status: true,
            error: 'Unsupported File Type',
          });
        }
      }
    } catch (error) {}
  };

  const getFileInfo = async (fileURI: string) => {
    const fileInfo = await FileSystem.getInfoAsync(fileURI);
    return fileInfo;
  };

  const pickImage = async () => {
    const response: any = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });
    if (!response.canceled) {
      setIsFileError({
        status: false,
        error: '',
      });

      const result = response.assets[0];

      const fileExtension = get_url_extension(result.uri);
      if (messageFileTypes.includes(fileExtension.toLowerCase())) {
        const fileInfo: any = await getFileInfo(result.uri);
        if (10 > Number(fileInfo?.size / 1048576)) {
          getFileMetaData(result.uri);

          const filename = result.uri.split('/').pop();
          setSelectedFileName(changeFileName(filename));
          setFilePath(result.uri);
        } else {
          setIsFileError({
            status: true,
            error: 'Maximum upload file size: 10mb',
          });
        }
      } else {
        setIsFileError({
          status: true,
          error: 'Unsupported File Type',
        });
      }
    }
  };

  useEffect(() => {
    if (uploadedContent && fileMetaData) {
      const { fileType } = fileMetaData;

      let uploadedFileType = 'file';

      if (['image', 'video'].includes(fileType)) {
        uploadedFileType = fileType;
      }

      setIsFileUploading(false);
      setStartFileUpload(false);

      callback({
        s3fileObject: { ...uploadedContent, fileName: selectedFileName },
        fileType: uploadedFileType,
      });

      onClose(false);
    }
  }, [uploadedContent, fileMetaData]);
  const startUploading = async () => {
    if (filePath) {
      const picture = await fetch(filePath);
      const pictureBlob = await picture.blob();
      const file = new File([pictureBlob], `${selectedFileName}`);
      setStartFileUpload(true);
      upload(file, s3FilePath, bucketLocation?.bucketName || '');
    }
  };

  const clearSelectedFile = () => {
    setSelectedFileName(null);
    setFilePath(null);
    setIsFileError({
      status: false,
      error: '',
    });
  };

  return (
    <View style={DownloadReportModalStyles.container}>
      <ModalWrapper>
        <View style={DownloadReportModalStyles.centeredView}>
          <View style={DownloadReportModalStyles.overlay}></View>
          <View style={DownloadReportModalStyles.content}>
            <TouchableOpacity
              style={DownloadReportModalStyles.close}
              onPress={() => onClose(false)}
            >
              <Ionicons
                name="close"
                style={{ fontSize: 25, color: 'white' }}
              />
            </TouchableOpacity>
            <Text style={SelectFileImageModalStyles.title}>
              Upload attachment
            </Text>

            <View>
              <TouchableOpacity onPress={pickDocument}>
                <View style={SelectFileImageModalStyles.uploadButtonWrapper}>
                  <Text style={SelectFileImageModalStyles.uploadButtonText}>
                    Select File
                  </Text>
                  <Text style={SelectFileImageModalStyles.uploadButtonType}>
                    PPT, PDF, DOC, DOCX, XLS, XLSX
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity onPress={pickImage}>
                <View style={SelectFileImageModalStyles.uploadButtonWrapper}>
                  <Text style={SelectFileImageModalStyles.uploadButtonText}>
                    Select Image / Video
                  </Text>
                  <Text style={SelectFileImageModalStyles.uploadButtonType}>
                    JPG, JPEG, PNG, MP4
                  </Text>
                </View>
              </TouchableOpacity>

              {isFileError.status && (
                <View style={SelectFileImageModalStyles.errorWrapper}>
                  <Text style={SelectFileImageModalStyles.errorText}>
                    {isFileError.error}
                  </Text>
                </View>
              )}
              {selectedFileName && (
                <View style={SelectFileImageModalStyles.file}>
                  <View>
                    <TouchableOpacity
                      onPress={clearSelectedFile}
                      style={SelectFileImageModalStyles.close}
                    >
                      <Ionicons
                        name="close"
                        color="white"
                        style={SelectFileImageModalStyles.uploadWrapperClose}
                      />
                    </TouchableOpacity>
                  </View>

                  <View style={SelectFileImageModalStyles.downloadInnerWrapper}>
                    <Image
                      style={SelectFileImageModalStyles.iconImage}
                      source={require('../../../../assets/icons/medical-icon.png')}
                    />
                    <Text style={SelectFileImageModalStyles.fileName}>
                      {selectedFileName}
                    </Text>
                  </View>
                </View>
              )}
              {isFileUploading && startFileUpload ? (
                <View style={SelectFileImageModalStyles.activitySpinner}>
                  <ActivitySpinner />
                </View>
              ) : (
                <TouchableOpacity
                  disabled={!filePath || !bucketLocation?.bucketName}
                  onPress={startUploading}
                >
                  <View
                    style={{
                      ...SelectFileImageModalStyles.submitButtonWrapper,
                      opacity: filePath ? 1 : 0.5,
                    }}
                  >
                    <Text style={SelectFileImageModalStyles.submitButtonText}>
                      Upload
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default SelectFileImageModal;
