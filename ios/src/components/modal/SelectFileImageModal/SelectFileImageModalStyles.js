import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const SelectFileImageModalStyles = colors => ({
  title: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
      },
  uploadButtonWrapper: {
    backgroundColor: colors.lightBlue,
    borderRadius: 10,
    padding: 10,
    marginBottom: 20,
  },
  uploadButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  uploadButtonType: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Regular',
      },
  errorWrapper: {
    alignItems: 'center',
    width: '100%',
    marginBottom: 20,
  },
  errorText: {
    color: colors.red,
  },
  submitButtonWrapper: {
    backgroundColor: colors.green,
    borderRadius: 10,
    padding: 10,
    marginBottom: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Regular',
      },

  activitySpinner: {
    marginTop: 20,
  },
  downloadInnerWrapper: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    width: '85%',
  },
  downloadWrapper: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  iconImage: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        resizeMode: 'contain',
      },

  file: {
    marginBottom: 10,
    backgroundColor: colors.darkBlue,
    padding: 15,
    borderRadius: 5,
  },

  fileName: isTabDevice()
    ? {
        color: colors.white,
        marginLeft: wp('0.5%'),
      }
    : {
        color: colors.white,
        marginLeft: wp('2%'),
      },
  close: {
    color: colors.white,
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 5,
  },
});

export default SelectFileImageModalStyles;
