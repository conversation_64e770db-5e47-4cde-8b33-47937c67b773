import React from 'react';
import { Text, View, TouchableOpacity, FlatList } from 'react-native';
import customTeamLeftViewStyles from './TeamLeftViewStyles';
import useStyles from '../../hooks/useStyles';

const TeamLeftView = props => {
  const {
    teamsData,
    isHorizontal,
    isLoading,
    selectedTeam,
    setSelectedTeam,
    isStopOnEndReached,
    onEndReached,
    numberOfColumns,
  } = props;
  const TeamLeftViewStyles = useStyles(customTeamLeftViewStyles);

  const renderItem = ({ item }) => {
    return (
      <View
        style={
          isHorizontal
            ? TeamLeftViewStyles.teamWrapperHorizontal
            : TeamLeftViewStyles.teamWrapperVertical
        }
      >
        <TouchableOpacity onPress={() => !isLoading && setSelectedTeam(item)}>
          <View
            style={
              selectedTeam?._id === item?._id
                ? isHorizontal
                  ? TeamLeftViewStyles.teamTileSelectedHorizontal
                  : TeamLeftViewStyles.teamTileSelectedVertical
                : isHorizontal
                ? TeamLeftViewStyles.teamTileHorizontal
                : TeamLeftViewStyles.teamTileVertical
            }
          >
            <Text
              style={
                isHorizontal
                  ? TeamLeftViewStyles.teamTileTextHorizontal
                  : TeamLeftViewStyles.teamTileTextVertical
              }
            >
              {item?.teamName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <FlatList
      horizontal={isHorizontal}
      numColumns={numberOfColumns}
      data={teamsData || []}
      renderItem={renderItem}
      keyExtractor={item => item?._id}
      onEndReached={() => {
        if (!isStopOnEndReached) {
          onEndReached();
        }
      }}
      style={TeamLeftViewStyles.list}
    />
  );
};

export default TeamLeftView;
