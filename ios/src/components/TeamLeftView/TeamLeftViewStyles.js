import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TeamLeftViewStyles = colors => ({
  // Vertical two column tiles
  teamWrapperVertical: {},
  teamTileVertical: {
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    width: wp('10%'),
    height: wp('10%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('1%'),
  },
  teamTileSelectedVertical: {
    backgroundColor: colors.green,
    borderRadius: wp('1%'),
    width: wp('10%'),
    height: wp('10%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('1%'),
  },
  teamTileTextVertical: {
    fontSize: wp('1.3%'),
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },

  // Horizontal rectangle tiles
  teamWrapperHorizontal: {
    borderBottomColor: colors.tileBackground,
    borderBottomWidth: 1,
    marginTop: wp('1%'),
    marginBottom: wp('2%'),
  },
  teamTileHorizontal: {
    backgroundColor: colors.borderBlue,
    borderRadius: wp('2%'),
    width: wp('30%'),
    height: wp('30%%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('2%'),
  },
  teamTileSelectedHorizontal: {
    backgroundColor: colors.green,
    borderRadius: wp('2%'),
    width: wp('30%'),
    height: wp('30%%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('2%'),
  },
  teamTileTextHorizontal: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },

  list: isTabDevice()
    ? {}
    : {
        width: wp('96%'),
      },
});
export default TeamLeftViewStyles;
