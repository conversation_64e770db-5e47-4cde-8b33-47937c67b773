import React from 'react';
import { View, Text, FlatList } from 'react-native';
import NoContentMessage from '../NoContents/NoContentMessage';
import EventItem from './EventItem';
import customEventsStyle from './EventsStyles';
import useStyles from '../../hooks/useStyles';
import { useSelector } from 'react-redux';
import { userRoleType } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';

const Events = ({ data, loadMoreEvents, selectedTab, teams }) => {
  const EventsStyle = useStyles(customEventsStyle);

  const { userData } = useSelector(state => state?.auth);
  const isParent = userRoleType.PARENT === userData?.type;

  if (!data?.length) {
    return <NoContentMessage message="No Content" />;
  }

  return (
    <View style={EventsStyle.container}>
      {selectedTab === 2 && (
        <View style={EventsStyle.header}>
          <Text style={EventsStyle.attendanceTitle}>Attendance</Text>
        </View>
      )}
      <FlatList
        data={data}
        renderItem={({ item }) => (
          <EventItem item={item} teams={teams} selectedTab={selectedTab} />
        )}
        keyExtractor={item => item._id}
        onEndReached={() => loadMoreEvents()}
        style={EventsStyle.list}
        contentContainerStyle={
          isParent  &&  {paddingBottom: !isTabDevice() ? 280 : 240 } 
        }
      />
    </View>
  );
};

export default Events;
