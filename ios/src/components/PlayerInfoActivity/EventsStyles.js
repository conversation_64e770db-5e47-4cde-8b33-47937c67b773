import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const EventsStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('72%'),
      }
    : {
        width: wp('97%'),
      },
  header: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
        marginRight: hp('0.5%'),
      },
  attendanceTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
      },
  list: isTabDevice()
    ? {}
    : {
        marginBottom: wp('40%'),
      },
});
export default EventsStyle;
