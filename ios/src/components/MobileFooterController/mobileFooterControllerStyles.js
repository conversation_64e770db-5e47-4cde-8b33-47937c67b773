import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const MobileFooterControllerStyles = colors => ({
  containerWrapper: {
    backgroundColor: colors.borderBlue,
    width: wp('100%'),
    height: hp('10%'),
    paddingLeft: hp('3%'),
    paddingRight: hp('3%'),
    paddingTop: hp('2%'),
    paddingBottom: hp('7%'),
    position: 'absolute',
    bottom: 0,
    left: 0,
    zIndex: 15,
  },
  container: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  option: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    width: wp('5%'),
    height: hp('5%'),
    resizeMode: 'contain',
  },
  text: {
    color: colors.white,
    fontSize: wp('4%'),
  },
});
export default MobileFooterControllerStyles;
