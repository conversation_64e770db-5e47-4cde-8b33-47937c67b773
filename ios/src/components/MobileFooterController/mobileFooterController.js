import { useNavigation, useRoute } from '@react-navigation/native';
import { Auth } from 'aws-amplify';
import React, { useEffect, useState } from 'react';
import { Image, Text, TouchableOpacity, View, Platform } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { LOGOUT_SUCCESS } from '../../store/actionTypes/auth';
import customFooterControllerStyles from './mobileFooterControllerStyles';
import { getCalendars } from 'expo-localization';

import homeIcon from '../../../assets/icons/homeIcon.png';
import profileIcon from '../../../assets/icons/profileIcon.png';
import schedulerIcon from '../../../assets/icons/schedulerIcon.png';
import { userRoleType } from '../../constants/constants';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import { MATCH_LOG_RESET_DATA } from '../../store/actionTypes/MatchLog/MatchLogActions';
import { RESET_TEAMS } from '../../store/actionTypes/Team/TeamAction';
import ChangePasswordModal from '../modal/ChangePasswordModal/ChangePasswordModal';
import ProfileModal from './ProfileModal/ProfileModal';
import * as Localization from 'expo-localization';
import {
  HEADER_LOGO_CLICKED,
  SET_ON_SYNC_START,
} from '../../store/actionTypes/common/commonActionTypes';
import useCalender from '../../hooks/useCalender';
import { isTabDevice } from '../../config/appConfig';
import TermAndCondition from '../TermAndCondition/TermAndCondition';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTermAndConditionHook } from '../../hooks/TermAndConditionAPIHook/useTermAndConditionHook';
import { TermAndConditionVersion } from '../../constants/TermAndCondition';
import { SET_TERM_AND_CONDITION } from '../../store/actionTypes/TermAndCondition/termAndCondtionAction';
import { USER_TYPE_FAILED } from '../../store/actionTypes/userType/userType';
import { ADD_USER_FAIL, ADD_USER_REQUEST, ADD_USER_SUCCESS, USER_TYPE_SUCCESS } from '../../store/actionTypes/User/User';
import useApiPromise from '../../hooks/useApiPromise';
export default function mobileFooterController() {
  const [{ automaticSync }] = useCalender();
  const FooterControllerStyles = useStyles(customFooterControllerStyles);
  const navigation = useNavigation();
  const route = useRoute();
  const [showProfileModal, setShowProfileModal] = useState(false);

  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [onUpdateUser] = useApiPromise()

  const {
    userData,
    expoPushNotificationToken,
    selectedCalenderID,
    isCalenderSyncEnabled,
  } = useSelector(state => state?.auth);
  const { parentDetails, parentDetailsLoading, isOnSync, updatedChildrenIds } =
    useSelector(state => state?.common);
  const isPlayerLoggedIn = userRoleType.PLAYER === userData?.type;

  const [, , onRemoveNotificationTokenPromise] = useApi();
  const dispatch = useDispatch();

  const logoutUser = async () => {
    try {
      await Auth.signOut();
      dispatch({
        type: LOGOUT_SUCCESS,
      });
      dispatch({
        type: MATCH_LOG_RESET_DATA,
      });
      dispatch({
        type: RESET_TEAMS,
      });
      AsyncStorage.removeItem('TermAndConditionData');
    } catch (error) {
      console.log('logout error', error);
    }
  };


  const handleLogout = async () => {
    if (expoPushNotificationToken) {
      try {
        await onRemoveNotificationTokenPromise(
          'DELETE',
          USER_MANAGEMENT_SERVICE,
          `/api/v1/users/${userData.id}/expoTokens`,
          {
            token: expoPushNotificationToken,
            preferredTimeZone: getCalendars()[0]?.timeZone,
            platform: Platform.OS,
          }
        );
        await logoutUser();
      } catch (error) {
        console.log('errorx', error);
      }
    } else {
      await logoutUser();
    }
  };


  const navigateToLanding = () => {
    if (route && route.name != 'landing') {
      dispatch({ type: HEADER_LOGO_CLICKED, payload: true });
      navigation.reset({
        index: 0,
        routes: [{ name: 'landing' }],
      });
      setTimeout(() => {
        dispatch({ type: HEADER_LOGO_CLICKED, payload: false });
      }, 2000);
    }
  };

  useEffect(() => {
    if (
      !isOnSync &&
      selectedCalenderID &&
      isCalenderSyncEnabled &&
      !isTabDevice()
    ) {
      automaticSync();
    }
    dispatch({
      type: SET_ON_SYNC_START,
    });
  }, [
    isOnSync,
    userData,
    selectedCalenderID,
    isCalenderSyncEnabled,
    updatedChildrenIds,
  ]);

  const [isTermAndConditionEnabled, setTermAndConditionEnabled] =
    useState(false);
  const [isTermAndConditionAccepted, setIsTermAndConditionAccepted] =
    useState(false);
  const [isExpired, setIsExpired] = useState(false);
  const { getTermAndConditionDetails } = useTermAndConditionHook();
  useState(false);
  const [isExistingUser, setIsExistingUser] = useState(false);
  const [userId, setUserId] = useState(null);

  const { termAndConditionData, setTermAndCondition } = useSelector(
    state => state.TermAndCondition
  );
  const processTermAndConditionData = (termAndConditionData, newDate) => {
    if (
      !termAndConditionData ||
      termAndConditionData.version !== TermAndConditionVersion
    ) {
      setTermAndConditionEnabled(true);
    } else if (termAndConditionData.termActionType !== 'ACCEPTED') {
      // if (newDate !== termAndConditionData.expireDate) {
      //   if (
      //     newDate >
      //     new Date(termAndConditionData.actionDate).toLocaleDateString()
      //   ) {
      //     dispatch({ type: SET_TERM_AND_CONDITION, payload: true });
      //   }
      // } else {
      //   setIsExpired(true);
      //   dispatch({ type: SET_TERM_AND_CONDITION, payload: true });
      // }
    } else {
    }
  };

  const retrieveData = async () => {
    try {
      const value = await AsyncStorage.getItem('TermAndConditionData');
      const newData = JSON.parse(value);
      !newData && getTermAndConditionDetails(userData.id);

      if (newData?.userType === 'ExistingUser') {
        setIsExistingUser(true);
      } else {
        if (value !== null) {
          const newDate = new Date().toLocaleDateString();
          termAndConditionData.termActionType === 'ACCEPTED' &&
          termAndConditionData.version === TermAndConditionVersion
            ? setIsTermAndConditionAccepted(true)
            : (processTermAndConditionData(termAndConditionData, newDate),
              setIsTermAndConditionAccepted(false));
        } else {
        }
      }
    } catch (error) {
      console.error('Error retrieving data:', error);
    }
  };

  useEffect(() => {
    retrieveData();
  }, [route, termAndConditionData]);

  useEffect(() => {
    setTermAndConditionEnabled(setTermAndCondition);
  }, [setTermAndCondition]);

  useEffect(() => {
    setUserId(userData?.id);
  }, [userData]);


  const handleChangeProfilePic = async (uri) => {
    await onUpdateUser(
      '/api/v1/users',
      ADD_USER_REQUEST,
      ADD_USER_SUCCESS,
      ADD_USER_FAIL,
      { ...userData, profileImage: uri },
      '',
      'PUT',
      null,
      USER_MANAGEMENT_SERVICE
    )

   await onUpdateUser(
      `/api/v1/users?email=${userData.emailId}`,
      "_",
      USER_TYPE_SUCCESS,
      USER_TYPE_FAILED,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  }

  return (
    <>
      {isTermAndConditionEnabled && (
        <TermAndCondition
          setTermAndConditionEnabled={setTermAndConditionEnabled}
          isTermAndConditionAccepted={isTermAndConditionAccepted}
          isExpired={isExpired}
          logOut={logoutUser}
          parentDetails={parentDetails}
          setIsTermAndConditionAccepted={setIsTermAndConditionAccepted}
          userId={userId}
        />
      )}
      <View style={FooterControllerStyles.containerWrapper}>
        <View style={FooterControllerStyles.container}>
          <TouchableOpacity onPress={navigateToLanding}>
            <View style={FooterControllerStyles.option}>
              <Image source={homeIcon} style={FooterControllerStyles.icon} />
              <Text style={FooterControllerStyles.text}>Home</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('PlannerScreen')}
          >
            <View style={FooterControllerStyles.option}>
              <Image
                source={schedulerIcon}
                style={FooterControllerStyles.icon}
              />
              <Text style={FooterControllerStyles.text}>Scheduler</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => setShowProfileModal(!showProfileModal)}
          >
            <View style={FooterControllerStyles.option}>
              <Image source={profileIcon} style={FooterControllerStyles.icon} />
              <Text style={FooterControllerStyles.text}>Profile</Text>
            </View>
          </TouchableOpacity>
        </View>
        {showProfileModal && (
          <ProfileModal
            handleLogout={handleLogout}
            closeModal={() => setShowProfileModal(false)}
            isPlayerLoggedIn={isPlayerLoggedIn}
            openChangePassword={() => {
              setShowProfileModal(false);
              setTimeout(() => {
                setShowChangePasswordModal(true);
              }, 1); //timeout is needed for iOS. Otherwise 2nd modal won't open
            }}
            parentDetails={parentDetails}
            loading={parentDetailsLoading}
            autoDisableTermAndConditionStatus={() => {
              setTimeout(() => {
                setTermAndConditionEnabled(false);
              }, 1);
            }}
            setTermAndConditionEnabled={() => {
              setShowProfileModal(false);
              setTimeout(() => {
                setTermAndConditionEnabled(true);
              }, 1);
            }}
            isExistingUser={isExistingUser}
            handleChangeProfilePic={handleChangeProfilePic}
          />
        )}
        <ChangePasswordModal
          showModal={showChangePasswordModal}
          closeModal={() => setShowChangePasswordModal(false)}
        />
      </View>
    </>
  );
}
