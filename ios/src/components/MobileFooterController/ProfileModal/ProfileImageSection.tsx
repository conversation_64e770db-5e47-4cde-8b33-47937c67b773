import React, { useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useEffect } from 'react';
import { View, Text } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import ProfileImage from '../../ProfileImage/ProfileImage';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import useStyles from '../../../hooks/useStyles';
import customUserProfileModalStyles from './ProfileModalStyles';
import * as FileSystem from 'expo-file-system';
import { imageError, S3_BUCKET_LOCATION } from '../../../constants/constants';
import useFileUploadPromise from '../../../hooks/useFileUploadPromise';
import useS3bucketLocationPromise from '../../../hooks/useS3bucketLocationPromise';
import { USER_MANAGEMENT_SERVICE } from '../../../constants/services';
import { get_url_extension } from '../../../helpers';

interface ProfileImageSectionProps {
    profileImageUrl: string | undefined;
    onImageChange: (imageUri: string) => void;
}

interface IFileErro {
    status: boolean;
    error: string | undefined
}

interface BucketLocationResult {
    bucketName: string;
    region: string;
    filePath: string;
}

export interface ProfileImageSectionRef {
    changeProfilePicture: () => Promise<void>;
}

export const ProfileImageSection = forwardRef<ProfileImageSectionRef, ProfileImageSectionProps>(
    ({ profileImageUrl, onImageChange }, ref) => {

        const ProfileModalStyles = useStyles(customUserProfileModalStyles);
        const { uploadFile } = useFileUploadPromise()
        const { getBucketLocation } = useS3bucketLocationPromise();

        const [isImageUploading, setIsImageUploading] = useState<boolean>(false);
        const [localImageUri, setLocalImageUri] = useState<string | undefined>(undefined);
        const [isFileError, setIsFileError] = useState<IFileErro>({
            status: false,
            error: undefined,
        });

        useEffect(() => {
            if (profileImageUrl) {
                setLocalImageUri(profileImageUrl);
            }
        }, [profileImageUrl]);

        const changeProfilePicture = async (): Promise<void> => {
            setIsFileError({
                status: false,
                error: undefined,
            });
            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 1,
            });

            try {

                if (!result.canceled && result.assets && result.assets.length > 0) {
                    const newImageUri = result.assets[0].uri;
                    const fileInfo = await FileSystem.getInfoAsync(newImageUri) as any;
                    const fileExtension = get_url_extension(result.assets[0].uri);
                    
                    if (3 > Number(fileInfo?.size / 1048576)) {
                        try {
                            setIsImageUploading(true);

                            const bucketLocation = await getBucketLocation({
                                path: S3_BUCKET_LOCATION.profileImages,
                                service: USER_MANAGEMENT_SERVICE,
                            });
                            const resultUploadingToS3 = bucketLocation && await UploadingToS3(result.assets[0].uri, `photo.${fileExtension}`, bucketLocation);
                            setLocalImageUri(newImageUri);  // Immediately update the local image
                            onImageChange(resultUploadingToS3);
                            setIsImageUploading(false);
                        } catch (error) {
                            console.log(error);
                        }
                    } else {
                        setIsFileError({
                            status: true,
                            error: imageError.MAX_FILE_SIZE_ERROR,
                        });
                        setIsImageUploading(false);
                    }

                }
            } catch (error) {
                console.log(error);

            }
        };

        const UploadingToS3 = async (filePath: string, fileName: string, bucketLocation: BucketLocationResult): Promise<any> => {
            try {
                const picture = await fetch(filePath);
                const pictureBlob = await picture.blob();
                const file = await new File([pictureBlob], fileName);

                return new Promise((resolve, reject) => {
                    setTimeout(async () => {
                        try {
                            const result = await uploadFile({
                                file: file,
                                path: bucketLocation.filePath,
                                bucket: bucketLocation.bucketName || '',
                            });
                            resolve(result);
                        } catch (error) {
                            console.log("Upload error:", error);
                            reject(error);
                        }
                    }, 200);
                });
            } catch (error) {
                console.log("picture", error);
            }
        }

        useImperativeHandle(ref, () => ({
            changeProfilePicture
        }));

        const renderImageUploadErrorMsg = () => {
            return (
                <View style={ProfileModalStyles.errorMessage2Wrapper}>
                    <Text style={ProfileModalStyles.errorText2}>
                        {isFileError.status ? isFileError.error : ''}
                    </Text>
                </View>
            );
        };

        return (
            <View style={{ alignItems: 'center' }}>
                {isImageUploading ? (
                    <ActivitySpinner color="white" />
                ) : (
                    <View>
                        <ProfileImage
                            profileImageUrl={localImageUri}
                            imageStyles={ProfileModalStyles.avatar}
                            style={undefined}
                        />
                    </View>
                )}
                {renderImageUploadErrorMsg()}
            </View>
        );
    }
);

export default ProfileImageSection;