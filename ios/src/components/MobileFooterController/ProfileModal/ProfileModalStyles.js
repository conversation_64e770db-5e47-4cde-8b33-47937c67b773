
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ProfileModalStyles = colors => ({
  centeredView: isTabDevice()
    ? {
        width: wp('100%'),
        height: hp('100%'),
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        width: wp('100%'),
        height: hp('100%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  overlay: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        opacity: 0.9,
      }
    : {
        backgroundColor: colors.darkBlue,
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        opacity: 0.9,
      },
      modal: isTabDevice() ? {
        backgroundColor: colors.borderBlue,
        paddingTop: 10,
        paddingBottom: 20,
        borderRadius: wp('3%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: wp('20%'),
        position: 'absolute',
        top: hp('10%'),
        right: wp('3%')
      } : {
        backgroundColor: colors.borderBlue,
        paddingTop: 30,
        paddingBottom: 20,
        borderRadius: wp('3%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        width: wp('65%'),
      },
  img: isTabDevice()
    ? {
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
      }
    : {
        width: wp('40%'),
        height: wp('20%'),
        marginBottom: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
      },
  avatar: isTabDevice()
    ? {
        width: wp('7%'),
        height: wp('7%'),
        borderRadius: wp('100%'),
        borderWidth: 4,
        borderColor: colors.green,
        marginTop: wp('0.5%'),
      }
    : {
        width: wp('20%'),
        height: wp('20%'),
        borderRadius: wp('100%'),
        borderWidth: 5,
        borderColor: colors.green,
      },
  avatarWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'center',
      },
  avatarParent: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
      }
    : {
        width: wp('10%'),
        height: wp('10%'),
        borderRadius: wp('100%'),
        marginRight: wp('2%'),
      },
  name: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        marginBottom: wp('1%'),
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('6%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1%'),
        width: wp('40%'),
        textAlign: 'center',
      },
  position: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        marginBottom: wp('1%'),
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        marginBottom: wp('7%'),
        textAlign: 'center',
      },
  password: isTabDevice()
  ? {
      color: colors.white,
      marginBottom: wp('1%'),
      fontSize: wp('1.1%'),
      textAlign: 'center',
      width: '100%',
    }
  : {
      color: colors.white,
      marginRight: wp('0.5%'),
      fontSize: hp('1.5%'),
      marginBottom: wp('3%'),
    },
  parentText: isTabDevice()
  ? {
      color: colors.white,
      marginBottom: wp('1%'),
      fontSize: wp('1.1%'),
      textAlign: 'center',
      width: '100%',
    }
  : {
      color: colors.white,
      marginRight: wp('0.5%'),
      fontSize: hp('1.5%'),
      marginTop: wp('3%'),
      marginBottom: wp('3%'),
      textAlign: 'center',
      width: '100%',
    },
  logout: isTabDevice()
  ? {
      color: colors.white,
      marginBottom: wp('1%'),
      fontSize: wp('1.1%'),
      textAlign: 'center',
      width: '100%',
    }
  : {
      color: colors.white,
      fontSize: hp('1.5%'),
    },
  closeButton: {
    position: 'absolute',
    top: wp('2 %'),
    right: wp('2%'),
    zIndex: 10,
  },
  userIdLabel: {
    color: 'white',
  },
  userId: {
    color: colors.green,
  },
  userIdContainer: isTabDevice()
    ? {
        fontSize: wp('1%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
      }
    : {
        fontSize: wp('3%'),
        textAlign: 'center',
        marginBottom: wp('5%'),
      },
  greenButton: isTabDevice() ? {
    backgroundColor: colors.green,
    borderRadius: 100,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 3,
    paddingBottom: 2,
  } : {
    backgroundColor: colors.green,
    borderRadius: 100,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 3,
    paddingBottom: 2,
  },
  buttonText: isTabDevice() ? {
    fontSize: wp('1%'),
    textAlign: 'center',
    fontFamily: 'Poppins-Bold',
    color: colors.white,
  } : {
    color: colors.white,
    fontSize: wp('3%'),
    textAlign: 'center',
    marginBottom: wp('1%'),
    fontFamily: 'Poppins-Bold'
  },
  divider: {
    width: '100%',
    height: 1,
    backgroundColor: colors.darkBlue,
    marginBottom: 10,
    marginTop: 15,
  },
  errorText2:isTabDevice() ? {
        color: colors.red,
        marginTop: wp('1%'),
        fontSize: wp('1.2%'),
        textAlign: 'left',
      }
    : {
        color: colors.red,
        marginTop: wp('1%'),
        marginBottom:wp('4%'),
        fontSize: wp('2.2%'),
      },

});
export default ProfileModalStyles;
