import React, { useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { useSelector } from 'react-redux';
import ModalWrapper from '../../../components/modal/ModalWrapper/ModalWrapper';
import customUserProfileModalStyles from './ProfileModalStyles';
import { AntDesign } from '@expo/vector-icons';
import useStyles from '../../../hooks/useStyles';
import ProfileImage from '../../ProfileImage/ProfileImage';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import ProfileImageWrapper from '../../../Container/ProfileImageWrapper/ProfileImageWrapper';
import AppVersion from '../../AppVersion/AppVersion';
import MessageNotification from '../../MessageNotification/MessageNotification';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import ProfileImageSection from './ProfileImageSection';
export default function ProfileModal({
  handleLogout,
  closeModal,
  openChangePassword,
  isPlayerLoggedIn,
  parentDetails,
  loading,
  setTermAndConditionEnabled,
  isExistingUser,
  autoDisableTermAndConditionStatus,
  handleChangeProfilePic
}) {
  const ProfileModalStyles = useStyles(customUserProfileModalStyles);
  const { userData } = useSelector(state => state?.auth);

  useEffect(() => {
    autoDisableTermAndConditionStatus();
  }, []);

  const profileImageSectionRef = useRef(null);

  return ( 
    <SafeAreaView>
      <ModalWrapper transparent visible>
        <View style={ProfileModalStyles.centeredView}>
          <View style={ProfileModalStyles.overlay}></View>
          <View style={ProfileModalStyles.modal}>
            <TouchableOpacity
              onPress={closeModal}
              style={ProfileModalStyles.closeButton}
            >
              <AntDesign name="close" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <View style={ProfileModalStyles.player}>
              <View style={ProfileModalStyles.img}>
                <ProfileImageSection
                  profileImageUrl={userData?.profileImageUrl}
                  imageStyles={ProfileModalStyles.avatar}
                  onImageChange={handleChangeProfilePic}
                  ref={profileImageSectionRef}
                />
              </View>
              <Text style={ProfileModalStyles.name}>
                {userData?.firstName} {userData?.lastName || ''}
              </Text>

              <Text style={ProfileModalStyles.userIdContainer}>
                <Text style={ProfileModalStyles.userIdLabel}>
                  User ID :
                  <Text style={ProfileModalStyles.userId}>
                    {userData?.uniqueUserId}
                  </Text>
                </Text>
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                profileImageSectionRef.current?.changeProfilePicture();
              }}
              style={ProfileModalStyles.greenButton}
            >
              <Text style={ProfileModalStyles.buttonText}>Change Profile Picture</Text>
            </TouchableOpacity>
            {isPlayerLoggedIn && (
              <View style={ProfileModalStyles.avatarWrapper}>
                <Text style={ProfileModalStyles.parentText}>Parent:</Text>
                {loading ? (
                  <ActivitySpinner />
                ) : (
                  parentDetails?.map((item, i) => (
                    <ProfileImageWrapper
                      key={item.id}
                      index={i}
                      item={item}
                      imageStyles={
                        i < 1
                          ? ProfileModalStyles.avatarParent
                          : {
                            ...ProfileModalStyles.avatarParent,
                            marginLeft: wp('0.5%'),
                          }
                      }
                    />
                  ))
                )}
              </View>
            )}
            <View style={ProfileModalStyles.divider}></View>
            <TouchableOpacity
              onPress={() => { 
                openChangePassword();
              }}
            >
              <Text style={ProfileModalStyles.password}>Change Password</Text>
            </TouchableOpacity>   
            <MessageNotification />
            {!isExistingUser && (
              <TouchableOpacity 
                onPress={() => {
                  setTermAndConditionEnabled();
                }}
              >
                <Text style={ProfileModalStyles.password}>
                  Terms and conditions
                </Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={handleLogout}>
              <Text style={ProfileModalStyles.logout}>Logout</Text>
            </TouchableOpacity>
            <AppVersion />
          </View>
        </View>
      </ModalWrapper>
    </SafeAreaView>
  );
}