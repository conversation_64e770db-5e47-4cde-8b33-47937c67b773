import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  text: {
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },

  progressBar: isTabDevice()
    ? {
        width: '100%',
        flexDirection: 'row',
        height: hp('1%'),
        backgroundColor: colors.chatFooterBlue,
        marginBottom: 10,
        marginTop: 12,
      }
    : {
        width: '100%',
        flexDirection: 'row',
        height: hp('0.5%'),
        backgroundColor: colors.chatFooterBlue,
        marginBottom: 10,
        marginTop: 12,
      },

  progressBarOneLeft: {
    width: '50%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'row',
  },
  progressBarOneLeftValue: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        height: hp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        height: hp('0.5%'),
      },
  progressBarOneRight: {
    width: '50%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  progressBarOneRightValue: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: hp('1%'),
      }
    : {
        backgroundColor: colors.green,
        height: hp('0.5%'),
      },
  progressBarOneLeftAvg: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        height: hp('1%'),
        marginLeft: 'auto',
        opacity: 0.2,
      }
    : {
        backgroundColor: colors.aquaBlue,
        height: hp('0.5%'),
        marginLeft: 'auto',
      },
  progressBarOneRightAvg: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: hp('1%'),
        marginRight: 'auto',
        opacity: 0.2,
      }
    : {
        backgroundColor: colors.green,
        height: hp('0.5%'),
        marginRight: 'auto',
      },
  progressBarSecondLeft: {
    width: '50%',
  },
  progressBarSecondRight: {
    width: '50%',
  },
  positionAverageIcon: {
    backgroundColor: '#1c626a',
    borderRadius: 20,
    padding: 4,
    borderColor: 'white',
    borderWidth: 1.5,
    zIndex: 1,
    position: 'absolute',
    bottom: -7,
  },
  positionAverageIconText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 10,
  },
  positionAverageToolTip: {
    color: '#000',
    backgroundColor: '#1c626a',
    position: 'absolute',
    bottom: 23,
    zIndex: 1,
    borderRadius: 5,
    padding: 3,
  },
  positionAverageToolTipText: {
    color: 'white',
  },
});
