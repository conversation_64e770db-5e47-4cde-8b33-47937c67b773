import React, { FC, useMemo, useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import customBarStyle from './PercentageBarStyles';

interface IPercentageBar {
  value: {
    left: number;
    right: number;
    avgLeft?: number;
    avgRight?: number;
  };
}

const RenderPositionAverageIcon = ({ section = 'left', value = 25 }) => {
  const barStyle = useStyles(customBarStyle);
  const positionStyle = isTabDevice()
    ? {
        [section]:
          section === 'right' ? `${50 + value / 2 - 4}%` : `${value - 8}%`,
      }
    : {
        [section]:
          section === 'right' ? `${50 + value / 2 - 8}%` : `${value - 16}%`,
      };
  const [showValue, setShowValue] = useState(false);
  return (
    <>
      {showValue && (
        <View style={{ ...barStyle.positionAverageToolTip, ...positionStyle }}>
          <Text style={barStyle.positionAverageToolTipText}>{value}</Text>
        </View>
      )}
      <TouchableOpacity
        style={{
          ...barStyle.positionAverageIcon,
          ...positionStyle,
        }}
        onPress={() => setShowValue(!showValue)}
      >
        <Text style={barStyle.positionAverageIconText}>PA</Text>
      </TouchableOpacity>
    </>
  );
};

const PercentageBar: FC<IPercentageBar> = ({
  value: { left = 0, right = 0, avgLeft, avgRight },
}) => {
  const isShowProgressiveAvgLeft = useMemo(() => {
    return avgLeft && avgLeft > left;
  }, [avgLeft, left]);

  const isShowProgressiveAvgRight = useMemo(() => {
    return avgRight && avgRight > right;
  }, [avgRight, right]);

  const barStyle = useStyles(customBarStyle);
  return (
    <View style={barStyle.progressBar}>
      {avgLeft ? (
        <RenderPositionAverageIcon section="right" value={avgLeft} />
      ) : null}
      <View style={barStyle.progressBarOneLeft}>
        {isShowProgressiveAvgLeft ? (
          <View
            style={{
              ...barStyle.progressBarOneLeftAvg,
              width: avgLeft ? `${avgLeft - left}%` : 0,
            }}
          ></View>
        ) : null}
        <View
          style={{
            ...barStyle.progressBarOneLeftValue,
            width: isTabDevice() ? `${left - 8}%` : `${left - 16}%`,
            marginLeft: !isShowProgressiveAvgLeft ? 'auto' : 0,
          }}
        ></View>
      </View>
      <View style={barStyle.progressBarOneRight}>
        {avgRight ? (
          <RenderPositionAverageIcon section="left" value={avgRight} />
        ) : null}
        <View
          style={{
            ...barStyle.progressBarOneRightValue,
            width: isTabDevice() ? `${right - 8}%` : `${right - 16}%`,
            marginRight: !isShowProgressiveAvgRight ? 'auto' : 0,
          }}
        ></View>
        {isShowProgressiveAvgRight ? (
          <View
            style={{
              ...barStyle.progressBarOneRightAvg,
              width: avgRight ? `${avgRight - right}%` : 0,
            }}
          ></View>
        ) : null}
      </View>
    </View>
  );
};

export default PercentageBar;
