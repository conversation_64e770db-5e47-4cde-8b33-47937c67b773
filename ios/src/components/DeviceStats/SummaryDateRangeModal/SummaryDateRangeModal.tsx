import { AntDesign } from '@expo/vector-icons';
import React, { FC, useState } from 'react';
import {
  Appearance,
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { useDispatch, useSelector } from 'react-redux';
import { DeviceStatsSessionType } from '../../../constants/data';
import { deviceStatsEventType } from '../../../constants/constants';
import { dateToString } from '../../../helpers/DateHelper';
import useGetSessionData from '../../../hooks/useGetSessionData';
import useInputSelectModal from '../../../hooks/useInputSelectModal';
import useStyles from '../../../hooks/useStyles';
import {
  SET_DEVICE_STAT_TYPE,
  SET_IS_SUMMARY_DETAILS,
  SET_IS_SUMMARY_MODAL,
  SET_SELECTED_EVENT_TYPE,
} from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import { RootStore } from '../../../store/store';
import SelectionModal from '../../modal/SelectionModal/SelectionModal';
import customDateRangeModalStyle from './DateRangeModalStyle';

interface ISummaryDateRangeModal {
  modalVisible: boolean;
}

const START_DATE = 'Start Date';
const END_DATE = 'End Date';

const SummaryDateRangeModal: FC<ISummaryDateRangeModal> = ({
  modalVisible,
}) => {
  const DateRangeModalStyle = useStyles(customDateRangeModalStyle);
  const dispatch = useDispatch();
  const { getSummaryStat, getTeamNdPosition, hasSummaryStat } =
    useGetSessionData();
  const [showToPicker, setshowToPicker] = useState(false);
  const [showFromPicker, setShowFromPicker] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const [setIsTeamModalOpen, isTeamModalOpen] = useInputSelectModal();
  const [setIsPositionModalOpen, isPositionModalOpen] = useInputSelectModal();

  const {
    teamList,
    positionList,
    summarySelectedDetails,
    PlayerMakerSyncedData,
    selectedEventType,
    selectedFilterType,
    summaryGraphStat,
  } = useSelector((state: RootStore) => state?.deviceStats);

  const pmUserId = PlayerMakerSyncedData?.pmUserId;
  const fromDate = summarySelectedDetails?.summaryFromDate;
  const toDate = summarySelectedDetails?.summaryToDate;

  const selectedTeam = summarySelectedDetails?.summarySelectedTeam || [];
  const selectedPosition =
    summarySelectedDetails?.summarySelectedPosition || [];

  const mappedTeamList =
    teamList?.map(teamName => {
      return {
        value: teamName.pmTeamName,
        label: teamName.pmTeamName,
      };
    }) || [];

  const mappedPositionList =
    positionList?.map(positionName => {
      return {
        value: positionName.pmPosition,
        label: positionName.pmPosition,
      };
    }) || [];

  const handelTeamSelection = (isOpen: boolean) => {
    if (!fromDate || !toDate) {
      setError('Please select a date range');
      return;
    }
    setIsTeamModalOpen(isOpen);
  };

  const handelPositonSelection = (isOpen: boolean) => {
    if (!fromDate || !toDate) {
      setError('Please select a date range');
      return;
    }
    if (selectedTeam.length === 0) {
      setError('Please select a Team');
      return;
    }
    setIsPositionModalOpen(isOpen);
  };

  const dateConvert = (date: Date) => {
    return `${date.toJSON()}`;
  };

  const onSelectTeamHook = (selectedItem: any) => {
    dispatch({
      type: SET_IS_SUMMARY_DETAILS,
      payload: {
        ...summarySelectedDetails,
        summarySelectedTeam: selectedItem,
        summarySelectedPosition: [],
      },
    });
    fromDate &&
      toDate &&
      pmUserId &&
      getTeamNdPosition(
        pmUserId,
        dateConvert(fromDate),
        dateConvert(toDate),
        selectedItem[0]?.value
      );
  };

  const onSelectPositonHook = (selectedItem: any) => {
    dispatch({
      type: SET_IS_SUMMARY_DETAILS,
      payload: {
        ...summarySelectedDetails,
        summarySelectedPosition: selectedItem,
      },
    });
  };

  const onButtonPress = async () => {
    if (!fromDate || !toDate) {
      setError('Please select a date range');
      return;
    }
    if (selectedTeam.length === 0) {
      setError('Please select a Team');
      return;
    }

    if (selectedPosition.length === 0) {
      setError('Please select a Position');
      return;
    }

    if (pmUserId && selectedEventType) {
      const {
        hasTrainingDataForSummary,
        hasMatchDataForSummary,
      }: {
        hasTrainingDataForSummary: boolean;
        hasMatchDataForSummary: boolean;
      } = await hasSummaryStat(
        pmUserId,
        dateConvert(fromDate),
        dateConvert(toDate),
        selectedTeam[0].value,
        selectedPosition[0].value
      );

      if (hasMatchDataForSummary && !hasTrainingDataForSummary) {
        const tmpSelectedEventType = deviceStatsEventType.MATCH;

        dispatch({
          type: SET_SELECTED_EVENT_TYPE,
          payload: { data: tmpSelectedEventType },
        });

        await getSummaryStat(
          pmUserId,
          dateConvert(fromDate),
          dateConvert(toDate),
          selectedFilterType,
          tmpSelectedEventType,
          selectedTeam[0].value,
          selectedPosition[0].value
        );
      } else {
        await getSummaryStat(
          pmUserId,
          dateConvert(fromDate),
          dateConvert(toDate),
          selectedFilterType,
          selectedEventType,
          selectedTeam[0].value,
          selectedPosition[0].value
        );
      }
    }

    dispatch({
      type: SET_IS_SUMMARY_DETAILS,
      payload: {
        summaryFromDate: fromDate,
        summaryToDate: toDate,
        summarySelectedTeam: selectedTeam,
        summarySelectedPosition: selectedPosition,
      },
    });

    dispatch({
      type: SET_IS_SUMMARY_MODAL,
      payload: false,
    });
  };

  const fromDateSelect = (date: Date) => {
    const genaratedDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()   
    );
    date &&
      toDate &&
      pmUserId &&
      getTeamNdPosition(
        pmUserId,
        dateConvert(genaratedDate),
        dateConvert(toDate)
      );
    setShowFromPicker(false);

    date &&
      dispatch({
        type: SET_IS_SUMMARY_DETAILS,
        payload: {
          ...summarySelectedDetails,
          summaryFromDate: genaratedDate,
        },
      });

    setError(undefined);
  };

  const toDateSelect = (date: Date) => {
    const genaratedDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()   
    );
    date &&
      fromDate &&
      pmUserId &&
      getTeamNdPosition(
        pmUserId,
        dateConvert(fromDate),
        dateConvert(genaratedDate)
      );
    setshowToPicker(false);
    date &&
      dispatch({
        type: SET_IS_SUMMARY_DETAILS,
        payload: {
          ...summarySelectedDetails,
          summaryToDate: genaratedDate,
        },
      });
    setError(undefined);
  };

  const onClose = () => {
    if (
      selectedPosition.length === 0 ||
      selectedTeam.length === 0 ||
      !fromDate ||
      !toDate ||
      !summaryGraphStat.length
    ) {
      dispatch({
        type: SET_DEVICE_STAT_TYPE,
        payload: DeviceStatsSessionType,
      });
    }

    dispatch({
      type: SET_IS_SUMMARY_MODAL,
      payload: false,
    });
  };

  const renderDate = (date: any) => (
    <View style={DateRangeModalStyle.inputSelection}>
      <Text style={DateRangeModalStyle.listItemText}>{dateToString(date)}</Text>
      <Image
        style={DateRangeModalStyle.calendarIcon}
        source={require('../../../../assets/icons/calendarIcon.png')}
      />
    </View>
  );

  const renderCalenderIcon = (label: string) => (
    <View style={DateRangeModalStyle.inputSelection}>
      <Text style={DateRangeModalStyle.listItemText}>{label}</Text>
      <Image
        style={DateRangeModalStyle.calendarIcon}
        source={require('../../../../assets/icons/calendarIcon.png')}
      />
    </View>
  );

  return (
    <View style={DateRangeModalStyle.container}>
      <View style={DateRangeModalStyle.centeredView}>
        <Modal animationType="slide" transparent={true} visible={modalVisible}>
          <View style={DateRangeModalStyle.centeredView}>
            <View style={DateRangeModalStyle.overlay}></View>
            <View style={DateRangeModalStyle.modalView}>
              <TouchableOpacity
                style={DateRangeModalStyle.closeButton}
                onPress={onClose}
              >
                <AntDesign name="close" size={20} color="#FFFFFF" />
              </TouchableOpacity>
              <View>
                <Text style={DateRangeModalStyle.title}>Summary</Text>

                <Text style={DateRangeModalStyle.subTitle}>
                  pick a date range
                </Text>

                <View style={DateRangeModalStyle.dateSelectorWrapper}>
                  <TouchableOpacity
                    style={DateRangeModalStyle.showDatePickerButton}
                    onPress={() => setShowFromPicker(true)}
                  >
                    <Text style={DateRangeModalStyle.modalText}>
                      {START_DATE}
                    </Text>
                    {fromDate
                      ? renderDate(fromDate)
                      : renderCalenderIcon(START_DATE)}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={DateRangeModalStyle.showDatePickerButton}
                    onPress={() => setshowToPicker(true)}
                  >
                    <Text style={DateRangeModalStyle.modalText}>
                      {END_DATE}
                    </Text>
                    {toDate ? renderDate(toDate) : renderCalenderIcon(END_DATE)}
                  </TouchableOpacity>
                </View>
                <Text style={DateRangeModalStyle.modalText}>Select a Team</Text>
                <View style={DateRangeModalStyle.selectionWrapper}>
                  <SelectionModal
                    title={'Select a Team'}
                    items={mappedTeamList}
                    onCloseHook={handelTeamSelection}
                    selectedItemLabel={selectedTeam[0]?.value}
                    onSelectItemHook={onSelectTeamHook}
                    selectedItem={[selectedTeam[0]]}
                    defaultValues={[selectedTeam[0]?.value]}
                    isModalOpen={isTeamModalOpen}
                    enableDefaultLabel
                    openModal={() => {}}
                  />
                </View>
                <Text style={DateRangeModalStyle.modalText}>
                  Select a Position
                </Text>
                <View style={DateRangeModalStyle.selectionWrapper}>
                  <SelectionModal
                    title={'Select a Position'}
                    items={mappedPositionList}
                    onCloseHook={handelPositonSelection}
                    selectedItemLabel={selectedPosition[0]?.value}
                    onSelectItemHook={onSelectPositonHook}
                    selectedItem={[selectedPosition[0]]}
                    defaultValues={[selectedPosition[0]?.value]}
                    isModalOpen={isPositionModalOpen}
                    enableDefaultLabel
                    openModal={() => {}}
                  />
                </View>
              </View>
              <Text style={DateRangeModalStyle.error}>{error}</Text>
              <View style={DateRangeModalStyle.row}>
                <TouchableOpacity
                  style={DateRangeModalStyle.button}
                  onPress={onButtonPress}
                >
                  <Text style={DateRangeModalStyle.textStyle}>Generate</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          {showFromPicker && (
            <View style={DateRangeModalStyle.datePickerWrapper}>
              <DateTimePickerModal
                isVisible
                mode="date"
                date={fromDate || undefined}
                style={DateRangeModalStyle.datePicker}
                onConfirm={fromDateSelect}
                // display="spinner"
                onCancel={() => setShowFromPicker(false)}
                modalStyleIOS={DateRangeModalStyle.datePickerSelector}
                pickerContainerStyleIOS={DateRangeModalStyle.datePickerWrapper}
                maximumDate={new Date()}
                isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
              />
            </View>
          )}
          {showToPicker && (
            <View style={DateRangeModalStyle.datePickerWrapper}>
              <DateTimePickerModal
                isVisible
                mode="date"
                date={toDate || undefined}
                style={DateRangeModalStyle.datePicker}
                onConfirm={toDateSelect}
                // display="spinner"
                onCancel={() => setshowToPicker(false)}
                modalStyleIOS={DateRangeModalStyle.datePickerSelector}
                pickerContainerStyleIOS={DateRangeModalStyle.datePickerWrapper}
                maximumDate={new Date()}
                isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
              />
            </View>
          )}
        </Modal>
      </View>
    </View>
  );
};

export default SummaryDateRangeModal;
