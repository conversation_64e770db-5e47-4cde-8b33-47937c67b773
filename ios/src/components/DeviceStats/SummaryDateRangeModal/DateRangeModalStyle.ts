import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const DateRangeModalStyle = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    // backgroundColor: colors.darkBlue,
    // opacity: 0.8,
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  modalView: isTabDevice()
    ? {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 20,
        padding: 35,
        alignItems: 'center',
        shadowColor: colors.black,
        width: wp('35%'),
        height: hp('80%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 20,
        padding: wp('4%'),
        alignItems: 'center',
        shadowColor: colors.black,
        width: wp('70%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      },
  closeButton: isTabDevice()
    ? {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('-0.5'),
        zIndex: 9,
      }
    : {
        width: wp('6%'),
        height: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: wp('4%'),
        top: wp('4%'),
        zIndex: 9,
      },
  dateSelectorWrapper: isTabDevice()
    ? {
        paddingTop: wp('1%'),
        width: wp('30%'),
        height: wp('15%'),
        marginBottom: wp('2%'),
      }
    : {
        width: wp('61%'),
        height: wp('38%'),
        marginBottom: wp('1.5%'),
      },
  inputSelection: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: wp('1%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: wp('2%'),
        borderRadius: wp('2%'),
        width: '100%',
      },
  selectionWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        borderRadius: wp('1%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        borderRadius: wp('2%'),
        width: '100%',
      },

  title: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1%'),
      },
  subTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.6%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      },
  error: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.red,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        // padding: wp('1%'),
        width: '100%',
        height: hp('7%'),
        marginTop: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        width: wp('60%'),
        height: hp('4%'),
        marginTop: wp('2%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  button2: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        // padding: wp('1%'),
        width: wp('11%'),
        height: hp('7%'),
        marginTop: wp('1%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: wp('1%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        padding: wp('1%'),
        width: wp('20%'),
        height: hp('4%'),
        marginTop: wp('2%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  textStyle: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        textAlign: 'center',
        fontSize: wp('3%'),
      },
  modalText: isTabDevice()
    ? {
        // marginBottom: 15,
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  dateString: isTabDevice()
    ? {
        // marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-Bold',
        paddingHorizontal: 10,
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('4%'),
        fontWeight: 'bold',
        paddingHorizontal: 10,
      },
  addView: {
    alignItems: 'flex-end',
    alignContent: 'flex-end',
    paddingHorizontal: 20,
  },
  AddTouchableOpacity: {
    height: 60,
    width: 60,
    paddingBottom: 5,
    borderRadius: 60,
    backgroundColor: '#1dc4d2',
    alignItems: 'center',
  },
  AddIcon: {
    paddingTop: 12,
  },
  showDatePickerButton: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginBottom: wp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginTop: wp('3%'),
        marginBottom: wp('1%'),
      },
  row: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
  },
  datePickerContainer: {},
  datePickerSelector: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'absolute',
        left: wp('27%'),
        top: wp('22%'),
      }
    : {
        // width: wp('40%'),
        position: 'absolute',
        left: wp('2%'),
        top: wp('40%'),
      },
  datePickerWrapper: isTabDevice()
    ? {
        zIndex: 3,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      }
    : {
        position: 'absolute',
        borderRadius: wp('3%'),
        top: 0,
        left: 0,
        zIndex: 3,
        width: wp('90%'),
        borderWidth: 10,
        borderColor: colors.semiDarkBlue,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },
  calendarIcon: isTabDevice()
    ? {
        height: wp('1.5%'),
        marginRight: 10,
        resizeMode: 'contain',
        width: wp('1.5%'),
      }
    : {
        height: wp('4%'),
        marginRight: 10,
        resizeMode: 'contain',
        width: wp('4%'),
      },
  listItemText: isTabDevice()
    ? {
        color: colors.grey,
        paddingLeft: wp('1%'),
        marginRight: wp('4%'),
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.grey,
        paddingLeft: wp('3%'),
        fontSize: wp('3.1%'),
        fontFamily: 'Poppins-Medium',
      },
  datePicker: {},
});
export default DateRangeModalStyle;
