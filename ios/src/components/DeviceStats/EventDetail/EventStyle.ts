import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  container: {
    flexDirection: 'row',
  },
  eventsWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        backgroundColor: colors.darkBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
      },
  header: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: wp('4%'),
      },
  dataMainWrapper: isTabDevice()
    ? {
        width: wp('55%'),
        backgroundColor: colors.darkBlue,
        padding: wp('2%'),
        borderRadius: wp('2%'),
        justifyContent: 'space-between',
      }
    : {},
  dateWrapper: {
    alignItems: 'center',
  },
  date: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        lineHeight: wp('1.7%'),
      }
    : {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Medium',
        lineHeight: wp('2.3%'),
      },
  type: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.9%'),
        fontFamily: 'Poppins-Medium',
        lineHeight: wp('2.7%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        lineHeight: wp('3.7%'),
      },
  dataWrapper: isTabDevice()
    ? {
        // flexDirection: 'row',
        // flex: 1,
      }
    : {},
  dataGroup: isTabDevice()
    ? {
        flexDirection: 'row',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: wp('3%'),
      },
  dataSet: isTabDevice()
    ? {
        // borderRightWidth: 1,
        // borderRightColor: colors.borderBlue,
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        width: '45%',
      }
    : {
        paddingLeft: wp('5%'),
        paddingRight: wp('5%'),
        width: '45%',
      },
  dataTitle: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  data: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Medium',
      },
  eventSelection: isTabDevice()
    ? {}
    : {
        marginBottom: wp('3%'),
        alignItems: 'center',
      },
  eventSelectionTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('0.5%'),
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginBottom: wp('1.5%'),
      },
  eventSelections: isTabDevice()
    ? {}
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
      },
  eventSelectionOption: isTabDevice()
    ? {
        padding: wp('1%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('1.5%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('1%'),
        backgroundColor: colors.darkBlue,
        width: '100%',
      }
    : {
        padding: wp('2.5%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('50%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('1%'),
        backgroundColor: colors.darkBlue,
        width: '48%',
      },
  eventSelectionOptionDisabled: isTabDevice()
    ? {
        padding: wp('1%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('1.5%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('1%'),
        backgroundColor: colors.darkBlue,
        width: '100%',
      }
    : {
        padding: wp('2.5%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('50%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('1%'),
        backgroundColor: colors.veryDarkBlue,
        width: '48%',
      },
  eventSelectionOptionSelected: isTabDevice()
    ? {
        padding: wp('1%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('1.5%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('1%'),
        backgroundColor: colors.green,
        width: '100%',
      }
    : {
        padding: wp('2.5%'),
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        borderRadius: wp('50%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('1%'),
        backgroundColor: colors.green,
        width: '48%',
      },
  eventSelectionText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.7%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  eventSelectionTextDisabled: isTabDevice()
    ? {
        color: colors.darkTextColor,
        fontSize: wp('1.7%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.darkTextColor,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
});
