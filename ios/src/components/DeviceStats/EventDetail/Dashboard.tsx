import React, { FC } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import customEventStyle from './EventStyle';
import { FontAwesome } from '@expo/vector-icons';
import { sessionDataType } from '../../../store/reducers/DeviceStats/DeviceStatsReducer';
import ActivitySpinner from '../../ActivitySpinner/ActivitySpinner';
import { RootStore } from '../../../store/store';
import { useSelector } from 'react-redux';
import { DEVICE_STATS_CATEGORY_LIST } from '../../../constants/data';
import moment from 'moment';

interface IDashboard {
  renderEventTypeForMobile: any;
  data: sessionDataType;
  handlePrev: () => void;
  handleNext: () => void;
  loading: boolean;
}

const Dashboard: FC<IDashboard> = ({
  renderEventTypeForMobile,
  data,
  handlePrev,
  handleNext,
  loading,
}) => {
  const {
    startDateTime,
    sessionType,
    position,
    teamName,
    phaseDurationInMints,
    sessionId,
    phaseType,
  } = data || {};
  const eventStyle = useStyles(customEventStyle);

  const getFormatedDate = (dateString: string) => {
    const dateObject = new Date(dateString);
    const month = dateObject.getMonth() + 1;
    const date = dateObject.getDate();
    const year = dateObject.getFullYear();
    return `${date} / ${month} / ${year}`;
  };

  const {
    selectedDeviceStateType,
    summarySelectedDetails,
    selectedEventType,
    sessionPaginationCount,
    sessionDataTotalRecords,
  } = useSelector((state: RootStore) => state?.deviceStats);

  const fromDate =
    (summarySelectedDetails?.summaryFromDate &&
      getFormatedDate(summarySelectedDetails.summaryFromDate.toJSON())) ||
    'N/A';
  const toDate =
    (summarySelectedDetails?.summaryToDate &&
      getFormatedDate(summarySelectedDetails.summaryToDate.toJSON())) ||
    'N/A';

  const selectedTeam =
    (summarySelectedDetails?.summarySelectedTeam?.length &&
      summarySelectedDetails?.summarySelectedTeam[0]?.value) ||
    'N/A';
  const selectedPosition =
    (summarySelectedDetails?.summarySelectedPosition?.length &&
      summarySelectedDetails?.summarySelectedPosition[0]?.value) ||
    'N/A';

  const renderHeading = () => {
    if (selectedDeviceStateType == DEVICE_STATS_CATEGORY_LIST[0].key) {
      return (
        <View style={eventStyle.header}>
          <TouchableOpacity
            disabled={sessionDataTotalRecords - 1 == sessionPaginationCount}
            onPress={handlePrev}
          >
            <FontAwesome
              name="arrow-circle-left"
              size={24}
              color={
                sessionDataTotalRecords - 1 == sessionPaginationCount
                  ? '#808080'
                  : '#36d982'
              }
            />
          </TouchableOpacity>
          <View style={eventStyle.dateWrapper}>
            <Text style={eventStyle.date}>
              {startDateTime ? moment(startDateTime).format('ll') : ''}
            </Text>
            <Text style={eventStyle.type}>
              {phaseType ? `${phaseType} Session` : ''}
            </Text>
          </View>
          <TouchableOpacity
            disabled={sessionPaginationCount <= 0}
            onPress={handleNext}
          >
            <FontAwesome
              name="arrow-circle-right"
              size={24}
              color={!sessionPaginationCount ? '#808080' : '#36d982'}
            />
          </TouchableOpacity>
        </View>
      );
    } else {
      return (
        <View style={eventStyle.header}>
          <View></View>
          <View style={eventStyle.dateWrapper}>
            <Text
              style={eventStyle.type}
            >{`${selectedEventType} Summary`}</Text>
          </View>
          <View></View>
        </View>
      );
    }
  };

  const renderSelectedDetail = () => {
    if (selectedDeviceStateType == DEVICE_STATS_CATEGORY_LIST[0].key) {
      return (
        <View style={eventStyle.dataWrapper}>
          <View style={eventStyle.dataGroup}>
            <View style={eventStyle.dataSet}>
              <Text style={eventStyle.dataTitle}>Position</Text>
              <Text style={eventStyle.data}>{position || 'N/A'}</Text>
            </View>
            <View style={eventStyle.dataSet}>
              <Text style={eventStyle.dataTitle}>Team</Text>
              <Text style={eventStyle.data}>{teamName || 'N/A'}</Text>
            </View>
          </View>

          <View style={eventStyle.dataGroup}>
            <View style={eventStyle.dataSet}>
              <Text style={eventStyle.dataTitle}>Start Time</Text>
              <Text style={eventStyle.data}>
                {startDateTime ? moment(startDateTime).format('LT') : 'N/A'}
              </Text>
            </View>
            <View style={{ ...eventStyle.dataSet, borderRightWidth: 0 }}>
              <Text style={eventStyle.dataTitle}>Duration</Text>
              <Text style={eventStyle.data}>
                {phaseDurationInMints ? `${phaseDurationInMints} min` : ' N/A'}
              </Text>
            </View>
          </View>
        </View>
      );
    } else {
      return (
        <View style={eventStyle.dataWrapper}>
          <View style={eventStyle.dataGroup}>
            <View style={eventStyle.dataSet}>
              <Text style={eventStyle.dataTitle}>Start Date</Text>
              <Text style={eventStyle.data}>{fromDate}</Text>
            </View>
            <View style={eventStyle.dataSet}>
              <Text style={eventStyle.dataTitle}>End Date</Text>
              <Text style={eventStyle.data}>{toDate}</Text>
            </View>
          </View>
          <View style={eventStyle.dataGroup}>
            <View style={eventStyle.dataSet}>
              <Text style={eventStyle.dataTitle}>Position</Text>
              <Text style={eventStyle.data}>{selectedPosition}</Text>
            </View>
            <View style={{ ...eventStyle.dataSet, borderRightWidth: 0 }}>
              <Text style={eventStyle.dataTitle}>Team</Text>
              <Text style={eventStyle.data}>{selectedTeam}</Text>
            </View>
          </View>
        </View>
      );
    }
  };

  return (
    <View style={eventStyle.dataMainWrapper}>
      {!isTabDevice() && renderEventTypeForMobile()}
      {loading ? (
        <ActivitySpinner />
      ) : (
        <>
          {renderHeading()}
          {/* <ScrollView horizontal={true}>{renderSelectedDetail()}</ScrollView> */}
          {renderSelectedDetail()}
        </>
      )}
    </View>
  );
};

export default Dashboard;
