import React, { FC, useEffect, useMemo, useState } from 'react';
import { Text, View, FlatList, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { isTabDevice } from '../../../../config/appConfig';
import {
  color,
  deviceStatfilterList,
  physicalTypeList,
  technicalTypeList,
} from '../../../../constants/constants';
import useInputSelectModal from '../../../../hooks/useInputSelectModal';
import useStyles from '../../../../hooks/useStyles';
import { RootStore } from '../../../../store/store';
import SelectionModal from '../../../modal/SelectionModal/SelectionModal';
import SummaryDataChart from './SummaryDataChart';
import SummaryDataModal from './SummaryDataModal';
import customSummaryDataStyle from './SummaryDataStyle';

const SummaryData = () => {
  const summaryDataStyle = useStyles(customSummaryDataStyle);
  const [selectedType, setSelectedType] = useState('');
  const [isOpenSummayModal, setIsOpenSummaryModal] = useState<boolean>(false);
  const [
    setIsTypeModalOpen,
    isTypeModalOpen,
    setSelectedDropdownType,
    selectedDropdownType,
  ] = useInputSelectModal();

  const { selectedFilterType, summaryGraphStat, summaryGraphLoading } =
    useSelector((state: RootStore) => state?.deviceStats);

  const onSelectTypeHook = (item: any) => {
    setSelectedType(item[0].value);
    setSelectedDropdownType(item);
  };

  useEffect(() => {
    if (selectedFilterType === deviceStatfilterList[0].id) {
      setSelectedType('totalTouchesCount');
      setSelectedDropdownType([
        { label: 'Total Touches', value: 'totalTouchesCount' },
      ]);
    } else {
      setSelectedType('distanceCoveredByPlayer');
      setSelectedDropdownType([
        { label: 'Distance Covered (km)', value: 'distanceCoveredByPlayer' },
      ]);
    }
  }, [selectedFilterType]);

  const computedGraphData = useMemo(() => {
    if (selectedType == '') {
      return {};
    }
    if (!summaryGraphStat.length) {
      return {};
    }
    const selectedDataSet =
      selectedFilterType === deviceStatfilterList[0].id
        ? technicalTypeList
        : physicalTypeList;

    const seletedFilterObj = selectedDataSet.find(
      filteSet => filteSet.value == selectedType
    );

    const isDiff = seletedFilterObj?.isDiff || false;

    if (isDiff) {
      const diffGarphDataset = seletedFilterObj?.option.map(
        (selectedOption: string) => {
          return summaryGraphStat.map((val: any) => {
            const selectedVal = (selectedType && val[selectedOption]) || 0;
            return [val.sessionStartDateTime, selectedVal];
          });
        }
      );

      return {
        series: diffGarphDataset.map((dataSet: any, index: number) => {
          return {
            name: seletedFilterObj?.legends[index],
            type: 'line',
            symbol: 'circle',
            data: dataSet,
            symbolSize: '8',
            itemStyle: { color: color[index] },
            lineStyle: {
              type: 'solid',
              color: 'white',
              width: 1,
            },
          };
        }),
        legendNames: [...(seletedFilterObj?.legends || [])],
        name: seletedFilterObj?.label,
      };
    } else {
      let graphData = summaryGraphStat.map((val: any) => {
        const selectedVal = (selectedType && val[selectedType]) || 0;
        return [val.sessionStartDateTime, selectedVal];
      });
      return {
        series: [
          {
            name: seletedFilterObj?.legends[0],
            type: 'line',
            symbol: 'circle',
            data: graphData,
            symbolSize: '8',
            itemStyle: { color: '#41C4D2' },
            lineStyle: {
              type: 'solid',
              color: 'white',
              width: 1,
            },
          },
        ],
        legendNames: [seletedFilterObj?.legends[0]],
        name: seletedFilterObj?.label,
      };
    }
  }, [JSON.stringify(summaryGraphStat), selectedType]);

  const renderChildData = ({ item }: any) => (
    <Item label={item.label} value={item.value} />
  );

  const Item = ({ label, value }: { label: string; value: string }) => (
    <View
      style={
        value == selectedType
          ? summaryDataStyle.graphSelection
          : summaryDataStyle.notSelection
      }
    >
      <TouchableOpacity
        onPress={() => {
          setSelectedType(value);
        }}
      >
        <Text style={summaryDataStyle.graphSelectionText}>{label}</Text>
      </TouchableOpacity>
    </View>
  );

  const renderUITab = () => {
    return (
      <View style={summaryDataStyle.topSelection}>
        <FlatList
          data={
            selectedFilterType === deviceStatfilterList[0].id
              ? technicalTypeList
              : physicalTypeList
          }
          renderItem={renderChildData}
          keyExtractor={item => item.value}
          horizontal
          initialNumToRender={6}
        />
      </View>
    );
  };

  const renderUIForMobile = () => (
    <SelectionModal
      title={'Select a Type'}
      items={
        selectedFilterType === deviceStatfilterList[0].id
          ? technicalTypeList
          : physicalTypeList
      }
      onCloseHook={setIsTypeModalOpen}
      selectedItemLabel={selectedDropdownType[0]?.label}
      onSelectItemHook={onSelectTypeHook}
      selectedItem={[selectedDropdownType[0]]}
      defaultValues={[selectedDropdownType[0]?.value]}
      isModalOpen={isTypeModalOpen}
      enableDefaultLabel
      openModal={() => {}}
    />
  );

  const renderBtnForMobile = () => (
    <View style={summaryDataStyle.btnOverlay}>
      <TouchableOpacity
        onPress={() =>
          setIsOpenSummaryModal(IsOpenSummaryModal => !IsOpenSummaryModal)
        }
      >
        <View style={summaryDataStyle.clickBtn}>
          <Text style={summaryDataStyle.btnText}>Click to view</Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  const content = () => {
    if (summaryGraphLoading) {
      return (
        <View style={summaryDataStyle.mainContainer}>
          <View style={summaryDataStyle.graphParentWrapper}>
            <Text style={summaryDataStyle.txtWhite}>Loading...</Text>
          </View>
        </View>
      );
    }

    if (!summaryGraphStat.length) {
      return (
        <View style={summaryDataStyle.mainContainer}>
          <View style={summaryDataStyle.graphParentWrapper}>
            <Text style={summaryDataStyle.txtWhite}>No data available</Text>
          </View>
        </View>
      );
    }
    return (
      <View style={summaryDataStyle.mainContainer}>
        <View style={summaryDataStyle.graphParentWrapper}>
          {isTabDevice() ? (
            <View style={summaryDataStyle.graphSelections}>
              {renderUITab()}
            </View>
          ) : (
            <View style={summaryDataStyle.graphSelections}>
              {renderUIForMobile()}
            </View>
          )}
          {!isTabDevice() && renderBtnForMobile()}
          <SummaryDataChart
            graphData={computedGraphData}
            selectedType={selectedType}
            key={selectedType}
          />
        </View>
      </View>
    );
  };

  return (
    <>
      {isOpenSummayModal && (
        <SummaryDataModal setIsOpenSummaryModal={setIsOpenSummaryModal} />
      )}
      {content()}
    </>
  );
};

export default SummaryData;
