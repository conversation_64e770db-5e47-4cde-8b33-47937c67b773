import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

export default (colors: any) => ({
  mainContainer: {},
  lableView: isTabDevice()
    ? {
        marginTop: hp('2.5%'),
        marginBottom: hp('2.5%'),
        justifyContent: 'space-between',
        marginRight: hp('7%'),
      }
    : {
        marginBottom: hp('1%'),
        marginHorizontal: wp('4%'),
      },
  selected: {
    backgroundColor: colors.green,
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
  },
  notSelected: {
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
  },
  text: isTabDevice()
    ? {
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      }
    : {
        fontSize: wp('3'),
        fontFamily: 'Poppins-Bold',
        color: colors.fontBlue,
      },
  textSelected: isTabDevice()
    ? {
        fontSize: hp('3%'),
        fontFamily: 'Poppins-Bold',
        color: colors.green,
      }
    : {
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.green,
      },
  notSelection: {
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
  },
  topSelection: {
    borderBottomWidth: 1,
    borderBottomColor: colors.veryDarkBlue,
    paddingBottom: wp('1%'),
    marginBottom: wp('1%'),
    width: '100%',
  },
  graphParentWrapper: isTabDevice()
    ? {
        height: hp('70%'),
      }
    : {
        height: hp('50%'),
        paddingTop: wp('3%'),
        position: 'relative',
      },
  graphSelections: isTabDevice()
    ? {
        backgroundColor: colors.lightBlue,
        padding: 10,
        paddingTop: 20,
        paddingBottom: 20,
        paddingLeft: 30,
        flexDirection: 'row',
        borderTopLeftRadius: wp('2%'),
        borderTopRightRadius: wp('2%'),
      }
    : {
        backgroundColor: colors.lightBlue,
        paddingTop: 10,
        paddingBottom: 10,
        paddingLeft: 15,
        paddingRight: 15,
        flexDirection: 'row',
        borderTopLeftRadius: wp('2%'),
        borderTopRightRadius: wp('2%'),
      },
  graphSelection: {
    backgroundColor: colors.aquaBlue,
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
  },
  graphSelectionText: isTabDevice()
    ? {
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      }
    : {
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
      },
  graphWrapper: {
    position: 'relative',
  },
  graphAxisLabel: isTabDevice()
    ? {
        position: 'absolute',
        left: 40,
        top: 30,
        zIndex: 1,
      }
    : {
        position: 'absolute',
        left: wp('-4%'),
        top: hp('20%'),
        zIndex: 1,
        transform: [{ rotate: '270deg' }],
      },
  graphAxisLabelText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  graphAxisLabelBottom: isTabDevice()
    ? {
        position: 'absolute',
        right: 20,
        bottom: 25,
        zIndex: 1,
      }
    : {
        position: 'absolute',
        right: 20,
        bottom: hp('6.5%'),
        zIndex: 1,
      },
  graph: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        // height: '100%',
        borderBottomLeftRadius: wp('2%'),
        borderBottomRightRadius: wp('2%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        paddingTop: 20,
        height: '95%',
        borderBottomLeftRadius: wp('2%'),
        borderBottomRightRadius: wp('2%'),
      },
  txtWhite: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  btnOverlay: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    zIndex: 100,
    opacity: 0.9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clickBtn: {
    backgroundColor: colors.green,
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
  },
  btnText: {
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },
  centeredView: {
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
    margin: 'auto',
    position: 'relative',
  },
  modalArea: {
    width: wp('90%'),
    height: hp('90%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  backButton: {
    marginBottom: wp('10%'),
  },
});
