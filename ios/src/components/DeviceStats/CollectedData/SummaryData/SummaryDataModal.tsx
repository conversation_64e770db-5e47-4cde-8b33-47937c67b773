import React, { useEffect, useMemo, useState } from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import {
  color,
  deviceStatfilterList,
  physicalTypeList,
  technicalTypeList,
} from '../../../../constants/constants';
import useInputSelectModal from '../../../../hooks/useInputSelectModal';
import useStyles from '../../../../hooks/useStyles';
import ModalWrapper from '../../../modal/ModalWrapper/ModalWrapper';
import { RootStore } from '../../../../store/store';
import SelectionModal from '../../../modal/SelectionModal/SelectionModal';
import SummaryDataChart from './SummaryDataChart';
import customSummaryDataStyle from './SummaryDataStyle';
import Filters from '../../FilterTypes/Filters';

const SummaryDataModal = ({
  setIsOpenSummaryModal,
}: {
  setIsOpenSummaryModal: any;
}) => {
  const summaryDataStyle = useStyles(customSummaryDataStyle);
  const [selectedType, setSelectedType] = useState('');
  const [
    setIsTypeModalOpen,
    isTypeModalOpen,
    setSelectedDropdownType,
    selectedDropdownType,
  ] = useInputSelectModal();

  const { selectedFilterType, summaryGraphStat, summaryGraphLoading } =
    useSelector((state: RootStore) => state?.deviceStats);

  const onSelectTypeHook = (item: any) => {
    setSelectedType(item[0].value);
    setSelectedDropdownType(item);
  };

  useEffect(() => {
    if (selectedFilterType === deviceStatfilterList[0].id) {
      setSelectedType('totalTouchesCount');
      setSelectedDropdownType([
        { label: 'Total Touches', value: 'totalTouchesCount' },
      ]);
    } else {
      setSelectedType('distanceCoveredByPlayer');
      setSelectedDropdownType([
        { label: 'Distance Covered (km)', value: 'distanceCoveredByPlayer' },
      ]);
    }
  }, [selectedFilterType]);

  const computedGraphData = useMemo(() => {
    if (selectedType == '') {
      return {};
    }
    if (!summaryGraphStat.length) {
      return {};
    }
    const selectedDataSet =
      selectedFilterType === deviceStatfilterList[0].id
        ? technicalTypeList
        : physicalTypeList;

    const seletedFilterObj = selectedDataSet.find(
      filteSet => filteSet.value == selectedType
    );

    const isDiff = seletedFilterObj?.isDiff || false;

    if (isDiff) {
      const diffGarphDataset = seletedFilterObj?.option.map(
        (selectedOption: string) => {
          return summaryGraphStat.map((val: any) => {
            const selectedVal = (selectedType && val[selectedOption]) || 0;
            return [val.sessionStartDateTime, selectedVal];
          });
        }
      );

      return {
        series: diffGarphDataset.map((dataSet: any, index: number) => {
          return {
            name: seletedFilterObj?.legends[index],
            type: 'line',
            symbol: 'circle',
            data: dataSet,
            symbolSize: '8',
            itemStyle: { color: color[index] },
            lineStyle: {
              type: 'solid',
              color: 'white',
              width: 1,
            },
          };
        }),
        legendNames: [...(seletedFilterObj?.legends || [])],
        name: seletedFilterObj?.label,
      };
    } else {
      let graphData = summaryGraphStat.map((val: any) => {
        const selectedVal = (selectedType && val[selectedType]) || 0;
        return [val.sessionStartDateTime, selectedVal];
      });
      return {
        series: [
          {
            name: seletedFilterObj?.legends[0],
            type: 'line',
            symbol: 'circle',
            data: graphData,
            symbolSize: '8',
            itemStyle: { color: '#41C4D2' },
            lineStyle: {
              type: 'solid',
              color: 'white',
              width: 1,
            },
          },
        ],
        legendNames: [seletedFilterObj?.legends[0]],
        name: seletedFilterObj?.label,
      };
    }
  }, [JSON.stringify(summaryGraphStat), selectedType]);

  const onBackButtonClicked = () => {
    setIsOpenSummaryModal(false);
  };

  const content = () => {
    if (summaryGraphLoading) {
      return (
        <View style={summaryDataStyle.mainContainer}>
          <View style={summaryDataStyle.graphParentWrapper}>
            <Text style={summaryDataStyle.txtWhite}>Loading...</Text>
          </View>
        </View>
      );
    }

    if (!summaryGraphStat.length) {
      return (
        <View style={summaryDataStyle.mainContainer}>
          <View style={summaryDataStyle.graphParentWrapper}>
            <Text style={summaryDataStyle.txtWhite}>No data available</Text>
          </View>
        </View>
      );
    }

    return (
      <>
        <View style={summaryDataStyle.graphSelections}>
          <SelectionModal
            title={'Select a Type'}
            items={
              selectedFilterType === deviceStatfilterList[0].id
                ? technicalTypeList
                : physicalTypeList
            }
            onCloseHook={setIsTypeModalOpen}
            selectedItemLabel={selectedDropdownType[0]?.label}
            onSelectItemHook={onSelectTypeHook}
            selectedItem={[selectedDropdownType[0]]}
            defaultValues={[selectedDropdownType[0]?.value]}
            isModalOpen={isTypeModalOpen}
            enableDefaultLabel
            openModal={() => {}}
          />
        </View>

        <SummaryDataChart
          graphData={computedGraphData}
          selectedType={selectedType}
          key={selectedType}
        />
      </>
    );
  };

  return (
    <View>
      <ModalWrapper visible transparent>
        <View style={summaryDataStyle.overlay} />
        <View style={summaryDataStyle.centeredView}>
          <View style={summaryDataStyle.modalArea}>
            <TouchableOpacity onPress={() => onBackButtonClicked()}>
              <Ionicons
                name="arrow-back"
                size={24}
                color="white"
                style={summaryDataStyle.backButton}
              />
            </TouchableOpacity>
            <Filters />
            <View style={summaryDataStyle.mainContainer}>
              <View style={summaryDataStyle.graphParentWrapper}>
                {content()}
              </View>
            </View>
          </View>
        </View>
      </ModalWrapper>
    </View>
  );
};

export default SummaryDataModal;
