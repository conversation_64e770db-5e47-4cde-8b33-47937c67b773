import React, { useMemo } from 'react';
import { FlatList, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { deviceStatfilterList } from '../../../../constants/constants';
import useStyles from '../../../../hooks/useStyles';
import { RootStore } from '../../../../store/store';
import PercentageBar from '../../PercentageBar';
import customSessionDataStyle from './SessionDataStyle';

interface IChartDatalistTemplate {
  label: string;
  id: string;
  max: string;
  player: string;
  avg: string;
  isDiff?: boolean;
  playerLeft?: string;
  playerRight?: string;
  rightLegAvg?: string;
  leftLegAvg?: string;
}

interface IChartDataTemplate {
  label: string;
  id: string;
  playerLabel: string | number;
  avgLabel: string | number;
  percentage: {
    left: number;
    right: number;
    avgLeft?: number;
    avgRight?: number;
  };
}

const technicalListTemplate: IChartDatalistTemplate[] = [
  {
    label: 'Total Touches',
    id: 'totalTouches',
    max: 'maxCount',
    player: 'countForPlayer',
    avg: 'positionAvg',
  },
  {
    label: 'Leg Use (%)',
    id: 'legUse',
    max: '',
    player: '',
    avg: '',
    isDiff: true,
    playerLeft: 'playerLeftLegUsePercentage',
    playerRight: 'playerRightLegUsePercentage',
    rightLegAvg: 'rightLegUsePositionAvgPercentage',
    leftLegAvg: 'leftLegUsePositionAvgPercentage',
  },
];

const physicalMappingTemplate: IChartDatalistTemplate[] = [
  {
    label: 'Distance Covered (km)',
    id: 'distanceCovered',
    max: 'maxValueOfDistanceCovered',
    player: 'distanceCoveredByPlayer',
    avg: 'positionAvgOfDistanceCovered',
  },
  {
    label: 'HID (m)',
    id: 'hid',
    max: 'maxValueOfHid',
    player: 'hidOfPlayer',
    avg: 'positionAvgOfHid',
  },
  {
    label: 'Intense Speed Changes',
    id: 'intenseSpeedChanges',
    max: 'maxValueOfIntenseSpeedChanges',
    player: 'intenseSpeedChangesByPlayer',
    avg: 'positionAvgOfIntenseSpeedChanges',
  },
  {
    label: 'Work Rate (m/min)',
    id: 'workRate',
    max: 'maxValueOfWorkRate',
    player: 'workRateByPlayer',
    avg: 'positionAvgOfWorkRate',
  },
];

const SessionData = () => {
  const summaryDataStyle = useStyles(customSessionDataStyle);

  const {
    sessionPercentageData,
    selectedFilterType,
    sessionPercentageLoading,
  } = useSelector((state: RootStore) => state?.deviceStats);

  const genarateLabel =
    selectedFilterType === deviceStatfilterList[0].id
      ? 'Technical Balance'
      : 'Physical Balance';

  const genaratePercentageProps = (
    selectedTemplate: IChartDatalistTemplate
  ) => {
    let chartDataTemplate: IChartDataTemplate = {
      label: '',
      id: '',
      playerLabel: 0,
      avgLabel: 0,
      percentage: {
        left: 0,
        right: 0,
      },
    };

    chartDataTemplate.label = selectedTemplate.label;
    chartDataTemplate.id = selectedTemplate.id;

    if (selectedTemplate?.isDiff) {
      const selectedType = sessionPercentageData?.[selectedTemplate.id];
      chartDataTemplate.percentage = {
        left:
          selectedTemplate?.playerLeft &&
          selectedType?.[selectedTemplate?.playerLeft],
        right:
          selectedTemplate?.playerRight &&
          selectedType?.[selectedTemplate?.playerRight],
        avgLeft:
          selectedTemplate?.leftLegAvg &&
          selectedType?.[selectedTemplate?.leftLegAvg],
        avgRight:
          selectedTemplate?.rightLegAvg &&
          selectedType?.[selectedTemplate?.rightLegAvg],
      };

      chartDataTemplate.playerLabel = `L ${chartDataTemplate.percentage?.left} | R ${chartDataTemplate.percentage?.right}`;
      chartDataTemplate.avgLabel = `L ${chartDataTemplate.percentage?.avgLeft} | R ${chartDataTemplate.percentage?.avgRight}`;

      return chartDataTemplate;
    }

    const selectedType = sessionPercentageData?.[selectedTemplate.id];

    chartDataTemplate.playerLabel = selectedType?.[selectedTemplate.player];
    chartDataTemplate.avgLabel = selectedType?.[selectedTemplate.avg];
    const max = selectedType?.[selectedTemplate.max];

    const playerPercentage =
      (selectedType?.[selectedTemplate.player] / max) * 100;
    const positionPercentage =
      (selectedType?.[selectedTemplate.avg] / max) * 100;

    chartDataTemplate.percentage = {
      left: playerPercentage,
      right: positionPercentage,
    };
    return chartDataTemplate;
  };

  const genatateStatList = useMemo(() => {
    if (sessionPercentageData && Object.keys(sessionPercentageData).length) {
      if (selectedFilterType === deviceStatfilterList[0].id) {
        return technicalListTemplate.map(technicalTemplate => {
          return genaratePercentageProps(technicalTemplate);
        });
      } else {
        return physicalMappingTemplate.map(physicalTemplate => {
          return genaratePercentageProps(physicalTemplate);
        });
      }
    } else {
      return [];
    }
  }, [sessionPercentageData, selectedFilterType]);

  const renderChildData = ({ item }: any) => <Item data={item} />;

  const Item = ({ data }: { data: any }) => (
    <View style={summaryDataStyle.percentageCard}>
      <View style={summaryDataStyle.percentageHeadingContainer}>
        <Text style={summaryDataStyle.dataValue}>{data.playerLabel}</Text>
        <Text style={summaryDataStyle.dataTitle}>{data.label}</Text>
        <Text style={summaryDataStyle.dataValue}>{data.avgLabel}</Text>
      </View>
      <View style={summaryDataStyle.percentage}>
        <View>
          <PercentageBar value={data.percentage} />
        </View>
      </View>
    </View>
  );

  const renderSelectedList = () => (
    <View>
      <View style={summaryDataStyle.heading}>
        <View>
          <Text style={summaryDataStyle.dataLabel}>PLAYER</Text>
        </View>
        <View>
          <Text style={summaryDataStyle.dataLabel}>POSITION AVG.</Text>
        </View>
      </View>
      <FlatList
        data={genatateStatList}
        renderItem={renderChildData}
        keyExtractor={item => item?.id}
        initialNumToRender={6}
      />
    </View>
  );

  if (sessionPercentageLoading) {
    return (
      <View style={summaryDataStyle.mainContainer}>
        <Text style={summaryDataStyle.txtWhite}>Loading...</Text>
      </View>
    );
  }

  if (genatateStatList?.length === 0) {
    return (
      <View style={summaryDataStyle.mainContainer}>
        <Text style={summaryDataStyle.txtWhite}>No data available</Text>
      </View>
    );
  }

  return (
    <View style={summaryDataStyle.mainContainer}>
      <Text style={summaryDataStyle.txtWhite}>{genarateLabel}</Text>
      {renderSelectedList()}
    </View>
  );
};

export default SessionData;
