import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../../config/appConfig';

export default (colors: any) => ({
  txtWhite: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  mainContainer: {
    backgroundColor: colors.darkBlue,
    padding: hp('3'),
    borderRadius: wp('2%'),
  },
  heading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: hp('3'),
  },
  percentageHeadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: wp('2%'),
    marginBottom: wp('0.1%'),
  },
  percentageCard: {},
  percentage: {
    // flexDirection: 'row',
  },
  dataTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('2.4%'),
        fontFamily: 'Poppins-Medium',
      },
  dataLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Medium',
      },
  dataValue: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('2.2%'),
        fontFamily: 'Poppins-Medium',
      },
});
