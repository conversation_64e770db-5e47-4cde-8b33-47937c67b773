import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  txtWhite: {
    color: colors.white,
  },
  mainContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }
    : {
        marginBottom: wp('2%'),
      },
  rightContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '60%',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
      },
  labelView: isTabDevice()
    ? {}
    : {
        width: '48%',
      },

  title: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      },
  navText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Meidum',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Meidum',
        textAlign: 'center',
        width: '100%',
      },
  textwrapper: isTabDevice()
    ? {
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('50%'),
        marginRight: wp('2%'),
      }
    : {
        paddingLeft: hp('3.5%'),
        paddingRight: hp('3.5%'),
        paddingTop: hp('0.2%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('50%'),
        marginRight: wp('2%'),
        marginLeft: wp('2%'),
        textAlign: 'center',
      },
  selectedWrapper: isTabDevice()
    ? {
        backgroundColor: colors.green,
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('50%'),
        marginRight: wp('2%'),
      }
    : {
        backgroundColor: colors.green,
        paddingLeft: hp('3.5%'),
        paddingRight: hp('3.5%'),
        paddingTop: hp('0.2%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('50%'),
        marginRight: wp('2%'),
        marginLeft: wp('2%'),
        textAlign: 'center',
      },
  userId: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'right',
        lineHeight: wp('1.8%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'right',
      },
  lastSyncText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'right',
      }
    : {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'right',
      },
  lastSyncDate: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'right',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
        textAlign: 'right',
      },

  lastSyncData: isTabDevice()
    ? {
        marginRight: 15,
      }
    : {
        marginRight: 5,
      },

  statsHeader: isTabDevice()
    ? {}
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: hp('1%'),
        width: '100%',
      },

  lastSyncWrapper: isTabDevice()
    ? {}
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        width: '33%',
      },
  lastSyncIcon: isTabDevice()
    ? {}
    : {
        marginLeft: wp('3%'),
      },
  list: isTabDevice()
    ? {}
    : {
        width: '100%',
        marginLeft: wp('2%'),
      },
});
