import { MaterialIcons } from '@expo/vector-icons';
import React, { FC, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { isTabDevice } from '../../../config/appConfig';
import {
  deviceStatsEventType,
  userRoleType,
} from '../../../constants/constants';
import { DEVICE_STATS_CATEGORY_LIST } from '../../../constants/data';

import { dateFormatConvert } from '../../../helpers/DateHelper';
import useGetSessionData from '../../../hooks/useGetSessionData';
import useStyles from '../../../hooks/useStyles';
import {
  SET_DEVICE_STAT_TYPE,
  SET_IS_SUMMARY_MODAL,
  SET_SELECTED_EVENT_TYPE,
} from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import { sessionDataType } from '../../../store/reducers/DeviceStats/DeviceStatsReducer';
import { RootStore } from '../../../store/store';
import customDeviceStatsCategoryStyle from './DeviceStatsCategoryStyle';

interface DeviceStatsCategoryProps {
  isUnsyncModalOpen: boolean;
  setIsUnsyncModalOpen: Function;
  pmUserId: string | undefined;
  sessionData: sessionDataType[];
}
const DeviceStatsCategory: FC<DeviceStatsCategoryProps> = ({
  isUnsyncModalOpen,
  setIsUnsyncModalOpen,
  pmUserId,
  sessionData,
}) => {
  const dispatch = useDispatch();
  const { selectedDeviceStateType, PlayerMakerSyncedData } = useSelector(
    (state: RootStore) => state?.deviceStats
  );
  const { userData } = useSelector((state: RootStore) => state?.auth);

  const deviceStatsCategoryStyle = useStyles(customDeviceStatsCategoryStyle);
  const isCoach =
    userRoleType.COACH === userData?.type ||
    userRoleType.HEAD_COACH === userData?.type;

  const { getInitialSessionData } = useGetSessionData();

  const setSelectedType = (key: string) => {
    dispatch({
      type: SET_DEVICE_STAT_TYPE,
      payload: key,
    });
  };

  const onPressCategory = (category: any) => {
    if (category.key === DEVICE_STATS_CATEGORY_LIST[1].key) {
      dispatch({
        type: SET_IS_SUMMARY_MODAL,
        payload: true,
      });
      dispatch({
        type: SET_SELECTED_EVENT_TYPE,
        payload: { data: deviceStatsEventType.TRAINING },
      });
    } else {
      PlayerMakerSyncedData &&
        getInitialSessionData(PlayerMakerSyncedData?.pmUserId);
    }
    setSelectedType(category.key);
  };

  const renderItem = ({ item }: any) => {
    const { title, key } = item;
    return (
      <View style={deviceStatsCategoryStyle.labelView1}>
        <TouchableOpacity onPress={() => onPressCategory(item)}>
          <View
            style={
              selectedDeviceStateType === item.key
                ? deviceStatsCategoryStyle.selectedWrapper
                : deviceStatsCategoryStyle.textwrapper
            }
          >
            <Text style={deviceStatsCategoryStyle.navText}>{title}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={deviceStatsCategoryStyle.mainContainer}>
      <View style={deviceStatsCategoryStyle.statsHeader}>
        <Text style={deviceStatsCategoryStyle.title}>PlayerMaker Stats</Text>
        {!isTabDevice() ? (
          <View style={deviceStatsCategoryStyle.lastSyncWrapper}>
            <View style={deviceStatsCategoryStyle.lastSyncData}>
              <Text style={deviceStatsCategoryStyle.userId}>{pmUserId}</Text>
              {sessionData?.[0]?.startDateTime ? (
                <>
                  <Text style={deviceStatsCategoryStyle.lastSyncText}>
                    Last synced date
                  </Text>
                  <Text style={deviceStatsCategoryStyle.lastSyncDate}>
                    {dateFormatConvert(sessionData?.[0]?.startDateTime)}
                  </Text>
                </>
              ) : null}
            </View>
            <TouchableOpacity
              onPress={() => isCoach && setIsUnsyncModalOpen(true)}
            >
              <MaterialIcons
                name="devices"
                size={isTabDevice() ? 35 : 25}
                color="#36d982"
                style={deviceStatsCategoryStyle.lastSyncIcon}
              />
            </TouchableOpacity>
          </View>
        ) : null}
      </View>

      <View style={deviceStatsCategoryStyle.rightContainer}>
        <FlatList
          data={DEVICE_STATS_CATEGORY_LIST}
          renderItem={renderItem}
          keyExtractor={item => item.key}
          numColumns={4}
          contentContainerStyle={deviceStatsCategoryStyle.list}
        />
        {isTabDevice() ? (
          <>
            <View style={deviceStatsCategoryStyle.lastSyncData}>
              <Text style={deviceStatsCategoryStyle.userId}>{pmUserId}</Text>
              {sessionData?.[0]?.startDateTime ? (
                <>
                  <Text style={deviceStatsCategoryStyle.lastSyncText}>
                    Last synced date
                  </Text>
                  <Text style={deviceStatsCategoryStyle.lastSyncDate}>
                    {dateFormatConvert(sessionData?.[0]?.startDateTime)}
                  </Text>
                </>
              ) : null}
            </View>
            <TouchableOpacity
              onPress={() => isCoach && setIsUnsyncModalOpen(true)}
            >
              <MaterialIcons
                name="devices"
                size={isTabDevice() ? 35 : 25}
                color="#36d982"
              />
            </TouchableOpacity>
          </>
        ) : null}
      </View>
    </View>
  );
};

export default DeviceStatsCategory;
