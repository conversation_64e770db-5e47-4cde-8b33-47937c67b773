import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

export default (colors: any) => ({
  container: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        paddingBottom: hp('2%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        paddingBottom: hp('1%'),
      },
  exportButton: isTabDevice()
    ? {
        backgroundColor: colors.blue,
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('0.5%'),
        flexDirection: 'row',
      }
    : {
        display: 'none',
      },
  exportText: {
    color: colors.white,
    fontFamily: 'Poppins-Medium',
    fontSize: wp('1%'),
    marginRight: wp('0.5%'),
  },
  text: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3%'),
      },
  selectedWrapper: isTabDevice()
    ? {
        backgroundColor: colors.green,
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('50%'),
      }
    : {
        backgroundColor: colors.green,
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('50%'),
      },
  unSelectedWrapper: isTabDevice()
    ? {
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
      }
    : {
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
      },
});
