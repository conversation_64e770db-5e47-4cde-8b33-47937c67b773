import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

export default (colors: any) => ({
  statsTableContainer: {
    display: 'flex',
    flexDirection: 'column',
  },
  tagContainer: {
    backgroundColor: colors.blue,
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
    display: 'flex',
    flexDirection: 'row',
  },
  tagLabel: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
