import React from 'react';
import { Text, View } from 'react-native';
import useStyles from '../../hooks/useStyles';

import customPlayerNoContentStyle from './NoContentMessageStyle';

const NoContentMessage = ({ message, customWrapperStyle }) => {
  const PlayerNoContentStyle = useStyles(customPlayerNoContentStyle);
  return (
    <View
      style={
        customWrapperStyle
          ? customWrapperStyle
          : PlayerNoContentStyle.noContentWrapper
      }
    >
      <Text style={PlayerNoContentStyle.noContentText}>{message}</Text>
    </View>
  );
};

export default NoContentMessage;
