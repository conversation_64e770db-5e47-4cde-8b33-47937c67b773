import { StyleSheet } from 'react-native';
import { isTabDevice } from '../../config/appConfig';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const PlayerNoContentStyle = colors => ({
  noContentText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontWeight: 'bold',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontWeight: 'bold',
      },
  noContentWrapper: isTabDevice()
    ? {
        borderRadius: wp('1%'),
        height: hp('60%'),
        width: wp('72%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        // flex: 1,
        justifyContent: 'center',
      }
    : {
        borderRadius: wp('1%'),
        height: hp('30%'),
        width: wp('90%'),
        marginLeft: wp('4%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        flex: 1,
        justifyContent: 'center',
      },
});
export default PlayerNoContentStyle;
