import React from 'react';
import { Text, TouchableOpacity, View, Image } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import customPlayerProfileStyle from './PlayerProfileStyle';
import { userRoleType } from '../../constants/constants';
import { getAge } from '../../helpers/DateHelper';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import useStyles from '../../hooks/useStyles';
import { convertDateObjectToJSDate } from '../../helpers/index';

import { stringLength } from '../../helpers/index';

export default function PlayerProfile({
  firstName,
  dateOfBirth,
  jerseyNumber,
  imgURL,
  lastName,
  teamID,
  profileID,
  userRole,
  isNotAvailable,
  sessionData,
  userId,
}) {
  const PlayerProfileStyle = useStyles(customPlayerProfileStyle);
  const navigation = useNavigation();

  const fullName = `${firstName || ''} ${lastName || ''}`.trim();

  const selectedUserSession = sessionData?.filter(
    user => user.userId === userId
  )[0];
  const isMinSession =
    selectedUserSession?.isMinSession || selectedUserSession?.isMinExpiryDate;

  const navigateHandler = () =>
    navigation.navigate('PlayerInfoScreen', {
      teamID,
      profileID,
      isNotAvailable,
    });

  const OnNavigateHandler = () => {
    if (userRole !== userRoleType.PLAYER && userRole !== userRoleType.PARENT) {
      navigateHandler();
    }
  };

  return (
    <TouchableOpacity onPress={OnNavigateHandler}>
      <View style={PlayerProfileStyle.container}>
        <View style={PlayerProfileStyle.playerWrapper}>
          {isMinSession ? (
            <Image
              style={PlayerProfileStyle.playerExpired}
              source={require('../../../assets/icons/session-expiry-icon.png')}
            />
          ) : null}
          <ProfileImage
            style={PlayerProfileStyle.img}
            imageStyles={PlayerProfileStyle.img}
            profileImageUrl={imgURL}
          />
        </View>

        <View style={PlayerProfileStyle.nameView}>
          <Text style={PlayerProfileStyle.firstName}>{fullName}</Text>
        </View>
        {dateOfBirth && (
          <Text style={PlayerProfileStyle.age}>
            {getAge(convertDateObjectToJSDate(dateOfBirth))} yrs
          </Text>
        )}
        {jerseyNumber != null && jerseyNumber != '' && (
          <Text style={PlayerProfileStyle.Jersey}>Jersey {jerseyNumber}</Text>
        )}
      </View>
    </TouchableOpacity>
  );
}
