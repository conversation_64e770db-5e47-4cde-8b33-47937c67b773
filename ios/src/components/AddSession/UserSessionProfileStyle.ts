import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const UserProfileTileStyle = (colors: any) => ({
  userTile: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('30%'),
        height: wp('15%'),
        marginBottom: wp('1%'),
        marginRight: wp('1%'),
        marginLeft: wp('1%'),
        padding: wp('1%'),
        justifyContent: 'space-between',
        position: 'relative',
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('5%'),
        width: wp('92%'),
        padding: wp('4%'),
        marginBottom: wp('2%'),
        justifyContent: 'space-between',
        position: 'relative',
      },
  imageLoader: {
    borderRadius: wp('1%'),
  },
  checkBox: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('50%'),
        width: wp('2%'),
        height: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('50%'),
        width: wp('6%'),
        height: wp('6%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  innerCheckBox: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('50%'),
        width: wp('1%'),
        height: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('50%'),
        width: wp('3%'),
        height: wp('3%'),
      },
  tileHeader: isTabDevice()
    ? {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('5%'),
      },
  tileNameWrapper: isTabDevice()
    ? {
        width: '80%',
      }
    : {
        width: '80%',
      },
  tileName: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  tileContentText: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  tileContentText2: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.green,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
        fontFamily: 'Poppins-Medium',
      },
  imgContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tileContentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: wp('1%'),
  },
  itemImage: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('50%'),
        marginRight: wp('1%'),
        resizeMode: 'cover',
      }
    : {
        width: wp('9%'),
        height: wp('9%'),
        borderRadius: wp('50%'),
        resizeMode: 'cover',
        marginRight: wp('4%'),
      },
  teamTileSelected: {
    backgroundColor: colors.aquaBlue,
    borderRadius: wp('1%'),
    width: wp('34%'),
    height: wp('10%'),
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('1%'),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tileContent: {},
});
export default UserProfileTileStyle;
