import * as DocumentPicker from 'expo-document-picker';
import React, { useEffect, useState } from 'react';
import {
  Appearance,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ActivityIndicator,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { dateTimeConversion } from '../../helpers/index';
import useStyles from '../../hooks/useStyles';
import { UPLOAD_REPORT_SUCCESS } from '../../store/actionTypes/UploadReport/uploadreportAction';
import customPlayerInfoUpdateFormStyle from './PlayerInfoUpdateFormStyle';
import { Ionicons } from '@expo/vector-icons';
import useFileUpload from '../../hooks/useFileUpload';
import useS3bucketLocation from '../../hooks/useS3bucketLocation';
import { useDispatch } from 'react-redux';
import { USER_MANAGEMENT_SERVICE } from '../../constants/services';

const PlayerInfoUpdateFormReport = ({
  selectedUpdateData,
  modalAction,
  action,
  playerUploadedReport,
  deleteReportAction,
  fileType,
  playerUploadedReportLoading,
}) => {
  const dispatch = useDispatch();
  const [upload, uploadedContent, , isUploading] = useFileUpload();
  const [getBucketLocation, bucketLocation] = useS3bucketLocation();

  const PlayerInfoUpdateFormStyle = useStyles(customPlayerInfoUpdateFormStyle);
  const { message, createdDate, _id } = selectedUpdateData;

  const [updateMessage, setUpdateMessage] = useState(message);

  const [updateDate, setUpdateDate] = useState(new Date(createdDate));
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: 'Maximum upload file size: 1mb',
  });

  const [fileObject, setFileObject] = useState(null);
  const [inputFileName, setInputFileName] = useState('');
  const [isReportUploading, setIsReportUploading] = useState(false);
  const [s3FilePath, setS3FilePath] = useState(null);

  useEffect(() => {
    getBucketLocation({
      path: fileType || '',
      service: USER_MANAGEMENT_SERVICE,
    });
  }, []);

  useEffect(() => {
    fileObject &&
      upload(
        fileObject,
        `${bucketLocation?.filePath}`,
        bucketLocation?.bucketName
      );
  }, [fileObject, bucketLocation]);

  useEffect(() => {
    if (uploadedContent && inputFileName && isReportUploading) {
      dispatch({
        type: UPLOAD_REPORT_SUCCESS,
        payload: {
          data: {
            fileName: inputFileName,
            ...uploadedContent,
          },
        },
      });
      setIsReportUploading(false);
    }
  }, [uploadedContent, inputFileName, isReportUploading]);

  useEffect(() => {
    if (bucketLocation?.filePath) {
      setS3FilePath(`/${bucketLocation.filePath}` + '/');
    }
  }, [bucketLocation]);

  const showMode = currentMode => {
    setShow(true);
    setMode(currentMode);
  };

  const showDatepicker = () => {
    showMode('date');
  };

  const onChange = selectedDate => {
    const currentDate = selectedDate || date;
    setShow(false);
    setUpdateDate(new Date(currentDate));
  };

  const changeFileName = selectedFileName => {
    const sentDate = new Date();
    const {
      monthNumberString,
      dateNumberString,
      hours24String,
      minutesString,
      year,
    } = dateTimeConversion(sentDate);

    const fileNameExtension = selectedFileName.split('.')[1];

    return `Koach_${year}_${monthNumberString}_${dateNumberString}_at_${hours24String}_${minutesString}.${fileNameExtension}`;
  };

  const pickDocument = async () => {
    try {
      const response = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        multiple: false,
        copyToCacheDirectory: true,
      });

      if (!response.canceled) {
        setIsFileError({
          status: false,
          error: '',
        });

        const result = response.assets[0];

        if (10 > Number(result.size / 1048576)) {
          setIsReportUploading(true);

          const selectedFileName = changeFileName(result.name);
          const picture = await fetch(result.uri);

          const pictureBlob = picture.blob ? await picture.blob() : picture;
          // const pictureBlob = await picture.blob();

          const file = new File([pictureBlob], `${selectedFileName}`);
          upload(file, s3FilePath, bucketLocation?.bucketName || '');

          setInputFileName(selectedFileName || '');
          setFileObject(file);
        } else {
          if (result.type !== 'cancel') {
            setIsFileError({
              status: true,
              error: 'Maximum upload file size: 1mb',
            });
          }
        }
      }
    } catch (error) {
      console.log(error);
      setIsReportUploading(false);
    }
  };

  const { year, month, date, dateString } = dateTimeConversion(updateDate);
  const [onClickSave, setOnClickSave] = useState(false);
  return (
    <>
      <View style={PlayerInfoUpdateFormStyle.container}>
        <KeyboardAwareScrollView
          contentContainerStyle={PlayerInfoUpdateFormStyle.scrollView}
        >
          <View style={PlayerInfoUpdateFormStyle.content}>
            <View style={PlayerInfoUpdateFormStyle.box}>
              <Text style={PlayerInfoUpdateFormStyle.dateLabel}>Date</Text>
              <View style={PlayerInfoUpdateFormStyle.selectedDateWrapper}>
                <TouchableOpacity onPress={() => showDatepicker()}>
                  <Text style={PlayerInfoUpdateFormStyle.selectedDate}>
                    {date}/{month}/{year}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={PlayerInfoUpdateFormStyle.box2}>
              <Text style={PlayerInfoUpdateFormStyle.dateLabel}>Message</Text>
              <View style={PlayerInfoUpdateFormStyle.selectedDateWrapper}>
                <TextInput
                  multiline={true}
                  numberOfLines={2}
                  style={PlayerInfoUpdateFormStyle.messageText}
                  onChangeText={text => setUpdateMessage(text)}
                  value={updateMessage}
                  placeholder={'Type Here'}
                  placeholderTextColor="#595959"
                  disableFullscreenUI={true}
                  returnKeyType={'go'}
                  blurOnSubmit={false}
                />

                {isError && (
                  <Text style={PlayerInfoUpdateFormStyle.error}>
                    Please enter your message
                  </Text>
                )}
              </View>
            </View>
            <View style={PlayerInfoUpdateFormStyle.reportContainer}>
              {isUploading ? (
                <ActivityIndicator color="green" />
              ) : (
                <>
                  <TouchableOpacity onPress={() => pickDocument()}>
                    <View style={PlayerInfoUpdateFormStyle.upload}>
                      <View
                        style={PlayerInfoUpdateFormStyle.uploadIconImageWrapper}
                      >
                        <Image
                          style={PlayerInfoUpdateFormStyle.iconImage}
                          source={require('../../../assets/icons/medical-upload-icon.png')}
                        />
                        <View style={{ display: 'flex', marginLeft: 10 }}>
                          <Text style={PlayerInfoUpdateFormStyle.btnText}>
                            Upload Report
                          </Text>
                          {isFileError.status && (
                            <Text style={PlayerInfoUpdateFormStyle.error}>
                              {isFileError.error}
                            </Text>
                          )}
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>

                  {playerUploadedReport?.length > 0 &&
                    playerUploadedReport.map(({ fileName }, index) => (
                      <View key={index}>
                        <View style={PlayerInfoUpdateFormStyle.uploadedWrapper}>
                          <View
                            style={
                              PlayerInfoUpdateFormStyle.uploadIconImageWrapper
                            }
                          >
                            <TouchableOpacity
                              onPress={() => deleteReportAction(index)}
                              style={PlayerInfoUpdateFormStyle.close}
                            >
                              <Ionicons
                                name="close"
                                color="white"
                                style={
                                  PlayerInfoUpdateFormStyle.uploadWrapperClose
                                }
                              />
                            </TouchableOpacity>
                            <Image
                              style={PlayerInfoUpdateFormStyle.uploadIconImage}
                              source={require('../../../assets/icons/medical-icon.png')}
                            />
                            <Text
                              style={PlayerInfoUpdateFormStyle.uploadIconText}
                            >
                              {fileName || 'Report'}
                            </Text>
                          </View>
                        </View>
                      </View>
                    ))}
                </>
              )}
            </View>
            <View style={PlayerInfoUpdateFormStyle.btn_wrapper}>
              <View style={PlayerInfoUpdateFormStyle.btns}>
                <TouchableWithoutFeedback
                  onPress={() => {
                    if (updateMessage == '') {
                      setIsError(true);
                    } else {
                      setOnClickSave(true);
                      action({
                        message: updateMessage,
                        createdDate: new Date(updateDate).toISOString(),
                        updateId: _id,
                        fileUploads: playerUploadedReport.length
                          ? playerUploadedReport
                          : null,
                      });
                      setIsError(false);
                      modalAction(false);
                    }
                  }}
                >
                  <View style={PlayerInfoUpdateFormStyle.btn}>
                    {onClickSave || isReportUploading ? (
                      <ActivityIndicator color="#FFF" />
                    ) : (
                      <Text style={PlayerInfoUpdateFormStyle.btnText}>
                        Save
                      </Text>
                    )}
                  </View>
                </TouchableWithoutFeedback>
                <TouchableWithoutFeedback onPress={() => modalAction(false)}>
                  <View style={PlayerInfoUpdateFormStyle.btn2}>
                    <Text style={PlayerInfoUpdateFormStyle.btnText}>
                      Cancel
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {show && (
          <View style={PlayerInfoUpdateFormStyle.datePickerWrapper}>
            <DateTimePickerModal
              isVisible
              mode={mode}
              date={updateDate}
              // style={addUserStyle.datePicker}
              onConfirm={onChange}
              is24Hour={true}
              display="spinner"
              onCancel={() => setShow(false)}
              modalStyleIOS={PlayerInfoUpdateFormStyle.datePickerSelector}
              pickerContainerStyleIOS={
                PlayerInfoUpdateFormStyle.datePickerWrapper
              }
              maximumDate={new Date()}
              isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
            />
          </View>
        )}
      </View>
    </>
  );
};

export default PlayerInfoUpdateFormReport;
