import React, { useState } from 'react';
import {
  Text,
  TouchableWithoutFeedback,
  TouchableOpacity,
  View,
  TextInput,
  Appearance,
  KeyboardAvoidingView,
} from 'react-native';

import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { dateTimeConversion } from '../../helpers/index';
import customPlayerInfoUpdateFormStyle from './PlayerInfoUpdateFormStyle';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import useStyles from '../../hooks/useStyles';

const PlayerInfoUpdateForm = ({ selectedUpdateData, modalAction, action }) => {
  const PlayerInfoUpdateFormStyle = useStyles(customPlayerInfoUpdateFormStyle);
  const { message, createdDate, _id } = selectedUpdateData;

  const [updateMessage, setUpdateMessage] = useState(message);

  const [updateDate, setUpdateDate] = useState(new Date(createdDate));
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);
  const [isError, setIsError] = useState(false);

  const showMode = currentMode => {
    setShow(true);
    setMode(currentMode);
  };

  const showDatepicker = () => {
    showMode('date');
  };

  const onChange = selectedDate => {
    const currentDate = selectedDate || date;
    setShow(false);
    setUpdateDate(new Date(currentDate));
  };
  const { year, month, date, dateString } = dateTimeConversion(updateDate);
  return (
    <>
      <View style={PlayerInfoUpdateFormStyle.container}>
        <KeyboardAwareScrollView
          contentContainerStyle={PlayerInfoUpdateFormStyle.scrollView}
        >
          <View style={PlayerInfoUpdateFormStyle.content}>
            <>
              <KeyboardAvoidingView
                behavior="position"
                keyboardVerticalOffset={50}
              >
                <View style={PlayerInfoUpdateFormStyle.box}>
                  <Text style={PlayerInfoUpdateFormStyle.dateLabel}>Date</Text>
                  <View style={PlayerInfoUpdateFormStyle.selectedDateWrapper}>
                    <TouchableOpacity onPress={() => showDatepicker()}>
                      <Text style={PlayerInfoUpdateFormStyle.selectedDate}>
                        {date}/{month}/{year}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={PlayerInfoUpdateFormStyle.box2}>
                  <Text style={PlayerInfoUpdateFormStyle.dateLabel}>
                    Message
                  </Text>
                  <View style={PlayerInfoUpdateFormStyle.selectedDateWrapper}>
                    <TextInput
                      multiline={true}
                      numberOfLines={2}
                      style={PlayerInfoUpdateFormStyle.messageText}
                      onChangeText={text => setUpdateMessage(text)}
                      value={updateMessage}
                      placeholder={'Type Here'}
                      placeholderTextColor="#595959"
                      disableFullscreenUI={true}
                      returnKeyType={'go'}
                      blurOnSubmit={false}
                    />

                    {isError && (
                      <Text style={PlayerInfoUpdateFormStyle.error}>
                        Please enter your message
                      </Text>
                    )}
                  </View>
                </View>
              </KeyboardAvoidingView>
            </>

            <View style={PlayerInfoUpdateFormStyle.btn_wrapper}>
              <View style={PlayerInfoUpdateFormStyle.btns}>
                <TouchableWithoutFeedback
                  onPress={() => {
                    if (updateMessage == '') {
                      setIsError(true);
                    } else {
                      action({
                        message: updateMessage,
                        createdDate: new Date(updateDate).toISOString(),
                        updateId: _id,
                      });
                      setIsError(false);
                      modalAction(false);
                    }
                  }}
                >
                  <View style={PlayerInfoUpdateFormStyle.btn}>
                    <Text style={PlayerInfoUpdateFormStyle.btnText}>Save</Text>
                  </View>
                </TouchableWithoutFeedback>
                <TouchableWithoutFeedback onPress={() => modalAction(false)}>
                  <View style={PlayerInfoUpdateFormStyle.btn2}>
                    <Text style={PlayerInfoUpdateFormStyle.btnText}>
                      Cancel
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </View>
          </View>
        </KeyboardAwareScrollView>

        {show && (
          <View style={PlayerInfoUpdateFormStyle.datePickerWrapper}>
            <DateTimePickerModal
              isVisible
              mode={mode}
              date={updateDate}
              // style={addUserStyle.datePicker}
              onConfirm={onChange}
              is24Hour={true}
              display="spinner"
              onCancel={() => setShow(false)}
              modalStyleIOS={PlayerInfoUpdateFormStyle.datePickerSelector}
              pickerContainerStyleIOS={
                PlayerInfoUpdateFormStyle.datePickerWrapper
              }
              maximumDate={new Date()}
              isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
            />
          </View>
        )}
      </View>
    </>
  );
};

export default PlayerInfoUpdateForm;
