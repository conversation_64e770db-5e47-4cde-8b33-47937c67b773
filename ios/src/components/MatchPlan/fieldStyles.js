import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const FieldStyles = colors => ({
  fieldContainer: {
    width: '100%',
    height: '100%',
  },
  fieldWrapper: {
    width: '100%',
    height: '100%',
  },
  ground: {
    width: '100%',
    height: '100%',
    // flex: 1,
    resizeMode: 'stretch',
  },
  playerImgWrapper: isTabDevice()
    ? {
        width: wp('5%'),
        height: wp('5%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'white',
        borderWidth: 3,
        position: 'relative',
      }
    : {
        width: wp('8.5%'),
        height: wp('8.5%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'white',
        borderWidth: 3,
        position: 'relative',
      },
  playerImgWrapperGK: isTabDevice()
    ? {
        width: wp('5%'),
        height: wp('5%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'orange',
        borderWidth: 3,
      }
    : {
        width: wp('8.5%'),
        height: wp('8.5%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'orange',
        borderWidth: 3,
      },
  matchLogPlayerImgWrapper: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'white',
        borderWidth: 3,
        position: 'relative',
      }
    : {
        width: wp('8%'),
        height: wp('8%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'white',
        borderWidth: 3,
        position: 'relative',
      },
  matchLogPlayerImgWrapperGK: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'orange',
        borderWidth: 3,
        position: 'relative',
      }
    : {
        width: wp('8%'),
        height: wp('8%'),
        borderRadius: wp('100%'),
        backgroundColor: 'gray',
        borderColor: 'orange',
        borderWidth: 3,
        position: 'relative',
      },
  playerImg: {
    width: '100%',
    height: '100%',
    borderRadius: wp('100%'),
  },
  dropdownContainer: isTabDevice()
    ? {
        height: hp('6%'),
      }
    : { height: wp('12%') },
  imageBackgound: {
    borderRadius: 100,
  },
  imageStyles: isTabDevice()
    ? {
        width: wp('3.5%'),
        height: wp('3.5%'),
        borderRadius: 100,
        resizeMode: 'cover',
        marginRight: wp('0.3%'),
      }
    : {
        width: wp('8%'),
        height: wp('8%'),
        borderRadius: 100,
        resizeMode: 'cover',
        marginRight: wp('2%'),
      },
  availabilityIcon: isTabDevice()
    ? {
        position: 'absolute',
        zIndex: 1,
        top: 0,
        right: 0,
        width: wp('2%'),
        height: wp('2%'),
      }
    : {
        position: 'absolute',
        zIndex: 1,
        top: -3,
        right: -3,
        width: wp('4%'),
        height: wp('4%'),
      },
  availabilityIconMatchLog: isTabDevice()
    ? {
        position: 'absolute',
        zIndex: 1,
        top: -3,
        right: -3,
        width: wp('1.3%'),
        height: wp('1.3%'),
      }
    : {
        position: 'absolute',
        zIndex: 1,
        top: -3,
        right: -3,
        width: wp('4%'),
        height: wp('4%'),
      },

  // Dropdown styles for the Match Plan Substitues
  dropdownView: isTabDevice()
    ? {
        width: '100%',
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        // height: wp('5%'),
        // marginTop: wp('-1%')
      }
    : {
        width: '96%',
        borderRadius: wp('2.5%'),
        backgroundColor: colors.borderBlue,
      },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  dropdownSelectedContainer: isTabDevice()
    ? {
        borderColor: colors.borderBlue,
        height: hp('5%'),
        width: '100%',
      }
    : {
        height: wp('10%'),
        borderColor: colors.borderBlue,
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        // marginTop: wp('-0.5%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,

        elevation: 10,
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        marginTop: wp('-1.5%'),
        borderBottomLeftRadius: wp('3.5%'),
        borderBottomRightRadius: wp('3.5%'),
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.34,
        shadowRadius: 6.27,

        elevation: 10,
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.4%'),
        width: '60%',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
      },
  jerseyCircle: isTabDevice()
    ? {
        width: '60%',
        height: '60%',
        borderRadius: wp('100%'),
        backgroundColor: colors.white,
        borderColor: colors.white,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: -10,
        bottom: -5,
        zIndex: 10,
      }
    : {
        width: '60%',
        height: '60%',
        borderRadius: wp('100%'),
        backgroundColor: colors.white,
        borderColor: colors.white,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: -10,
        bottom: -5,
        zIndex: 10,
      },
  jerseyCircleText: isTabDevice()
    ? {
        color: colors.black,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Thin',
      }
    : {
        color: colors.black,
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Thin',
      },
  jerseyCircleMatchText: isTabDevice()
    ? {
        color: colors.black,
        fontSize: wp('0.9%'),
        fontFamily: 'Poppins-Thin',
      }
    : {
        color: colors.black,
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Thin',
      },

  dropdownTopArea: {
    backgroundColor: colors.transparent,
    borderColor: colors.transparent,
    width: '100%',
    position: 'absolute',
    zIndex: 5,
  },
  toolTipPlayers: {
    backgroundColor: colors.borderBlue,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    padding: 5,
    borderRadius: 5,
    zIndex: 10,
  },

  toolTipPlayersText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('0.9%'),
        fontFamily: 'Poppins-Thin',
      }
    : {
        color: colors.white,
        fontSize: wp('2.0%'),
        fontFamily: 'Poppins-Thin',
      },
});

export default FieldStyles;
