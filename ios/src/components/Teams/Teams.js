import React, { useCallback, useEffect, useState, useMemo } from 'react';
import {
  Text,
  View,
  Alert,
  Modal,
  ScrollView,
  TouchableHighlight,
  ActivityIndicator,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import PlayerProfile from '../PlayerProfile/PlayerProfile';
import { Ionicons, AntDesign } from '@expo/vector-icons';
import customTeamsStyle from './TeamsStyle';
import useApi from '../../hooks/useApi';
import { useFocusEffect } from '@react-navigation/native';
import {
  PLAYER_REQUEST,
  PLAYER_SUCCESS,
  PLAYER_FAIL,
  RESET_PLAYER,
  SET_CURRENT_FILTERING_TEAM,
} from '../../store/actionTypes/player/playerAction';
import { useDispatch, useSelector } from 'react-redux';
import { FOOTBALL_SERVICE } from '../../constants/services';
import {
  TEAM_REMOVE_PLAYER_FAILED,
  TEAM_REMOVE_PLAYER_REQUEST,
  TEAM_REMOVE_PLAYER_RESET,
  TEAM_REMOVE_PLAYER_SUCCESS,
  GET_CHILD_TEAM_DATA_REQUEST,
  GET_CHILD_TEAM_DATA_SUCCESS,
  GET_CHILD_TEAM_DATA_FAILED,
} from '../../store/actionTypes/Team/TeamAction';
import { SET_SELECTED_CHILD } from '../../store/actionTypes/common/commonActionTypes';
import ActivitySpinner from '../ActivitySpinner/ActivitySpinner';
import NoContentMessage from '../NoContents/NoContentMessage';
import useStyles from '../../hooks/useStyles';
import useColors from '../../hooks/useColors';
import { userRoleType } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';
import useDebounce from '../../hooks/useDebounce';
import { useSessionBulkHook } from '../../hooks/SessionAPIHook/useSessionBulkHook';
import { FlatList } from 'react-native-gesture-handler';
import useApiPromise from '../../hooks/useApiPromise';

export default function Teams({
  navigation,
  isEditMode,
  userRole,
  selectedChild,
}) {
  const TeamsStyle = useStyles(customTeamsStyle);
  const colors = useColors();
  const [isModalVisble, setIsModalVisible] = useState(false);
  const [key, setKey] = useState(null);
  const [filterPlayerData, setFilterPlayerData] = useState([]);

  const dispatch = useDispatch();
  const [fetchData] = useApiPromise();
  const [fetchChildTeam] = useApi();
  const [searchBarText, setSearchBarText] = useState(undefined);
  const debouncedSearchText = useDebounce(searchBarText, 200);
  const [flatListData, setFlatListData] = useState(null);

  const {
    playerDataLoading,
    playerData,
    playerDataTotalRecord,
    searchedPlayerTotalRecord,
    playerDataPage,
    isPlayerAdded,
  } = useSelector(state => state?.player);
  const { userData } = useSelector(state => state?.auth);

  const isCoach =
    userRoleType.HEAD_COACH === userData?.type ||
    userRoleType.COACH === userData?.type;

  const { children } = useSelector(state => state?.common);
  const {
    playerRemoveLoading,
    playerRemoveSuccess,
    playerRemoveFailed,
    playerRemoveFailedErrorMessage,
  } = useSelector(state => state?.teamID);
  const { teamDataLoading, teamIdData, teamData } = useSelector(
    state => state?.team
  );

  const [selectedTeam, setSelectedTeam] = useState({});
  const [sessionSummary, setsessionSummary] = useState([]);

  const { getUserSessionList } = useSessionBulkHook();

  const formattedUserData = useMemo(() => {
    return sessionSummary.map(user => {
      const today = new Date();
      const expireDay = new Date(user.expiryDate);
      const diffInMs = expireDay.getTime() - today.getTime();
      const diffInDays = diffInMs / (1000 * 60 * 60 * 24);
      const roundedDiffInDays = Math.round(diffInDays);

      return {
        ...user,
        isMinSession:
          user.numberOfSessionsAvailable !== null
            ? user.numberOfSessionsAvailable < 2
              ? true
              : false
            : false,
        isMinExpiryDate:
          user.expiryDate !== null
            ? roundedDiffInDays < 7
              ? true
              : false
            : false,
      };
    });
  }, [JSON.stringify(sessionSummary)]);

  const searchPlayerCount =
    debouncedSearchText &&
    (searchedPlayerTotalRecord === 0
      ? ''
      : searchedPlayerTotalRecord == 1
      ? `${searchedPlayerTotalRecord} Player`
      : `${searchedPlayerTotalRecord} Players`);

  const handleSearch = () => {
    searchBarText && setSearchBarText(undefined);
  };

  useEffect(() => {
    getPlayerData(1, debouncedSearchText || '');
  }, [debouncedSearchText]);

  useEffect(() => {
    if (teamData?.data?.length) {
      setSelectedTeam(
        teamData?.data?.find(team => teamIdData === team._id) || null
      );
    }
  }, [teamIdData, teamData]);

  const isParent = userRole === userRoleType.PARENT;
  const isPlayer = userRole === userRoleType.PLAYER;

  const fetchPlayers = () => {
    setSearchBarText(undefined);
    dispatch({
      type: RESET_PLAYER,
      payload: null,
    });
    dispatch({
      type: SET_CURRENT_FILTERING_TEAM,
      payload: teamIdData,
    });
    getPlayerData(1);
  };

  const getPlayerData = async (page, searchVal) => {
    if (teamIdData) {
      let requestedUrl = `/api/v1/teams/${teamIdData}/players?page=${page}&size=20`;
      let hasSearch = false;
      if (searchVal) {
        requestedUrl = `${requestedUrl}&search=${searchVal}`;
        hasSearch = true;
      }

      const value = await fetchData(
        requestedUrl,
        PLAYER_REQUEST,
        PLAYER_SUCCESS,
        PLAYER_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE,
        {
          filterByTeamId: teamIdData,
          pageNo: page,
          hasSearch: hasSearch,
        }
      );

      const userListIds =
        (value?.data?.data && value?.data?.data.map(player => player.userId)) ||
        [];

      if (userRoleType.HEAD_COACH === userData?.type) {
        const userSessionData = await getUserSessionList(userListIds);
        let tmpSessionSummary = sessionSummary;
        if (page === 1) {
          tmpSessionSummary = [];
        }
        setsessionSummary([...tmpSessionSummary, ...userSessionData]);
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      teamIdData && fetchPlayers();
    }, [teamIdData])
  );

  useEffect(() => {
    return () =>
      dispatch({
        type: RESET_PLAYER,
      });
  }, []);

  useEffect(() => {
    isPlayerAdded && fetchPlayers();
  }, [isPlayerAdded]);

  useEffect(() => {
    if (playerRemoveSuccess && teamIdData) {
      setIsModalVisible(false);
      fetchPlayers();
      resetPlayerDeleteData();
    }
  }, [playerRemoveSuccess, teamIdData]);

  const resetPlayerDeleteData = () => {
    dispatch({
      type: TEAM_REMOVE_PLAYER_RESET,
    });
  };

  const removePlayerFromTeam = playerId => {
    const deleteRequestBody = {
      sportsProfileId: playerId,
    };
    fetchData(
      `/api/v1/teams/${teamIdData}/team-member-profile`,
      TEAM_REMOVE_PLAYER_REQUEST,
      TEAM_REMOVE_PLAYER_SUCCESS,
      TEAM_REMOVE_PLAYER_FAILED,
      deleteRequestBody,
      null,
      'DELETE',
      false,
      FOOTBALL_SERVICE
    );
  };

  const getChildTeamData = userId => {
    fetchChildTeam(
      `/api/v1/teams?page=1&size=15&playerIds=${userId}`,
      GET_CHILD_TEAM_DATA_REQUEST,
      GET_CHILD_TEAM_DATA_SUCCESS,
      GET_CHILD_TEAM_DATA_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    if (isParent && selectedChild) {
      getChildTeamData(selectedChild?.id);
    }
  }, [selectedChild]);

  const removeProfile = id => {
    removePlayerFromTeam(id);
  };

  const removeModalVisible = id => {
    setIsModalVisible(true);
    setKey(id);
  };

  const setSelectedChild = payload => {
    dispatch({
      type: SET_SELECTED_CHILD,
      payload,
    });
  };

  useEffect(() => {
    return () => {
      setSelectedChild(children[0]);
    };
  }, []);

  const isCloseToBottom = ({
    layoutMeasurement,
    contentOffset,
    contentSize,
  }) => {
    const paddingToBottom = 20;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const loadNextPage = () => {
    if (debouncedSearchText) {
      if (searchedPlayerTotalRecord > playerData?.length || 0) {
        getPlayerData(playerDataPage + 1, debouncedSearchText);
      }
    } else {
      if (playerDataTotalRecord > playerData?.length || 0) {
        getPlayerData(playerDataPage + 1);
      }
    }
  };
  useEffect(() => {
    setFilterPlayerData(
      playerData?.filter(
        player =>
          player.userId !== userData?.id && selectedChild?.id !== player.userId
      ) || []
    );
  }, [JSON.stringify(playerData), selectedChild]);

  const renderPlayerDetails = items => {
    const { item, index } = items;
    return (
      <View key={index} style={TeamsStyle.pictureWrapper}>
        <View
          style={
            userData?.id !== item?.userId && selectedChild?.id !== item.userId
              ? TeamsStyle.iconView
              : ''
          }
        >
          <>
            <PlayerProfile
              userId={item?.userId}
              teamID={teamIdData}
              profileID={item.sportsProfileId}
              key={item}
              firstName={item.firstName}
              lastName={item.lastName}
              dateOfBirth={item.dateOfBirth}
              jerseyNumber={item.jersyNo}
              imgURL={item.profileImageUrl}
              navigation={navigation}
              userRole={userRole}
              isNotAvailable={item?.isAvailable === false}
              sessionData={formattedUserData}
            />

            {isEditMode && (
              <View style={TeamsStyle.removeIconWrapper}>
                <Ionicons
                  style={TeamsStyle.removeIcon}
                  name="close-circle"
                  color="red"
                  onPress={() => removeModalVisible(item.sportsProfileId)}
                />
              </View>
            )}
            {item?.isAvailable === false && !isEditMode && (
              <View style={TeamsStyle.removeIconWrapper}>
                <AntDesign
                  style={TeamsStyle.exclamationIcon}
                  name="exclamationcircle"
                  color="red"
                />
              </View>
            )}
          </>
        </View>
      </View>
    );
  };

  const renderSearchElement = () => (
    <View style={TeamsStyle.searchBarContainer}>
      <TextInput
        style={TeamsStyle.searchBarInput}
        placeholder="Search User"
        placeholderTextColor="#FFFF"
        onChangeText={value => {
          setSearchBarText(value);
        }}
        value={searchBarText}
      />
      <View style={TeamsStyle.searchBarIcon}>
        <View>
          <Text style={TeamsStyle.searchResultCounter}>
            {searchPlayerCount}
          </Text>
        </View>

        <TouchableOpacity onPress={handleSearch}>
          <AntDesign
            name={!searchBarText ? 'search1' : 'close'}
            color="white"
            size={isTabDevice() ? 16 : 20}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={TeamsStyle.wrapper}>
      {key !== null && (
        <View>
          {isModalVisble && (
            <Modal animationType="slide" transparent visible>
              <View style={TeamsStyle.centeredView}>
                <View style={TeamsStyle.overlay}></View>
                <View style={TeamsStyle.modalView}>
                  <Text style={TeamsStyle.modalText}>
                    Are you sure you want to remove this player?
                  </Text>
                  {playerRemoveFailed ? (
                    <Text
                      style={[
                        TeamsStyle.modalText,
                        TeamsStyle.modalSubText,
                        TeamsStyle.errorText,
                      ]}
                    >
                      {playerRemoveFailedErrorMessage}
                    </Text>
                  ) : null}
                  <View style={TeamsStyle.btnView}>
                    {playerRemoveLoading ? (
                      <View
                        style={{
                          ...TeamsStyle.submitButton,
                        }}
                      >
                        <ActivityIndicator color={colors.white} />
                      </View>
                    ) : (
                      <TouchableHighlight
                        style={{ ...TeamsStyle.submitButton }}
                        onPress={() => {
                          removeProfile(key);
                        }}
                      >
                        <Text style={TeamsStyle.textStyle}>Yes</Text>
                      </TouchableHighlight>
                    )}

                    <TouchableHighlight
                      style={{ ...TeamsStyle.cancelButton }}
                      onPress={() => {
                        setIsModalVisible(!isModalVisble);
                        resetPlayerDeleteData();
                      }}
                    >
                      <Text style={TeamsStyle.textStyle}>No</Text>
                    </TouchableHighlight>
                  </View>
                </View>
              </View>
            </Modal>
          )}
        </View>
      )}

      {(playerRemoveLoading || teamDataLoading || playerDataLoading) && (
        <ActivitySpinner />
      )}

      <View style={TeamsStyle.profileView}>
        <View style={TeamsStyle.header}>
          <View>
            <Text style={TeamsStyle.teamInfoName}>
              {selectedTeam?.teamName || ''}
            </Text>
            <Text style={TeamsStyle.teamInfoCount}>
              {playerDataTotalRecord || 0} Players
            </Text>
          </View>
          {isCoach && renderSearchElement()}
        </View>
        <ScrollView
          style={TeamsStyle.scrollView}
          contentContainerStyle={TeamsStyle.contentContainer}
          onScroll={({ nativeEvent }) => {
            if (isCloseToBottom(nativeEvent) && !playerDataLoading) {
              loadNextPage();
            }
          }}
          scrollEventThrottle={400}
        >
          {!playerRemoveLoading &&
            !teamDataLoading &&
            !playerDataLoading &&
            !playerData && <NoContentMessage message="No Players" />}
          {teamDataLoading ? null : isParent && !teamData?.data?.length ? (
            <NoContentMessage message="Player is not assigned to a team" />
          ) : filterPlayerData.length ? (
            <FlatList
              keyExtractor={item => item.emailId}
              data={filterPlayerData}
              renderItem={renderPlayerDetails}
              initialNumToRender={50}
              numColumns={
                isTabDevice()
                  ? isParent || isPlayer
                    ? 4
                    : 7
                  : isParent || isPlayer
                  ? 2
                  : 3
              }
              key={
                isTabDevice()
                  ? isParent || isPlayer
                    ? '#1'
                    : '#2'
                  : isParent || isPlayer
                  ? '#3'
                  : '#4'
              }
            />
          ) : null}
        </ScrollView>
      </View>
    </View>
  );
}
