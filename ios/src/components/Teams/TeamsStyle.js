import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TeamsStyle = colors => ({
  pictureWrapper: isTabDevice()
    ? { marginRight: 0 }
    : {
        marginRight: wp('8%'),
      },
  wrapper: isTabDevice()
    ? {}
    : {
        height: hp('70%'),
        paddingBottom: hp('10%'),
      },
  container: {
    marginTop: '10%',
    backgroundColor: colors.white,
    flexDirection: 'row',
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        height: hp('100%'),
      }
    : {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        height: hp('100%'),
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.borderBlue,
        padding: wp('4%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('70%'),
      },
  scrollView: isTabDevice()
    ? {
        height: hp('60%'),
      }
    : {
        width: wp('92%'),
      },
  contentContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: '100%',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: '100%',
        justifyContent: 'space-between',
      },
  submitButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        width: wp('8%'),
        height: wp('4.5%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: hp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        width: wp('15%'),
        height: wp('8%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: hp('2%'),
      },
  cancelButton: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        width: wp('8%'),
        height: wp('4.5%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.darkBlue,
        width: wp('15%'),
        height: wp('8%'),
        borderRadius: wp('2%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  textStyle: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        textAlign: 'center',
        fontSize: wp('1.4%'),
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Regular',
        textAlign: 'center',
        fontSize: wp('4%'),
      },
  modalText: isTabDevice()
    ? {
        marginBottom: hp('4%'),
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
        width: wp('30%'),
      }
    : {
        marginBottom: hp('4%'),
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  modalSubText: {
    fontSize: wp('1.3%'),
  },
  errorText: {
    color: colors.red,
  },
  profileView: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        paddingLeft: wp('2%'),
        height: '100%',
        marginBottom: wp('20%'),
      }
    : {
        flexDirection: 'column',
        flexWrap: 'wrap',
        paddingTop: 0,
        paddingLeft: wp('3.5%'),
        height: '100%',
      },
  profileTouchableOpacity: {
    alignItems: 'flex-end',
    alignContent: 'flex-end',
    paddingHorizontal: 10,
  },
  profileImg: {
    alignItems: 'flex-end',
    alignContent: 'flex-end',
  },
  btnView: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
  },
  btn: {},
  removeIconWrapper: isTabDevice()
    ? {
        backgroundColor: colors.white,
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        position: 'absolute',
        top: 1,
        right: '10%',
        flexDirection: 'row',
        justifyContent: 'center',
        alignSelf: 'center',
      }
    : {
        backgroundColor: colors.white,
        width: wp('7%'),
        height: wp('7%'),
        borderRadius: wp('100%'),
        position: 'absolute',
        top: 1,
        right: '1%',
        flexDirection: 'row',
        justifyContent: 'center',
        alignSelf: 'center',
      },
  removeIcon: isTabDevice()
    ? {
        fontSize: wp('3%'),
        marginTop: wp('-0.1%'),
        marginRight: wp('-0.1'),
      }
    : {
        fontSize: wp('7%'),
      },
  exclamationIcon: isTabDevice()
    ? {
        fontSize: wp('2.5%'),
        marginTop: wp('0.23%'),
      }
    : {
        fontSize: wp('5.5%'),
        marginTop: wp('0.65%'),
      },
  iconView: isTabDevice()
    ? {
        flexDirection: 'row',
        width: '100%',
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('25%'),
      },
  wrapperView: isTabDevice()
    ? {
        flex: 1,
        flexDirection: 'row',
      }
    : {
        flex: 1,
        flexDirection: 'row',
      },
  glanceView: isTabDevice()
    ? {
        flex: 1,
        maxWidth: wp('35%'),
      }
    : {
        flex: 1,
        backgroundColor: colors.red,
        maxWidth: wp('30%'),
      },
  TeamsViewPlayer: isTabDevice()
    ? {
        flex: 2,
      }
    : {
        flex: 2,
      },
  TeamsViewCoach: {
    flex: 1,
  },
  teamInfoWrapper: {
    width: '100%',
    marginBottom: 20,
  },
  teamInfoName: isTabDevice()
    ? {
        fontSize: wp('2%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      },
  teamInfoCount: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.green,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
        fontFamily: 'Poppins-Medium',
      },
  header: isTabDevice()
    ? {
        width: wp('98%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginRight: wp('2%'),
        marginBottom: wp('2%'),
      }
    : {
        marginBottom: wp('4%'),
      },
  searchBarContainer: isTabDevice()
    ? {
        marginRight: hp('5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: wp('30%'),
        borderRadius: wp('1%'),
        backgroundColor: colors.semiDarkBlue,
        height: hp('6%'),
        paddingRight: hp('2%'),
        paddingLeft: hp('2%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('1%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: wp('2%'),
        backgroundColor: colors.semiDarkBlue,
        marginTop: wp('4%'),
        height: hp('6%'),
        paddingRight: hp('2%'),
        paddingLeft: hp('2%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('1%'),
        width: wp('92%'),
      },
  searchBarInput: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        width: '68%',
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        width: '90%',
        color: colors.white,
      },
  searchResultCounter: isTabDevice()
    ? {
        color: colors.green,
        marginLeft: hp('-8%'),
        marginRight: hp('1%'),
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
        marginLeft: hp('-8%'),
        marginRight: hp('1.0%'),

        fontFamily: 'Poppins-Medium',
      },
  searchBarIcon: isTabDevice()
    ? {
        marginLeft: hp('8%'),
        flexDirection: 'row',
        alignItems: 'center',
      }
    : {
        marginLeft: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
      },
});
export default TeamsStyle;
