import React, { useState } from 'react';
import { Text, TouchableOpacity, View, ScrollView } from 'react-native';
import ProfileGlance from '../ProfileGlance/ProfileGlance';
import Teams from './Teams';
import { userRoleType } from '../../constants/constants';
import customTeamsStyle from './TeamsStyle';
import useStyles from '../../hooks/useStyles';

export default function TeamWrapper({
  navigation,
  isEditMode,
  userRole,
  userData,
  teamIdData,
  selectedChild,
}) {
  const TeamsStyle = useStyles(customTeamsStyle);
  return (
    <View style={TeamsStyle.wrapperView}>
      {(userRole === userRoleType.PLAYER ||
        userRole === userRoleType.PARENT) && (
        <View style={TeamsStyle.glanceView}>
          <ProfileGlance
            navigation={navigation}
            userRole={userRole}
            selectedChild={selectedChild}
          />
        </View>
      )}
      <View
        style={
          userRole == userRoleType.PLAYER
            ? TeamsStyle.TeamsViewPlayer
            : TeamsStyle.TeamsViewCoach
        }
      >
        <Teams
          navigation={navigation}
          isEditMode={isEditMode}
          userRole={userRole}
          selectedChild={selectedChild}
        />
      </View>
    </View>
  );
}
