import Constants from 'expo-constants';
import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import useColors from '../../hooks/useColors';
import { isTabDevice } from '../../config/appConfig';

const AppVersion = ({ className }) => {
  const colors = useColors();
  const styles = StyleSheet.create({
    versionWrapper: {},
    version: isTabDevice()
      ? {
          color: colors.green,
          fontSize: wp('1%'),
        }
      : {
          color: colors.green,
          fontSize: hp('1.2%'),
          marginTop: wp('3%'),
        },
  });

  return (
    <View style={styles.versionWrapper}>
      <Text style={styles.version}>
        {' '}
        Version {Constants.expoConfig.version}
      </Text>
    </View>
  );
};
export default AppVersion;
