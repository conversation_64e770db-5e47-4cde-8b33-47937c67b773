import { StyleSheet, Text, View, Dimensions, Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TilesStyles = colors => ({
  TouchableOpacity: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: hp('20%'),
        width: hp('22%'),
        borderRadius: hp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
        padding: hp('1%'),
        position: 'relative',
        flexDirection: 'row',
      }
    : {
        backgroundColor: colors.tileBackground,
        height: wp('43%'),
        width: wp('43%'),
        borderRadius: wp('5%'),
        alignItems: 'center',
        justifyContent: 'center',
        margin: hp('1%'),
        padding: Platform.OS === 'android' ? wp('3%') : wp('0.3%'),
      },
  TouchablePressed: {
    backgroundColor: colors.green,
    height: hp('15%'),
    width: hp('15%'),
    borderRadius: hp('1%'),
    alignItems: 'center',
    justifyContent: 'center',
    margin: hp('1%'),
  },
  Text: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('2.5%'),
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
        flexShrink: 1,
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        textAlign: 'center',
        fontFamily: 'Poppins-Medium',
      },
  Text2: isTabDevice()
    ? {
        color: colors.aquaBlue,
        // fontWeight: 'bold',
        fontSize: wp('1.5%'),
        marginTop: hp('1%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.aquaBlue,
        // fontWeight: 'bold',
        fontSize: wp('4.5%'),
        fontFamily: 'Poppins-Regular',
      },
  pageFold: isTabDevice()
    ? {
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: 0,
        height: 0,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderRightWidth: wp('3%'),
        borderTopWidth: wp('3%'),
        borderRightColor: 'transparent',
        borderTopColor: colors.aquaBlue,
        borderTopLeftRadius: wp('1%'),
        transform: [{ rotate: '180deg' }],
      }
    : {
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: 0,
        height: 0,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderRightWidth: wp('10%'),
        borderTopWidth: wp('10%'),
        borderRightColor: 'transparent',
        borderTopColor: colors.aquaBlue,
        borderTopLeftRadius: wp('4%'),
        transform: [{ rotate: '180deg' }],
      },
  tag: isTabDevice()
    ? {
        position: 'absolute',
        right: 0,
        top: 0,
        height: hp('8%'),
        width: hp('8%'),
        resizeMode: 'cover',
      }
    : {
        position: 'absolute',
        right: 0,
        top: 0,
        height: hp('8%'),
        width: hp('8%'),
        resizeMode: 'cover',
      },
  tile: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

export default TilesStyles;
