import React, { useState, useEffect } from 'react';
import { Text, TouchableOpacity, View, Image } from 'react-native';
import customTilesStyles from './TilesStyles';
import useStyles from '../../hooks/useStyles';
import { userRoleType } from '../../constants/constants';

export default function Tile({
  data,
  navigation,
  active,
  loginUserRole,
  onPressPayment,
  teamID,
  profileID,
  isNotAvailable,
}) {
  const { name, path, isTesting } = data;
  const TilesStyles = useStyles(customTilesStyles);
  const [isPress, setIsPress] = useState(false);
  const button = {
    style: isPress
      ? TilesStyles.TouchablePressed
      : TilesStyles.TouchableOpacity,
    onPress: () => appNavigation(),
  };

  const appNavigation = () => {
    if (
      (loginUserRole === userRoleType.PARENT ||
        loginUserRole === userRoleType.PLAYER) &&
      data.key === 'payment'
    ) {
      onPressPayment();
    } else {
      if (
        path === 'Teams' &&
        (loginUserRole === userRoleType.PARENT ||
          loginUserRole === userRoleType.PLAYER)
      ) {
          navigation.navigate('PlayerInfoScreen', {
            teamID,
            profileID,
            isNotAvailable,
          });
        return;
      }
      navigation.navigate(path);
    }
  };

  return (
    <View>
      <TouchableOpacity {...button}>
        {isTesting && (
          <Image
            style={TilesStyles.tag}
            source={require('../../../assets/beta-tag.png')}
          />
        )}
        <Text style={TilesStyles.Text}>{name}</Text>

        {!active && <View style={TilesStyles.pageFold}></View>}
      </TouchableOpacity>
    </View>
  );
}
