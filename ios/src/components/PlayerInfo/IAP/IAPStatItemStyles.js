import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPStatItemStyle = colors => ({
  statItemContainer: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        marginBottom: wp('1%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: wp('3%'),
      },
  statItem: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('3.2%'),
        fontFamily: 'Poppins-Regular',
      },
  buttonWrapper: {
    marginLeft: wp('-3%'),
    flexDirection: 'column',
    alignItems: 'center',
  },
  button: isTabDevice()
    ? {
        width: wp('3.5%'),
        height: wp('3.5%'),
      }
    : {
        width: wp('9%'),
        height: wp('9%'),
        marginTop: wp('1%'),
        marginLeft: wp('-4%'),
      },
  firstCol: isTabDevice()
    ? {
        width: wp('25%'),
      }
    : {
        width: wp('27%'),
        marginRight: wp('3%'),
      },
  secondCol: isTabDevice()
    ? {
        width: wp('17%'),
        flexDirection: 'column',
        alignItems: 'center',
      }
    : {
        width: wp('24%'),
        flexDirection: 'column',
        alignItems: 'center',
        marginRight: wp('10%'),
      },
  thirdCol: isTabDevice()
    ? {
        width: wp('17%'),
        flexDirection: 'column',
        alignItems: 'center',
        paddingLeft: wp('1.5%'),
        marginRight: wp('1%'),
      }
    : {
        width: wp('24%'),
        flexDirection: 'column',
        alignItems: 'center',
        paddingLeft: wp('1.5%'),
        marginRight: wp('1%'),
      },
});

export default IAPStatItemStyle;
