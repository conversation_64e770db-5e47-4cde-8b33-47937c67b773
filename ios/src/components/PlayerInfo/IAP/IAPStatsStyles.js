import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPStatsStyle = colors => ({
  statsLabelContainer: {
    marginBottom: '5%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsLabel: isTabDevice()
    ? {
        fontFamily: 'Poppins-Bold',
        color: colors.aquaBlue,
        fontSize: wp('1.5%'),
      }
    : {
        fontFamily: 'Poppins-Bold',
        color: colors.aquaBlue,
        fontSize: wp('4%'),
      },
  statsLabel2: isTabDevice()
    ? {
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        fontFamily: 'Poppins-Bold',
        color: colors.aquaBlue,
        fontSize: wp('4%'),
        paddingLeft: wp('1%'),
      },
  statsLabelWrapper: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingLeft: hp('1.5%'),
        paddingRight: hp('1.5%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        borderRadius: wp('100%'),
        marginRight: wp('2%'),
        width: wp('10%'),
        flexDirection: 'row',
        justifyContent: 'center',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'center',
      },
  firstCol: isTabDevice()
    ? {
        width: wp('25%'),
      }
    : {
        width: wp('27%'),
        marginRight: wp('3%'),
      },
  secondCol: isTabDevice()
    ? {
        paddingLeft: wp('1.5%'),
        width: wp('17%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
      }
    : {
        width: wp('20%'),
      },
  thirdCol: isTabDevice()
    ? {
        paddingLeft: wp('1.5%'),
        width: wp('17%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
      }
    : {
        marginLeft: wp('14%'),
        width: wp('20%'),
      },
});

export default IAPStatsStyle;
