import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPNumberInputStyle = colors => ({
  plusMinusButton: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        position: 'relative',
      }
    : {
        width: wp('6%'),
        height: wp('6%'),
        backgroundColor: colors.darkBlue,
        borderRadius: wp('50%'),
        position: 'relative',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  dataContainer: isTabDevice()
    ? {
        flexDirection: 'row',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      },
  plusMinusButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('3%'),
        position: 'absolute',
        top: wp('-0.5%'),
        left: wp('0.7%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        // fontFamily: 'Poppins-Bold',
      },
  editTextWrapper: isTabDevice()
    ? {
        backgroundColor: colors.veryDarkBlue,
        borderRadius: wp('1%'),
        width: wp('6%'),
        height: wp('3%'),
        marginLeft: wp('1.5%'),
        marginRight: wp('1.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.veryDarkBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('7%'),
        margin: wp('1.5%'),
        // marginRight: wp('1.5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      },
  editText: isTabDevice()
    ? {
        textAlign: 'center',
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        textAlign: 'center',
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
      },
  editTextInput: isTabDevice()
    ? {
        color: 'red',
        width: wp('6%'),
        height: wp('3%'),
        backgroundColor: colors.darkBlue,
        textAlign: 'center',
        marginLeft: 15,
        marginRight: 15,
        marginBottom: 10,
      }
    : {
        color: 'red',
        width: wp('13%'),
        height: wp('12%'),
        backgroundColor: colors.darkBlue,
        textAlign: 'center',
        marginLeft: 15,
        marginRight: 15,
        marginBottom: 10,
      },
});

export default IAPNumberInputStyle;
