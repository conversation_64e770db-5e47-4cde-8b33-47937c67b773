import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const IAPCategoriesStyle = colors => ({
  container: {
    paddingBottom: wp('1%'),
  },
  categoryItemWrapper: {
    backgroundColor: colors.green,
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('50%'),
    marginRight: wp('2%'),
  },
  categoryItemWrapperNoColor: {
    paddingLeft: hp('1.5%'),
    paddingRight: hp('1.5%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    borderRadius: wp('100%'),
    marginRight: wp('2%'),
  },
  categoryItem: {
    fontSize: 18,
    color: colors.white,
    fontFamily: 'Poppins-Medium',
  },
  dropdownView: isTabDevice() ? {
    width: wp('90%'),
    marginTop: wp('-1%'),
    marginLeft: wp('2.5%'),
    backgroundColor: colors.darkBlue,
    borderRadius: wp('100%'),
  } : {
    width: wp('65%'),
    marginTop: wp('-1%'),
    marginLeft: wp('2.5%'),
    backgroundColor: colors.darkBlue,
    borderRadius: wp('100%'),
  },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
    marginBottom: hp('20%'),
  },
  dropdownSelectedPlaceholder: {
    color: colors.lightGrey,
    fontSize: wp('3.5%'),
  },
  dropdownSelectedContainer: {
    height: wp('8%'),
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('-1%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 5,
        width: wp('80%'),
        marginLeft: wp('4%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: -14,
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 5,
        width: wp('80%'),
        marginLeft: wp('4%'),
      },
  dropDownLabel: {
    color: colors.white,
    fontSize: wp('3.5%'),
    fontWeight: 'bold',
  },
  placeholderStyle: {
    color: colors.lightGrey,
    fontSize: wp('3.5%'),
    fontWeight: 'bold',
  },
  dropdownTopArea: {
    backgroundColor: colors.aqua,
    borderColor: colors.transparent,
    position: 'absolute',
    zIndex: 5,
  },
});

export default IAPCategoriesStyle;
