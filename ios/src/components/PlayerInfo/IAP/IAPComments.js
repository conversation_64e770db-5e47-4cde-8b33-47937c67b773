import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { isTabDevice } from '../../../config/appConfig';
import { dateTimeConversion } from '../../../helpers';
import customIAPCommentsStyles from './IAPCommentsStyles';
import useStyles from '../../../hooks/useStyles';
import { userRoleType } from '../../../constants/constants';
import { useSelector } from 'react-redux';

const IAPComments = ({ comments, loadMoreComments }) => {
  const IAPCommentsStyles = useStyles(customIAPCommentsStyles);

  const { userData } = useSelector(state => state?.auth);
  const isParent = userRoleType.PARENT === userData?.type;

  const Comment = ({ item }) => {
    const timestamp = Date.parse(item.date);
    let dateStr;
    let day;
    if (!isNaN(timestamp)) {
      const { year, month, date, dateString } = dateTimeConversion(timestamp);
      dateStr = date + '/' + month + '/' + year;
      day = dateString;
    }
    return (
      <View
        style={IAPCommentsStyles.commentContainer}
        onStartShouldSetResponder={() => true}
      >
        <View style={IAPCommentsStyles.dateWrapper}>
          <Text style={IAPCommentsStyles.date}>{dateStr ? dateStr : ''}</Text>
          <Text style={IAPCommentsStyles.day}>{day ? day : ''}</Text>
        </View>
        <Text style={IAPCommentsStyles.comment}>{item.comment}</Text>
      </View>
    );
  };

  return (
    <FlatList
      data={comments}
      renderItem={Comment}
      keyExtractor={item => item._id}
      onEndReached={() => loadMoreComments()}
      onEndReachedThreshold={1}
      style={IAPCommentsStyles.list}
      contentContainerStyle={
        isParent && !isTabDevice() &&  {paddingBottom: 120 } 
      }
    />
  );
};

export default IAPComments;
