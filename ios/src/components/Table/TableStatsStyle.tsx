import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

export default (colors: any) => ({
  statsTableContainer: isTabDevice() ? {
    display: 'flex',
    flexDirection: 'column',
  } : {
    display: 'flex',
    flexDirection: 'column',
  },
  statsTable: isTabDevice() ? {
    width: '100%',
  } : {
    width: '100%',
  },
  statsTableHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statsTableBody: isTabDevice()
    ? {
        maxHeight: hp('40%'),
      }
    : {
        maxHeight: hp('15%'),
      },
  statsTableFooter: {
    backgroundColor: colors.aquaBlue,
  },
  noData: isTabDevice()
    ? {
        borderRadius: wp('1%'),
        height: hp('40%'),
        width: wp('72%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        // flex: 1,
      }
    : {
        borderRadius: wp('1%'),
        height: hp('30%'),
        width: wp('90%'),
        marginLeft: wp('4%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        flex: 1,
      },
  row: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
        marginRight: wp('1%'),
        width: '100%',
      }
    : {},
  rowFooter: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginRight: wp('1%'),
        width: '100%',
      }
    : {},
  column: isTabDevice()
    ? {
        padding: wp('1%'),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        width: wp('8.3%'),
      }
    : {
        padding: wp('1%'),
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        width: wp('20%'),
      },
  firstColumn: isTabDevice()
    ? {
        justifyContent: 'flex-start',
        width: wp('12%'),
      }
    : {
        justifyContent: 'flex-start',
        width: wp('30%'),
      },
  secondColumn: isTabDevice()
    ? {
        justifyContent: 'flex-start',
        width: wp('12%'),
      }
    : {
        justifyContent: 'flex-start',
        width: wp('40%'),
      },
  columnFooter: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        padding: wp('0.5%'),
        width: wp('7%'),
      }
    : {},
  headerText: isTabDevice()
    ? {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1.2%'),
      }
    : {
        color: colors.aquaBlue,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('4%'),
      },
  bodyText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.1%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Regular',
      },
  loader: isTabDevice()
    ? {
        minHeight: wp('20%'),
      }
    : {
        minHeight: wp('20%'),
      },
});
