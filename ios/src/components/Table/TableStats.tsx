import React, { FC, useMemo, useCallback } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import useStyles from '../../hooks/useStyles';
import customStatTableStyles from './TableStatsStyle';
import {
  tableDataType,
  tableTotalDataType,
} from '../../store/reducers/PlayerStats/PlayerStatsReducer';
import ActivitySpinner from '../ActivitySpinner/ActivitySpinner';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
interface ItableStats {
  tableData: tableDataType[];
  tableTotalData: tableTotalDataType;
  selectedStatCategory: string;
  tableDataLoading: boolean;
  tableDataError: boolean;
  onReachEndHandler: Function;
  getReadableDateStamp: Function;
}
const TableStats: FC<ItableStats> = ({
  tableData,
  tableTotalData,
  selectedStatCategory,
  tableDataLoading,
  onReachEndHandler,
  getReadableDateStamp,
}) => {
  const TableStatsStyle = useStyles(customStatTableStyles);
  const {
    durationInMinutes,
    goals,
    assists,
    yellowCard,
    redCard,
    rating,
    appearances,
  } = tableTotalData || {};
  const renderTotalRow = ({ item, index }: any) => {
    return (
      <View onStartShouldSetResponder={() => true}>
        <View
          style={
            index === 0
              ? [TableStatsStyle.column, TableStatsStyle.firstColumn]
              : index === 1
              ? [TableStatsStyle.column, TableStatsStyle.secondColumn]
              : TableStatsStyle.column
          }
          key={item?.key}
        >
          <Text style={TableStatsStyle.bodyText}>{item?.data}</Text>
        </View>
      </View>
    );
  };
  const renderRowsAndBar = ({ item, index }: any) => {
    return (
      <>
        <View onStartShouldSetResponder={() => true}>
          {item?.data?.map((value: string, i: number) => (
            <View
              style={
                index === 0
                  ? [TableStatsStyle.column, TableStatsStyle.firstColumn]
                  : index === 1
                  ? [TableStatsStyle.column, TableStatsStyle.secondColumn]
                  : TableStatsStyle.column
              }
              key={i}
            >
              <Text
                style={TableStatsStyle.bodyText}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {value ? value : "0"}
              </Text>
            </View>
          ))}
        </View>
      </>
    );
  };
  const renderColumns = ({ item, index }: any) => {
    return (
      <View
        style={
          index === 0
            ? [TableStatsStyle.column, TableStatsStyle.firstColumn]
            : index === 1
            ? [TableStatsStyle.column, TableStatsStyle.secondColumn]
            : TableStatsStyle.column
        }
      >
        <Text style={TableStatsStyle.headerText}>{item}</Text>
      </View>
    );
  };
  const customTotalData = useMemo(
    () => [
      { key: 'total', data: ['Total'] },
      { key: 'tournament', data: '' },
      { key: 'Apps', data: appearances },
      { key: 'Mins', data: durationInMinutes || '' },
      { key: 'Goals', data: goals },
      { key: 'Assist', data: assists },
      { key: 'Yel', data: yellowCard },
      { key: 'Red', data: redCard },
    ],
    [tableTotalData]
  );
  const filterTableData = useCallback(
    (type: any) => {
      return tableData?.map(item => item?.[type]);
    },
    [tableData]
  );
  const firstThreeColumnHeadings = useMemo(() => {
    if (selectedStatCategory !== '3') {
      return ['Season', 'Tournament', 'Apps'];
    } else {
      return ['Opponent', 'Date', 'Position'];
    }
  }, [selectedStatCategory]);

  const tableHeadings = [
    ...firstThreeColumnHeadings,
    'Mins',
    'Goals',
    'Assists',
    'Yel',
    'Red',
    'Rating',
  ];

  const checkIfPropertyExists = (property: string) => {
    return [...Object.keys(tableData?.[0] || {})].includes(property);
  };

  const filterTableDateStamp = (type: any) => {
    return tableData?.map(item => getReadableDateStamp(item?.[type]));
  };

  const customTableData = useMemo(
    () => [
      {
        key: 'Season',
        data: filterTableData(
          checkIfPropertyExists('season') ? 'season' : 'opponent'
        ),
      },
      {
        key: 'Tournament',
        data: checkIfPropertyExists('tournament')
          ? filterTableData('tournament')
          : filterTableDateStamp('date'),
      },
      {
        key: 'App',
        data: filterTableData(
          checkIfPropertyExists('appearances') ? 'appearances' : 'position'
        ),
      },
      {
        key: 'Mins',
        data: filterTableData('durationInMinutes'),
      },
      {
        key: 'Goals',
        data: filterTableData('goals'),
      },
      {
        key: 'Assists',
        data: filterTableData('assists'),
      },
      {
        key: 'Yel',
        data: filterTableData('yellowCard'),
      },
      {
        key: 'Red',
        data: filterTableData('redCard'),
      },
      {
        key: 'Rating',
        data: filterTableData('rating'),
      },
    ],
    [tableData]
  );

  return (
    <ScrollView
      horizontal={true}
      contentContainerStyle={TableStatsStyle.statsTableContainer}
    >
      <View
        style={TableStatsStyle.statsTable}
        onStartShouldSetResponder={() => true}
      >
        <View style={TableStatsStyle.statsTableHeader}>
          <View style={TableStatsStyle.row}>
            <FlatList
              data={tableHeadings}
              renderItem={renderColumns}
              keyExtractor={item => item}
              numColumns={9}
            ></FlatList>
          </View>
        </View>
        {tableDataLoading ? (
          <View style={TableStatsStyle.loader}>
            <ActivitySpinner />
          </View>
        ) : !tableData?.length ? (
          <NoContentMessage
            message={'No data available'}
            customWrapperStyle={TableStatsStyle.noData}
          />
        ) : (
          <FlatList
            style={TableStatsStyle.statsTableBody}
            data={customTableData}
            renderItem={renderRowsAndBar}
            keyExtractor={item => item?.key}
            numColumns={9}
            onEndReached={() => onReachEndHandler()}
          ></FlatList>
        )}
      </View>
      {selectedStatCategory !== '3' && (
        <View style={TableStatsStyle.statsTableFooter}>
          <View style={TableStatsStyle.rowFooter}>
            <FlatList
              data={customTotalData}
              renderItem={renderTotalRow}
              keyExtractor={item => item?.key}
              numColumns={9}
            ></FlatList>
          </View>
        </View>
      )}
    </ScrollView>
  );
};
export default TableStats;
