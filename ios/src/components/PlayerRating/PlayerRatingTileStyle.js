import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerRatingTileStyle = colors => ({
  player: {
    margin: wp('1.5%'),
    marginBottom: wp('1.5%'),
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  proImage: isTabDevice()
    ? {
        width: wp('12%'),
        height: wp('12%'),
        borderRadius: wp('2%'),
        resizeMode: 'cover',
        marginBottom: wp('0.5%'),
      }
    : {
        width: wp('27%'),
        height: wp('27%'),
        borderRadius: wp('2%'),
        resizeMode: 'cover',
        marginBottom: wp('0.5%'),
      },
  playerName: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.2%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
        width: wp('10%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
        width: wp('40%'),
      },
  numberInput: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        width: wp('5%'),
        marginLeft: wp('0.5%'),
        marginRight: wp('0.5%'),
        textAlign: 'center',
      }
    : {
        fontSize: wp('3.3%'),
        color: colors.white,
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        width: wp('14%'),
        marginLeft: wp('1.5%'),
        marginRight: wp('1.5%'),
        textAlign: 'center',
      },
  minusButton: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        backgroundColor: colors.red,
        borderRadius: wp('1%'),
        position: 'relative',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        backgroundColor: colors.red,
        borderRadius: wp('2%'),
        position: 'relative',
      },
  plusButton: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        position: 'relative',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        backgroundColor: colors.green,
        borderRadius: wp('2%'),
        position: 'relative',
      },
  plusMinusButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('3%'),
        position: 'absolute',
        top: wp('-0.5%'),
        left: wp('0.7%'),
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        position: 'absolute',
        top: wp('-1%'),
        left: wp('0.9%'),
      },
  icon: {
    position: 'absolute',
    top: 3,
    right: 3,
    fontSize: wp('2.5%'),
  },
  availableIconWrapper: {
    backgroundColor: colors.white,
    width: wp('3%'),
    height: wp('3%'),
    borderRadius: wp('100%'),
    position: 'absolute',
    top: '-4%',
    right: '-1%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'center',
  },
});

export default PlayerRatingTileStyle;
