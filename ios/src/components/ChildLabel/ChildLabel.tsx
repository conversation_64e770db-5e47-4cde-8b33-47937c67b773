import { View, Text, FlatList, TouchableOpacity } from 'react-native';
import React from 'react';
import ProfileImage from '../ProfileImage/ProfileImage';
import customHeaderStyle from '../header/HeaderStyle';
import useStyles from '../../hooks/useStyles';

interface IChildDetails {
  id: string;
  firstName: string;
  lastName: string;
  nickName: string;
  dateOfBirth: string;
  profileImageUrl: string;
  type: string;
  height: string;
  weight: string;
  contact: string;
  parentContact: string;
  emergencyContact: string;
  emailId: string;
  expoPushNotificationTokens?: any[];
  profileImage: string;
  joinedDate: string;
}

interface IChildLabel {
  setSelectedChild: (item: any) => void;
  data: IChildDetails[];
  selectedChild: IChildDetails;
}

const ChildLabel: React.FC<IChildLabel> = ({
  data,
  setSelectedChild,
  selectedChild,
}) => {
  const HeaderStyle = useStyles(customHeaderStyle);
  const renderChildData = ({ item }: any) => {
    return (
      <TouchableOpacity onPress={() => setSelectedChild(item)}>
        <ProfileImage
          imageStyles={
            selectedChild?.id === item?.id
              ? HeaderStyle.teamChildrenAvatarSelected
              : HeaderStyle.teamChildrenAvatar
          }
          profileImageUrl={item.profileImageUrl}
          style=""
        />
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <FlatList
        data={data}
        renderItem={renderChildData}
        keyExtractor={item => item.id}
        horizontal
        initialNumToRender={6}
      />
    </View>
  );
};

export default ChildLabel;
