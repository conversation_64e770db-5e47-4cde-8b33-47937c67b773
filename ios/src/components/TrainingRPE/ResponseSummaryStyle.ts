import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const ResponseSummaryStyle = (colors: any) => ({
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    }
    : {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: wp('1%'),
    zIndex: 10,
  },
  modalView: isTabDevice()
    ? {
      backgroundColor: '#23344B',
      borderRadius: wp('2%'),
      alignItems: 'center',
      height: hp('70%'),
      maxHeight: hp('90%'),
      width: wp('40%'),
      // paddingVertical: hp("3.53%"),
      // paddingHorizontal: wp("3.59%")
    }
    : {
      backgroundColor: colors.borderBlue,
      borderRadius: wp('4%'),
      alignItems: 'center',
      width: wp('80%'),
      maxHeight: hp('85%'),
    },
  modalTitleContainer: isTabDevice()
    ? {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      paddingVertical: 10,
    }
    : {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      paddingVertical: 10,
    },
  header: isTabDevice()
    ? {
      fontSize: wp('1.7%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
    }
    : {
      fontSize: wp('4.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Bold',
    },
  responseSummaryContainerWrapper: {
    width: '90%',
  },
  responseSummaryContainer: isTabDevice()
    ? {
      flexDirection: 'row',
      backgroundColor: '#0B1627',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: hp('6.95%'),
      minWidth: wp('38.30% '),
      gap: wp('1%'),
    }
    : {
      flexDirection: 'row',
      backgroundColor: '#0B1627',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: hp('7%'),
      minWidth: wp('30%'),
      gap: wp('5%'),
    },
  responseSummaryContainer2: isTabDevice()
    ? {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: hp('6.95%'),
      minWidth: wp('38.30% '),
      gap: wp('1%'),
    }
    : {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: hp('7%'),
      minWidth: wp('30%'),
      gap: wp('5%'),
    },
    responseSummaryContainer3: isTabDevice()
    ? {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#0B1627',
      justifyContent: 'space-between',
      width: '100%',
      height: hp('6.95%'),
      minWidth: wp('38.30% '),
      paddingHorizontal: wp('3%'),
    }
    : {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#0B1627',
      justifyContent: 'space-between',
      width: '100%',
      height: hp('7%'),
      minWidth: wp('30%'),
      paddingHorizontal: wp('3%'),
    },
  responseCountContainer: isTabDevice()
    ? {
      width: wp('15.45%'),
      flexDirection: 'row',
      backgroundColor: '#333E50',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderRadius: 7,
      paddingVertical: hp('0.5%'),
      paddingHorizontal: wp('1.4%'),
    }
    : {
      width: wp('35%'),
      flexDirection: 'row',
      backgroundColor: '#333E50',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderRadius: 7,
      paddingVertical: hp('0.5%'),
      paddingHorizontal: wp('2%'),
    },
  responseSummaryCountText: isTabDevice()
    ? {
      fontSize: wp('1.3%'),
      color: colors.white,
      fontFamily: 'Poppins-Medium',
    }
    : {
      fontSize: wp('3.5%'),
      color: colors.white,
      fontFamily: 'Poppins-Medium',
    },
  flatListContainer: isTabDevice() ? {} : {},
  flatListItem: isTabDevice()
    ? {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('1%'),
    }
    : {
      flexDirection: 'row',
      alignItems: 'center',
      gap: wp('1%'),
      marginTop: 10,
    },
  avatar: isTabDevice()
    ? {
      width: wp('5%'),
      height: wp('5%'),
      borderRadius: 15,
      marginTop: wp('0.5%'),
    }
    : {
      width: wp('12%'),
      height: wp('12%'),
      borderRadius: 12,
    },
  flatListItemDetailSection: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    width: '83%',
  },
  flatListItemName: isTabDevice() ? {
    width: '70%',
  } : {
    width: '60%',
  },
  flatListItemName2: {
    width: '70%'
  },
  flatListItemCountSection: isTabDevice() ? {
    flexDirection: 'row',
    gap: wp('0.5%'),
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '30%'
  } : {
    flexDirection: 'row',
    gap: wp('0.5%'),
    alignItems: 'center',
    justifyContent: 'space-around',
    width: '40%',
  },
  flatListItemCount: isTabDevice()
    ? {
      paddingHorizontal: wp('1.10%'),
      paddingVertical: hp('0.6%'),
      borderRadius: 18,
    }
    : {
      paddingHorizontal: wp('3.5'),
      paddingVertical: hp('0.4%'),
      borderRadius: 18,
    },
  flatListItemIconContainer: isTabDevice()
    ? {
      backgroundColor: '#0B1627',
      paddingHorizontal: wp('2%'),
      paddingVertical: hp('0.88%'),
      borderRadius: 18,
    }
    : {
      backgroundColor: '#0B1627',
      paddingHorizontal: wp('3.5'),
      paddingVertical: hp('0.7%'),
      borderRadius: 18,
    },
  listWrapper: isTabDevice()
    ? {
      width: '100%',
      marginTop: wp('2%'),
      paddingBottom: wp('8%'),
      height: hp('37%'),
    }
    : {
      width: '100%',
      height: wp('70%'),
      paddingBottom: wp('5%'),
      marginTop: wp('2%'),
    },
  listWrapper2: isTabDevice()
    ? {
      width: '100%',
      marginTop: wp('2%'),
      paddingBottom: wp('8%'),
      height: hp('55%'),
    }
    : {
      width: '100%',
      height: wp('70%'),
      paddingBottom: wp('5%'),
      marginTop: wp('4%'),
    },
  noContentWrapper: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  }
});

export default ResponseSummaryStyle;
