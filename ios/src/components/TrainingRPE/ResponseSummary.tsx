import React, { useState } from 'react';
import { View, Text } from 'react-native';
import SelectionModal from '../../components/modal/SelectionModal/SelectionModal';
import colors from '../../config/colors';
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import useStyles from '../../hooks/useStyles';
import ModalWrapper from '../modal/ModalWrapper/ModalWrapper';
import ResponsePlayerList from './ResponsePlayerList';
import CustomResponeSummaryStyle from './ResponseSummaryStyle';
import RPEHeader from './RPEheader';
import TabResponseButton from './TabResponseButton';
import TabRPEButton from './TabRPEButton';

const ResponseSummary = () => {
  const ResponseSummaryStyle = useStyles(CustomResponeSummaryStyle);
  const [isResponseTab, setIsResponseTab] = useState<boolean>(true);
  const [isRPEScoreTab, setIsRPEScoreTab] = useState<boolean>(false);

  const {
    handleRPEModal,
    SelectedRPEPlayerList,
    repResponseCount,
    rpePlayerListLoading,
    RPEList,
    responseSummerySelection,
    handlePlayerResponsePagination,
    handlePlayerResponseFilter,
    handleResponseSummerySelection,
  } = useTrainingHook({
    isInitalComponent: false,
  });

  const [
    setIsFilterByModalOpen,
    isFilterByModalOpen,
    setSelectedFilterBy,
    selectedFilterBy,
  ] = useInputSelectModal();

  const defaultRpeLabelAndValue = [
    {
      label: 'All',
      value: '_',
    },
  ];

  const rpeLabelAndValue = RPEList?.map(r => ({
    label: r.value.toString(),
    value: r.value.toString(),
  }))?.reverse();

  const finalRpeLabelAndValue = [
    ...defaultRpeLabelAndValue,
    ...rpeLabelAndValue,
  ];

  return (
    <ModalWrapper transparent>
      <View style={ResponseSummaryStyle.centeredView}>
        <View style={ResponseSummaryStyle.overlay}></View>
        <View style={ResponseSummaryStyle.modalView}>
          <RPEHeader
            onClose={() => {
              handleResponseSummerySelection({
                isRPEScoreCal: false,
                isResponse: true,
              });
              handleRPEModal({ eventId: '', showRPEModal: false })}
            }
            styles={ResponseSummaryStyle}
          />
          <View style={ResponseSummaryStyle.responseSummaryContainer2}>
            <TabRPEButton
              label="RPE Score"
              onPress={() => {
                if(rpePlayerListLoading){
                  return
                }
                setIsRPEScoreTab(false);
                handleResponseSummerySelection({
                  isRPEScoreCal: false,
                  isResponse: true,
                });
                setIsResponseTab(true);
              }}
              style={ResponseSummaryStyle}
              backgroundColor={!isRPEScoreTab ? colors.aquaBlue : colors.blue}
            />
            <TabRPEButton
              label="RPE Training Load"
              onPress={() => {
                if(rpePlayerListLoading){
                  return
                }
                setIsRPEScoreTab(true);
                handleResponseSummerySelection({
                  isRPEScoreCal: true,
                  isResponse: true,
                });
              }}
              style={ResponseSummaryStyle}
              backgroundColor={isRPEScoreTab ? colors.aquaBlue : colors.blue}
            />
          </View>
          {!isRPEScoreTab && (
            <>
              <View style={ResponseSummaryStyle.responseSummaryContainer}>
                <TabResponseButton
                  label="Responded"
                  count={repResponseCount?.responded}
                  isActive={isResponseTab}
                  onPress={() => {
                    if(rpePlayerListLoading){
                      return
                    }
                    handleResponseSummerySelection({
                      isRPEScoreCal: false,
                      isResponse: true,
                    });
                    setIsResponseTab(true);
                  }}
                  style={ResponseSummaryStyle}
                  backgroundColor={isResponseTab ? colors.green : undefined}
                />
                <TabResponseButton
                  label="No Response"
                  count={repResponseCount?.noResponse}
                  isActive={!isResponseTab}
                  onPress={() => {
                    if(rpePlayerListLoading){
                      return
                    }
                    handleResponseSummerySelection({
                      isRPEScoreCal: false,
                      isResponse: false,
                    });
                    setIsResponseTab(false);
                  }}
                  style={ResponseSummaryStyle}
                  backgroundColor={!isResponseTab ? colors.green : undefined}
                />
              </View>
              {responseSummerySelection &&
                 ( 
                  <View style={[ResponseSummaryStyle.responseSummaryContainerWrapper, {marginTop: 10}]}>
                    <SelectionModal
                      title={'Filter By'}
                      items={finalRpeLabelAndValue}
                      onCloseHook={setIsFilterByModalOpen}
                      onSelectItemHook={(item: any) => {
                        handlePlayerResponseFilter(item?.[0]?.value);
                        setSelectedFilterBy(item);
                      }}
                      defaultValues={
                        selectedFilterBy?.[0]
                          ? [selectedFilterBy?.[0]?.value]
                          : [finalRpeLabelAndValue?.[0]?.value]
                      }
                      selectedItemLabel={selectedFilterBy?.[0]?.label}
                      isModalOpen={isFilterByModalOpen}
                      enableDefaultLabel
                      selectFirstOptionOnInitialRender
                    />
                  </View>
                )}
            </>
          )}
          {isRPEScoreTab && (
            <>
              <View style={ResponseSummaryStyle.responseSummaryContainer3}>
                <View>
                  <Text style={ResponseSummaryStyle.responseSummaryCountText}>Player Name</Text>
                </View>
                <View>
                  <Text style={ResponseSummaryStyle.responseSummaryCountText}>Training Load</Text>
                </View>
              </View>
            </>
          )}
          <View style={ResponseSummaryStyle.responseSummaryContainerWrapper}>
            <ResponsePlayerList
              players={SelectedRPEPlayerList}
              isLoading={rpePlayerListLoading}
              onEndReached={() => handlePlayerResponsePagination({
                isRPEScoreCal: isRPEScoreTab,
                isResponse: isResponseTab,
              })}
              styles={ResponseSummaryStyle}
              isShowScoreBlock={!isRPEScoreTab && isResponseTab}
              isResponseTab={isResponseTab}
            />
          </View>
        </View>
      </View>
    </ModalWrapper>
  );
};

export default ResponseSummary;
