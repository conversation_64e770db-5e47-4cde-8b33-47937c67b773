import React from 'react';
import { Text, View, TouchableHighlight } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HeaderProps {
  onClose: () => void;
  styles: any;
}

const RPEHeader = ({ onClose, styles }: HeaderProps) => (
  <View style={styles.modalTitleContainer}>
    <Text style={styles.header}> </Text>
    <TouchableHighlight underlayColor={'transparent'} onPress={onClose}>
      <Ionicons
        name="close"
        style={{
          ...styles.closeButton,
          fontSize: 25,
          color: 'white',
        }}
      />
    </TouchableHighlight>
  </View>
);

export default RPEHeader;
