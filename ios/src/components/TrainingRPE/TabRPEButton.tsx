import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';

interface TabButtonProps {
  label: string;
  onPress: () => void;
  backgroundColor?: string;
  style: any;
}

const TabRPEButton = ({
  label,
  onPress,
  style,
  backgroundColor,
}: TabButtonProps) => {
  return (
    <TouchableOpacity onPress={onPress} style={[
      style.responseCountContainer,
      backgroundColor ? { backgroundColor } : {},
    ]}>
        <Text style={style.responseSummaryCountText}>{label}</Text>
    </TouchableOpacity>
  );
};

export default TabRPEButton;
