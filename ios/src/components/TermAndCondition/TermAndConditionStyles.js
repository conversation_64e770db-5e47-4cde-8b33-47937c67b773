import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TermAndConditionStyles = colors => ({
  container: {},
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.95,
  },
  centeredView: isTabDevice()
    ? {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
      },
  modalView: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        alignItems: 'center',
        width: wp('80%'),
        maxHeight: wp('60%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        paddingBottom: wp('3%'),
        borderRadius: wp('4%'),
        alignItems: 'center',
        width: wp('80%'),
      },
  modalTitleContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        backgroundColor: colors.veryDarkBlue,
        width: '100%',
        borderTopLeftRadius: wp('2%'),
        borderTopRightRadius: wp('2%'),
        justifyContent: 'center',
        padding: wp('1.5%'),
      }
    : {
        flexDirection: 'row',
        backgroundColor: colors.veryDarkBlue,
        width: '100%',
        borderTopLeftRadius: wp('4%'),
        borderTopRightRadius: wp('4%'),
        justifyContent: 'center',
        padding: wp('3%'),
      },

  modalTitle: isTabDevice()
    ? {
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('1.8%'),
        // fontFamily: 'Poppins-regular',
      }
    : {
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('3%'),
        // fontFamily: 'Poppins-regular',
      },
  modalBody: isTabDevice()
    ? {
        flexDirection: 'row',
        width: '100%',
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
      }
    : {
        flexDirection: 'column-reverse',
        paddingTop: wp('4%'),
        paddingLeft: wp('4%'),
        paddingRight: wp('4%'),
      },
  content: isTabDevice()
    ? {
        maxWidth: wp('55%'),
        maxHeight: wp('50%'),
        marginTop: wp('3.5%'),
      }
    : {},
  titleList: isTabDevice()
    ? {
        marginTop: wp('2%'),
        maxWidth: wp('20%'),
        maxHeight: hp('75%'),
        borderRightColor: colors.white,
        borderRightWidth: 1,
      }
    : {},
  titles: isTabDevice()
    ? {
        marginTop: wp('1.5%'),
        paddingRight: wp('3%'),
      }
    : {},
  titleListText: isTabDevice()
    ? {
        marginBottom: hp('2%'),
        fontSize: wp('1.3%'),
        // fontFamily: 'Poppins-Regular',
        marginBottom: hp('0.5%'),
        color: colors.white,
      }
    : {},
  contentText: isTabDevice()
    ? {
        fontSize: hp('2%'),
        // fontFamily: 'Poppins-Regular',
        marginBottom: hp('0.5%'),
        color: colors.white,
        maxWidth: wp('50%'),
      }
    : {
        fontSize: wp('2%'),
        // fontFamily: 'Poppins-Regular',
        marginBottom: hp('0.5%'),
        color: colors.white,
      },
  contentTitle: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('1.3%'),
        // fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
        marginTop: wp('0.5%'),
        color: colors.green,
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('2.5%'),
        // fontFamily: 'Poppins-Regular',
        marginBottom: wp('1.5%'),
        marginTop: wp('1.5%'),
        color: colors.green,
      },
  flatList: isTabDevice()
    ? {
        paddingBottom: wp('2%'),
      }
    : { maxHeight: wp('120%') },
  checkBoxSection: isTabDevice()
    ? {
        flexDirection: 'row',
        marginBottom: wp('2%'),
      }
    : {
        flexDirection: 'row',
        marginLeft: wp('2%'),
        marginBottom: wp('4%'),
        marginTop: wp('4%'),
      },
  checkBox: isTabDevice()
    ? {
        borderRadius: 5,
        width: wp('2%'),
        height: wp('2%'),
      }
    : {
        borderRadius: 3,
        width: wp('3%'),
        height: wp('3%'),
      },
  checkBoxText: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('1.3%'),
        // fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginLeft: wp('1%'),
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('2.5%'),
        // fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginLeft: wp('2%'),
        marginTop: wp('-0.5%'),
      },
  termAndConditionAcceptSection: isTabDevice()
    ? {
        marginTop: wp('1.5%'),
        paddingRight: wp('3%'),
      }
    : {},
  agreeButton: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        padding: wp('1%'),
        borderRadius: wp('1%'),
        marginBottom: wp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        alignItems: 'center',
        padding: wp('2.5%'),
        borderRadius: wp('2.5%'),
        marginBottom: wp('3%'),
      },
  agreeButtonText: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('1.3%'),
        // fontFamily: 'Poppins-Regular',
        color: colors.white,
        textAlign: 'center',
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('4%'),
        // fontFamily: 'Poppins-Regular',
        color: colors.white,
        textAlign: 'center',
      },
  remindMeLaterButton: isTabDevice() ? {} : {},
  remindMeLaterText: isTabDevice()
    ? {
        fontWeight: 'bold',
        marginTop: wp('2%'),
        fontSize: wp('1%'),
        // fontFamily: 'Poppins-Regular',
        color: colors.white,
        textAlign: 'center',
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('2.5%'),
        marginTop: wp('4%'),
        // fontFamily: 'Poppins-Regular',
        color: colors.white,
        textAlign: 'center',
      },
});

export default TermAndConditionStyles;
