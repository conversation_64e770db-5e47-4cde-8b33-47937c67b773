import React, { useState } from 'react';
import { Image } from 'expo-image';
import customImagePerLoaderStyles from './ImagePerLoaderStyles';
import useStyles from '../../hooks/useStyles';

const ImagePerLoader = ({ imageStyles, source }) => {
  const ImagePerLoaderStyles = useStyles(customImagePerLoaderStyles);
  const [isError, setIsError] = useState(false);
  const [isImageLoaded, setImageLoaded] = useState(false);

  return (
    <Image
      style={[ImagePerLoaderStyles.image, imageStyles]}
      source={
        source
          ? !isImageLoaded
            ? require('../../../assets/loader.gif')
            : isError
            ? require('../../../assets/profilepictures/default_profile.png')
            : { uri: source }
          : require('../../../assets/profilepictures/default_profile.png')
      }
      onLoad={() => setImageLoaded(true)}
      cachePolicy={'disk'}
      onError={() => {
        setIsError(true);
      }}
    />
  );
};

export default ImagePerLoader;
