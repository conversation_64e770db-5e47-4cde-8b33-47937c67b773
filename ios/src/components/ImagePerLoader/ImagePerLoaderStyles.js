import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { colorPalette } from '../../constants/constants';

const ImagePerLoaderStyles = colors => ({
  imagePerLoaderImage: {
    width: '100%',
    height: '100%',
    transform: [{ scale: 0.5 }],
  },
  perLoadImage: {
    // overflow: 'hidden',
    backgroundColor: colors.white,
  },
  loaderWrapper: {
    backgroundColor: colors.white,
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  spinner: {
    width: wp('5%'),
    height: wp('5%'),
    transform: [{ scale: 0.5 }],
  },
  image: {
    // width: wp('0.5%'),
    // height: wp('0.5%'),
    // resizeMode: 'cover',
    // position: 'absolute',
    // left: 0,
    // top: 0,
    // zIndex: 10,
  },
});
export default ImagePerLoaderStyles;
