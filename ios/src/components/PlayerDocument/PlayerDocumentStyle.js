import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerDocumentStyle = colors => ({
  dateView: isTabDevice()
    ? {
        backgroundColor: 'green',
        padding: wp('0.5%'),
        width: wp('10%'),
        borderRadius: 7,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: wp('1%'),
        // marginTop: wp('0.5%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        // backgroundColor: colors.white
      },
  dateText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium'
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        fontFamily: 'Poppins-Medium',
        marginRight: wp('2%'),
      },
  dateText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        fontFamily: 'Poppins-Medium',
      },
  msgWrapper: isTabDevice()
    ? {
        alignItems: 'center',
        borderColor: colors.darkBlue,
        borderRightWidth: 0.3,
        paddingVertical: 10,
        paddingRight: wp('0.5%'),
        flexDirection: 'row',
        width: wp('33%'),
      }
    : {
        width: wp('65%'),
      },
  msgWrapperSelected: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('28%'),
      }
    : {
        width: wp('44%'),
      },
  msg: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        textAlign: 'justify',
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3.2%'),
        color: colors.white,
        textAlign: 'justify',
        marginTop: wp('1%'),
        fontFamily: 'Poppins-Regular',
      },
  msgTypeWrapper: isTabDevice()
    ? {
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        width: wp('13%'),
      }
    : {},
  msgType: isTabDevice()
    ? {
        color: colors.green,
      }
    : {
      color: colors.green
    },
  optionView: isTabDevice()
    ? {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        paddingLeft: 13,
        justifyContent: 'space-between',
        paddingHorizontal: 5,
        marginLeft: -20,
      }
    : {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        paddingLeft: 13,
        justifyContent: 'space-between',
        paddingHorizontal: 5,
        marginLeft: -10,
        marginRight: 10,
      },
  editBtn: isTabDevice()
    ? {
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        paddingVertical: 10,
        paddingLeft: 10,
        paddingRight: 10,
        alignItems: 'center',
        flexDirection: 'row',
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        marginRight: wp('2%'),
      },
  editBtn2: isTabDevice()
    ? {
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        paddingVertical: 10,
        paddingLeft: 10,
        paddingRight: 10,
        alignItems: 'center',
        flexDirection: 'row',
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
      },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        marginHorizontal: wp('1%'),
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        marginHorizontal: wp('1%'),
        fontSize: wp('2.5'),
        fontWeight: 'bold',
        fontFamily: 'Poppins-Regular',
      },
  highlight: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        // marginVertical: 5,
        width: '95%',
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        height: 90,
        marginBottom: hp('-0.5%'),
      },
  highlightColored: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        // marginVertical: 5,
        width: '95%',
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        marginBottom: 15,
      },
  contain: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rightView: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
  },

  updateViewSelected: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '105%',
        marginTop: 8,
        width: wp('25%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '104%',
        marginTop: 3,
        width: wp('45%'),
      },
  rightViewSelected: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '105%',
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        marginTop: 8,
        width: wp('25%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '104%',
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        marginTop: 3,
        width: wp('45%'),
      },
  primary: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        padding: wp('1%'),
      }
    : {
        flexDirection: 'column',
        padding: wp('3%'),
        width: wp('35%'),
      },
  icon: isTabDevice()
    ? {
        width: wp('1.5%'),
        height: wp('1.5%'),
      }
    : {
        width: wp('7%'),
        height: wp('7%'),
      },
  iconImage: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        resizeMode: 'contain',
      },
});
export default PlayerDocumentStyle;
