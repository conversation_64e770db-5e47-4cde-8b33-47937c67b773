import React from 'react';
import customPlayerCommonStyle from './PlayerDocumentStyle';
import useStyles from '../../hooks/useStyles';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import deleteIcon from '../../../assets/buttons/deleteIcon.png';
import editIcon from '../../../assets/buttons/editIcon.png';
import downloadIcon from '../../../assets/icons/color-download-icon.png';
import uploadIcon from '../../../assets/icons/color-upload-icon.png';

const RenderColumnItems = ({
  item,
  isEditMode,
  setOpenDeleteModal,
  downloadDocumentHandler,
  setSelectedItem,
  openEditModal = () => {},
}) => {
  const PlayerCommonStyle = useStyles(customPlayerCommonStyle);

  return (
    <View style={PlayerCommonStyle.optionView}>
      {isEditMode && (
        <TouchableOpacity
          style={PlayerCommonStyle.editBtn}
          onPress={() => {
            setSelectedItem(item);
            openEditModal();
          }}
        >
          <Image source={editIcon} style={PlayerCommonStyle.iconImage} />
        </TouchableOpacity>
      )}
      <TouchableOpacity
        style={PlayerCommonStyle.editBtn2}
        onPress={() => {
          if (isEditMode) {
            setSelectedItem(item);
            setOpenDeleteModal(true);
          } else {
            downloadDocumentHandler();
          }
        }}
      >
        <Image
          source={isEditMode ? deleteIcon : downloadIcon}
          style={PlayerCommonStyle.iconImage}
        />
      </TouchableOpacity>
    </View>
  );
};

export default RenderColumnItems;
