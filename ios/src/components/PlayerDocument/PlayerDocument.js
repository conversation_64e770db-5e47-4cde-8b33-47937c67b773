import React, { useEffect, useState } from 'react';
import {
  Image,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import DeleteModal from '../modal/DeleteModal/DeleteModal';
import { userRoleType } from '../../constants/constants';
import { dateTimeConversion } from '../../helpers/index';
import useStyles from '../../hooks/useStyles';
import DownloadReportModal from '../modal/DownloadReportModal/DownloadReportModal';
import customPlayerCommonStyle from './PlayerDocumentStyle';
import { Feather } from '@expo/vector-icons';
import RenderColumnItems from './RenderColumnItems';
import DownloadDocumentModal from '../modal/DownloadDocumentModal/DownloadDocumentModal';
import { PLAYER_INFO_EDIT_UPLOAD_MODAL } from '../../store/actionTypes/player/playerAction';
import { isTabDevice } from '../../config/appConfig';

const PlayerDocument = ({
  deleteItem,
  item,
  openEditModal,
  selectedUpdate,
  setSelectedUpdate,
  setUploadedFile,
  contentType,
  isVisible,
  isFileCountVisible = true,
  iconType,
  isEditMode,
  onDeleteHandler,
}) => {
  const PlayerCommonStyle = useStyles(customPlayerCommonStyle);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openDownloadModal, setOpenDownloadModal] = useState(false);
  const { dateTime, title, _id, document, documentTypeId, documentTypeLabel } =
    item;
  const dispatch = useDispatch();

  const { isUploadDocumentEditMode } = useSelector(state => state?.player);

  const showEditModal = () =>
    dispatch({
      type: PLAYER_INFO_EDIT_UPLOAD_MODAL,
      payload: true,
    });

  const { yearLastTwoDigit, month, date, dateString } =
    dateTimeConversion(dateTime);

  const { userData } = useSelector(state => state?.auth);

  const isPlayer = userRoleType.PLAYER === userData?.type;

  const cancelAction = () => {
    setOpenDeleteModal(false);
  };

  const deleteAction = () => {
    selectedUpdate && onDeleteHandler(selectedUpdate?._id);
    setSelectedUpdate(null);
  };

  const downloadDocumentHandler = () => {
    setOpenDownloadModal(true);
  };

  return (
    <>
      <TouchableWithoutFeedback onPress={() => setSelectedUpdate(_id)}>
        <View style={PlayerCommonStyle.highlightColored}> 
          <View style={PlayerCommonStyle.contain}>
            <View style={PlayerCommonStyle.primary}>
              <View style={PlayerCommonStyle.dateView}>
                <Text style={PlayerCommonStyle.dateText}>
                  {date}/{month}/{yearLastTwoDigit}
                </Text>
                {!isTabDevice() && (
                  <>
                    <View style={{width: 1, height: '100%', marginRight: 10, backgroundColor: 'grey'}}></View>
                    <Text
                      style={PlayerCommonStyle.msgType}
                      numberOfLines={1}
                      ellipsizeMode="end"
                    >
                      {documentTypeLabel}
                    </Text>
                  </>
                )}
              </View>
              <View style={PlayerCommonStyle.msgWrapper}>
                <Text
                  style={PlayerCommonStyle.msg}
                  numberOfLines={2}
                  ellipsizeMode="end"
                >
                  {title}
                </Text>
              </View>
              {isTabDevice() && (
                <View style={PlayerCommonStyle.msgTypeWrapper}>
                  <Text
                    style={PlayerCommonStyle.msgType}
                    numberOfLines={1}
                    ellipsizeMode="end"
                  >
                    {documentTypeLabel}
                  </Text>
                </View>
              )}
            </View>

            <View>
              <RenderColumnItems
                item={item}
                isEditMode={isEditMode}
                setOpenDeleteModal={setOpenDeleteModal}
                downloadDocumentHandler={downloadDocumentHandler}
                openEditModal={showEditModal}
                setSelectedItem={setSelectedUpdate}
              />
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
      {openDownloadModal && (
        <DownloadDocumentModal
          item={item || null}
          setOpenDownloadModal={setOpenDownloadModal}
          contentType={contentType || ''}
          isDownloadTileVisible={isFileCountVisible}
        />
      )}

      {openDeleteModal && (
        <DeleteModal
          submitAction={deleteAction}
          cancelAction={cancelAction}
          message={'Are you sure you want to delete?'}
        />
      )}
    </>
  );
};

export default PlayerDocument;
