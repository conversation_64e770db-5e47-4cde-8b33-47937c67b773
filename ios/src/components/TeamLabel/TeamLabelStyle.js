import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TeamLabelStyle = colors => ({
  container: {},
  item: {
    marginTop: hp('2.5%'),
    marginBottom: hp('2.5%'),
    marginRight: wp('5%'),
    marginLeft: wp('0.5%'),
  },
  title: {
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
    color: colors.lightGrey,
  },
  titleSelected: {
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
    color: colors.green,
  },
  flatList: isTabDevice()
    ? {
        marginLeft: hp('3%'),
        zIndex: 5,
      }
    : {
        marginLeft: hp('2%'),
        zIndex: 5,
        flex: 1,
      },
  teamChildrenWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        paddingBottom: wp('1%'),
      }
    : {
        flexDirection: 'column',
        paddingBottom: wp('1%'),
      },
  teamChildrenOnlyWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        paddingBottom: wp('1%'),
      }
    : {
        flexDirection: 'row',
        paddingBottom: wp('1%'),
      },
  teamChildrenWrapperLeft: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('1%'),
        flex: 1,
      }
    : {
        paddingTop: wp('2%'),
        paddingLeft: wp('5%'),
        paddingRight: wp('5%'),
        paddingBottom: wp('2%'),
        width: wp('100%'),
      },
  teamChildrenWrapperRight: isTabDevice()
    ? {
        borderLeftWidth: 1,
        borderLeftColor: colors.borderBlue,
        paddingLeft: wp('2%'),
        flex: 2,
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingBottom: wp('2%'),
        width: wp('100%'),
      },
  teamWrapperRight: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: wp('2%'),
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingBottom: wp('2%'),
        width: wp('100%'),
      },
  teamRightDrowndownView :{
    width: wp('20%'),
    marginRight: 10
  },
  teamList: {
    width: '75%'
  },
  titleLabel: {
    width: wp('3%'),
    height: hp('1.3%'),
    borderRadius: wp('1%'),
    marginTop: wp('0.5%'),
  },
  dropdownView: isTabDevice()
    ? {
        width: wp('20%'),
      }
    : {
        width: wp('100%'),
        height: wp('11%'),
        paddingRight: wp('4%'),
      },
  dropdown: isTabDevice()
    ? {
        justifyContent: 'flex-start',
        backgroundColor: colors.darkBlue,
      }
    : {
        justifyContent: 'flex-start',
        backgroundColor: colors.borderBlue,
      },
  dropdownSelected: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginBottom: hp('20%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        marginBottom: hp('20%'),
      },
  dropdownSelectedPlaceholder: {
    color: colors.lightGrey,
    fontSize: wp('1.2%'),
  },
  dropdownSelectedContainer: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        height: hp('5%'),
      }
    : {
        height: wp('11%'),
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        marginTop: wp('-1%'),
        borderRadius: wp('100%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        marginTop: hp('1%'),
        borderRadius: wp('100%'),
        position: 'absolute',
        top: 0,
        zIndex: 999,
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
      },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.2%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
      },
  dropdownTopArea: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderColor: colors.darkBlue,
        borderRadius: wp('100%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderColor: colors.borderBlue,
        borderRadius: wp('100%'),
      },
  arrowStyle: isTabDevice()
    ? {}
    : {
        // fontSize: wp('10%')
      },
});
export default TeamLabelStyle;
