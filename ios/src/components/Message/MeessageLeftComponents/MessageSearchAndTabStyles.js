import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MessageSearchAndTabStyles = colors => ({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  toggleBar: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: wp('1%'),
        borderRadius: wp('100%'),
        overflow: 'hidden',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
        borderRadius: wp('100%'),
        overflow: 'hidden',
      },
  toggleText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        textAlign: 'center',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        textAlign: 'center',
      },
  toggleTextSelected: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1%'),
        textAlign: 'center',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        textAlign: 'center',
      },
  toggleCount: isTabDevice()
    ? {
        fontSize: wp('0.9%'),
        color: colors.white,
        textAlign: 'center',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        textAlign: 'center',
      },
  toggleCountWrapper: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        minHeight: wp('1.2'),
        minWidth: wp('1.6'),
        padding: wp('0.3%'),
        position: 'absolute',
        top: 5,
        right: 5,
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        minHeight: wp('5%'),
        minWidth: wp('6%'),
        padding: wp('1%'),
        textAlign: 'center',
        position: 'absolute',
        top: 5,
        right: 10,
      },
  selectionText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1%'),
        backgroundColor: colors.tileBackground,
        width: wp('10.8%'),
        textAlign: 'center',
        padding: wp('0.7%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        backgroundColor: colors.tileBackground,
        width: wp('45%'),
        textAlign: 'center',
        padding: wp('2%'),
      },
  selectedText: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1%'),
        backgroundColor: colors.lightBlue,
        width: wp('10.8%'),
        textAlign: 'center',
        padding: wp('0.7%'),
        position: 'relative',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        backgroundColor: colors.lightBlue,
        width: wp('45%'),
        textAlign: 'center',
        padding: wp('2%'),
        position: 'relative',
      },
  listSingleMessage: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        marginBottom: wp('1%'),
        padding: wp('0.5%'),
        borderRadius: 10,
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        marginBottom: wp('1%'),
        padding: wp('3  %'),
        borderRadius: 10,
      },
  listSingleMessageSelected: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        marginBottom: wp('1%'),
        padding: wp('0.5%'),
        borderRadius: 10,
      }
    : {
        backgroundColor: colors.tileBackground,
        marginBottom: wp('1%'),
        padding: wp('3%'),
        borderRadius: 10,
      },
  listSingleMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listSingleMessageLeft: isTabDevice()
    ? {
        marginRight: wp('1%'),
      }
    : {
        marginRight: wp('3%'),
      },
  listSingleMessageRight: {},
  listSingleMessageImage: isTabDevice()
    ? {
        borderRadius: 1000,
        width: wp('4%'),
        height: wp('4%'),
        resizeMode: 'cover',
        position: 'relative',
      }
    : {
        borderRadius: 1000,
        width: wp('10%'),
        height: wp('10%'),
        resizeMode: 'cover',
        position: 'relative',
      },
  listSingleMessageUserName: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.white,
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
      },
  listSingleMessageBody: isTabDevice()
    ? {
        fontSize: wp('1%'),
        color: colors.lightGrey,
      }
    : {
        fontSize: wp('3%'),
        color: colors.lightGrey,
      },
  listSingleMessageUnreadCountWrapper: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('100%'),
        minHeight: wp('1.2'),
        minWidth: wp('1.6'),
        padding: wp('0.3%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: 0,
        right: 0,
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('100%'),
        minHeight: wp('5%'),
        minWidth: wp('5.5%'),
        padding: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: 0,
        right: 0,
      },
  listSingleMessageUnreadCount: isTabDevice()
    ? {
        fontSize: wp('0.8%'),
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
      },
  search: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        color: colors.white,
        padding: wp('1.5%'),
        paddingTop: wp('1%'),
        paddingRight: wp('3%'),
        marginBottom: wp('1%'),
        width: '100%',
        borderRadius: 10,
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        color: colors.white,
        padding: wp('3%'),
        paddingTop: wp('3%'),
        paddingRight: wp('8%'),
        marginBottom: wp('2%'),
        width: '100%',
        fontSize: wp('4%'),
        borderRadius: 10,
      },
  searchContainer: {
    width: '100%',
    position: 'relative',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  searchIcon: isTabDevice()
    ? {
        position: 'absolute',
        top: '-7%',
        marginTop: wp('1%'),
        right: '2%',
      }
    : {
        position: 'absolute',
        top: '-7%',
        marginTop: wp('3%'),
        right: '4%',
      },
});

export default MessageSearchAndTabStyles;
