import React, { FC, useState, useEffect } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import { isTabDevice } from '../../../config/appConfig';
import { singleMessageTypes } from '../../../constants/constants';
import { stringLength } from '../../../helpers';
import useStyles from '../../../hooks/useStyles';
import customMessageSearchAndTabStyles from '../MeessageLeftComponents/MessageSearchAndTabStyles';
import { RootStore } from '../../../store/store';
import useGeneratedFileUrl from '../../../hooks/useGeneratedFileUrl';
import CacheImage from '../../CacheImage/CacheImage';

type SingleUserMessageBoxProps = {
  item: any | null;
  messageClicked: Function;
  selectedMessage?: string | null;
  isSelectedPersonal: boolean;
};

const SingleUserMessageBox: FC<SingleUserMessageBoxProps> = ({
  item,
  messageClicked,
  selectedMessage,
  isSelectedPersonal,
}) => {
  const MessageSearchAndTabStyles = useStyles(customMessageSearchAndTabStyles);
  const { unreadMessageCount } = useSelector(
    (state: RootStore) => state?.message
  );
  const { img: personalProfileImg, image: teamProfileImg } = item || {};
  const [image, setImage] = useState<any>(null);
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  
  const content = item?.deleted ? "This message was deleted" : item?.lastMessage

  useEffect(() => {
    setImage(null);
  }, [isSelectedPersonal]);

  useEffect(() => {
    setImage(null);
  }, [JSON.stringify(teamProfileImg), personalProfileImg]);

  useEffect(() => {
    url && !isDownloading && setImage(url);
  }, [url, isDownloading]);

  //set image for personal chat
  useEffect(() => {
    personalProfileImg && setImage(personalProfileImg);
  }, [item]);

  useEffect(() => {
    if (!isSelectedPersonal && teamProfileImg) {
      s3FileObject({
        bucketName: teamProfileImg?.bucketName,
        fileKey: teamProfileImg?.fileKey,
      });
    }
  }, [JSON.stringify(teamProfileImg), isSelectedPersonal]);

  return (
    <View
      //need to check id
      style={
        selectedMessage === item?.id
          ? MessageSearchAndTabStyles.listSingleMessageSelected
          : MessageSearchAndTabStyles.listSingleMessage
      }
    >
      <TouchableOpacity
        onPress={() => messageClicked(item)}
        style={MessageSearchAndTabStyles.listSingleMessageContainer}
      >
        <View style={MessageSearchAndTabStyles.listSingleMessageLeft}>
          <View style={MessageSearchAndTabStyles.listSingleMessageImageWrapper}>
            <CacheImage
              style={MessageSearchAndTabStyles.listSingleMessageImage}
              uri={image}
            />
          </View>
        </View>
        <View style={MessageSearchAndTabStyles.listSingleMessageRight}>
          <View style={MessageSearchAndTabStyles.test}>
            <Text style={MessageSearchAndTabStyles.listSingleMessageUserName}>
              {item?.firstName || item?.lastName
                ? stringLength(
                    `${item?.firstName || ''} ${item?.lastName || ''}`,
                    isTabDevice() ? 23 : 30
                  )
                : ''}
            </Text>
          </View>
          {item?.type === singleMessageTypes.USER_PAST_MESSAGE_VIEW && (
            <Text style={MessageSearchAndTabStyles.listSingleMessageBody}>
              {stringLength(content, isTabDevice() ? 23 : 25)}
            </Text>
          )}
          {item?.type ===
            singleMessageTypes.USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE && (
            <>
              <Text style={MessageSearchAndTabStyles.listSingleMessageBody}>
                {stringLength(content, isTabDevice() ? 23 : 25)}
              </Text>
            </>
          )}
        </View>
        {item?.type ===
          singleMessageTypes.USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE &&
        unreadMessageCount?.[item?._id]?.count ? (
          <>
            <View
              style={
                MessageSearchAndTabStyles.listSingleMessageUnreadCountWrapper
              }
            >
              <Text
                style={MessageSearchAndTabStyles.listSingleMessageUnreadCount}
              >
                {unreadMessageCount?.[item?._id]?.count || 0}
              </Text>
            </View>
          </>
        ) : undefined}
      </TouchableOpacity>
    </View>
  );
};

export default SingleUserMessageBox;
