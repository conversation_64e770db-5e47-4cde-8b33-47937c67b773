import {
  AntDesign,
  Entypo,
  FontAwesome5,
  MaterialCommunityIcons,
} from '@expo/vector-icons';
import React, { FC, useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import { isTabDevice } from '../../../../config/appConfig';
import useStyles from '../../../../hooks/useStyles';
import {
  createNewMessageUsersType,
  s3Object,
} from '../../../../store/reducers/Message/MessageReducer';
import SelectFileImageModal from '../../../modal/SelectFileImageModal/SelectFileImageModal';
import customMessageStyles from '../../MeessageRightComponents/MessageRightStyles';
import MessageFooterReplyComponent from './MessageFooterReplyComponent';

type MessageFooterComponentProps = {
  sendMsg: () => void;
  setMsgContent: (text: string) => void;
  msgContent: string;
  disableSendMessage: boolean;
  isReplyMode: boolean;
  selectedMsgContent: any;
  setSelectedMsgContent: Function;
  setIsReplyMode: Function;
  pastMessageChatMemberInfo: createNewMessageUsersType[] | null;
  setMsgFile: (file: any) => void;
  msgFile: { s3fileObject: s3Object } | null;
  mainSelectedChatId: string | null;
  isChatValid: boolean;
};

const MessageFooterComponent: FC<MessageFooterComponentProps> = ({
  sendMsg,
  setMsgContent,
  msgContent,
  disableSendMessage,
  isReplyMode,
  setIsReplyMode,
  selectedMsgContent,
  pastMessageChatMemberInfo,
  setSelectedMsgContent,
  setMsgFile,
  msgFile,
  mainSelectedChatId,
  isChatValid,
}) => {
  const MessageRightStyles = useStyles(customMessageStyles);
  const [isAttachModalOpen, setIsAttachModalOpen] = useState(false);
  const inputRef = React.useRef<TextInput>(null);
  return (
    <>
      {isAttachModalOpen && (
        <SelectFileImageModal
          onClose={setIsAttachModalOpen}
          callback={setMsgFile}
          mainSelectedChatId={mainSelectedChatId}
        />
      )}
      {isReplyMode && (
        <MessageFooterReplyComponent
          selectedMsgContent={selectedMsgContent}
          setSelectedMsgContent={setSelectedMsgContent}
          pastMessageChatMemberInfo={pastMessageChatMemberInfo}
          setIsReplyMode={setIsReplyMode}
        />
      )}

      {msgFile && (
        <View style={MessageRightStyles.MessageFooterUploadSection}>
          <View style={MessageRightStyles.MessageFooterUploadWrapper}>
            <View style={MessageRightStyles.MessageFooterUploadContainer}>
              <FontAwesome5
                name="file-export"
                size={isTabDevice() ? 18 : 15}
                color="white"
              />
              <Text style={MessageRightStyles.MessageFooterUploadText}>
                {msgFile?.s3fileObject?.fileName}
              </Text>
            </View>
            <TouchableOpacity onPress={() => setMsgFile(null)}>
              <AntDesign
                name="close"
                size={isTabDevice() ? 22 : 20}
                color="white"
              />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {!disableSendMessage && isChatValid ? (
        <View style={MessageRightStyles.MessageFooterContainer}>
          <TouchableOpacity onPress={() => setIsAttachModalOpen(true)}>
            <Entypo
              name="attachment"
              size={isTabDevice() ? 30 : 20}
              color="#c3c5c8"
              style={MessageRightStyles.icon}
            />
          </TouchableOpacity>
          <TextInput
            style={MessageRightStyles.MessageFooterMessageBox}
            onChangeText={Value => setMsgContent(Value)}
            placeholder="Type your message"
            placeholderTextColor="#fff"
            value={msgContent}
            ref={inputRef}
            multiline={true}
            numberOfLines={5}
          />
          {msgContent?.length ? (
            <TextInput
              selectTextOnFocus={false}
              onFocus={() => {
                inputRef.current?.focus();
                !(!msgContent.length ? (msgFile ? false : true) : false) &&
                  sendMsg();
              }}
              value={''}
              style={MessageRightStyles.MessageFooterSendButton}
              caretHidden={true}
            >
              <MaterialCommunityIcons
                name="send"
                size={isTabDevice() ? 38 : 30}
                color="#36d982"
              />
            </TextInput>
          ) : (
            <TouchableOpacity
              onPress={() => sendMsg()}
              disabled={!msgContent.length ? (msgFile ? false : true) : false}
            >
              <MaterialCommunityIcons
                name="send"
                size={isTabDevice() ? 38 : 30}
                color="#36d982"
                style={MessageRightStyles.icon2}
              />
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <></>
      )}
    </>
  );
};

export default MessageFooterComponent;
