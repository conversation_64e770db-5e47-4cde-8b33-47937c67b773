import React from 'react';
import { Linking, Text, View , TouchableOpacity} from 'react-native';
import useStyles from '../../../../hooks/useStyles';
import customMessageStyles from '../../MeessageRightComponents/MessageRightStyles';

const linkRegex = /(https?:\/\/[^\s]+)|(www\.[^\s]+)/g;
const wwwRegex = /www\.[^\s]+/g;

const CustomMessageText = ({ text }: { text: string }) => {
  const MessageRightStyles = useStyles(customMessageStyles);
  const extractLinks = (str: string) => {
    return str.match(linkRegex);
  };

  const links = extractLinks(text);

  if (!links) return <Text style={MessageRightStyles.sentMessage}>{text}</Text>;

  const parts = text.split(linkRegex);

  const renderTextWithLinks = () => {
    return parts.map((part, index) => {
      if (links.includes(part)) {
        return (
          <TouchableOpacity
            key={index}
            onPress={() => {
              if (part.startsWith("http://") || part.startsWith("https://")) {
                Linking.openURL(part);
            } else if (part.match(wwwRegex)) {
                Linking.openURL(`https://${part}`);
            } else {
                Linking.openURL(part);
            }
            }}
          >
            <Text
              style={{
                ...MessageRightStyles.sentMessage,
                color: 'white',
                textDecorationLine: 'underline',
              }}
            >
              {part}
            </Text>
          </TouchableOpacity>
        );
      }
      return (
        <Text style={MessageRightStyles.sentMessage} key={index}>
          {part}
        </Text>
      );
    });
  };

  return (
    <View
      style={{ flexDirection: 'row', flexWrap: 'wrap', alignItems: 'baseline' }}
    >
      {renderTextWithLinks()}
    </View>
  );
};

export default CustomMessageText;
