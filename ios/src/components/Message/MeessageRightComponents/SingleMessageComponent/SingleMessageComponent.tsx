import React, { FC, useCallback, useEffect, useState } from 'react';
import { Image, Linking, Pressable, Text, View } from 'react-native';
import { TouchableOpacity as GHTouchableOpacity } from 'react-native-gesture-handler';
import {
  FileContentType,
  messageContentType,
} from '../../../../constants/constants';
import { dateTimeConversion } from '../../../../helpers';
import { dateFormatConvert } from '../../../../helpers/DateHelper';
import useGeneratedFileUrl from '../../../../hooks/useGeneratedFileUrl';
import useStyles from '../../../../hooks/useStyles';
import {
  createNewMessageUsersType,
  IrepliedMessageContentType,
  singleMessageContentTypes,
} from '../../../../store/reducers/Message/MessageReducer';
import customMessageStyles from '../../MeessageRightComponents/MessageRightStyles';
import CacheImage from '../../../CacheImage/CacheImage';
import CustomMessageText from './CustomMessageText';
import MessageWasDeletedContent from './MessageWasDeletedContent';

type SingleMessageComponentProps = {
  data: singleMessageContentTypes;
  onLongPressHandler: Function;
  chatMembersInfo: createNewMessageUsersType[] | null;
  repliedContent: any;
  onMediaPreview: Function;
};
const SingleMessageComponent: FC<SingleMessageComponentProps> = ({
  data: msg,
  onLongPressHandler,
  chatMembersInfo,
  repliedContent,
  onMediaPreview,
}) => {
  const [s3FileObject, url, isDownloading] = useGeneratedFileUrl();
  const [s3FileObjectPreviewImage, urlPreview, isDownloadingPreview] =
    useGeneratedFileUrl();
  const [fileType, setFileType] = useState<string | null>(null);

  const {
    isUserOwnMessage,
    content,
    senderUserId,
    sentDate,
    repliedForMessageId,
    forwardedMessageId,
    image,
    video,
    file,
    type,
    previewImage,
    previewVideoImage,
    deleted,
  } = msg || {};

  const fileContent = image || video || file;

  const chatMemberData: createNewMessageUsersType | null = senderUserId?.length
    ? chatMembersInfo?.find(data => data?.id === senderUserId) || null
    : null;
  const { hours12, minutesString, amPm } = dateTimeConversion(sentDate!);
  const MessageRightStyles = useStyles(customMessageStyles);
  
  const messageDeleted = deleted ? true : false

  const filterRepliedMessageContent = () => {
    const repliedMsgContent = repliedContent?.find(
      (item: IrepliedMessageContentType) => item?._id === repliedForMessageId
    );

    if(repliedMsgContent?.deleted){
      return <MessageWasDeletedContent/>
    }
    
    return repliedMsgContent?.type === messageContentType.TEXT
      ? repliedMsgContent?.content
      : repliedMsgContent?.[repliedMsgContent?.type?.toLowerCase()]?.fileName;
  };

  const renderForwardMessageText = () =>
    forwardedMessageId ? (
      <Text style={MessageRightStyles.forwardedMessage}>Forwarded Message</Text>
    ) : null;

  const renderReplyMessage = () =>
    repliedForMessageId?.length ? (
      <View style={MessageRightStyles.sentMessageFowardWrapper}>
        <Text style={MessageRightStyles.sentMessageFoward}>
          {filterRepliedMessageContent()}
        </Text>
      </View>
    ) : null;

  const [isDownloadStared, setIsDownloadStarted] = useState(false);
  const downloadFile = (url: string) => {
    Linking.openURL(url);
  };
  useEffect(() => {
    url && downloadFile(url);
  }, [url]);

  useEffect(() => {
    if (type === messageContentType?.FILE) {
      getFileType(fileContent);
    }
  }, [fileContent, type]);

  const getFileType = (fileContent: any) => {
    const fileNameSplit = fileContent?.fileName?.split('.');

    switch (fileNameSplit?.[1]) {
      case FileContentType?.PDF:
        setFileType(FileContentType?.PDF);
        break;
      case FileContentType?.PPT:
        setFileType(FileContentType?.PPT);
        break;
      case FileContentType?.DOC:
      case FileContentType?.DOCX:
        setFileType(FileContentType?.DOC);
        break;
      case FileContentType?.XLS:
      case FileContentType?.XLSX:
        setFileType(FileContentType?.XLS);
        break;
      default:
        break;
    }
  };

  const getPreviewImage = useCallback(
    (key: string) => {
      switch (key) {
        case FileContentType?.PPT:
          return (
            <Image
              source={require('../../../../../assets/message/ppt_preview.png')}
              style={MessageRightStyles.downloadIcon}
            />
          );
        case FileContentType?.PDF:
          return (
            <Image
              source={require('../../../../../assets/message/pdf_preview.png')}
              style={MessageRightStyles.downloadIcon}
            />
          );
        case FileContentType?.DOC:
        case FileContentType?.DOCX:
          return (
            <Image
              source={require('../../../../../assets/message/doc_preview.png')}
              style={MessageRightStyles.downloadIcon}
            />
          );
        case FileContentType?.XLS:
        case FileContentType?.XLSX:
          return (
            <Image
              source={require('../../../../../assets/message/xls_preview.png')}
              style={MessageRightStyles.downloadIcon}
            />
          );
        case messageContentType.VIDEO:
          return (
            <CacheImage
              uri={urlPreview}
              style={
                urlPreview
                  ? MessageRightStyles.previewMedia
                  : MessageRightStyles.downloadIcon
              }
              customLoaderStyle={MessageRightStyles.downloadIcon}
              defaultPicture={require('../../../../../assets/message/video_preview.png')}
              isVideoPreview
            />
          );
        case messageContentType.IMAGE:
          return (
            <CacheImage
              uri={urlPreview}
              style={
                urlPreview
                  ? MessageRightStyles.previewMedia
                  : MessageRightStyles.downloadIcon
              }
              customLoaderStyle={MessageRightStyles.downloadIcon}
              defaultPicture={require('../../../../../assets/message/image_preview.png')}
            />
          );

        default:
          break;
      }
    },
    [type, fileType, urlPreview]
  );
  useEffect(() => {
    if (previewImage) {
      s3FileObjectPreviewImage({
        ...({
          fileKey: previewImage?.fileKey,
          bucketName: previewImage?.bucketName,
        } || {}),
      });
    }
  }, [previewImage]);

  useEffect(() => {
    if (previewVideoImage) {
      s3FileObjectPreviewImage({
        ...({
          fileKey: previewVideoImage?.fileKey,
          bucketName: previewVideoImage?.bucketName,
        } || {}),
      });
    }
  }, [previewVideoImage]);

  const msgFileContent = (isSent: boolean, type: string) => {
    return isSent ? (
      <>
        <View style={MessageRightStyles.downloadWrapperType2}>
          <GHTouchableOpacity
            onPress={() => {
              if (type === messageContentType.FILE) {
                setIsDownloadStarted(true);
                s3FileObject({
                  ...({
                    fileKey: fileContent?.fileKey,
                    bucketName: fileContent?.bucketName,
                  } || {}),
                });
              } else if (
                type === messageContentType.IMAGE ||
                type === messageContentType.VIDEO
              ) {
                onMediaPreview(
                  fileContent,
                  chatMemberData,
                  {
                    hours12: hours12,
                    minutesString: minutesString,
                    amPm: amPm,
                    sendDate: dateFormatConvert(sentDate),
                  },
                  type
                );
              }
            }}
            style={MessageRightStyles.downloadIconWrapper}
          >
            {type === messageContentType.FILE
              ? getPreviewImage(fileType || '')
              : getPreviewImage(type)}
          </GHTouchableOpacity>

          <Text style={MessageRightStyles.downloadIconText}>
            {fileContent?.fileName}
          </Text>
        </View>
      </>
    ) : (
      <>
        <View style={MessageRightStyles.downloadWrapperType2}>
          <GHTouchableOpacity
            style={MessageRightStyles.downloadIconWrapper}
            onPress={() => {
              if (type === messageContentType.FILE) {
                setIsDownloadStarted(true);
                s3FileObject({
                  ...({
                    fileKey: fileContent?.fileKey,
                    bucketName: fileContent?.bucketName,
                  } || {}),
                });
              } else if (
                type === messageContentType.IMAGE ||
                type === messageContentType.VIDEO
              ) {
                onMediaPreview(
                  fileContent,
                  chatMemberData,
                  {
                    hours12: hours12,
                    minutesString: minutesString,
                    amPm: amPm,
                    sendDate: dateFormatConvert(sentDate),
                  },
                  type
                );
              }
            }}
          >
            {type === messageContentType.FILE
              ? getPreviewImage(fileType || '')
              : getPreviewImage(type)}
            <Text style={MessageRightStyles.downloadIconText}>
              {fileContent?.fileName}
            </Text>
          </GHTouchableOpacity>
        </View>
      </>
    );
  };

  return isUserOwnMessage ? (
    <Pressable
      style={MessageRightStyles.sentMessageContainer}
      onLongPress={() => !messageDeleted && onLongPressHandler(msg)}
    >
      {
        messageDeleted ?
          <MessageWasDeletedContent /> :
          <View style={MessageRightStyles.sentMessageWrapper}>
            {renderForwardMessageText()}
            {renderReplyMessage()}
            {fileContent && msgFileContent(true, type || '')}
            {content?.length ? (
              <View
                style={
                  forwardedMessageId && MessageRightStyles.sentMessageFowardWrapper
                }
              >
                <CustomMessageText text={content} />
              </View>
            ) : null}
          </View>}
      <View style={MessageRightStyles.sentMessageDetails}>
        <Text style={MessageRightStyles.sentMessageDetailsText}>
          {hours12 || ''}
          {'.'}
          {minutesString || ''} {amPm || ''}
        </Text>
        <Text style={MessageRightStyles.sentMessageDetailsText}>
          {' '}
          {dateFormatConvert(sentDate)}
        </Text>
      </View>
    </Pressable>
  ) : (
    <Pressable
      style={MessageRightStyles.receivedMessageContainer}
      onLongPress={() => !messageDeleted && onLongPressHandler(msg)}
    >
        {messageDeleted ?
          <MessageWasDeletedContent /> :
          <View style={MessageRightStyles.receivedMessageWrapper}>
            {renderForwardMessageText()}
            {renderReplyMessage()}
            {fileContent && msgFileContent(false, type || '')}
            {content?.length ? (
              <>
                <CustomMessageText text={content} />
              </>
            ) : null}
          </View>
        }
      <View style={MessageRightStyles.receivedMessageDetailsWrapper}>
        <View style={MessageRightStyles.receivedMessageDetailsImage}>
          <CacheImage
            style={MessageRightStyles.receivedMessageDetailsPic}
            uri={chatMemberData?.profileImageUrl || ''}
          />
          <Text style={MessageRightStyles.receivedMessageDetailsName}>
            {chatMemberData?.firstName || ''} {chatMemberData?.lastName || ''}
          </Text>
        </View>
        <View style={MessageRightStyles.receivedMessageDetails}>
          <Text style={MessageRightStyles.receivedMessageDetailsText}>
            {' '}
            {hours12 || ''}
            {'.'}
            {minutesString || ''} {amPm || ''}
          </Text>
          <Text style={MessageRightStyles.receivedMessageDetailsText}>
            {' '}
            {dateFormatConvert(sentDate)}
          </Text>
        </View>
      </View>
    </Pressable>
  );
};

export default SingleMessageComponent;
