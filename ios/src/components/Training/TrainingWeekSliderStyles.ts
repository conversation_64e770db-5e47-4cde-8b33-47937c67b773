import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TrainingWeekSliderStyles = (colors : any) => ({
  weeksContainer: isTabDevice()
    ? {
        marginTop: wp('1%'),
        backgroundColor: colors.black,
        width: '95%',
        marginBottom: wp('1%'),
      }
    : {
        marginTop: wp('1%'),
        backgroundColor: colors.black,
        width: '103%',
        marginBottom: wp('3%'),
        marginLeft: wp('-3%'),
      },
  weeksWrapper: isTabDevice()
    ? {
        paddingTop: wp('0.8%'),
        paddingBottom: wp('0.8%'),
      }
    : {
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
      },
  weeks: isTabDevice()
    ? {
        marginRight: wp('3'),
        marginLeft: wp('3'),
      }
    : {
        marginRight: wp('3'),
        marginLeft: wp('4'),
      },
  weeksText: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      },
  weeksTextSelected: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        color: colors.green,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3%'),
        color: colors.green,
        fontFamily: 'Poppins-Regular',
      },
});
export default TrainingWeekSliderStyles;
