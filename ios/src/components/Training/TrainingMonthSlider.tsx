import React from 'react';
import { Text, View, FlatList, TouchableOpacity } from 'react-native';
import custommonthSliderStyles from './TrainingMonthSliderStyles';
import useStyles from '../../hooks/useStyles';

const TrainingMonthSlider = ({ months, setSelectedMonth, selectedMonth } : any) => {
  const monthSliderStyles = useStyles(custommonthSliderStyles);
  const month = ({ item } : any) => {
    const { monthName, index } = item;
    return (
      <TouchableOpacity onPress={() => setSelectedMonth(item)}>
        <View
          style={
            index === selectedMonth.index
              ? monthSliderStyles.selectedMonth
              : monthSliderStyles.month
          }
        >
          <Text style={monthSliderStyles.monthText}>{monthName}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={monthSliderStyles.monthSet}>
      <FlatList
        horizontal={true}
        data={months}
        renderItem={month}
        keyExtractor={item => item.monthName}
      />
    </View>
  );
};

export default TrainingMonthSlider;
