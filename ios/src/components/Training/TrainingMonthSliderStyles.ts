import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TrainingMonthSliderStyles = (colors : any) => ({
  monthSet: isTabDevice()
    ? {
        width: wp('67.4%'),
        // backgroundColor: colors.green,
      }
    : {
        width: wp('94%'),
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
      },
  selectedMonth: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        borderRadius: wp('2%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        borderRadius: wp('6%'),
      },
  month: isTabDevice()
    ? {
        padding: wp('1%'),
      }
    : {
        padding: wp('2%'),
      },
  monthText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Medium',
      },
});
export default TrainingMonthSliderStyles;
