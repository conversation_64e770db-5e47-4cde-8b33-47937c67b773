import React from 'react';
import { Text, View, FlatList, TouchableOpacity } from 'react-native';
import useStyles from '../../hooks/useStyles';
import customWeekSliderStyles from './TrainingWeekSliderStyles';
import { TouchableHighlight } from 'react-native-gesture-handler';

const TrainingWeekSlider = ({
  selectedMonthCalendar,
  onSelectedWeek,
  selectedWeekIndex,
  year,
  month,
}: any) => {
  const weekSliderStyles = useStyles(customWeekSliderStyles); 

  const { calendar } = selectedMonthCalendar;

  const addLeadingZeros = (value : number) => {
    return value < 10 ? '0' + value : value;
  };

  const renderWeek = ({ item, index } : any) => { 
    return (
      <View style={weekSliderStyles.weeksWrapper}>
        <TouchableHighlight onPress={() => onSelectedWeek(item)}>
          <View style={weekSliderStyles.weeks}>
            <Text
              style={
                selectedWeekIndex ==
                Number(`${year}${addLeadingZeros(month)}${item.week}`)
                  ? weekSliderStyles.weeksTextSelected
                  : weekSliderStyles.weeksText
              }
            >
              Week {item.week + 1}
            </Text>
          </View>
        </TouchableHighlight>
      </View>
    );
  };

  
  return (
    <View style={weekSliderStyles.weeksContainer}>
      <FlatList
        horizontal={true}
        data={calendar}
        renderItem={renderWeek}
        keyExtractor={(item, index) => index.toString()}
      />
    </View>
  );
};

export default TrainingWeekSlider;
