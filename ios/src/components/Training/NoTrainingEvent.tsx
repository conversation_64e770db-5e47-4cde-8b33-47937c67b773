import React from 'react';
import { Text, View } from 'react-native';
import customNoTrainingEventStyle from './NoTrainingEventStyle';
import useStyles from '../../hooks/useStyles';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { RootStore } from '../../store/store';
import { userRoleType } from '../../constants/constants';

const NoTrainingEvent = () => {
  const NoTrainingEventStyle = useStyles(
    customNoTrainingEventStyle
  );
  const navigation = useNavigation();

  const { userRole } = useSelector((state: RootStore) => state.auth);
  const isCoach: boolean = userRole === userRoleType.COACH
  const isHeadCoach: boolean = userRole === userRoleType.HEAD_COACH;
  

  return (
    <View style={NoTrainingEventStyle.noScheduledWrapper}>
      <Text style={NoTrainingEventStyle.noScheduledWrapperTextMain}>
        You do not have any training sessions scheduled at the moment.
      </Text>

      {(isHeadCoach || isCoach) && (
        <>
          <Text style={NoTrainingEventStyle.noScheduledWrapperTextSub}>
          To schedule a Training session, please click on the 'Planner' button below.
          </Text>
          <TouchableOpacity
            style={NoTrainingEventStyle.noScheduledWrapperButton}
            onPress={() => [navigation.navigate('PlannerScreen')]}
          >
            <Text
              style={NoTrainingEventStyle.noScheduledWrapperButtonText}
            >
              Planner
            </Text>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

export default NoTrainingEvent;
