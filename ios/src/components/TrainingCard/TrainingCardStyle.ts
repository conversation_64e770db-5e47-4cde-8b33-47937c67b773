import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TrainingCardStyle = (colors: any) => ({
  container: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: 'auto',
        paddingVertical: hp('2%'),
        paddingHorizontal: wp('1.4%'),
        borderRadius: 15,
        width: wp('22%'),
        margin: 'auto',
        marginBottom: wp('2%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        paddingVertical: hp('1.89% '),
        height: 'auto',
        paddingHorizontal: wp('2%'),
        borderRadius: 15,
        width: wp('42%'),
        marginHorizontal: wp('2%'),
        marginBottom: wp('2%'),
      },
  timeSection: {
    gap: 6,
  },
  timeText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2'),
        fontFamily: 'Poppins-Medium'
      }
    : { 
      color: colors.white, 
      fontSize: wp('3'),
      fontFamily: 'Poppins-Medium'
    },
  buttonSection: isTabDevice()
    ? {
        gap: 10,
        alignItems: 'center',
        marginTop: hp('2%'),
      }
    : {
        gap: 10,
        alignItems: 'center',
        marginTop: hp('2%'),
      },
  buttonContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        width: '100%',
        backgroundColor: '#0A1528',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: hp('1.4%'),
        paddingHorizontal: wp('1.46%'),
        borderRadius: 12,
      }
    : {
        flexDirection: 'row',
        backgroundColor: '#0A1528',
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: hp('1%'),
        paddingHorizontal: wp('3.5%'),
        gap: 15,
        borderRadius: 8,
      },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
      }
    : { 
      color: colors.white,
      fontSize: wp('2.5%')
    },
  buttonIcon: isTabDevice()
    ? {
        height: hp('4%'),
        width: wp('2.93%'),
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        height: hp('2%'),
        width: wp('7%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  attendanceButtonContainer: {
    flexDirection: 'row',
    backgroundColor: '#0A1528',
    width: '100%',
    alignItems: 'center',
    paddingVertical: hp('2%'),
    paddingHorizontal: wp('3.5%'),
    gap: 15,
    borderRadius: 12,
    justifyContent: 'space-between',
  },
});

export default TrainingCardStyle;
