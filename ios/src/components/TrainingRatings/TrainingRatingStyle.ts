
import {
    widthPercentageToDP as wp,
    heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig'
import { Positions } from 'react-native-calendars/src/expandableCalendar';

const TrainingRatingStyle = (colors: any) => ({
    container: {},
    overlay: {
        width: wp('100%'),
        height: hp('100%'),
        backgroundColor: colors.darkBlue,
        position: 'absolute',
        left: 0,
        top: 0,
        opacity: 0.95,
    },
    centeredView: isTabDevice()
        ? {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
        }
        : {
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
        },
    modalView: isTabDevice()
        ? {
            backgroundColor: "#23344B",
            borderRadius: wp('2%'),
            alignItems: 'center',
            height: hp('65%'),
            paddingVertical: hp("3.53%"),
            paddingHorizontal: wp("3.59%"),
            width: wp('65%'),
            position: 'relative'
        }
        : {
            backgroundColor: colors.borderBlue,
            borderRadius: wp('4%'),
            alignItems: 'center',
            width: wp('80%'),
            position: 'relative',
            maxHeight: hp('85%'),
            paddingVertical: hp("3.53%"),
            paddingHorizontal: wp("3.59%")
        },

    modalTitleContainer: isTabDevice() ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
    } : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',

    },
    header: isTabDevice()
        ? {
            fontSize: wp('1.7%'),
            color: colors.white,
            fontFamily: 'Poppins-Bold',
        } : {
            fontSize: wp('4.5%'),
            color: colors.white,
            fontFamily: 'Poppins-Bold',
        },
    closeButton: isTabDevice()
        ? {
            alignItems: 'center',
            justifyContent: 'center',
        }
        : {
            alignItems: 'center',
            justifyContent: 'center',
        },
    infoButton: {
        flexDirection: 'row',
        marginVertical: wp('2%'),
        alignSelf:'flex-end'
    },
    infoButtonText: isTabDevice() ? {
        fontSize: wp('1.2%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginRight: wp('1%')
    } : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginRight: wp('2%')
    },
    flatListContainer: isTabDevice() ? {
        width: '100%',
        marginTop: wp('3%'),
        height: hp('65%'),
    } : {
        width: '100%',
        height: hp('65%'),
        marginTop: wp('10%')
    },
    flatListContainerContent: isTabDevice() ? {
        alignItems: 'center'
    } : {},
    flatListContainerContent2: isTabDevice() ? {
        alignItems: 'flex-start'
    } : {},
    buttonContainer: isTabDevice() ? {
        flexDirection: 'row',
        width: wp('18%'),
        margin: 5,
        borderRadius: 10,
        paddingHorizontal: wp("2.05%"),
        paddingVertical: wp("2%"),
        justifyContent: 'flex-start',
        alignItems: 'center',
        gap: 15
    } : {
        flexDirection: 'row',
        width: wp('70%'),
        height: hp("6.05%"),
        margin: 5,
        borderRadius: 10,
        paddingHorizontal: wp("2.05%"),
        justifyContent: 'flex-start',
        alignItems: 'center',
        gap: 15
    },
    buttonContainer2: isTabDevice() ? {
        flexDirection: 'row',
        width: '100%',
        margin: 5,
        borderRadius: 10,
        paddingHorizontal: wp("2.05%"),
        paddingVertical: wp("0.5%"),
        justifyContent: 'flex-start',
        alignItems: 'center',
        gap: 15
    } : {
        flexDirection: 'row',
        width: wp('50%'),
        height: hp("6.05%"),
        margin: 5,
        borderRadius: 10,
        paddingHorizontal: wp("2.05%"),
        justifyContent: 'flex-start',
        alignItems: 'center',
        gap: 15
    },
    confirmButton: isTabDevice() ? {
        flexDirection: 'row',
        backgroundColor: colors.green,
        width: wp('40%'),
        height: hp("6.05%"),
        margin: 5,
        borderRadius: 15,
        paddingHorizontal: wp("2.05%"),
        justifyContent: 'center',
        alignItems: 'center',
        gap: 15
    } : {
        flexDirection: 'row',
        backgroundColor: colors.green,
        width: wp('70%'),
        height: hp("6.05%"),
        margin: 5,
        borderRadius: 15,
        paddingHorizontal: wp("2.05%"),
        justifyContent: 'center',
        alignItems: 'center',
        gap: 15
    },
    numberText: isTabDevice() ? {
        color: colors.white
    } : {
        color: colors.white

    },
    buttonText: isTabDevice() ? {
        fontSize: wp('1%'),
        color: colors.white,
        textAlign: 'left'
    } : {
        fontSize: wp('3%'),
        color: colors.white,
        textAlign: 'left'
    },
    backButtonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 15,
        width: '100%'
    },
    colorIndicatorBox: isTabDevice() ? {
        height: hp('5.69%%'),
        width: wp('3.59%%'),
        borderRadius: 8,
        alignItems: 'center',
        justifyContent : 'center'
    } : {
        height: hp('5.69%%'),
        width: wp('12.31%%'),
        borderRadius: 8,
        alignItems: 'center',
        justifyContent : 'center'
    }



})
export default TrainingRatingStyle