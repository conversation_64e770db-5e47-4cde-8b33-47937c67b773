import React from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { isTabDevice } from '../../config/appConfig';
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';
import useStyles from '../../hooks/useStyles';
import CustomTrainingRatingStyle from './TrainingRatingStyle';
import Ionicons from '@expo/vector-icons/Ionicons';

const RPEListStaticContent = ({ setIsShowIno }: { setIsShowIno: Function }) => {
  const TrainingRatingStyle = useStyles(CustomTrainingRatingStyle);
  const { RPEList } = useTrainingHook({
    isInitalComponent: false,
  });

  const renderInfo = ({ item }: any) => {
    const { labelColor, description, value ,textColor } = item;
    return (
      <View
        style={[
          TrainingRatingStyle.buttonContainer,TrainingRatingStyle.buttonContainer2 
        ]}
      >
        <View
          style={{
            ...TrainingRatingStyle.colorIndicatorBox,
            backgroundColor: labelColor,
          }}
        >
          <Text style={[TrainingRatingStyle.numberText,{color : textColor}]}>
            {value}
          </Text>
        </View>
        <Text
          style={{
            ...TrainingRatingStyle.buttonText,
            width : "auto"
          }}
        >
          {description} 
        </Text>
      </View>
    );
  };

  return (
    <View style={TrainingRatingStyle.modalView}>
      <View style={TrainingRatingStyle.backButtonContainer}>
        <TouchableOpacity onPress={() => setIsShowIno(false)}>         
          <Ionicons name="arrow-back-outline" size={24} color="white" />
        </TouchableOpacity>
        <Text style={{ ...TrainingRatingStyle.header}}>  
          Rate of perceived exertion
        </Text>
      </View> 

      <FlatList
        data={RPEList}
        renderItem={renderInfo}
        contentContainerStyle={[TrainingRatingStyle.flatListContainerContent,TrainingRatingStyle.flatListContainerContent2]} 
        keyExtractor={item => item._id.toString()}
        style={TrainingRatingStyle.flatListContainer} 
        // scrollEnabled={isTabDevice() ? false : true}    
      />
    </View>
  );
};

export default RPEListStaticContent;
