import * as FileSystem from 'expo-file-system';
import React, { FC, useEffect, useState } from 'react';
import { Image } from 'react-native';
import shorthash from 'shorthash';
import { AntDesign } from '@expo/vector-icons';

interface CacheImageProps {
  uri: string;
  style: any;
  isProfilePic?: boolean;
  defaultPicture?: any;
  customLoaderStyle?: any;
  isVideoPreview?: boolean;
}

const CacheImage: FC<CacheImageProps> = ({
  uri,
  style,
  isProfilePic = true,
  defaultPicture = require('../../../assets/profilepictures/default_coach.png'),
  customLoaderStyle = null,
  isVideoPreview = false,
}) => {
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [isImageLoaded, setImageLoaded] = useState(false);
  const [isError, setIserror] = useState(false);

  useEffect(() => {
    if (uri) {
      const name = shorthash.unique(uri?.split('?X-Amz')?.[0]);
      const path = `${FileSystem.cacheDirectory}${name}`;
      downloadImage(path);
    }
  }, [uri]);

  const downloadImage = async (path: string) => {
    const image = await FileSystem.getInfoAsync(path);
    if (image?.exists) {
      setImageUri(image?.uri);
      return;
    }
    const newImage = await FileSystem.downloadAsync(uri, path);
    setImageUri(newImage?.uri);
  };

  return isProfilePic ? (
    <>
      {isVideoPreview && imageUri && isImageLoaded && (
        <AntDesign
          name="play"
          size={45}
          color="white"
          style={{
            width: 50,
            height: 50,
            position: 'absolute',
            top: '40%',
            left: '42%',
            zIndex: 10,
            opacity: 0.8,
          }}
        />
      )}
      <Image
        style={
          uri && !isError
            ? !isImageLoaded || !imageUri
              ? customLoaderStyle || style
              : style
            : style
        }
        source={
          uri && !isError
            ? !isImageLoaded || !imageUri
              ? require('../../../assets/loader.gif')
              : { uri: uri }
            : defaultPicture
        }
        onError={() => setIserror(true)}
        onLoad={() => setImageLoaded(true)}
      />
    </>
  ) : (
    <Image
      style={style}
      source={{ uri: uri || '' }}
      onLoad={() => setImageLoaded(true)}
    />
  );
};
export default CacheImage;
