import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateStyle = colors => ({
  dateView: isTabDevice()
    ? {
        backgroundColor: 'green',
        height: wp('5%'),
        width: wp('10%'),
        borderRadius: 7,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: wp('1%'),
        marginTop: wp('0.5%'),
      }
    : {
        flexDirection: 'row',
        // backgroundColor: colors.white
      },
  dateText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        marginRight: wp('2%'),
        fontFamily: 'Poppins-Bold',
      },
  dateText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.green,
        fontSize: wp('3.2%'),
        fontFamily: 'Poppins-Medium',
      },
  msgWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('53%'),
      }
    : {
        width: wp('90%'),
      },
  msgWrapperSelected: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('28%'),
      }
    : {
        width: wp('44%'),
      },
  msg: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        textAlign: 'justify',
        fontFamily: 'Poppins-Regular',
        marginTop: wp('1.7%'),
      }
    : {
        fontSize: wp('3.2%'),
        color: colors.white,
        textAlign: 'justify',
        marginTop: wp('1%'),
        fontFamily: 'Poppins-Regular',
      },
  optionView: {
    display: 'flex',
    paddingLeft: 15,
    borderLeftWidth: 1,
    borderColor: colors.darkBlue,
    height: '100%',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
    marginLeft: -10,
  },
  reports: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        height: '100%',
        marginLeft: wp('-4%'),
      }
    : {
        marginLeft: wp('-8%'),
        width: wp('20%'),
      },
  reportsMedical: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        height: '100%',
        marginLeft: wp('-7%'),
        marginRight: wp('0.3%'),
        justifyContent: 'center',
      }
    : {
        marginLeft: wp('-8%'),
        width: wp('20%'),
      },
  report: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      },
  reportIcon: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        padding: 15,
        borderRadius: 8,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        padding: 15,
        borderRadius: 8,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('8%'),
        height: wp('8%'),
      },
  length: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: 8,
        marginLeft: 5,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: 10,
        marginTop: 5,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('8%'),
        height: wp('8%'),
      },
  lengthText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
      },
  editBtn: isTabDevice()
    ? {
        alignItems: 'center',
        flexDirection: 'row',
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        marginRight: wp('2%'),
      },
  editBtn2: isTabDevice()
    ? {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 5,
      }
    : {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 5,
      },
  btnText: isTabDevice()
    ? {
        color: colors.white,
        marginHorizontal: wp('1%'),
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        marginHorizontal: wp('1%'),
        fontSize: wp('2.5'),
        fontWeight: 'bold',
        fontFamily: 'Poppins-Regular',
      },
  highlight: isTabDevice()
    ? {
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        height: 90,
        marginBottom: hp('-0.5%'),
      }
    : {
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        height: 70,
        marginBottom: hp('-0.5%'),
      },
  highlightColored: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        // marginVertical: 5,
        width: '100%',
        height: 120,
        marginBottom: hp('-0.5%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: 10,
        marginVertical: 5,
        width: '100%',
        height: 90,
        marginBottom: hp('-0.5%'),
      },
  contain: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rightView: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
  },

  updateViewSelected: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '105%',
        marginTop: 8,
        width: wp('25%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '104%',
        marginTop: 3,
        width: wp('45%'),
      },
  rightViewSelected: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '105%',
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        marginTop: 8,
        width: wp('25%'),
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '104%',
        borderLeftWidth: 1,
        borderColor: colors.darkBlue,
        marginTop: 3,
        width: wp('45%'),
      },
  primary: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'flex-start',
        padding: wp('1%'),
      }
    : {
        flexDirection: 'column',
        padding: wp('3%'),
        width: wp('35%'),
      },
  icon: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
      }
    : {
        width: wp('7%'),
        height: wp('7%'),
      },
  iconImage: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
        marginTop: wp('-0.3%'),
        marginLeft: wp('-0.3%'),
      }
    : {
        width: wp('5%'),
        height: wp('5%'),
        marginTop: wp('-0.7%'),
        marginLeft: wp('-0.3%'),
      },
});
export default PlayerInfoUpdateStyle;
