import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const HeadStyle = colors => ({
  safeArea: {
    backgroundColor: colors.darkBlue,
  },
  container: {
    width: '100%',
  },
  img: isTabDevice()
    ? {
        width: wp('15%'),
        height: hp('8%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('30%'),
        height: hp('8%'),
        resizeMode: 'contain',
      },
  centerView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: wp('33%'),
        height: hp('7%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'center',
        width: wp('70%'),
        height: hp('7%'),
      },
  headerView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: hp('7%'),
        zIndex: 1000,
        paddingLeft: '3%',
        paddingRight: '3%',
        backgroundColor: '#0f1c2f',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: hp('7%'),
        zIndex: 1000,
        paddingLeft: '3%',
        paddingRight: '3%',
        backgroundColor: '#0f1c2f',
      },
  leftView: isTabDevice()
    ? {
        width: wp('31%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
      }
    : {
        width: wp('10%'),
        height: hp('7%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
      },
  logoWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  logo: {
    width: wp('13%'),
    height: hp('5%'),
    resizeMode: 'contain',
  },
  rightView: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        width: wp('33%'),
        paddingRight: wp('3%'),
        paddingTop: wp('0.7%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'flex-end',
        width: wp('10%'),
        paddingRight: wp('3%'),
        paddingTop: wp('0.7%'),
      },
  rightViewPlanner: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        width: wp('33%'),
        paddingRight: wp('3%'),
        paddingTop: wp('0.7%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'center',
        justifyContent: 'flex-end',
        width: wp('18%'),
        paddingRight: wp('3%'),
        paddingTop: wp('0.7%'),
      },
  profileNameView: {
    backgroundColor: colors.aquaBlue,
    borderRadius: wp('100%'),
    // height: hp('5%'),
    width: wp('12%'),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignContent: 'center',
    marginRight: hp('1%'),
  },
  reportView: {
    backgroundColor: colors.darkBlue,
    borderRadius: wp('100%'),
    // height: hp('5%'),
    // width: wp('20%'),
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginRight: hp('1%'),
  },
  logView: {
    backgroundColor: colors.green,
    borderRadius: wp('100%'),
    // height: hp('5%'),
    // width: wp('20%'),
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginRight: hp('1%'),
  },
  profileName: {
    color: colors.white,
    paddingLeft: hp('2%'),
    paddingRight: hp('2%'),
    paddingTop: hp('0.5%'),
    paddingBottom: hp('0.5%'),
    fontSize: hp('2%'),
    position: 'relative',
  },
  ongoingMatch: isTabDevice()
    ? {
        color: colors.white,
        paddingLeft: hp('0.5%'),
        paddingRight: hp('2%'),
        paddingTop: hp('0.5%'),
        paddingBottom: hp('0.5%'),
        fontSize: hp('2%'),
        position: 'relative',
      }
    : {
        color: colors.white,
        paddingLeft: wp('2%'),
        // paddingRight: hp('2%'),
        // paddingTop: hp('0.5%'),
        // paddingBottom: hp('0.5%'),
        fontSize: wp('2.5%'),
        position: 'relative',
      },
  profileImg: {
    height: hp('5%'),
    width: hp('5%'),
    borderRadius: wp('100%'),
  },
  parentText: {
    fontSize: hp('2%'),
    color: colors.white,
    marginRight: wp('0.5%'),
  },
  avatarParent: {
    height: hp('3%'),
    width: hp('3%'),
    borderRadius: wp('100%'),
    marginRight: wp('0.5%'),
  },
  teamChildrenAvatar: isTabDevice()
    ? {
        height: hp('8%'),
        width: hp('8%'),
        borderRadius: wp('100%'),
        marginRight: wp('0.5%'),
      }
    : {
        height: hp('6%'),
        width: hp('6%'),
        borderRadius: wp('100%'),
        marginRight: wp('0.5%'),
      },
  teamChildrenAvatarSelected: isTabDevice()
    ? {
        height: hp('8%'),
        width: hp('8%'),
        borderRadius: wp('100%'),
        borderWidth: 4,
        borderColor: colors.green,
        marginRight: wp('0.5%'),
      }
    : {
        height: hp('6%'),
        width: hp('6%'),
        borderRadius: wp('100%'),
        borderWidth: 4,
        borderColor: colors.green,
        marginRight: wp('0.5%'),
      },
  avatarWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        paddingLeft: hp('1%'),
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        borderBottomWidth: 1,
        borderBottomColor: colors.white,
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
      },
  notificationWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
      }
    : {
        alignItems: 'center',
        paddingTop: wp('1%'),
        paddingBottom: wp('1%'),
        paddingLeft: wp('0.5%'),
        marginBottom: wp('3%'),
      },
  notificationText: isTabDevice()
    ? {
        color: colors.white,
        marginBottom: wp('1%'),
        fontSize: wp('1.1%'),
        textAlign: 'center',
        width: '100%',
      }
    : {
        color: colors.white,
        marginRight: wp('0.5%'),
        fontSize: hp('1.5%'),
        marginBottom: wp('3%'),
      },
  notificationText2: isTabDevice()
    ? {
        color: colors.white,
        marginTop: wp('0.5%'),
        marginLeft: wp('0.5%'),
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        marginLeft: wp('2%'),
        fontSize: hp('1.5%'),
        marginTop: wp('2%'),
        fontFamily: 'Poppins-Bold',
      },
  notificationToggle: isTabDevice()
    ? {
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'center',
        width: '100%',
      }
    : {
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: 'center',
      },
  buttonIcon: isTabDevice()
    ? {}
    : {
        width: wp('9%'),
        height: wp('9%'),
        resizeMode: 'cover',
      },
  logout: {
    backgroundColor: colors.tileBackground,
    width: '100%',
    paddingTop: hp('1%'),
    paddingBottom: hp('1%'),
    position: 'absolute',
    top: hp('4%'),
    borderRadius: hp('2%'),
    borderWidth: 1,
    borderColor: colors.white,
    justifyContent: 'center',
    flexDirection: 'column',
  },
  logoutSelectionBorder: {
    borderBottomWidth: 1,
    borderBottomColor: colors.white,
  },
  changePassword: {
    paddingLeft: hp('1%'),
    paddingRight: hp('1%'),
    paddingBottom: hp('1%'),
    color: colors.white,
    fontSize: hp('2%'),
    backgroundColor: colors.Black,
  },
  logoutFeature: {
    paddingTop: hp('1%'),
    paddingLeft: hp('1%'),
    paddingRight: hp('1%'),
    color: colors.white,
    fontSize: hp('2%'),
  },
  backButton: isTabDevice()
    ? {
        marginTop: hp('1%'),
        fontSize: wp('3%'),
      }
    : {
        marginTop: hp('1%'),
        fontSize: wp('7%'),
      },
  matchPlanView: isTabDevice()
    ? {
        flexDirection: 'row',
        padding: wp('1%'),
        alignItems: 'center',
        marginTop: hp('1%'),
      }
    : {
        flexDirection: 'row',
        padding: wp('1%'),
        marginLeft: wp('2%'),
        alignItems: 'center',
        marginTop: hp('1%'),
        width: wp('15%'),
      },
  matchPlanViewButtons: {
    backgroundColor: colors.tileBackground,
    borderRadius: wp('100%'),
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    marginRight: hp('1%'),
  },
});
export default HeadStyle;
