import React, { useEffect } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  Image,
  FlatList,
  ScrollView,
} from 'react-native';

import editIcon from '../../../../assets/buttons/editIcon.png';
import deleteIcon from '../../../../assets/buttons/deleteIcon.png';
import customUserProfileTileStyle from './UserProfileTileStyle';
import ProfileImage from '../../../components/ProfileImage/ProfileImage';
import { useNavigation } from '@react-navigation/native';
import useStyles from '../../../hooks/useStyles';

const UserProfileTile = ({ item, selectedTeam, openUserDeleteModal, isParent, childrensName }) => {
  const UserProfileTileStyle = useStyles(customUserProfileTileStyle);
  const navigation = useNavigation();

  let { firstName, id, lastName, profileImageUrl, emailId, teamLabels } = item;   

  const renderItem = ({ item }) => (
    <Text key={item?._id} style={UserProfileTileStyle.teamTileText3}> 
      {item?.teamName} {' | '}
    </Text>
  );

  const listOfChildren = () => {
    return <Text style={UserProfileTileStyle.teamTileText3}>
      {childrensName || ''}
    </Text>
  }

  return (
    <View
      style={UserProfileTileStyle.teamTile}
      onStartShouldSetResponder={() => true}
    >
      <View style={UserProfileTileStyle.teamTileNameContainer}>
        <ScrollView horizontal={true}>
          <Text style={UserProfileTileStyle.teamTileText1}>
            {firstName} {lastName}
          </Text>
        </ScrollView>  
      </View>
      <View style={UserProfileTileStyle.teamTileContainer}>
        <View style={UserProfileTileStyle.colOne}>
          <View style={UserProfileTileStyle.listItemImageContainer}>
            <ProfileImage
              imageStyles={UserProfileTileStyle.listItemImage}
              style={UserProfileTileStyle.imageLoader}
              profileImageUrl={profileImageUrl}
            />
          </View>
        </View>
        <ScrollView style={UserProfileTileStyle.colTwo}>
          <ScrollView horizontal={true}>
            <Text style={UserProfileTileStyle.teamTileText2}>
              {emailId || ''}
            </Text>
          </ScrollView>
          {selectedTeam ? (
            <Text style={UserProfileTileStyle.teamTileText3}></Text>
          ) : teamLabels && Array.isArray(teamLabels) ? (
            <FlatList
              horizontal
              data={teamLabels}
              renderItem={renderItem}
              keyExtractor={item => item._id}
            />
          ) : (
            <>
              <Text style={UserProfileTileStyle.teamTileText3}>
                {teamLabels || ''}
              </Text>
            </>
          )}
          {isParent && listOfChildren()}
        </ScrollView>
        <View style={UserProfileTileStyle.colThree}>
          <View style={UserProfileTileStyle.optionView}>
            <TouchableOpacity
              style={UserProfileTileStyle.editBtn}
              onPress={() => navigation.navigate('AddUser', { userData: item })}
            >
              <Image source={editIcon} style={UserProfileTileStyle.icon} />
            </TouchableOpacity>

            <TouchableOpacity
              style={UserProfileTileStyle.editBtn}
              onPress={() => openUserDeleteModal(id)}
            >
              <Image source={deleteIcon} style={UserProfileTileStyle.icon} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

export default UserProfileTile;
