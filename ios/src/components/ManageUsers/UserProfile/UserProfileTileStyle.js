import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const UserProfileTileStyle = colors => ({
  teamTile: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('34%'),
        height: wp('13%'),
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('2%'),
        width: wp('96%'),
        height: wp('32%'),
        padding: wp('2.5%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  teamTileNameContainer: isTabDevice() ? {} : {
    marginBottom: 10
  },
  teamTileContainer: isTabDevice() ? {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    marginTop: 10
  } : {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },  
  teamTileSelected: {
    backgroundColor: colors.aquaBlue,
    borderRadius: wp('1%'),
    width: wp('34%'),
    height: wp('10%'),
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1%'),
    marginRight: wp('1%'),
    marginBottom: wp('1%'),
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  colOne: {},
  colTwo: isTabDevice()
    ? {
        width: wp('17%'),
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        height: '100%'
      }
    : {
        width: wp('57%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('1%'),
      },
  colThree: isTabDevice () ? {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  } : {
    justifyContent: 'center',
    alignItems: 'center',
    height: '90%',
  },
  teamTileText1Scoller: isTabDevice()
    ? {
        height: wp('2.5%'),
      }
    : {
        height: wp('4.5%'),
      },
  teamTileText1: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      },
  teamTileText2: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.green,
        marginBottom: wp('0.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.7%'),
        color: colors.green,
        marginBottom: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      },
  teamTileText3: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        height: wp('2.3%'),
        marginBottom: wp('0.5%'),
      }
    : {
        fontSize: wp('2.7%'),
        fontFamily: 'Poppins-Medium',
        color: colors.white,
        height: wp('3.7%'),
        marginBottom: wp('1.5%'),
      },
  listItemImageContainer: isTabDevice()
    ? {
        width: wp('8%'),
        height: wp('8%'),
      }
    : {
        width: wp('20%'),
        height: wp('20%'),
      },
  listItemImage: isTabDevice()
    ? {
        width: '100%',
        height: '100%',
        resizeMode: 'contain',
        borderRadius: wp('1%'),
        resizeMode: 'cover',
      }
    : {
        width: '100%',
        height: '100%',
        resizeMode: 'contain',
        borderRadius: wp('2%'),
        resizeMode: 'cover',
      },
  imageLoader: {
    borderRadius: wp('1%'),
  },
  optionView: isTabDevice()
    ? {
        flexDirection: 'column',
        justifyContent: 'space-between',
        height: '60%',
      }
    : {
        flexDirection: 'column',
        justifyContent: 'space-between',
        height: '85%',
      },
  editBtn: {
    paddingHorizontal: wp('1%'),
  },
  icon: isTabDevice()
    ? {
        width: wp('2%'),
        height: wp('2%'),
      }
    : {
        width: wp('7%'),
        height: wp('7%'),
      },
});
export default UserProfileTileStyle;
