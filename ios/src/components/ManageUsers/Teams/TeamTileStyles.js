import { StyleSheet } from 'react-native';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const TeamTileStyles = colors => ({
  teamTile: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1.5%'),
        width: wp('23.2%'),
        height: wp('13%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  teamTileSelected: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1.5%'),
        width: wp('23.2%'),
        height: wp('13%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  teamTileAddTeam: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        width: wp('10%'),
        height: wp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('1.5%'),
        width: wp('23.2%'),
        height: wp('13%'),
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      },
  teamTileText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('2.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
export default TeamTileStyles;
