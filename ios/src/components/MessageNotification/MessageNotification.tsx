import React, { useEffect } from 'react';
import { View, Switch, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { RootStore } from '../../store/store';
import ActivitySpinner from '../ActivitySpinner/ActivitySpinner';
import {
  SET_NOTFICATION_REQUEST,
  SET_NOTFICATION_SUCCESS,
  SET_NOTFICATION_FAIL,
  GET_NOTFICATION_REQUEST,
  GET_NOTFICATION_SUCCESS,
  GET_NOTFICATION_FAIL,
} from '../../store/actionTypes/Message/MessageAction';
import useApi from '../../hooks/useApi';
import {
  MESSAGING_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import useStyles from '../../hooks/useStyles';
import customHeaderStyle from '../../components/header/HeaderStyle';

const MessageNotification = () => {
  const HeaderStyle = useStyles(customHeaderStyle);
  const [allowPushNotificationForMsg] = useApi();
  const [getPushNotificationForMsg] = useApi();
  const { userData } = useSelector((state: RootStore) => state?.auth);
  const {
    isAllowPushNotificationForMsg,
    isAllowPushNotificationForMsgLoading,
  } = useSelector((state: RootStore) => state?.message);

  useEffect(() => {
    getPushNotificationForMsg(
      `/api/v1/users/${userData.id}/user-notification-settings`,
      GET_NOTFICATION_REQUEST,
      GET_NOTFICATION_SUCCESS,
      GET_NOTFICATION_FAIL,
      null,
      '',
      'GET',
      undefined,
      MESSAGING_SERVICE
    );
  }, []);

  const setAllowPushNotificationForMsg = (
    tmpIsAllowPushNotificationForMsg: boolean
  ) => {
    allowPushNotificationForMsg(
      `/api/v1/user-notification-settings`,
      SET_NOTFICATION_REQUEST,
      SET_NOTFICATION_SUCCESS,
      SET_NOTFICATION_FAIL,
      {
        allowPushNotification: tmpIsAllowPushNotificationForMsg,
        userId: userData.id,
      },
      '',
      'PUT',
      undefined,
      MESSAGING_SERVICE
    );
  };

  return (
    <View style={HeaderStyle.notificationWrapper}>
      <Text style={HeaderStyle.notificationText}>Message Notification:</Text>
      {isAllowPushNotificationForMsgLoading ? (
        <ActivitySpinner />
      ) : (
        <View style={HeaderStyle.notificationToggle}>
          <Switch
            trackColor={{ false: '#c3c5c8', true: '#c3c5c8' }}
            thumbColor={isAllowPushNotificationForMsg ? '#36d982' : '#071324'}
            ios_backgroundColor="#3e3e3e"
            onValueChange={setAllowPushNotificationForMsg}
            value={isAllowPushNotificationForMsg}
          />
          <Text style={HeaderStyle.notificationText2}>
            {isAllowPushNotificationForMsg ? 'On' : 'Off'}
          </Text>
        </View>
      )}
    </View>
  );
};

export default MessageNotification;
