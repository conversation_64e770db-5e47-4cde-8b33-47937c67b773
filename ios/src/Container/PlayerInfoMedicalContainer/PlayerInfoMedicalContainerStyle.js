import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateContainerStyle = colors => ({
  PlayerInfoMedicalsWrapper: isTabDevice()
    ? {
        width: wp('67%'),
      }
    : {
        width: wp('95%'),
        marginLeft: wp('2%'),
        marginRight: wp('2%'),
      },
  container: {
    width: '100%',
    height: hp('60%'),
  },
  dataRow: {},
  list: {
    backgroundColor: colors.red,
    height: 20,
    paddingBottom: 40,
  },
});
export default PlayerInfoUpdateContainerStyle;
