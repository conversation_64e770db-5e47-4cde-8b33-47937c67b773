import React, { useEffect, useState } from 'react';
import { FlatList, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import AddPlayerMedicalHistoryModal from '../../components/modal/AddPlayerMedicalHistoryModal/AddPlayerMedicalHistoryModal';
import EditPlayerMedicalHistoryModal from '../../components/modal/EditPlayerMedicalHistoryModal/EditPlayerMedicalHistoryModal';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
// component
import PlayerInfoUpdate from '../../components/PlayerInfoUpdate/PlayerInfoUpdate';
import { isTabDevice } from '../../config/appConfig';
import { userRoleType } from '../../constants/constants';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import {
  PLAYER_ADD_MEDICAL_HISTORY_FAIL,
  PLAYER_ADD_MEDICAL_HISTORY_REQUEST,
  PLAYER_ADD_MEDICAL_HISTORY_SUCCESS,
  PLAYER_DELETE_MEDICAL_HISTORY_FAIL,
  PLAYER_DELETE_MEDICAL_HISTORY_REQUEST,
  PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS,
  PLAYER_EDIT_MEDICAL_HISTORY_FAIL,
  PLAYER_EDIT_MEDICAL_HISTORY_REQUEST,
  PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS,
  PLAYER_INITIAL_MEDICAL_HISTORY_FAIL,
  PLAYER_INITIAL_MEDICAL_HISTORY_REQUEST,
  PLAYER_INITIAL_MEDICAL_HISTORY_SUCCESS,
  PLAYER_MORE_MEDICAL_HISTORY_FAIL,
  PLAYER_MORE_MEDICAL_HISTORY_REQUEST,
  PLAYER_MORE_MEDICAL_HISTORY_SUCCESS,
} from '../../store/actionTypes/player/playerAction';
import {
  REPORT_DELETE,
  REPORT_RESET,
  UPLOAD_REPORT_UPDATE,
} from '../../store/actionTypes/UploadReport/uploadreportAction';
// Style
import customPlayerInfoMedicalContainerStyle from './PlayerInfoMedicalContainerStyle';

const PlayerInfoUpdateContainer = ({ highlight }) => {
  const PlayerInfoMedicalContainerStyle = useStyles(
    customPlayerInfoMedicalContainerStyle
  );
  const {
    playerMedicalHistory,
    playerMedicalHistoryLoading,
    playerMedicalHistoryPage,
    playerMedicalHistoryPageInitial,
    playerMedicalHistoryPageSize,
    playerMedicalHistoryPageSizeInitial,
    playerUploadedReport,
    playerUploadedReportLoading,
    playerMedicalHistoryTriggerLoading,
  } = useSelector(state => state?.player);

  const { sportsProfileId } = useSelector(
    state => state?.playerInfo?.PlayerInfoData
  );
  const { userRole, userData } = useSelector(state => state?.auth);

  const [selectedUpdate, setSelectedUpdate] = useState(null);
  const [isEditModeOpen, setIsEditModeOpen] = useState(false);
  const [selectedUpdateData, setSelectedUpdateData] = useState(null);

  const [fetchData] = useApi();
  const [saveData] = useApi();
  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;

  const dispatch = useDispatch();

  const setUploadedFile = uploadedFiles => {
    dispatch({
      type: REPORT_RESET,
    });
    dispatch({
      type: UPLOAD_REPORT_UPDATE,
      payload: uploadedFiles,
    });
  };

  const getData = () => {
    fetchData(
      `/api/v1/sport-profiles/${
        isPlayer ? userData?.sportsProfileId : sportsProfileId
      }/medical-history?page=${playerMedicalHistoryPageInitial}&size=${playerMedicalHistoryPageSizeInitial}`,
      PLAYER_INITIAL_MEDICAL_HISTORY_REQUEST,
      PLAYER_INITIAL_MEDICAL_HISTORY_SUCCESS,
      PLAYER_INITIAL_MEDICAL_HISTORY_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    !playerMedicalHistoryTriggerLoading && getData();
  }, [playerMedicalHistoryTriggerLoading]);
  useEffect(() => {
    getData();
  }, []);

  const loadMoreUpdate = () => {
    fetchData(
      `/api/v1/sport-profiles/${
        isPlayer ? userData?.sportsProfileId : sportsProfileId
      }/medical-history?page=${
        playerMedicalHistoryPage + 1
      }&size=${playerMedicalHistoryPageSize}`,
      PLAYER_MORE_MEDICAL_HISTORY_REQUEST,
      PLAYER_MORE_MEDICAL_HISTORY_SUCCESS,
      PLAYER_MORE_MEDICAL_HISTORY_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const editUpdate = ({ message, createdDate, updateId, fileUploads }) => {
    let postValues = { createdDate, message };

    if (fileUploads) {
      postValues = { ...postValues, fileUploads };
    }

    fetchData(
      `/api/v1/medical-histories/${updateId}`,
      PLAYER_EDIT_MEDICAL_HISTORY_REQUEST,
      PLAYER_EDIT_MEDICAL_HISTORY_SUCCESS,
      PLAYER_EDIT_MEDICAL_HISTORY_FAIL,
      postValues,
      '',
      'PUT',
      null,
      FOOTBALL_SERVICE,
      { message, createdDate, updateId }
    );
  };

  const openEditModal = updateData => {
    dispatch({
      type: REPORT_RESET,
    });
    setIsEditModeOpen(true);
    setSelectedUpdateData(updateData);
    updateData?.fileUploads && setUploadedFile(updateData?.fileUploads);
  };

  const deleteItem = deleteData => {
    fetchData(
      `/api/v1/medical-histories?medicalHistoryId=${deleteData._id}`,
      PLAYER_DELETE_MEDICAL_HISTORY_REQUEST,
      PLAYER_DELETE_MEDICAL_HISTORY_SUCCESS,
      PLAYER_DELETE_MEDICAL_HISTORY_FAIL,
      null,
      '',
      'DELETE',
      null,
      FOOTBALL_SERVICE,
      deleteData
    );
  };

  const addUpdate = ({ message, createdDate, fileUploads, updateId }) => {
    let postValues = { createdDate, message };

    if (fileUploads) {
      postValues = { ...postValues, fileUploads };
    }

    saveData(
      `/api/v1/sport-profiles/${sportsProfileId}/medical-history`,
      PLAYER_ADD_MEDICAL_HISTORY_REQUEST,
      PLAYER_ADD_MEDICAL_HISTORY_SUCCESS,
      PLAYER_ADD_MEDICAL_HISTORY_FAIL,
      postValues,
      '',
      'POST',
      null,
      FOOTBALL_SERVICE
    );
  };

  const deleteReportAction = index => {
    dispatch({
      type: REPORT_DELETE,
      payload: { data: index },
    });
  };

  const renderItem = ({ item }) => {
    return (
      <PlayerInfoUpdate
        key={item._id}
        selectedUpdate={selectedUpdate}
        item={item}
        setSelectedUpdate={setSelectedUpdate}
        openEditModal={openEditModal}
        deleteItem={deleteItem}
        setUploadedFile={setUploadedFile}
        contentType={'Medical Report'}
        isVisible
      />
    );
  };

  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    modalVisible &&
      dispatch({
        type: REPORT_RESET,
      });
  }, [modalVisible]);

  return (
    <>
      <View style={PlayerInfoMedicalContainerStyle.PlayerInfoMedicalsWrapper}>
        <View style={PlayerInfoMedicalContainerStyle.container}>
          {playerMedicalHistoryLoading ? (
            <ActivitySpinner />
          ) : playerMedicalHistory?.length ? (
            <FlatList
              data={playerMedicalHistory}
              renderItem={renderItem}
              keyExtractor={item => item._id}
              onEndReached={() => loadMoreUpdate()}
              contentContainerStyle={
                isTabDevice() ? { paddingBottom: 40 } : isParent ?  {paddingBottom:320} : { paddingBottom: 230 }
              }
            />
          ) : (
            <NoContentMessage message="No Content" />
          )}
        </View>

        {isEditModeOpen && (
          <EditPlayerMedicalHistoryModal
            editUpdate={editUpdate}
            setIsEditModeOpen={data => {
              setIsEditModeOpen(data);
              dispatch({
                type: REPORT_RESET,
              });
            }}
            selectedUpdateData={selectedUpdateData}
            playerUploadedReport={playerUploadedReport}
            deleteReportAction={deleteReportAction}
            playerUploadedReportLoading={playerUploadedReportLoading}
          />
        )}

        {!isPlayer && !isParent && (
          <AddPlayerMedicalHistoryModal
            modalVisible={modalVisible}
            setModalVisible={setModalVisible}
            addUpdate={addUpdate}
            isAddModal={true}
          />
        )}
      </View>
    </>
  );
};

export default PlayerInfoUpdateContainer;
