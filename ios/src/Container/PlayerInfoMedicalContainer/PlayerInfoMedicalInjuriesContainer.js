import React, { useEffect, useState } from 'react';
import { FlatList, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import AddPlayerMedicalInjuriesModal from '../../components/modal/AddPlayerMedicalInjuriesModal/AddPlayerMedicalInjuriesModal';
import EditPlayerMedicalInjuriesModal from '../../components/modal/EditPlayerMedicalInjuriesModal/EditPlayerMedicalInjuriesModal';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
// component
import PlayerInfoUpdate from '../../components/PlayerInfoUpdate/PlayerInfoUpdate';
import { userRoleType } from '../../constants/constants';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import {
  PLAYER_ADD_MEDICAL_INJURIES_FAIL,
  PLAYER_ADD_MEDICAL_INJURIES_REQUEST,
  PLAYER_ADD_MEDICAL_INJURIES_SUCCESS,
  PLAYER_DELETE_MEDICAL_INJURIES_FAIL,
  PLAYER_DELETE_MEDICAL_INJURIES_REQUEST,
  PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS,
  PLAYER_EDIT_MEDICAL_INJURIES_FAIL,
  PLAYER_EDIT_MEDICAL_INJURIES_REQUEST,
  PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS,
  PLAYER_INITIAL_MEDICAL_INJURIES_FAIL,
  PLAYER_INITIAL_MEDICAL_INJURIES_REQUEST,
  PLAYER_INITIAL_MEDICAL_INJURIES_SUCCESS,
  PLAYER_MORE_MEDICAL_INJURIES_FAIL,
  PLAYER_MORE_MEDICAL_INJURIES_REQUEST,
  PLAYER_MORE_MEDICAL_INJURIES_SUCCESS,
} from '../../store/actionTypes/player/playerAction';
import {
  REPORT_DELETE,
  REPORT_RESET,
  UPLOAD_REPORT_UPDATE,
} from '../../store/actionTypes/UploadReport/uploadreportAction';
// Style
import customPlayerInfoMedicalContainerStyle from './PlayerInfoMedicalContainerStyle';
import { isTabDevice } from '../../config/appConfig';

const PlayerInfoUpdateContainer = ({ message, highlight }) => {
  const PlayerInfoMedicalContainerStyle = useStyles(
    customPlayerInfoMedicalContainerStyle
  );
  const {
    playerInjuries,
    playerInjuriesLoading,
    playerInjuriesPage,
    playerInjuriesPageInitial,
    playerInjuriesPageSize,
    playerInjuriesPageSizeInitial,
    playerUploadedReport,
    playerUploadedReportLoading,
    playerInjuriesTriggerLoading,
  } = useSelector(state => state?.player);

  const { sportsProfileId } = useSelector(
    state => state?.playerInfo?.PlayerInfoData
  );

  const { userRole, userData } = useSelector(state => state?.auth);

  const [selectedUpdate, setSelectedUpdate] = useState(null);
  const [isEditModeOpen, setIsEditModeOpen] = useState(false);
  const [selectedUpdateData, setSelectedUpdateData] = useState(null);

  const [fetchData] = useApi();

  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;
  const dispatch = useDispatch();

  const setUploadedFile = uploadedFiles => {
    dispatch({
      type: REPORT_RESET,
    });
    dispatch({
      type: UPLOAD_REPORT_UPDATE,
      payload: uploadedFiles,
    });
  };

  const getData = () => {
    fetchData(
      `/api/v1/sport-profiles/${
        isPlayer ? userData?.sportsProfileId : sportsProfileId
      }/injuries?page=${playerInjuriesPageInitial}&size=${playerInjuriesPageSizeInitial}`,
      PLAYER_INITIAL_MEDICAL_INJURIES_REQUEST,
      PLAYER_INITIAL_MEDICAL_INJURIES_SUCCESS,
      PLAYER_INITIAL_MEDICAL_INJURIES_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    !playerInjuriesTriggerLoading && getData();
  }, [playerInjuriesTriggerLoading]);
  useEffect(() => {
    getData();
  }, []);

  const loadMoreUpdate = () => {
    fetchData(
      `/api/v1/sport-profiles/${
        isPlayer ? userData?.sportsProfileId : sportsProfileId
      }/injuries?page=${playerInjuriesPage + 1}&size=${playerInjuriesPageSize}`,
      PLAYER_MORE_MEDICAL_INJURIES_REQUEST,
      PLAYER_MORE_MEDICAL_INJURIES_SUCCESS,
      PLAYER_MORE_MEDICAL_INJURIES_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const editUpdate = ({ message, createdDate, updateId, fileUploads }) => {
    let postValues = { createdDate, message };

    if (fileUploads) {
      postValues = { ...postValues, fileUploads };
    }

    fetchData(
      `/api/v1/injuries/${updateId}`,
      PLAYER_EDIT_MEDICAL_INJURIES_REQUEST,
      PLAYER_EDIT_MEDICAL_INJURIES_SUCCESS,
      PLAYER_EDIT_MEDICAL_INJURIES_FAIL,
      postValues,
      '',
      'PUT',
      null,
      FOOTBALL_SERVICE,
      { message, createdDate, updateId }
    );
  };

  const openEditModal = updateData => {
    dispatch({
      type: REPORT_RESET,
    });
    setIsEditModeOpen(true);
    setSelectedUpdateData(updateData);
    updateData?.fileUploads && setUploadedFile(updateData?.fileUploads);
  };

  const deleteItem = deleteData => {
    fetchData(
      `/api/v1/injuries/${deleteData._id}`,
      PLAYER_DELETE_MEDICAL_INJURIES_REQUEST,
      PLAYER_DELETE_MEDICAL_INJURIES_SUCCESS,
      PLAYER_DELETE_MEDICAL_INJURIES_FAIL,
      null,
      '',
      'DELETE',
      null,
      FOOTBALL_SERVICE,
      deleteData
    );
  };

  const addUpdate = ({ message, createdDate, fileUploads, updateId }) => {
    let postValues = { createdDate, message };

    if (fileUploads) {
      postValues = { ...postValues, fileUploads };
    }

    fetchData(
      `/api/v1/sport-profiles/${sportsProfileId}/injuries`,
      PLAYER_ADD_MEDICAL_INJURIES_REQUEST,
      PLAYER_ADD_MEDICAL_INJURIES_SUCCESS,
      PLAYER_ADD_MEDICAL_INJURIES_FAIL,
      postValues,
      '',
      'POST',
      null,
      FOOTBALL_SERVICE
    );
  };

  const deleteReportAction = index => {
    dispatch({
      type: REPORT_DELETE,
      payload: { data: index },
    });
  };

  const renderItem = ({ item }) => {
    return (
      <PlayerInfoUpdate
        key={item._id}
        selectedUpdate={selectedUpdate}
        item={item}
        highlight={highlight}
        setSelectedUpdate={setSelectedUpdate}
        openEditModal={openEditModal}
        deleteItem={deleteItem}
        setUploadedFile={setUploadedFile}
        contentType={'Injuries Report'}
        isVisible
      />
    );
  };

  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    modalVisible &&
      dispatch({
        type: REPORT_RESET,
      });
  }, [modalVisible]);

  return (
    <View style={PlayerInfoMedicalContainerStyle.PlayerInfoMedicalsWrapper}>
      <View style={PlayerInfoMedicalContainerStyle.container}>
        {playerInjuriesLoading ? (
          <ActivitySpinner />
        ) : playerInjuries?.length ? (
          <FlatList
            data={playerInjuries}
            renderItem={renderItem}
            keyExtractor={item => item._id}
            onEndReached={() => loadMoreUpdate()}
            contentContainerStyle={
              isTabDevice() ? { paddingBottom: 40 } : isParent ? {paddingBottom : 320}  : { paddingBottom: 230 }
            }
          />
        ) : (
          <NoContentMessage message="No Content" />
        )}
      </View>

      {isEditModeOpen && (
        <EditPlayerMedicalInjuriesModal
          editUpdate={editUpdate}
          setIsEditModeOpen={data => {
            setIsEditModeOpen(data);
            dispatch({
              type: REPORT_RESET,
            });
          }}
          selectedUpdateData={selectedUpdateData}
          playerUploadedReport={playerUploadedReport}
          deleteReportAction={deleteReportAction}
          playerUploadedReportLoading={playerUploadedReportLoading}
        />
      )}

      {!isPlayer && !isParent && (
        <AddPlayerMedicalInjuriesModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          addUpdate={addUpdate}
          isAddModal={true}
        />
      )}
    </View>
  );
};

export default PlayerInfoUpdateContainer;
