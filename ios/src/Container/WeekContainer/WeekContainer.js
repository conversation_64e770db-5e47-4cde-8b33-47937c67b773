import React, { useState, useEffect } from 'react';
import { Button, Text, View, FlatList,TouchableOpacity } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import useApi from '../../hooks/useApi';
import { EVENT_SERVICE } from '../../constants/services';
import MatchContainer from '../../Container/MatchContainer/MatchContainer';
import {
  MATCH_STATUS_FAIL,
  MATCH_STATUS_REQUEST,
  MATCH_STATUS_SUCCESS,
} from '../../store/actionTypes/MatchesInfo/MatchesAction';
import customWeekContainerStyles from './WeekContainerStyles';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import { isTabDevice } from '../../config/appConfig';
import useStyles from '../../hooks/useStyles';
import { REMOVE_SELECTED_MATCH_PLAN } from '../../store/actionTypes/MatchPlan/MatchPlanActions';
import { userRoleType } from '../../constants/constants';
import { useNavigation } from '@react-navigation/native';

const WeekContainer = ({
  loadMoreMatches,
  currentWeekIndex,
  selectedWeekIndex,
}) => {
  const WeekContainerStyles = useStyles(customWeekContainerStyles);
  const { matches, isMatchesLoading } = useSelector(state => state?.matches);
  const { userRole } = useSelector(state => state.auth);

  const [pastEvent, setPastEvent] = useState(false);
  const [fetchData] = useApi();
  const dispatch = useDispatch();
  const navigation = useNavigation();


  const isCoach = userRole === userRoleType.COACH;
  const isHeadCoach = userRole === userRoleType.HEAD_COACH;

  useEffect(() => {
    dispatch({
      type: REMOVE_SELECTED_MATCH_PLAN,
    });
  }, []);

  useEffect(() => {
    setPastEvent(selectedWeekIndex < currentWeekIndex);

    let matchIDs = [];

    matches.forEach(element => {
      if (element && !element.isReady) {
        matchIDs = [...matchIDs, element._id];
      }
    });

    if (matchIDs.length > 0) {
      fetchData(
        `/api/v1/match-plans?matchIds=${matchIDs.toString()}`,
        MATCH_STATUS_REQUEST,
        MATCH_STATUS_SUCCESS,
        MATCH_STATUS_FAIL,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  }, [matches]);

  const match = ({ item }) => {
    return <MatchContainer item={item} pastEvent={pastEvent} />;
  };

  return isMatchesLoading ? (
    <ActivitySpinner isMatchLog />
  ) : (
    <View>
      <View style={WeekContainerStyles.containerWrapper}>
        {matches.length ? (
          <FlatList
            numColumns={isTabDevice() ? 2 : null}
            data={matches}
            renderItem={match}
            keyExtractor={(item, index) => index.toString()}
            onEndReached={() => matches?.length && loadMoreMatches()}
            style={WeekContainerStyles.list}
          />
        ) : (
          <View style={WeekContainerStyles.noScheduledWrapper}>
          <Text style={WeekContainerStyles.noScheduledWrapperTextMain}>You do not have any matches scheduled at the moment.</Text>

          {(isHeadCoach || isCoach) && (
            <>
              <Text style={WeekContainerStyles.noScheduledWrapperTextSub}>To schedule a Match, please click on the 'Planner' button below.</Text>
              <TouchableOpacity
                style={WeekContainerStyles.noScheduledWrapperButton}
                onPress={() => navigation.navigate('PlannerScreen')}
              >
                <Text style={WeekContainerStyles.noScheduledWrapperButtonText}>Planner</Text>
              </TouchableOpacity>
            </>
          )}
          </View>
        )}
      </View>
    </View>
  );
};

export default WeekContainer;
