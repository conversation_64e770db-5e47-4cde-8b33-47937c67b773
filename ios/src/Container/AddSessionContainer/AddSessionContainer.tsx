import { Text, View, FlatList, TextInput } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { AntDesign } from '@expo/vector-icons';
import { useEffect, useMemo } from 'react';
import { isTabDevice } from '../../config/appConfig';
import useStyles from '../../hooks/useStyles';
import customAddSessionContainerStyle from './AddSessionStyle';
import { TouchableOpacity } from 'react-native-gesture-handler';
import UserSessionProfile from '../../components/AddSession/UserSessionProfile';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
import { RouteProp, useRoute } from '@react-navigation/native';
import {
  IUserMappedSession,
  useSessionBulkHook,
} from '../../hooks/SessionAPIHook/useSessionBulkHook';
import useDebounce from '../../hooks/useDebounce';
import SessionAddModal from '../SessionsPlan/SessionsAddModal';
import useSession from '../../hooks/useSession';

interface RouteParams {
  teamId: string;
}

const RenderItem = ({
  userData,
  index,
  onUserChecked,
}: {
  userData: IUserMappedSession;
  index: number;
  onUserChecked: (index: number, hasChecked: boolean) => void;
}) => {
  return (
    <UserSessionProfile
      key={userData.userId}
      userData={userData}
      index={index}
      onUserChecked={onUserChecked}
    />
  );
};

const AddSessionContainer = () => {
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const {
    onLoadUser,
    onUserChecked,
    setSearchBarText,
    userSessionList,
    noUser,
    currentPageNo,
    totalRecords,
    isLoading,
    searchBarText,
  } = useSessionBulkHook();

  const {
    addNumberOfSessions,
    setAddNumberOfSessions,
    setIsEditModalOpen,
    isDatePikerEnable,
    setIsDatePikerEnable,
    mode,
    getSessionExpiryDate,
    expireDateFormatted,
    errorMassageOnSubmit,
    openSubmitModal,
    isEditModalOpen,
    updateSessionBulk,
  } = useSession();

  const AddSessionContainerStyle = useStyles(customAddSessionContainerStyle);

  const debouncedSearchText = useDebounce(searchBarText, 300);
  const checkedUserList = useMemo(() => {
    return userSessionList
      .filter((userSession: IUserMappedSession) => userSession.isChecked)
      .map((userSession: IUserMappedSession) => userSession.userId);
  }, [JSON.stringify(userSessionList)]);

  useEffect(() => {
    if (route.params.teamId) {
      onLoadUser(route.params.teamId, 1);
    }
  }, [route.params.teamId]);

  useEffect(() => {
    onLoadUser(route.params.teamId, 1, debouncedSearchText);
  }, [debouncedSearchText]);

  const handleSessionModal = () => {
    if (checkedUserList.length > 0) {
      setIsEditModalOpen(true);
    }
  };

  const onUpdateSession = () => {
    if (checkedUserList.length > 0) {
      const response = updateSessionBulk(checkedUserList);
      if (response) {
        if (searchBarText) {
          onLoadUser(route.params.teamId, 1, searchBarText);
        } else {
          onLoadUser(route.params.teamId, 1);
        }
      }
    }
  };

  const onSearch = (searchBarText: string) => {
    onLoadUser(route.params.teamId, 1, searchBarText);
  };

  const loadPaginatedUserSession = () => {
    if (userSessionList.length != totalRecords && !isLoading) {
      if (searchBarText) {
        onLoadUser(route.params.teamId, currentPageNo + 1, searchBarText);
      } else {
        onLoadUser(route.params.teamId, currentPageNo + 1);
      }
    }
  };

  const renderSearchBar = () => (
    <View style={AddSessionContainerStyle.header}>
      <View style={AddSessionContainerStyle.searchBarContainer}>
        <TextInput
          style={AddSessionContainerStyle.searchBarInput}
          placeholder="Search User"
          placeholderTextColor="#FFFF"
          onChangeText={value => {
            setSearchBarText(value);
          }}
          value={searchBarText}
          onSubmitEditing={() => {
            onSearch(searchBarText);
          }}
        />
        <View style={AddSessionContainerStyle.searchBarIcon}>
          <TouchableOpacity onPress={() => setSearchBarText('')}>
            <AntDesign
              name={!searchBarText ? 'search1' : 'close'}
              color="white"
              size={isTabDevice() ? 25 : 13}
            />
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity
        disabled={checkedUserList.length == 0 ? true : false}
        onPress={handleSessionModal}
      >
        <View
          style={{
            ...AddSessionContainerStyle.SessionBtnContainer,
            opacity: checkedUserList.length == 0 ? 0.5 : 1,
          }}
        >
          <Text style={AddSessionContainerStyle.SessionBtnText}>
            Add Session
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );

  if (noUser) {
    return (
      <View style={AddSessionContainerStyle.container}>
        <Text style={AddSessionContainerStyle.screenTitle}>Sessions</Text>
        {renderSearchBar()}
        <NoContentMessage
          message="No Users Found"
          customWrapperStyle={undefined}
        />
      </View>
    );
  }

  return (
    <View style={AddSessionContainerStyle.container}>
      <Text style={AddSessionContainerStyle.screenTitle}>Sessions</Text>
      {renderSearchBar()}
      <View style={AddSessionContainerStyle.content}>
        {userSessionList.length > 0 && (
          <FlatList
            renderItem={({
              item,
              index,
            }: {
              item: IUserMappedSession;
              index: number;
            }) => (
              <RenderItem
                userData={item}
                index={index}
                onUserChecked={onUserChecked}
              />
            )}
            data={userSessionList}
            numColumns={isTabDevice() ? 3 : undefined}
            horizontal={false}
            onEndReached={loadPaginatedUserSession}
            contentContainerStyle={AddSessionContainerStyle.list}
            style={AddSessionContainerStyle.list2}
          />
        )}
        {isLoading && (
          <ActivityIndicator
            size="large"
            color={'#00ff00'}
            style={{
              width: '100%',
              height: '10%',
            }}
          />
        )}
        {isEditModalOpen && (
          <SessionAddModal
            setIsEditModalOpen={openSubmitModal}
            addNumberOfSessions={addNumberOfSessions}
            setAddNumberOfSessions={setAddNumberOfSessions}
            isDatePikerEnable={isDatePikerEnable}
            setIsDatePikerEnable={setIsDatePikerEnable}
            mode={mode}
            addExpireDate={expireDateFormatted}
            setAddExpireDate={getSessionExpiryDate}
            updateSessionUpdate={onUpdateSession}
            errorMassageOnSubmit={errorMassageOnSubmit}
          />
        )}
      </View>
    </View>
  );
};

export default AddSessionContainer;
