import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const addUserStyle = (colors: any) => ({
  container: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      }
    : {
        flexDirection: 'column',
        paddingLeft: wp('4%'),
        paddingRight: wp('4%'),
      },
  playerCards: {
    flex: 1,
  },
  screenTitle: isTabDevice()
    ? {
        fontSize: wp('2%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      },
  header: isTabDevice()
    ? {
        flexDirection: 'row',
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
        marginBottom: wp('2%'),
      }
    : {},
  content: isTabDevice()
    ? {
        flexDirection: 'column',
        height: hp('65%'),
      }
    : {
        flexDirection: 'column',
        height: hp('57%'),
        marginTop: wp('5%'),
      },
  searchBarContainer: isTabDevice()
    ? {
        marginRight: hp('5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: wp('81%'),
        borderRadius: wp('1%'),
        backgroundColor: colors.semiDarkBlue,
        paddingRight: hp('2%'),
        paddingLeft: hp('2%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('1%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: wp('2%'),
        backgroundColor: colors.semiDarkBlue,
        height: hp('6%'),
        paddingRight: hp('1%'),
        paddingLeft: hp('2%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('1%'),
        marginBottom: wp('3%'),
        width: '100%',
      },
  searchBarInput: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        width: '95%',
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        width: '60%',
        color: colors.white,
      },
  searchBarIcon: isTabDevice()
    ? {
        marginLeft: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
      }
    : {
        marginLeft: hp('12%'),
        flexDirection: 'row',
        alignItems: 'center',
      },
  SessionBtnContainer: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('3%'),
      },
  SessionBtnText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
});
export default addUserStyle;
