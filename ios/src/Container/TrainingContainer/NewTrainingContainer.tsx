import React, { FC, useCallback } from 'react';
import { Text, View } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { useSelector } from 'react-redux';
import ChildLabel from '../../components/ChildLabel/ChildLabel';
import ModalWrapper from '../../components/modal/ModalWrapper/ModalWrapper';
import TrainingFormReport from '../../components/PlayerInfoUpdateForm/TrainingFormReport';
import NoTrainingEvent from '../../components/Training/NoTrainingEvent';
import TrainingMonthSlider from '../../components/Training/TrainingMonthSlider';
import TrainingWeekSlider from '../../components/Training/TrainingWeekSlider';
import TrainingRatings from '../../components/TrainingRatings/TrainingRatings';
import ResponseSummary from '../../components/TrainingRPE/ResponseSummary';
import { isTabDevice } from '../../config/appConfig';
import { userRoleType } from '../../constants/constants';
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';
import useStyles from '../../hooks/useStyles';
import { RootStore } from '../../store/store';
import AttendanceContainer from '../AttendanceContainer/AttendanceContainer';
import TeamContainer from '../TeamMatchesContainer/TeamContainer';
import customTrainingContainerStyles from './TrainingContainerStyle';
import TrainingEventContainer from './TrainingEventContainer';

export interface INewTrainingContainerProps {
  screenType: 'Tablet' | 'TrainingTeam' | 'TrainingEvent';
}

const NewTrainingContainer: FC<INewTrainingContainerProps> = ({
  screenType,
}) => {
  const TrainingContainerStyles = useStyles(customTrainingContainerStyles);
  const {
    children,
    futureMonths,
    selectedMonth,
    selectedWeekIndex,
    year,
    month,
    selectedMonthCalendar,
    eventList,
    isLoading,
    selectedTeamName,
    showRPEModal,
    selectedChild,
    onSelectedTeam,
    onSelectMonth,
    setMatchScreen,
    handleChildSelection,
    onSelectedWeek,
    selectedEventId,
    isAttendaceModal,
    handleAttendanceModal,
    isSessionModal,
    selectedEvent,
    handleSessionModal,
    onSaveDocuments
  } = useTrainingHook({
    isInitalComponent: screenType === 'Tablet' || screenType === 'TrainingTeam',
  });
  const { userRole } = useSelector((state: RootStore) => state.auth);

  const isParent = userRole === userRoleType.PARENT;
  const isPlayer = userRole === userRoleType.PLAYER;
  const isPlayerOrParent = isParent || isPlayer;
  const isCoaches =
    userRole === userRoleType.COACH || userRole === userRoleType.HEAD_COACH;


  const handleMonthSelection = useCallback(
    (data: any) => {
      onSelectMonth(data);
    },
    [onSelectMonth]
  );

  const renderLeftView = () => (
    <View style={TrainingContainerStyles.leftView}>
      <View style={TrainingContainerStyles.teamWrapper}>
        <View style={TrainingContainerStyles.childWrapper}>
          <ChildLabel
            data={children}
            setSelectedChild={handleChildSelection}
            selectedChild={selectedChild}
          />
        </View>
        <TeamContainer
          setMatchScreen={setMatchScreen}
          onSelectedTeam={onSelectedTeam}
          setShowTeamData={undefined}
          selectedChild={selectedChild}
        />
      </View>
    </View>
  );

  const renderRightView = () => (
    <View style={TrainingContainerStyles.rightView}>
      <View>
        {/* Team Name shows if mobile selected */}
        {!isTabDevice() && (
          <View>
            <Text style={TrainingContainerStyles.rightViewTeamName}>
              {selectedTeamName}
            </Text>
          </View>
        )}
        <TrainingMonthSlider
          selectedMonth={selectedMonth}
          months={futureMonths}
          setSelectedMonth={handleMonthSelection}
        />
        <TrainingWeekSlider
          selectedMonthCalendar={selectedMonthCalendar}
          onSelectedWeek={onSelectedWeek}
          selectedWeekIndex={selectedWeekIndex}
          year={year}
          month={month}
        />
        <View style={{ width: '95%', height: hp('70%') }}>
          {isLoading && (
            <ActivityIndicator
              size="large"
              color={'#00ff00'}
              style={{
                width: '100%',
                height: '10%',
              }}
            />
          )}
          {/* Event List Container*/}
          {!isLoading && eventList.length > 0 && <TrainingEventContainer />}
          {/* NO Training Events  */}
          {!isLoading && eventList.length === 0 && <NoTrainingEvent />}
        </View>
      </View>
    </View>
  );

  return (
    <View style={TrainingContainerStyles.container}>
      {screenType === 'Tablet' && (
        <>
          {renderLeftView()}
          {renderRightView()}
        </>
      )}
      {/* This For Mobile, It's better for Navigation example, for going back */}
      {screenType === 'TrainingTeam' && renderLeftView()}
      {screenType === 'TrainingEvent' && renderRightView()}
      {/*Modals */}
      {showRPEModal && isPlayerOrParent && <TrainingRatings />}
      {showRPEModal && isCoaches && <ResponseSummary />}
      {isAttendaceModal && (
        <ModalWrapper visible>
          <AttendanceContainer
            selectedEvent={{
              _id: selectedEventId,
            }}
            setShowAttendanceModal={handleAttendanceModal}
          />
        </ModalWrapper>
      )}
      {isSessionModal && (
        <ModalWrapper visible>
          <TrainingFormReport
            selectedEvent={selectedEvent}
            onCloseModal={() => handleSessionModal({event : undefined, isSessionModal:  false})}
            onSaveDocuments={onSaveDocuments}
            isPlayerOrParent={isPlayerOrParent}
          />
        </ModalWrapper>
      )}
    </View>
  );
};

export default NewTrainingContainer;
