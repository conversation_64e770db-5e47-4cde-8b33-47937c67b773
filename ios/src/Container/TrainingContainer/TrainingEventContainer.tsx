import React from 'react';
import { View, FlatList } from 'react-native';
import customTrainingEventContainerStyle from './TrainingEventContainerStyle';
import useStyles from '../../hooks/useStyles';
import TrainingCard from '../../components/TrainingCard/TrainingCard';
import { ITraningEvent } from '../../store/reducers/Trainings/NewTrainingReducer';
import useTrainingHook from '../../hooks/ServiceHook/TrainingServiceHook/useTrainingHook';
import { isTabDevice } from '../../config/appConfig';
import { ActivityIndicator } from 'react-native-paper';

const TrainingEventContainer = () => {
  const TrainingEventContainerStyle = useStyles(
    customTrainingEventContainerStyle
  );
  const { eventList, handleRPEModal, onLoadTrainingEvent, eventListLoading } =
    useTrainingHook({
      isInitalComponent: false,
    });

  const renderTrainingCard = ({ item }: { item: ITraningEvent }) => {
    return (
      <View style={TrainingEventContainerStyle.itemWrapper}>
        <TrainingCard
          item={item}
          handleRPEModal={(eventId: string) =>
            handleRPEModal({
              eventId: eventId,
              showRPEModal: true,
            })
          }
        />
      </View>
    );
  };

  return (
    <View style={TrainingEventContainerStyle.listWrapper}>
      <FlatList
        data={eventList}
        renderItem={renderTrainingCard}
        keyExtractor={(item, index) => index.toString()}
        numColumns={isTabDevice() ? 3 : 2} // Adjust columns for tablets
        contentContainerStyle={TrainingEventContainerStyle.listContainer}
        style={TrainingEventContainerStyle.listContainerWrapper}
        columnWrapperStyle={{ justifyContent: 'flex-start' }}
        onEndReached={() => onLoadTrainingEvent()}
      />
      {eventListLoading && (
        <ActivityIndicator
          size="large"
          color={'#00ff00'}
          style={{
            width: '100%',
            height: '10%',
          }}
        />
      )}
    </View>
  );
};

export default TrainingEventContainer;
