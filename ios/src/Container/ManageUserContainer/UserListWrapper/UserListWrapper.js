import React, { useEffect, useState } from 'react';
import { View, FlatList, Text } from 'react-native';
import UserProfileTile from '../../../components/ManageUsers/UserProfile/UserProfileTile';
import NoContentMessage from '../../../components/NoContents/NoContentMessage';
import { isTabDevice } from '../../../config/appConfig';
import customUserListWrapperStyle from './UserListWrapperStyle';
import useStyles from '../../../hooks/useStyles';
import DeleteModal from '../../../components/modal/DeleteModal/DeleteModal';
import useApi from '../../../hooks/useApi';
import {
  DELETE_USER_FAIL,
  DELETE_USER_REQUEST,
  DELETE_USER_RESET,
  DELETE_USER_SUCCESS,
  FETCH_ONGOING_RECURRING_PLAN_REQUEST,
  FETCH_ONGOING_RECURRING_PLAN_SUCCESS,
  FETCH_ONGOING_RECURRING_PLAN_FAIL,
  ONGOING_RECURRING_PLAN_RESET,
  FETCH_CHILDREN_REQUEST,
  FETCH_CHILDREN_SUCCESS,
  FETCH_CHILDREN_FAIL,
} from '../../../store/actionTypes/ManageUsers/ManageUsersAction';
import { USER_MANAGEMENT_SERVICE , EVENT_SERVICE} from '../../../constants/services';
import { useDispatch, useSelector } from 'react-redux';
import { changePasswordConst } from '../../../constants/constants';
import DeleteUnassignModal from '../../../components/modal/DeleteUnassignModal/DeleteUnassignModal';
import { removeDuplicatesFromArray } from '../../../helpers/common';

const UserListWrapper = ({
  handleChange,
  selectedUser,
  userData,
  loadMoreUsers,
  selectedTeam,
  selectedUserType,
}) => {
  const UserListWrapperStyle = useStyles(customUserListWrapperStyle);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingUserId, setDeletingUserId] = useState(null);
  const [isDeleteUnassigModalOpen,setIsDeleteUnassignModalOpen] = useState(false)
  const [deleteUserApi] = useApi();
  const dispatch = useDispatch();
  const [ongoingRecurringPlanApi] = useApi();
  const [fetchData] = useApi();
  const {
    deleteUserLoading,
    deleteUserSuccess,
    deleteUserError,
    deleteUserErrorMessage,
    isOngoingRecurringPlanAvilable,
    isOngoingRecurringPlanAvilableSuccess,
    childrenList,
  } = useSelector(state => state.manageUsers);

  const { clubSettings } = useSelector(state => state?.common);

  const isParent = selectedUserType === "PARENT"

  const deleteUser = () => {
    deletingUserId &&
      deleteUserApi(
        `/api/v1/users/${deletingUserId}`,
        DELETE_USER_REQUEST,
        DELETE_USER_SUCCESS,
        DELETE_USER_FAIL,
        null,
        null,
        'DELETE',
        null,
        USER_MANAGEMENT_SERVICE
      );
  };

  const checkOngoingRecurringPlan = (userId) => {
    if(userId){
      const userIdList = [userId]
      ongoingRecurringPlanApi(
          `/api/v1/user-subscriptions?userIds=${userIdList}&isRecurring=true`,
          FETCH_ONGOING_RECURRING_PLAN_REQUEST,
          FETCH_ONGOING_RECURRING_PLAN_SUCCESS,
          FETCH_ONGOING_RECURRING_PLAN_FAIL,
          null,
          '',
          'GET',
          null,
          EVENT_SERVICE
        );
    }

  };

  useEffect(() => {
    if (deleteUserSuccess) {
      setShowDeleteModal(false);
      resetDeleteUserData();
      resetOngoingRecurringPlanStatus();
    }
  }, [deleteUserSuccess]);

  const resetDeleteUserData = () => {
    dispatch({
      type: DELETE_USER_RESET,
    });
  };

  const resetOngoingRecurringPlanStatus = () => {
    dispatch({
      type: ONGOING_RECURRING_PLAN_RESET,
    });
  };

  const openUserDeleteModal = userId => {
    setDeletingUserId(userId);
    checkOngoingRecurringPlan(userId)
  };

  useEffect(() => {
    if (
      isOngoingRecurringPlanAvilableSuccess &&
      isOngoingRecurringPlanAvilable
    ) {
      setIsDeleteUnassignModalOpen(true);
    }
    if (
      isOngoingRecurringPlanAvilableSuccess &&
      !isOngoingRecurringPlanAvilable
    ) {
      setShowDeleteModal(true);
    }
  }, [isOngoingRecurringPlanAvilableSuccess, isOngoingRecurringPlanAvilable]);

  const renderItem = ({ item }) => (
    <UserProfileTile
      item={item}
      handleChange={handleChange}
      selectedUser={selectedUser}
      selectedTeam={selectedTeam}
      openUserDeleteModal={openUserDeleteModal}
      isParent={isParent}
      childrensName = {getRelatedChildrenNameString(item)}
    />
  );

  const getChildrenInfo = () => {
    const childrenIdList = userData?.flatMap(user => user?.childrenIds);
    const uniqueChildrenIdList= removeDuplicatesFromArray(childrenIdList || [])
    if (uniqueChildrenIdList?.length > 0 && uniqueChildrenIdList[0] !== undefined) {
      fetchData(
        `/api/v1/users?userIds=${uniqueChildrenIdList}&generateImageUrl=false&type=PLAYER&page=1&size=${uniqueChildrenIdList?.length}`,
        FETCH_CHILDREN_REQUEST,
        FETCH_CHILDREN_SUCCESS,
        FETCH_CHILDREN_FAIL,
        null,
        '',
        'GET',
        null,
        USER_MANAGEMENT_SERVICE,
        null
      );
    }
  };

  useEffect(() => {
   isParent && getChildrenInfo()
  }, [userData]);

  const getRelatedChildrenNameString = parent => {
    if(isParent){
      const childrenIdList = parent?.childrenIds;
      const filteredChildList = childrenList?.filter(f =>
        childrenIdList?.includes(f.id)
      );
      const resultString = filteredChildList
        ?.map(child => {
          const fullName = child.lastName
            ? `${child.firstName} ${child.lastName}`
            : `${child.firstName}`;
          return fullName;
        })
        .join(' | ');
      return resultString;
    }
  };

  return (
    <View
      style={UserListWrapperStyle.container}
      onStartShouldSetResponder={() => true}
    >
      <Text style={UserListWrapperStyle.teamName}>
        {selectedTeam?.teamName || ''}
      </Text>
      {userData?.length > 0 ? (
        <FlatList
          numColumns={isTabDevice() ? 2 : null}
          data={userData}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          onEndReached={() => loadMoreUsers()}
          style={UserListWrapperStyle.list}
        />
      ) : (
        <View style={UserListWrapperStyle.noUsers}><NoContentMessage message="No Users Found" /></View>
      )}
      {showDeleteModal && (
        <DeleteModal
          cancelAction={() => {
            setShowDeleteModal(false);
            setDeletingUserId(null);
            resetDeleteUserData();
            resetOngoingRecurringPlanStatus();
          }}
          loading={deleteUserLoading}
          message={`Are you sure you want to remove the selected ${selectedUserType.toLowerCase()}?`}
          submitAction={() => deleteUser()}
          errorMessage={
            deleteUserError
              ? typeof deleteUserErrorMessage === 'string'
                ? deleteUserErrorMessage
                : changePasswordConst.NetWorkError
              : ''
          }
        />
      )}

      {isDeleteUnassigModalOpen && (
        <DeleteUnassignModal
          setIsDeleteUnassignModalOpen={value => {
            setIsDeleteUnassignModalOpen(value);
            resetOngoingRecurringPlanStatus();
          }}
          clubSettings={clubSettings}
        />
      )}
    </View>
  );
};

export default UserListWrapper;
