import React, { useState, useEffect, useCallback } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  Image,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { Searchbar } from 'react-native-paper';
import { useSelector, useDispatch } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import DeleteTeamModal from '../../components/modal/DeleteModal/DeleteModal';

import editIcon from '../../../assets/buttons/editIconPen.png';
import deleteIcon from '../../../assets/buttons/binIcon.png';

import {
  CHANGE_PARAMS_TYPE,
  DELETE_TEAM_FAILED,
  DELETE_TEAM_REQUEST,
  DELETE_TEAM_RESET,
  DELETE_TEAM_SUCCESS,
  FETCH_ALL_USERS_FAIL,
  FETCH_ALL_USERS_REQUEST,
  FETCH_ALL_USERS_RESET,
  FETCH_ALL_USERS_SUCCESS,
  MANAGE_USERS_RESET,
  FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_REQUEST,
  FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_SUCCESS,
  FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_FAILED,
  UPDATE_TEAM_FAILED,
  UPDATE_TEAM_REQUEST,
  UPDATE_TEAM_SUCCESS,
} from '../../store/actionTypes/ManageUsers/ManageUsersAction';
import {
  filterByAssign,
  manageUsersListInitialPageSize,
  manageUsersListInitialPageNo,
  userRoleType,
} from '../../constants/constants';
import {
  FOOTBALL_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import useApi from '../../hooks/useApi';
import UserTypeSelect from './UserTypeWrapper/UserTypeSelect';
import TeamWrapper from './TeamWrapper/TeamWrapper';
import customManageUserContainerStyle from './ManageUserContainerStyle';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import UserListWrapper from './UserListWrapper/UserListWrapper';
import AddTeamModal from '../../components/modal/AddEditTeamModal/AddTeamModal';
import useStyles from '../../hooks/useStyles';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import SelectionModal from '../../components/modal/SelectionModal/SelectionModal';
import generateQueryParams from '../../utils/generateQueryParams';

export const removeDuplicateUsers = (users) => {
  const uniqueUsers = {};
  users.forEach(user => {
    uniqueUsers[user.id] = user;
  });
  return Object.values(uniqueUsers);
};

const ManageUserContainer = ({ navigation }) => {
  const ManageUserContainerStyle = useStyles(customManageUserContainerStyle);
  const [searchText, setSearchText] = useState('');
  const [filterBy, setFilterBy] = useState('');
  const [selectedUserType, setSelectedUserType] = useState('COACH');
  const [selectedteam, setSelectedteam] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [showTeamEditModal, setShowTeamEditModal] = useState(false);
  const [showTeamDeleteModal, setShowTeamDeleteModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const futureMatchAlreadyStartedError = 'FUTURE_MATCH_ALREADY_STARTED';

  const { userData } = useSelector(state => state?.auth);
  const isHeadCoach = userRoleType.HEAD_COACH === userData?.type;

  const [
    setIsFilterByModalOpen,
    isFilterByModalOpen,
    setSelectedFilterBy,
    selectedFilterBy,
  ] = useInputSelectModal();

  const [fetchData] = useApi();
  const dispatch = useDispatch();
  const {
    allUsers,
    allUsersPageNo,
    allUsersLoading,
    allUsersTotalRecords,
    stopFetchingUsers,
    deleteUserSuccess,
    teamDeleteLoading,
    teamDeleteError,
    teamDeleteSuccess,
    teamDeleteErrorMessage,
  } = useSelector(state => state?.manageUsers);

  const { creatUserSuccess } = useSelector(state => state?.addUser);

  useEffect(() => {
    deleteUserSuccess && searchUsers();
  }, [deleteUserSuccess]);

  useEffect(() => {
    if (teamDeleteSuccess) {
      setShowTeamDeleteModal(false);
      setSelectedteam('');
      resetTeamDeleteData();
    } else {
      handleTeamDeleteError();
    }
  }, [teamDeleteSuccess]);

  const resetTeamDeleteData = () => {
    dispatch({
      type: DELETE_TEAM_RESET,
    });
  };

  useFocusEffect(
    useCallback(() => {
      // Do something when the screen is focused
      dispatch({ type: MANAGE_USERS_RESET });
      loadUsers(manageUsersListInitialPageNo, manageUsersListInitialPageSize);
      return () => {
        // Do something when the screen is unfocused
        setSelectedteam('');
        setFilterBy('');
        setSearchText('');
        dispatch({ type: MANAGE_USERS_RESET });
      };
    }, [])
  );

  useEffect(() => {
    if (!selectedteam) {
      setFilterBy('');
    }
  }, [selectedteam]);

  useEffect(() => {
    searchUsers();
  }, [selectedteam, selectedUserType, filterBy]);

  // When clicking search bar close button
  useEffect(() => {
    if (!searchText) {
      searchUsers();
    }
  }, [searchText]);

  useEffect(() => {
    if (creatUserSuccess) {
      loadUsers(manageUsersListInitialPageNo, manageUsersListInitialPageSize);
      if (!allUsersLoading && allUsersTotalRecords) {
        getTeamsInfo();
      }
    }
  }, [creatUserSuccess]);

  useEffect(() => {
    if (!allUsersLoading && allUsersTotalRecords) {
      getTeamsInfo();
    }
  }, [allUsersLoading]);

  // Trigger loadUsers when FETCH_ALL_USERS_RESET is dispatched
  useEffect(() => {
    if (allUsers === null) {
      loadUsers(manageUsersListInitialPageNo, manageUsersListInitialPageSize);
    }
  }, [allUsers]);

  const searchUsers = () => {
    // Restricting API call on add team
    if (allUsers) {
      dispatch({
        type: FETCH_ALL_USERS_RESET,
        payload: null,
      });
    }
  };

  const loadUsers = (pageNo, pageSize) => {
    // Only check stopFetchingUsers for pagination, not initial load
    if (stopFetchingUsers && pageNo > manageUsersListInitialPageNo) {
      return;
    }

    let requestUrl = `/api/v1/users${generateQueryParams(
      pageNo,
      pageSize,
      searchText,
      selectedUserType
    )}`;

    if (selectedteam && selectedteam !== '-1') {
      requestUrl += `&teamId=${selectedteam._id}`;
    } else {
      if (filterBy) {
        requestUrl += `&isAssignedToTeam=${filterBy === 'ASSIGNED'}`;
      }
    }

    dispatch({
      type: CHANGE_PARAMS_TYPE,
      payload: [
        selectedUserType,
        selectedteam?._id || '',
        searchText,
        filterBy,
      ],
    });

    fetchData(
      requestUrl,
      FETCH_ALL_USERS_REQUEST,
      FETCH_ALL_USERS_SUCCESS,
      FETCH_ALL_USERS_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE,
      {
        searchParams: [
          selectedUserType,
          selectedteam?._id || '',
          searchText,
          filterBy,
        ],
      }
    );
  };

  const getUrl = (type, ids) => {
    const userIds = ids?.toString();

    if (type === userRoleType.PLAYER) {
      return `/api/v1/teams/user-team-data?playerUserIds=${userIds}&page=1&size=${ids?.length}`;
    } else if (type === userRoleType.COACH) {
      return `/api/v1/teams/user-team-data?coachUserIds=${userIds}&page=1&size=${ids?.length}`;
    } else {
      return `/api/v1/teams/user-team-data?parentUserIds=${userIds}&page=1&size=${ids?.length}`;
    }
  };

  const getTeamsInfo = () => {
    const loadedUserIds = allUsers?.map(user => user?.id);

    if (loadedUserIds?.length > 0 && loadedUserIds[0] !== undefined) {
      fetchData(
        getUrl(selectedUserType, loadedUserIds),
        FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_REQUEST,
        FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_SUCCESS,
        FETCH_SELECTED_TEAMS_LABELS_FOR_USER_MANAGEMENT_FAILED,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE,
        null
      );
    }
  };

  const handleTeamEdit = updatedTeamData => {
    const updatedTeam = {
      ...selectedteam,
      teamName: updatedTeamData.teamName,
      colour: updatedTeamData.colour,
    };
    setSelectedteam(updatedTeam);
    saveTeamData(updatedTeam);
    setShowTeamEditModal(false);
  };

  const saveTeamData = teamData => {
    teamData &&
      fetchData(
        `/api/v1/teams`,
        UPDATE_TEAM_REQUEST,
        UPDATE_TEAM_SUCCESS,
        UPDATE_TEAM_FAILED,
        teamData,
        null,
        'PUT',
        false,
        FOOTBALL_SERVICE
      );
  };

  const deleteTeam = team => {
    team?._id &&
      fetchData(
        `/api/v1/teams/${team?._id}`,
        DELETE_TEAM_REQUEST,
        DELETE_TEAM_SUCCESS,
        DELETE_TEAM_FAILED,
        null,
        null,
        'DELETE',
        null,
        FOOTBALL_SERVICE
      );
  };

  const handleTeamEditClick = () => {
    setShowTeamEditModal(true);
  };

  const handleTeamDeleteError = () => {
    if (teamDeleteErrorMessage?.includes(futureMatchAlreadyStartedError)) {
      setErrorMessage('There is an ongoing match. The team cannot be deleted.');
    } else {
      setErrorMessage(teamDeleteErrorMessage);
    }
  };

  return (
    <TouchableWithoutFeedback
      onPress={Keyboard.dismiss}
      accessible={false}
      touchSoundDisabled={true}
    >
      <View style={ManageUserContainerStyle.container}>
        <View style={ManageUserContainerStyle.leftView}>
          <UserTypeSelect
            handleChange={setSelectedUserType}
            selectedType={selectedUserType}
            disabled={allUsersLoading}
          />
          <TeamWrapper
            handleChange={setSelectedteam}
            selectedTeam={selectedteam}
          />
          <View>
            <TouchableOpacity onPress={() => navigation.navigate('AddUser')}>
              <View style={ManageUserContainerStyle.addUserButton}>
                <Text style={ManageUserContainerStyle.addUserButtonText}>
                  + Add User
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          <View>
            {isHeadCoach && (
              <TouchableOpacity
                disabled={!(selectedteam?._id && selectedUserType == 'PLAYER')}
                onPress={() =>
                  navigation.navigate('AddSession', {
                    teamId: selectedteam?._id,
                  })
                }
              >
                <View
                  style={{
                    ...ManageUserContainerStyle.SessionBtnContainer,
                    opacity:
                      selectedteam?._id && selectedUserType == 'PLAYER'
                        ? 1
                        : 0.5,
                  }}
                >
                  <Text style={ManageUserContainerStyle.SessionBtnText}>
                    Session
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
        <View
          style={ManageUserContainerStyle.rightView}
          onStartShouldSetResponder={() => true}
        >
          <View style={ManageUserContainerStyle.topRow}>
            <Text style={ManageUserContainerStyle.title}>User Managment</Text>
          </View>
          <View style={ManageUserContainerStyle.bottomRow}>
            <Searchbar
              placeholder="Search User"
              placeholderTextColor="#595959"
              onChangeText={text => setSearchText(text.trim())}
              editable
              defaultValue={searchText}
              iconColor="#FFF"
              inputStyle={ManageUserContainerStyle.searchText}
              style={ManageUserContainerStyle.search}
              onSubmitEditing={() => searchUsers()}
              onIconPress={() => searchUsers()}
            />
            <View style={ManageUserContainerStyle.dropdownView}>
              {selectedteam._id === '-1' || selectedteam === '' ? (
                <SelectionModal
                  title={'Filter By'}
                  items={filterByAssign}
                  onCloseHook={setIsFilterByModalOpen}
                  onSelectItemHook={item => {
                    setFilterBy(item?.[0]?.value);
                    setSelectedFilterBy(item);
                  }}
                  defaultValues={
                    selectedFilterBy?.[0]
                      ? [selectedFilterBy?.[0]?.value]
                      : [filterByAssign?.[0]?.value]
                  }
                  selectedItemLabel={selectedFilterBy?.[0]?.label}
                  isModalOpen={isFilterByModalOpen}
                  enableDefaultLabel
                  selectFirstOptionOnInitialRender
                />
              ) : (
                <View style={ManageUserContainerStyle.controllerButtons}>
                  <TouchableOpacity style={ManageUserContainerStyle.Btn}>
                    <View
                      style={[
                        ManageUserContainerStyle.colorPick,
                        { backgroundColor: `${selectedteam.colour}` },
                      ]}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={ManageUserContainerStyle.Btn}
                    onPress={() => setShowTeamDeleteModal(true)}
                  >
                    <Image
                      source={deleteIcon}
                      style={ManageUserContainerStyle.icon}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={ManageUserContainerStyle.Btn}
                    onPress={() => handleTeamEditClick()}
                  >
                    <Image
                      source={editIcon}
                      style={ManageUserContainerStyle.icon}
                    />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
          {allUsersLoading && allUsersPageNo === 0 ? (
            <ActivitySpinner />
          ) : (
            <View>
              {allUsersLoading && <ActivitySpinner />}
              <UserListWrapper
                handleChange={setSelectedUser}
                selectedUser={selectedUser}
                userData={removeDuplicateUsers(allUsers || []) || []}
                loadMoreUsers={() => {
                  if (!stopFetchingUsers) {
                      loadUsers(
                        allUsersPageNo + 1,
                        manageUsersListInitialPageSize
                      );
                  }
                }}
                selectedTeam={selectedteam}
                selectedUserType={selectedUserType}
              />
            </View>
          )}
        </View>
        <AddTeamModal
          teamData={selectedteam}
          closeModal={() => setShowTeamEditModal(false)}
          showModal={showTeamEditModal}
          onTeamCreate={handleTeamEdit}
        />
        {showTeamDeleteModal && (
          <DeleteTeamModal
            message="Are you sure you want to remove this team?"
            subMessage="(The players in this team will not be removed)"
            cancelAction={() => {
              setShowTeamDeleteModal(false);
              resetTeamDeleteData();
            }}
            submitAction={() => deleteTeam(selectedteam)}
            isTeamModal
            loading={teamDeleteLoading}
            errorMessage={teamDeleteError ? errorMessage : ''}
          />
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ManageUserContainer;
