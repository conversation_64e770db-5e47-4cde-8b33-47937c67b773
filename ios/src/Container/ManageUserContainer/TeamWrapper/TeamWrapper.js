import React, { useState, useEffect, useCallback } from 'react';
import { View, FlatList, TouchableOpacity, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';

import TeamTile from '../../../components/ManageUsers/Teams/TeamTile';
import AddTeamModal from '../../../components/modal/AddEditTeamModal/AddTeamModal';
import customTeamWrapperStyle from './TeamWrapperStyle';
import useApi from '../../../hooks/useApi';
import {
  FOOTBALL_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../../constants/services';
import {
  CREATE_TEAM_FAILED,
  CREATE_TEAM_REQUEST,
  CREATE_TEAM_RESET,
  CREATE_TEAM_SUCCESS,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_FAIL,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_REQUEST,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_RESET,
  FETCH_TEAMS_FOR_USER_MANAGEMENT_SUCCESS,
  MANAGE_USERS_RESET,
} from '../../../store/actionTypes/ManageUsers/ManageUsersAction';
import { manageUsersteamInitialSize } from '../../../constants/constants';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { isTabDevice } from '../../../config/appConfig';
import useStyles from '../../../hooks/useStyles';
import useApiPromise from '../../../hooks/useApiPromise';

const TeamWrapper = ({ handleChange, selectedTeam, disabled }) => {
  const TeamWrapperStyle = useStyles(customTeamWrapperStyle);
  const isFocused = useIsFocused();
  const [showAddTeamModal, setShowAddTeamModal] = useState(false);
  const [fetchData] = useApi();
  const [fetchDataTotalUsers] = useApiPromise();
  const dispatch = useDispatch();
  const {
    createTeamSuccess,
    teamUpdateSuccess,
    allTeams,
    allTeamsPageNo,
    allTeamsLoading,
    stopFetchingTeams,
    teamDeleteSuccess,
    createTeamError,
    createTeamErrorMessage,
    createTeamLoading,
    allTeamsTotalRecords,
  } = useSelector(state => state?.manageUsers);

  const { creatUserSuccess } = useSelector(state => state?.addUser);

  useEffect(() => {
    setShowAddTeamModal(false);
  }, [selectedTeam]);

  useFocusEffect(
    useCallback(() => {
      // Do something when the screen is focused
      dispatch({ type: FETCH_TEAMS_FOR_USER_MANAGEMENT_RESET });
      loadTeams(1, manageUsersteamInitialSize);
      return () => {
        // Do something when the screen is unfocused
        dispatch({ type: MANAGE_USERS_RESET });
      };
    }, [])
  );

  useEffect(() => {
    if (teamUpdateSuccess || teamDeleteSuccess) {
      dispatch({ type: FETCH_TEAMS_FOR_USER_MANAGEMENT_RESET });
      loadTeams(1, manageUsersteamInitialSize);
    }
  }, [teamUpdateSuccess, teamDeleteSuccess]);

  const loadTeams = (pageNo, pageSize) => {
    fetchData(
      `/api/v1/teams?page=${pageNo}&size=${pageSize}`,
      FETCH_TEAMS_FOR_USER_MANAGEMENT_REQUEST,
      FETCH_TEAMS_FOR_USER_MANAGEMENT_SUCCESS,
      FETCH_TEAMS_FOR_USER_MANAGEMENT_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const renderItem = ({ item }) => (
    <TeamTile
      item={item}
      handleChange={handleChange}
      selectedTeam={selectedTeam}
      openAddTeamModal={() => setShowAddTeamModal(true)}
      disabled={disabled}
    />
  );

  const addTeamButton = () => [
    {
      _id: '-1',
      teamName: '+ Add Team',
    },
  ];

  const onTeamCreate = ({ teamName, colour }) => {
    fetchData(
      `/api/v1/teams`,
      CREATE_TEAM_REQUEST,
      CREATE_TEAM_SUCCESS,
      CREATE_TEAM_FAILED,
      {
        teamName,
        colour,
      },
      null,
      'POST',
      false,
      FOOTBALL_SERVICE
    );
  };

  const resetCreateTeam = () => {
    dispatch({
      type: CREATE_TEAM_RESET,
    });
  };

  useEffect(() => {
    if (createTeamSuccess) {
      setShowAddTeamModal(false);
      resetCreateTeam();
      dispatch({ type: FETCH_TEAMS_FOR_USER_MANAGEMENT_RESET });
      loadTeams(1, manageUsersteamInitialSize);
    }
  }, [createTeamSuccess]);

  const [totalUsers, setTotalUsers] = useState(0);

  const getTotalUsersCount = () => {
    let requestUrl = `/api/v1/users?page=1&size=1`;

    fetchDataTotalUsers(
      requestUrl,
      '-',
      '-',
      '-',
      null,
      '',
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    ).then(res => {
      setTotalUsers(res?.data?.totalRecords || 0);
    });
  };

  useEffect(() => {
    getTotalUsersCount();
  }, []);

  useEffect(() => {
    creatUserSuccess && getTotalUsersCount();
  }, [creatUserSuccess]);

  return allTeamsLoading && allTeamsPageNo === 0 ? (
    <ActivitySpinner />
  ) : (
    <View>
      <View style={TeamWrapperStyle.container}>
        <View style={TeamWrapperStyle.teamWrapper}>
          <TouchableOpacity onPress={() => handleChange('')}>
            <View
              style={
                selectedTeam === ''
                  ? TeamWrapperStyle.allUsersButtonSelected
                  : TeamWrapperStyle.allUsersButton
              }
            >
              <Text style={TeamWrapperStyle.allUsersButtonText}>All Users</Text>
              <View style={TeamWrapperStyle.allUsersButtonTextWrapper}>
                <Text style={TeamWrapperStyle.allUsersButtonText}>
                  {totalUsers}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
        <FlatList
          // horizontal={isTabDevice() ? false : true}
          numColumns={isTabDevice() ? 2 : 4}
          data={
            allTeams ? [...allTeams, ...addTeamButton()] : [...addTeamButton()]
          }
          renderItem={renderItem}
          keyExtractor={item => item._id}
          onEndReached={() => {
            if (!stopFetchingTeams && isFocused) {
              loadTeams(allTeamsPageNo + 1, manageUsersteamInitialSize);
            }
          }}
          style={TeamWrapperStyle.flatList}
        />
      </View>
      <AddTeamModal
        showModal={showAddTeamModal}
        closeModal={() => {
          setShowAddTeamModal(false);
          resetCreateTeam();
        }}
        onTeamCreate={onTeamCreate}
        message={createTeamError && createTeamErrorMessage}
        loading={createTeamLoading}
      />
    </View>
  );
};

export default TeamWrapper;
