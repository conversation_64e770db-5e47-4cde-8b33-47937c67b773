import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const TeamWrapperStyle = colors => ({
  container: isTabDevice()
    ? {
        width: '100%',
        height: hp('43%'),
        marginBottom: hp('5%'),
      }
    : {
        width: wp('96%'),
      },
  allUsersButton: isTabDevice()
    ? {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        width: wp('21%'),
        height: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.borderBlue,
        borderRadius: wp('4%'),
        width: wp('95.5%'),
        height: wp('13%'),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
        paddingLeft: wp('9%'),
        paddingRight: wp('9%'),
      },
  allUsersButtonSelected: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('1%'),
        width: wp('21%'),
        height: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('4%'),
        width: wp('95.5%'),
        height: wp('13%'),
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
        paddingLeft: wp('9%'),
        paddingRight: wp('9%'),
      },
  allUsersButtonText: isTabDevice()
    ? {
        fontSize: wp('1.3%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  allUsersButtonTextWrapper: {
    backgroundColor: colors.blackOpcaity,
    borderRadius: 10,
    paddingTop: 2,
    paddingBottom: 2,
    paddingLeft: 10,
    paddingRight: 10,
  },
  flatList: isTabDevice()
    ? {}
    : {
        height: wp('33%'),
      },
  teamWrapper: isTabDevice()
    ? {}
    : {
        marginRight: wp('2%'),
      },
});
export default TeamWrapperStyle;
