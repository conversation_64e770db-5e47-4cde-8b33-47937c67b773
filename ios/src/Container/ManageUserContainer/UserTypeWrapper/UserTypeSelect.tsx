import React from 'react';
import { View } from 'react-native';
import UserTypeTile from '../../../components/ManageUsers/UserTypes/UserTypeTile';
import useStyles from '../../../hooks/useStyles';
import customUserTypeSelectStyle from './UserTypeSelectStyle';

interface UserTypeSelectProps {
  handleChange: Function;
  selectedType: string;
  disabled: boolean;
}

const UserTypeSelect = ({
  handleChange,
  selectedType,
  disabled,
}: UserTypeSelectProps) => {
  const UserTypeSelectStyle = useStyles(customUserTypeSelectStyle);

  const users = [
    {
      id: 'COACH',
      typeName: 'Coaches',
    },
    {
      id: 'PLAYER',
      typeName: 'Players',
    },
    {
      id: 'PARENT',
      typeName: 'Parent',
    },
  ];

  return (
    <View style={UserTypeSelectStyle.container}>
      {users.map((user, index) => (
        <UserTypeTile
          item={user}
          handleChange={handleChange}
          selectedType={selectedType}
          disabled={disabled}
          key={index}
          index={index}
        />
      ))}
    </View>
  );
};

export default UserTypeSelect;
