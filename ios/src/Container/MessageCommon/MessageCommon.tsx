import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ForceModal from '../../components/modal/ForceModal/ForceModal';
import {
  forcedModalType,
  messageTypes,
  socketMessageTypes,
  WebSocketParams,
} from '../../constants/constants';
import {
  MESSAGING_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import {
  getArrayIndexUsingKey,
  messageDataMapping,
} from '../../helpers/common';
import useApi from '../../hooks/useApi';
import {
  TRY_WS_RE_CONNECT,
  SET_REFRESH_COUNT,
} from '../../store/actionTypes/common/commonActionTypes';
import {
  GET_CHAT_INFO_DETAILS_FOR_NEW_USER_FAIL,
  GET_CHAT_INFO_DETAILS_FOR_NEW_USER_REQUEST,
  GET_CHAT_INFO_DETAILS_FOR_NEW_USER_SUCCESS,
  GET_USER_DETAILS_FOR_NEW_USER_FAIL,
  GET_USER_DETAILS_FOR_NEW_USER_REQUEST,
  GET_USER_DETAILS_FOR_NEW_USER_SUCCESS,
  GET_FAST_CHAT_LATEST_MESSAGE_FAIL,
  GET_FAST_CHAT_LATEST_MESSAGE_REQUEST,
  GET_FAST_CHAT_LATEST_MESSAGE_SUCCESS,
  IMPORT_LAST_MESSAGE,
  IMPORT_MESSAGES,
  MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
  SET_CONNECTION_ID,
  SET_UNREAD_MESSAGE_COUNT,
  UPDATE_MESSAGE_COUNT_PERSONAL,
  UPDATE_MESSAGE_COUNT_TEAM,
  SET_REAL_TIME_PREVIEW_IMAGE,
  SET_REAL_TIME_VIDEO_PREVIEW_IMAGE,
  REMOVE_MESSAGE,
  DECREASE_UNREAD_MESSAGE_COUNT,
} from '../../store/actionTypes/Message/MessageAction';
import { singleMessageContentTypes } from '../../store/reducers/Message/MessageReducer';
import { RootStore } from '../../store/store';
import useApiPromiseArray from '../../hooks/useApiPromiseArray';
import { useNetInfo } from '@react-native-community/netinfo';

const MessageCommon = () => {
  const { userData } = useSelector((state: RootStore) => state.auth);
  const netInfo = useNetInfo();
  const [fetchUserDetails] = useApi();
  const [fetchChatDetails] = useApi();
  const {
    wsClient,
    wsIsConnect,
    refreshCount,
    refreshCountLimit,
    wsConnectionLoading,
  } = useSelector((state: RootStore) => state.common);

  const customRefreshCount = refreshCount?.[WebSocketParams.MESSAGE_WS];

  const messageWsClient = wsClient?.[WebSocketParams.MESSAGE_WS];
  const dispatch = useDispatch();
  const [newMessage, setNewMessage] = useState<any>(null);
  const navigation = useNavigation();

  const {
    selectedUsersForMessages,
    pastMessageChatInfo,
    selectedMessageType,
    selectedMessageChatId,
  } = useSelector((state: RootStore) => state.message);

  const fetchPastMsgChatMemberInfo = (userId: string) => {
    userId &&
      fetchUserDetails(
        `/api/v1/users?page=1&size=1&userIds=${userId}`,
        GET_USER_DETAILS_FOR_NEW_USER_REQUEST,
        GET_USER_DETAILS_FOR_NEW_USER_SUCCESS,
        GET_USER_DETAILS_FOR_NEW_USER_FAIL,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
  };


  const getChatDetails = (senderId: string) => {
    fetchChatDetails(
      `/api/v1/chats?type=PERSONAL&membersUserIds=${userData?.id},${senderId}`,
      GET_CHAT_INFO_DETAILS_FOR_NEW_USER_REQUEST,
      GET_CHAT_INFO_DETAILS_FOR_NEW_USER_SUCCESS,
      GET_CHAT_INFO_DETAILS_FOR_NEW_USER_FAIL,
      null,
      '',
      'GET',
      false,
      MESSAGING_SERVICE,
      { userId: userData?.id }
    );
  };

  const handleTabCount = (msgObject: singleMessageContentTypes | null) => {
    if (msgObject?.chatId && msgObject?.chatType) {
      if (msgObject?.chatType === messageTypes.PERSONAL) {
        msgObject?.chatId !==
          selectedMessageChatId?.[selectedUsersForMessages?.[0]?.id || 0]
            ?._id &&
          dispatch({
            type: UPDATE_MESSAGE_COUNT_PERSONAL,
            payload: {
              data: msgObject,
            },
          });
      } else {
        msgObject?.chatId !== Object.keys(selectedMessageChatId || {})?.[0] &&
          dispatch({
            type: UPDATE_MESSAGE_COUNT_TEAM,
            payload: {
              data: msgObject,
            },
          });
      }
    }
  };

  const customWsIsConnect = wsIsConnect?.[WebSocketParams.MESSAGE_WS];

  useEffect(() => {
    if (messageWsClient && customWsIsConnect) {
      messageWsClient.onmessage = e => {
        const { data } = e;

        const msgObject: singleMessageContentTypes | null = JSON.parse(
          data.toString()
        );

        if (msgObject?.isDeleted) {
          dispatch({
            type : REMOVE_MESSAGE, 
            payload: {
              senderUserId: msgObject.senderUserId,
              chatId : msgObject.chatId,
              messageId: msgObject._id,
              chatType: msgObject.chatType,
            },
          })          
          return
        }

        if (msgObject?.connectionId) {
          dispatch({
            type: SET_CONNECTION_ID,  
            payload: {
              data: msgObject?.connectionId,
            },
          });
        }

        const isChatHistoryAvailableIndex = getArrayIndexUsingKey(
          pastMessageChatInfo || [],
          '_id',
          msgObject?.chatId
        );

        handleTabCount(msgObject);

        if (
          msgObject?.socketMessageType === socketMessageTypes.CHAT_MESSAGE
        ) {
          if (selectedMessageType == msgObject?.chatType) {
            let selectedMessageUniqueId: string | null = null;
            if (selectedMessageType === messageTypes.PERSONAL) {
              selectedMessageUniqueId =
                selectedUsersForMessages?.[0]?.id || null;
              isChatHistoryAvailableIndex < 0 && setNewMessage(msgObject);
            } else {
              selectedMessageUniqueId = Object.keys(
                selectedMessageChatId || {}
              )?.[0];
              dispatch({
                type: MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
                payload: {
                  data: msgObject?.chatId,
                },
              });
            }

            if (
              selectedMessageType === messageTypes.PERSONAL
                ? selectedMessageUniqueId === msgObject?.senderUserId
                : selectedMessageUniqueId === msgObject?.chatId
            ) {
              dispatch({
                type: IMPORT_MESSAGES,
                payload: {
                  data: messageDataMapping(userData, [msgObject]),
                  customInput: selectedMessageUniqueId,
                },
              });
            } else {
              dispatch({
                type: SET_UNREAD_MESSAGE_COUNT,
                payload: { data: msgObject },
              });
            }

            dispatch({
              type: IMPORT_LAST_MESSAGE,
              payload: {
                data: msgObject,
              },
            });
          }
        } else if (
          msgObject?.socketMessageType ===
          socketMessageTypes.IMAGE_MESSAGE_COMPRESSED
        ) {
          if (selectedMessageType === messageTypes.PERSONAL) {
            msgObject?.chatId ===
              selectedMessageChatId?.[selectedUsersForMessages?.[0]?.id || 0]
                ?._id &&
              dispatch({
                type: SET_REAL_TIME_PREVIEW_IMAGE,
                payload: {
                  data: {
                    message: msgObject,
                    uniqueKey: selectedUsersForMessages?.[0]?.id,
                  },
                },
              });
          } else {
            msgObject?.chatId ===
              Object.keys(selectedMessageChatId || {})?.[0] &&
              dispatch({
                type: SET_REAL_TIME_PREVIEW_IMAGE,
                payload: {
                  data: {
                    message: msgObject,
                    uniqueKey: Object.keys(selectedMessageChatId || {})?.[0],
                  },
                },
              });
          }
        } else if (
          msgObject?.socketMessageType ===
          socketMessageTypes.VIDEO_MESSAGE_PREVIEW_IMAGE_GENERATED
        ) {
          let uniqueKeyId = null;
          if (selectedMessageType === messageTypes.PERSONAL) {
            const isChatIsEqual =
              msgObject?.chatId ===
              selectedMessageChatId?.[selectedUsersForMessages?.[0]?.id || 0]
                ?._id;
            if (isChatIsEqual) {
              uniqueKeyId = selectedUsersForMessages?.[0]?.id;
            }
          } else {
            const isChatIsEqual =
              msgObject?.chatId ===
              Object.keys(selectedMessageChatId || {})?.[0];
            if (isChatIsEqual) {
              uniqueKeyId = Object.keys(selectedMessageChatId || {})?.[0];
            }
          }

          uniqueKeyId &&
            dispatch({
              type: SET_REAL_TIME_VIDEO_PREVIEW_IMAGE,
              payload: {
                data: {
                  message: msgObject,
                  uniqueKey: uniqueKeyId,
                },
              },
            });
        }
      };
    }
  }, [
    messageWsClient,
    JSON.stringify(selectedUsersForMessages),
    JSON.stringify(selectedMessageChatId),
    selectedMessageType,
    customWsIsConnect,
  ]);

  useEffect(() => {
    if (newMessage) {
      fetchPastMsgChatMemberInfo(newMessage?.senderUserId);
      getChatDetails(newMessage?.senderUserId);
    }
  }, [newMessage]);

  const setRefreshCount = (count: any) => {
    dispatch({
      type: SET_REFRESH_COUNT,
      payload: count,
      customInput: WebSocketParams.MESSAGE_WS,
    });
  };

  useEffect(() => {
    return () => {
      setRefreshCount(0);
    };
  }, []);

  return (!messageWsClient && userData?.id) ||
    // @ts-ignore
    customRefreshCount >= refreshCountLimit ? (
    netInfo.isInternetReachable && (
      <ForceModal
        errorMessage=""
        type={forcedModalType.RE_ESTABLISH_WS}
        onClose={() => {
          setRefreshCount(0);
          dispatch({
            type: TRY_WS_RE_CONNECT,
            payload: { data: true },
            customInput: WebSocketParams.MESSAGE_WS,
          });
        }}
        goBack={() => navigation.navigate('landing')}
        isWsReconnect={wsConnectionLoading?.[WebSocketParams.MESSAGE_WS]}
      />
    )
  ) : (
    <></>
  );
};

export default MessageCommon;
