import React, { useEffect, useState } from 'react';
import { FlatList, View } from 'react-native';
import { useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import AddPlayerUpdateModal from '../../components/modal/AddPlayerUpdateModal/AddPlayerUpdateModal';
import EditPlayerUpdateModal from '../../components/modal/EditPlayerUpdateModal/EditPlayerUpdateModal';
import NoContentMessage from '../../components/NoContents/NoContentMessage';
// component
import PlayerInfoUpdate from '../../components/PlayerInfoUpdate/PlayerInfoUpdate';
import { isTabDevice } from '../../config/appConfig';
import { userRoleType } from '../../constants/constants';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import {
  PLAYER_ADD_UPDATE_FAIL,
  PLAYER_ADD_UPDATE_REQUEST,
  PLAYER_ADD_UPDATE_SUCCESS,
  PLAYER_DELETE_UPDATE_FAIL,
  PLAYER_DELETE_UPDATE_REQUEST,
  PLAYER_DELETE_UPDATE_SUCCESS,
  PLAYER_EDIT_UPDATE_FAIL,
  PLAYER_EDIT_UPDATE_REQUEST,
  PLAYER_EDIT_UPDATE_SUCCESS,
  PLAYER_INITIAL_UPDATE_FAIL,
  PLAYER_INITIAL_UPDATE_REQUEST,
  PLAYER_INITIAL_UPDATE_SUCCESS,
  PLAYER_MORE_UPDATE_FAIL,
  PLAYER_MORE_UPDATE_REQUEST,
  PLAYER_MORE_UPDATE_SUCCESS,
} from '../../store/actionTypes/player/playerAction';
// Style
import customPlayerInfoUpdateContainerStyle from './PlayerInfoUpdateContainerStyle';

const PlayerInfoUpdateContainer = ({ highlight }) => {
  const PlayerInfoUpdateContainerStyle = useStyles(
    customPlayerInfoUpdateContainerStyle
  );
  const {
    playerUpdate,
    playerUpdateLoading,
    playerUpdatePage,
    playerUpdatePageInitial,
    playerUpdatePageSize,
    playerUpdatePageSizeInitial,
  } = useSelector(state => state?.player);

  const { sportsProfileId } = useSelector(
    state => state?.playerInfo?.PlayerInfoData
  );

  const { userRole, userData } = useSelector(state => state?.auth);

  const [selectedUpdate, setSelectedUpdate] = useState(null);
  const [isEditModeOpen, setIsEditModeOpen] = useState(false);
  const [selectedUpdateData, setSelectedUpdateData] = useState(null);

  const [fetchData] = useApi();

  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;

  const getData = () => {
    fetchData(
      `/api/v1/sport-profiles/${
        isPlayer ? userData?.sportsProfileId : sportsProfileId
      }/updates?page=${playerUpdatePageInitial}&size=${playerUpdatePageSizeInitial}`,
      PLAYER_INITIAL_UPDATE_REQUEST,
      PLAYER_INITIAL_UPDATE_SUCCESS,
      PLAYER_INITIAL_UPDATE_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    getData();
  }, []);

  const loadMoreUpdate = () => {
    fetchData(
      `/api/v1/sport-profiles/${
        isPlayer ? userData?.sportsProfileId : sportsProfileId
      }/updates?page=${playerUpdatePage + 1}&size=${playerUpdatePageSize}`,
      PLAYER_MORE_UPDATE_REQUEST,
      PLAYER_MORE_UPDATE_SUCCESS,
      PLAYER_MORE_UPDATE_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const editUpdate = ({ message, createdDate, updateId }) => {
    fetchData(
      `/api/v1/updates/${updateId}`,
      PLAYER_EDIT_UPDATE_REQUEST,
      PLAYER_EDIT_UPDATE_SUCCESS,
      PLAYER_EDIT_UPDATE_FAIL,
      { createdDate, message },
      '',
      'PUT',
      null,
      FOOTBALL_SERVICE,
      { message, createdDate, updateId }
    );
  };

  const openEditModal = updateData => {
    setIsEditModeOpen(true);
    setSelectedUpdateData(updateData);
  };

  const deleteItem = deleteData => {
    fetchData(
      `/api/v1/updates?updateId=${deleteData._id}`,
      PLAYER_DELETE_UPDATE_REQUEST,
      PLAYER_DELETE_UPDATE_SUCCESS,
      PLAYER_DELETE_UPDATE_FAIL,
      null,
      '',
      'DELETE',
      null,
      FOOTBALL_SERVICE,
      deleteData
    );
  };

  const addUpdate = ({ message, createdDate, updateId }) => {
    fetchData(
      `/api/v1/sport-profiles/${sportsProfileId}/updates`,
      PLAYER_ADD_UPDATE_REQUEST,
      PLAYER_ADD_UPDATE_SUCCESS,
      PLAYER_ADD_UPDATE_FAIL,
      { createdDate, message },
      '',
      'POST',
      null,
      FOOTBALL_SERVICE
    );
  };

  const renderItem = ({ item }) => {
    return (
      <PlayerInfoUpdate
        key={item._id}
        selectedUpdate={selectedUpdate}
        item={item}
        setSelectedUpdate={setSelectedUpdate}
        openEditModal={openEditModal}
        deleteItem={deleteItem}
        style={PlayerInfoUpdateContainerStyle.dataRow}
        contentType={'Current Update'}
        isVisible
        isFileCountVisible={false}
        iconType={'document'}
      />
    );
  };

  return (
    <View style={PlayerInfoUpdateContainerStyle.PlayerInfoUpdateWrapper}>
      <View style={PlayerInfoUpdateContainerStyle.container}>
        {playerUpdateLoading ? (
          <ActivitySpinner />
        ) : playerUpdate?.length ? (
          <FlatList
            data={playerUpdate}
            renderItem={renderItem}
            keyExtractor={item => item._id}
            onEndReached={() => loadMoreUpdate()}
            contentContainerStyle={
              isTabDevice() ? { paddingBottom: 40 } : isParent ?  {paddingBottom: 320}: { paddingBottom: 230 }
            }
          />
        ) : (
          <NoContentMessage message="No Content" />
        )}
      </View>

      {isEditModeOpen && (
        <EditPlayerUpdateModal
          editUpdate={editUpdate}
          setIsEditModeOpen={setIsEditModeOpen}
          selectedUpdateData={selectedUpdateData}
        />
      )}

      {!isPlayer && !isParent && <AddPlayerUpdateModal addUpdate={addUpdate} />}
    </View>
  );
};

export default PlayerInfoUpdateContainer;
