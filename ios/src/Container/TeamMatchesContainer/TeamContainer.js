import React, { useEffect, useRef, useState } from 'react';
import { View, FlatList, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import {
  SET_SELECTED_TEAM,
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import customTeamContainerStyle from './TeamContainerStyle';

import Team from '../../components/Matches/Team';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import { userRoleType } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';
import useStyles from '../../hooks/useStyles';
import {  
  GET_CHILD_INFORMATION_FAILED,
  GET_CHILD_INFORMATION_REQUEST,
  GET_CHILD_INFORMATION_SUCCESS,
} from '../../store/actionTypes/common/commonActionTypes';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import SelectionModal from '../../components/modal/SelectionModal/SelectionModal';
import useApiPromise from '../../hooks/useApiPromise';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';

const TeamContainer = ({ selectedChild, setShowTeamData, setMatchScreen , onSelectedTeam}) => {
  const TeamContainerStyle = useStyles(customTeamContainerStyle);
  const { childInformation } = useSelector(state => state?.common);
  const dispatch = useDispatch();
  const [fetchData] = useApiPromise();
  const [fetchChildInformation] = useApi();
  const {
    selectedTeam
  } = useSelector(state => state?.team);
  const { userData, userRole } = useSelector(state => state?.auth);
  const { selectedMatchDetails } = useSelector(state => state?.matchPlan);
  const flatListRef = useRef(null);
  const [teamData, setTeamData] = useState(undefined)
  const [isLoading, setIsLoading] = useState(false);

  const [setIsTeamModalOpen, isTeamModalOpen, setSelectedModalTeam, selectedModalTeam] =
    useInputSelectModal();

  const isParent = userRole === userRoleType.PARENT;

  useEffect(() => {
    !isParent && fetchTeamList(userData)
  }, []);

  useEffect(() => {
    if (teamData?.data?.length > 0) {
      updateSelectedTeam();
    }
  }, [teamData, selectedTeam]);

  useEffect(() => {
    if (selectedChild?.id) {
      getChildInformation(selectedChild.id);
    }
  }, [selectedChild]);

  useEffect(() => {
    fetchTeamList(childInformation)
  }, [childInformation]);

  useEffect(() => {    
    if(isTabDevice() && onSelectedTeam &&  selectedTeam?._id){
      onSelectedTeam(selectedTeam)
    }
  }, [selectedTeam?._id])

  useEffect(() => {
    if (teamData?.data?.length) {
      if (selectedMatchDetails?.team) {
        dispatch({
          type: SET_SELECTED_TEAM,
          payload: selectedMatchDetails?.team,
        });
        return;
      }

      if (isParent) {
        dispatch({
          type: SET_SELECTED_TEAM,
          payload: teamData.data[0],
        });
        return;
      }

      if (selectedTeam?._id) {
        return;
      }

      dispatch({
        type: SET_SELECTED_TEAM,
        payload: teamData.data[0],
      });
    }
  }, [teamData, selectedChild]);

  /**
   * Changing size to 100, since we added search bar. We don't have a search Team with a modal
   */
  const filterUrl = (userData, page, size) => {   
    switch (userData?.type) {
      case userRoleType.PLAYER:
      case userRoleType.PARENT:
        return `/api/v1/sport-profiles/${userData?.sportsProfileId}/teams?page=${1}&size=${200}`;
      case userRoleType.COACH:
        return `/api/v1/teams?coachId=${userData?.id}&page=${1}&size=${200}`;
      default:
        return `/api/v1/teams?page=1&size=200`;
    }
  };

  const fetchTeamList = async (userData) => {
    if(!userData?.type){
      return
    }
    setTeamData(undefined)
    setIsLoading(true);
    dispatch({
      type: SET_SELECTED_TEAM,
      payload: null,
    });
    try {
      const result = await fetchData(
        filterUrl(userData?.type === userRoleType.PARENT ? userData : userData),
        TEAM_REQUEST,
        TEAM_SUCCESS,
        TEAM_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
      if(result.status == 204){
        onSelectedTeam && onSelectedTeam(undefined)
      }
      setTeamData(result.data)
    } catch (error) {
      setTeamData(undefined)
    } finally {
      setIsLoading(false);
    }
  }

  const getChildInformation = playerId => {
    fetchChildInformation(
      `/api/v1/sport-profiles?userIds=${playerId}`,
      GET_CHILD_INFORMATION_REQUEST,
      GET_CHILD_INFORMATION_SUCCESS,
      GET_CHILD_INFORMATION_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const setSelectedTeam = team => {
    setSelectedModalTeam([{
      value: team._id,
      label: team.teamName,
      textStyle: { color: team.colour }
    }]);

    dispatch({
      type: SET_SELECTED_TEAM,
      payload: team,
    });
    !isTabDevice() && onSelectedTeam &&  onSelectedTeam(team)
    const targetId = team._id
    if (flatListRef.current) {
      const index = teamData?.data?.findIndex(team => team._id === targetId) || 0;
      flatListRef.current.scrollToIndex({ index, animated: true });
    }
  };

  const updateSelectedTeam = () => {
    let targetId = selectedTeam?._id || teamData.data[0]._id;
    const targetedTeam = teamData?.data.find(team => team._id === targetId) || teamData.data[0];

    setSelectedModalTeam([{
      value: targetedTeam._id,
      label: targetedTeam.teamName,
      textStyle: { color: targetedTeam.colour }
    }]);
    if (selectedTeam?._id !== targetedTeam._id) {
      dispatch({
        type: SET_SELECTED_TEAM,
        payload: targetedTeam,
      });
    }
  };

  const renderItem = ({ item, index }) => (
    <Team
      item={item}
      setSelectedTeam={setSelectedTeam}
      selectedTeamID={selectedTeam?._id || undefined}
      setShowTeamData={setShowTeamData}
      setMatchScreen={setMatchScreen}
      index={index}
    />
  );
  return (
    <View style={TeamContainerStyle.container}>
      {teamData?.data?.length ? (
        <>
          <View style={TeamContainerStyle.teamSelectionContainer}>
            <View style={TeamContainerStyle.teamSelection}>
              <SelectionModal
                title={'Select Team'}
                items={
                  teamData?.data
                    ? teamData?.data?.map(({ _id, teamName, coachIds }) => ({
                      label: teamName,
                      value: _id,
                      textStyle: coachIds,
                    }))
                    : []
                }
                onCloseHook={setIsTeamModalOpen}
                onSelectItemHook={team => {
                  const targetId = team[0]?.value
                  if (targetId) {
                    setSelectedModalTeam(team);
                    setSelectedTeam(teamData?.data.find(team => team._id === targetId))
                    if (!isTabDevice() && setShowTeamData !== undefined) {
                      setShowTeamData(true);
                      setMatchScreen(true);
                    }
                  }
                }}
                isEnableAutoComplete
                selectedItemLabel={"Select Team"}
                isModalOpen={isTeamModalOpen}
              />
            </View>
            <View>
              <FlatList
                style={[TeamContainerStyle.list, isParent && { height : "85%"}]}
                data={teamData.data}
                renderItem={renderItem}
                keyExtractor={item => item._id}
                ref={flatListRef}
              />
            </View>
          </View>
        </>
      ) : (
        isLoading ? (<ActivitySpinner />)
          : !isLoading && (
            <Text style={TeamContainerStyle.noContent}>No Content</Text>
          )
      )}
    </View>
  );
};

export default TeamContainer;
