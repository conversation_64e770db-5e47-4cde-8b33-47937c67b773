import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const TeamContainerStyle = colors => ({
  container: isTabDevice()
    ? {
        width: '100%',
        height: hp('90%'),
      }
    : {
        width: '98%',
        height: hp('75%'),
        flexDirection: 'column',
        flexWrap: 'wrap',
      },
  list: isTabDevice()
    ? {
      height: '90%',
    }
    : {
        height: '83%',
        width: '100%',
      },
  noContent: {
    color: colors.white,
    fontFamily: 'Poppins-Bold',
    fontSize: wp('3%'),
    padding: wp('1.5%'),
  },
  teamSelection: {
    marginBottom: 10
  },
  listContainer: {
    paddingBottom: 10
  },
  teamSelectionContainer: {   
    flexDirection: 'column'
  }
});
export default TeamContainerStyle;
