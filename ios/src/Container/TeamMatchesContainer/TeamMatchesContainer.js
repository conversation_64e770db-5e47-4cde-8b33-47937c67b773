import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import customTeamMatchesContainerStyle from './TeamMatchesContainerStyle';
import ProfileImage from '../../Container/PlayerInfo/ProfileImage';

import { calendarFutureMonths } from '../../helpers/index';
import MonthSlider from '../../components/Matches/MonthSlider';
import MonthContainer from '../../Container/MonthContainer/MonthContainer';
import TeamContainer from '../../Container/TeamMatchesContainer/TeamContainer';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import { userRoleType } from '../../constants/constants';
import { MATCHES_RESET } from '../../store/actionTypes/MatchesInfo/MatchesAction';

import { RESET_TEAMS } from '../../store/actionTypes/Team/TeamAction';
import useStyles from '../../hooks/useStyles';
import ChildLabel from '../../components/ChildLabel/ChildLabel';
import {
  GET_CHILD_INFORMATION_FAILED,
  GET_CHILD_INFORMATION_REQUEST,
  GET_CHILD_INFORMATION_SUCCESS,
  IS_USER_IN_MATCHES,
} from '../../store/actionTypes/common/commonActionTypes';
import { FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useGetChildInformation from '../../hooks/useGetChildInformation';

import { isTabDevice } from '../../config/appConfig';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';

const TeamMatchesContainer = () => {
  const TeamMatchesContainerStyle = useStyles(customTeamMatchesContainerStyle);
  const dispatch = useDispatch();
  const [fetchChildInformation] = useApi();

  const { selectedTeam, teamDataLoading } = useSelector(state => state?.team);
  const { userData, userRole } = useSelector(state => state?.auth);
  const [selectedMonth, setSelectedMonth] = useState({});
  const [futureMonths, setFutureMonths] = useState({});
  const [showTeamData, setShowTeamData] = useState(false);
  const { children, isUserInMatches, isUserInMatchLog } = useSelector(
    state => state?.common
  );
  const isParent = userRole === userRoleType.PARENT;
  const [selectedChild, setSelectedChild] = useState();

  useEffect(() => {
    if (!isUserInMatches) {
      setShowTeamData(false);
    } else setShowTeamData(true);
  }, [isUserInMatches]);

  useEffect(() => {
    let monthList = calendarFutureMonths(12);
    setSelectedMonth(monthList[1]);
    setFutureMonths(monthList);
  }, []);

  useEffect(() => {
    dispatch({ type: RESET_TEAMS });
    return () => {
      resetRedux();
    };
  }, []);

  useEffect(() => {
    if(children?.length){
      const firstChild = children[0]
       setSelectedChild(firstChild);
       getChildInformation(firstChild.id)
    } 
  }, [children]);

  const setMatchScreen = response => {
    dispatch({
      type: IS_USER_IN_MATCHES,
      payload: response,
    });
  };

  const getChildInformation = playerId => {
    fetchChildInformation(
      `/api/v1/sport-profiles?userIds=${playerId}`,
      GET_CHILD_INFORMATION_REQUEST,
      GET_CHILD_INFORMATION_SUCCESS,
      GET_CHILD_INFORMATION_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  const resetRedux = () => {
    dispatch({ type: RESET_TEAMS });
    dispatch({ type: MATCHES_RESET });
  };

  return (
    <View style={TeamMatchesContainerStyle.container}>
      {!isTabDevice() && showTeamData ? null : (
        <View
          style={
            userData?.type === userRoleType.PLAYER
              ? TeamMatchesContainerStyle.leftView
              : TeamMatchesContainerStyle.leftViewPlayer
          }
        >
          {/* {userData?.type === userRoleType.PLAYER && (
          <View style={TeamMatchesContainerStyle.profileImage}>
            <ProfileImage
              isToggleEnabled={userData?.isAvailable}
              isEditMode={false}
              setIsEditMode={false}
              PlayerData={userData}
              isPlayer={true}
              isPlayerInfor
            />
          </View>
        )} */}
          {isParent && (
            <View
              style={
                isTabDevice()
                  ? { ...TeamMatchesContainerStyle.childWrapper }
                  : isUserInMatches
                  ? {
                      ...TeamMatchesContainerStyle.childWrapper,
                      display: 'none',
                    }
                  : { ...TeamMatchesContainerStyle.childWrapper }
              }
            >
              <ChildLabel
                data={children}
                setSelectedChild={data => {
                  resetRedux();
                  setSelectedChild(data);
                  getChildInformation(data.id)
                }}
                selectedChild={selectedChild}
              />
            </View>
          )}
          {isTabDevice() ? (
            <TeamContainer selectedChild={selectedChild} />
          ) : (
            !showTeamData && (
              <TeamContainer
                selectedChild={selectedChild}
                setShowTeamData={setShowTeamData}
                setMatchScreen={setMatchScreen}
              />
            )
          )}
        </View>
      )}
      <View
        style={
          isTabDevice()
            ? { ...TeamMatchesContainerStyle.rightView }
            : showTeamData
            ? { ...TeamMatchesContainerStyle.rightView }
            : { ...TeamMatchesContainerStyle.rightView, display: 'none' }
        }
      >
        {!teamDataLoading && selectedTeam && selectedTeam._id ? (
          <View>
            {!isTabDevice() && (
              <View>
                <Text style={TeamMatchesContainerStyle.rightViewTeamName}>
                  {selectedTeam?.teamName}
                </Text>
              </View>
            )}
            <MonthSlider
              selectedMonth={selectedMonth}
              months={futureMonths}
              setSelectedMonth={setSelectedMonth}
            />
            <MonthContainer selectedMonth={selectedMonth} />
          </View>
        ) : teamDataLoading ? (
          <ActivitySpinner />
        ) : null}
      </View>
    </View>
  );
};

export default TeamMatchesContainer;
