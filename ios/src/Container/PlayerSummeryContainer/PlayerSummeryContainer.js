import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import SummerRow from './SummerRow';
import customPlayerSummeryStyles from './PlayerSummeryStyles';
import useApi from '../../hooks/useApi';
import { AntDesign } from '@expo/vector-icons';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import {
  MATCH_SUMMERY_FAILED,
  MATCH_SUMMERY_SUCCESS,
  MATCH_SUMMERY_REQUEST,
  MATCH_MORE_SUMMERY_REQUEST,
  MATCH_MORE_SUMMERY_SUCCESS,
  MATCH_MORE_SUMMERY_FAILED,
  RESET_SUMMERY,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import useStyles from '../../hooks/useStyles';
import { isTabDevice } from '../../config/appConfig';

import { EVENT_SERVICE } from '../../constants/services';
import { summeryTitle } from '../../constants/constants';
const PlayerSummeryContainer = ({ setShowSummeryModal, selectedEvent }) => {
  const playerSummeryStyles = useStyles(customPlayerSummeryStyles);
  const dispatch = useDispatch();
  const { _id } = selectedEvent;
  const [summeryTitleArray, setSummeryTitleArray] = useState(summeryTitle);
  const [fetchData] = useApi();
  const {
    isSummeryLoading,
    summery,
    activities,
    summeryPage,
    summerySize,
    summeryInitialPage,
    summeryInitialSize,
  } = useSelector(state => state?.matchLog);
  useEffect(() => {
    let summeryTitleList = summeryTitle;

    activities.forEach(element => {
      const activityIndex = summeryTitleList.findIndex(
        item => item.key == element.code
      );

      if (activityIndex !== -1) {
        summeryTitleList[activityIndex].ids = [
          { id: element._id, key: element.code.toString() },
        ];
      }
    });

    const subInOutIndex = summeryTitleList.findIndex(
      item => item.key.toString() == 'SUB_IN_OUT'
    );

    if (subInOutIndex !== -1) {
      const subInIndex = activities.findIndex(item => item.code == 'SUB_IN');

      const subOutIndex = activities.findIndex(item => item.code == 'SUB_OUT');

      let subArray = [];

      if (subInIndex !== -1) {
        subArray.push({ id: activities[subInIndex]._id, key: 'SUB_IN' });
      }

      if (subOutIndex !== -1) {
        subArray.push({ id: activities[subOutIndex]._id, key: 'SUB_OUT' });
      }
      summeryTitleList[subInOutIndex].ids = subArray;
    }

    setSummeryTitleArray([...summeryTitleList]);
  }, [activities]);

  useEffect(() => {
    summeryInitialPage &&
      summeryInitialSize &&
      _id &&
      fetchData(
        `/api/v1/match-activities/${_id}/summaryByPlayer?page=${summeryInitialPage}&size=${summeryInitialSize}`,
        MATCH_SUMMERY_REQUEST,
        MATCH_SUMMERY_SUCCESS,
        MATCH_SUMMERY_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
  }, [summeryInitialPage, summeryInitialSize, _id]);

  const loadMoreSummery = () => {
    fetchData(
      `/api/v1/match-activities/${_id}/summaryByPlayer?page=${
        summeryPage + 1
      }&size=${summerySize}`,
      MATCH_MORE_SUMMERY_REQUEST,
      MATCH_MORE_SUMMERY_SUCCESS,
      MATCH_MORE_SUMMERY_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  useEffect(() => {
    return () => {
      dispatch({ type: RESET_SUMMERY });
    };
  }, []);

  const renderItem = ({ item }) => (
    <SummerRow summeryTitleArray={summeryTitleArray} item={item} />
  );

  return (
    <View style={playerSummeryStyles.container}>
      <View style={playerSummeryStyles.wrapper}>
        <View style={playerSummeryStyles.titleRow}>
          <Text style={playerSummeryStyles.title}>Summary</Text>

          <View style={playerSummeryStyles.buttons}>
            <TouchableOpacity onPress={() => setShowSummeryModal(false)}>
              <AntDesign
                name="close"
                size={20}
                color="#FFFFFF"
                style={playerSummeryStyles.closeButton}
              />
            </TouchableOpacity>
          </View>
        </View>
        {!isSummeryLoading ? (
          <View>
            {/* Header */}
            <View style={playerSummeryStyles.tableHeaderRow}>
              <View
                style={{
                  ...playerSummeryStyles.tableHeaderCell,
                  justifyContent: 'flex-start',
                }}
              >
                <Text style={playerSummeryStyles.tableHeaderText}>Name</Text>
              </View>
              {summeryTitleArray.map(item => (
                <View
                  style={playerSummeryStyles.tableHeaderCell}
                  key={item?.key}
                >
                  <Text style={playerSummeryStyles.tableHeaderText}>
                    {item?.title}
                  </Text>
                </View>
              ))}
            </View>

            <View style={playerSummeryStyles.tableBodyWrapper}>
              <FlatList
                data={summery || []}
                renderItem={renderItem}
                keyExtractor={item => item.playerId}
                style={playerSummeryStyles.flatList}
                onEndReached={() => loadMoreSummery()}
                contentContainerStyle={
                  isTabDevice() ? {} : { paddingBottom: 130 }
                }
              />
            </View>
          </View>
        ) : (
          <View style={playerSummeryStyles.loaderContainer}>
            <ActivitySpinner />
          </View>
        )}
      </View>
    </View>
  );
};

export default PlayerSummeryContainer;
