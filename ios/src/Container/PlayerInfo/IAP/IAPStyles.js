import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { colorPalette } from '../../../constants/constants';

const IAPStyle = colors => ({
  iapWrapper: isTabDevice()
    ? {
      width: wp('70%'),
      paddingBottom: wp('5%'),
    }
    : {
      width: wp('100%'),
      height: hp('75%'),
      paddingBottom: hp('10%'),
    },
  iapContent: isTabDevice()
    ? {}
    : {
      width: wp('100%'),
    },
  spinnerWrapper: isTabDevice()
    ? {
      width: wp('70'),
      height: '50%',
    }
    : {},
  iapData: isTabDevice()
    ? {
      // padding: wp('2%'),
      paddingTop: wp('2%'),
      paddingBottom: wp('2%'),
      borderRadius: wp('2%'),
      backgroundColor: colors.borderBlue,
      marginBottom: hp('1%'),
    }
    : {
      // width: wp('90%'),
      paddingTop: wp('4%'),
      paddingBottom: wp('2%'),
      borderRadius: wp('2%'),
      backgroundColor: colors.borderBlue,
      marginBottom: hp('1%'),
      marginLeft: wp('2%'),
      marginRight: wp('2%'),
    },
  categories: isTabDevice()
    ? {
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
      paddingBottom: hp('1%'),
      marginBottom: hp('3%'),
      borderBottomWidth: 1,
      borderBottomColor: colors.darkBlue,
    }
    : {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: hp('2%'),
      zIndex: 10,
    },
  btnComments: isTabDevice()
    ? {}
    : {
      backgroundColor: colors.aquaBlue,
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
      marginRight: wp('2%'),
      borderRadius: wp('1%'),
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: 35,
    },
  btnCommentTxt: isTabDevice()
    ? {}
    : {
      color: colors.white,
      fontSize: wp('3%'),
      fontFamily: 'Poppins-Medium'
    },
  stats: isTabDevice()
    ? {
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
    }
    : {
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
      // backgroundColor: colors.green,
    },
  statsData: {
    width: 100,
    height: 50,
    backgroundColor: '#1DC4D2',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  comment: isTabDevice()
    ? {
      backgroundColor: colors.borderBlue,
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
      paddingTop: hp('2%'),
      paddingBottom: hp('2%'),
      borderRadius: wp('2%'),
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    }
    : {
      backgroundColor: colors.borderBlue,
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
      paddingTop: hp('2%'),
      paddingBottom: hp('2%'),
      marginLeft: wp('2%'),
      marginRight: wp('2%'),
      borderRadius: wp('2%'),
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: hp('20%'),
      height: hp('17%'),
    },
  commentText: isTabDevice()
    ? {
      color: colors.white,
      backgroundColor: colors.borderBlue,
      borderBottomWidth: 0,
      width: '80%',
      height: 50,
      fontSize: wp('1.5%'),
    }
    : {
      color: colors.white,
      backgroundColor: colors.borderBlue,
      borderBottomWidth: 0,
      width: '100%',
      height: 50,
      fontSize: wp('2.5%'),
      marginBottom: wp('1%'),
    },
  saveButton: {
    backgroundColor: colors.aquaBlue,
    width: wp('8%'),
    height: hp('6%'),
    borderRadius: wp('1%'),
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveText: {
    color: colors.white,
    fontSize: wp('1.5%'),
  },
  commentsList: isTabDevice()
    ? {
      height: hp('35%'),
    }
    : {
      height: hp('40%'),
      paddingLeft: wp('2%'),
      paddingRight: wp('2%'),
    },
  mobileComment: {
    width: '100%',
  },
  mobileCommentsaveButton: {
    width: '100%',
    padding: wp('2%'),
    borderRadius: wp('3%'),
    backgroundColor: colors.aquaBlue,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  mobileCommentsaveText: {
    color: colors.white,
    fontSize: wp('4%'),
  },
  iapScroll: {},
  iapInnerScroll: {
    marginBottom: wp('20%'),
  },
});

export default IAPStyle;
