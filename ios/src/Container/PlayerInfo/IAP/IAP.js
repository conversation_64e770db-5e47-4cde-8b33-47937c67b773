import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { TextInput } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';
import UnsavedChangesModal from '../../../components/modal/UnsavedChangesModal/UnsavedChangesModal';
import IapCommentsModal from '../../../components/modal/IapCommentsModal/IapCommentsModal';
import NoContentMessage from '../../../components/NoContents/NoContentMessage';
import IAPCategories from '../../../components/PlayerInfo/IAP/IAPCategories';
import IAPStats from '../../../components/PlayerInfo/IAP/IAPStats';
import { isTabDevice } from '../../../config/appConfig';
import { FOOTBALL_SERVICE } from '../../../constants/services';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';
import { IS_USER_IN_IAP_CHART } from '../../../store/actionTypes/common/commonActionTypes';
import {
  IAP_STAT_SET_GRAPH_TITLE,
  PLAYER_IAP_CATEGORY_FAILED,
  PLAYER_IAP_CATEGORY_REQUEST,
  PLAYER_IAP_CATEGORY_SET,
  PLAYER_IAP_CATEGORY_SUCCESS,
  PLAYER_IAP_COMMENTS_FAILED,
  PLAYER_IAP_COMMENTS_REQUEST,
  PLAYER_IAP_COMMENTS_SUCCESS,
  PLAYER_IAP_COMMENTS_UPDATE_FAILED,
  PLAYER_IAP_COMMENTS_UPDATE_REQUEST,
  PLAYER_IAP_COMMENTS_UPDATE_SUCCESS,
  PLAYER_IAP_CRITERIA_FAILED,
  PLAYER_IAP_CRITERIA_REQUEST,
  PLAYER_IAP_CRITERIA_SUCCESS,
  PLAYER_IAP_STAT_FAILED,
  PLAYER_IAP_STAT_REQUEST,
  PLAYER_IAP_STAT_SAVE_FAILED,
  PLAYER_IAP_STAT_SAVE_REQUEST,
  PLAYER_IAP_STAT_SAVE_SUCCESS,
  PLAYER_IAP_STAT_SUCCESS,
  PLAYER_IAP_STAT_UPDATE_SUCCESS,
  PLAYER_IAP_SET_SHOW_STATS_GRAPH,
} from '../../../store/actionTypes/PlayerIAP/PlayerIapAction';
import { TRIGGER_IAP_TAB_CLICK } from '../../../store/actionTypes/TeamID/TeamID';
import IAPStatGraph from './IAPStatGraph';
import customIAPStyles from './IAPStyles';
import { ScrollView } from 'react-native-gesture-handler';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

const IAP = ({ isEditMode, isCommentsModalOpen, setIsCommentsModalOpen }) => {
  const IAPStyles = useStyles(customIAPStyles);
  const dispatch = useDispatch();
  const [comment, setComment] = useState('');
  const [updatedStats, setUpdatedStats] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [modalResponse, setModalResponse] = useState(false);
  const [selectedCategoryTemp, setSelectedCategoryTemp] = useState(null);
  const [mergedStats, setMergedStats] = useState([]);
  const [selectedCriteria, setSelectedCriteria] = useState(null);
  const [selectedCategoryName, setSelectedCategoryName] = useState('');

  const [IAPApi] = useApi();
  const [IAPCategoryApi] = useApi();
  const [IAPSaveApi] = useApi();
  const [GetCommentsApi] = useApi();
  const { sportsProfileId } = useSelector(
    state => state?.playerInfo?.PlayerInfoData
  );
  const {
    categories,
    categoryLoading,
    comments,
    commentsCurrentPage,
    commentsPageSize,
    criteria,
    selectedIAPCategory,
    stats,
    statsCurrentPage,
    statsPageSize,
    statsSaveLoading,
    statsSaveSuccess,
    statsLoading,
    showStatGraph,
  } = useSelector(state => state.playerIAP);

  const setShowStatGraph = payload => {
    dispatch({
      type: PLAYER_IAP_SET_SHOW_STATS_GRAPH,
      payload: payload,
    });
  };

  const iapCategoryRequest = () => {
    IAPCategoryApi(
      `/api/v1/iap-categories`,
      PLAYER_IAP_CATEGORY_REQUEST,
      PLAYER_IAP_CATEGORY_SUCCESS,
      PLAYER_IAP_CATEGORY_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    //get iap categories
    iapCategoryRequest();
    dispatch({
      type: TRIGGER_IAP_TAB_CLICK,
      payload: false,
    });

    return () => {
      //cleanup method to reset the player data on unmount
      const paginatedPayload = {
        data: [],
        page: 1,
        size: 1,
        totalRecords: 0,
      };

      dispatch({
        type: PLAYER_IAP_STAT_SUCCESS,
        payload: paginatedPayload,
      });
      dispatch({
        type: PLAYER_IAP_COMMENTS_SUCCESS,
        payload: paginatedPayload,
      });
      dispatch({
        type: PLAYER_IAP_CATEGORY_SUCCESS,
        payload: [],
      });
      dispatch({
        type: IS_USER_IN_IAP_CHART,
        payload: false,
      });
    };
  }, []);

  useEffect(() => {
    categories?.length && selectCategory(categories[0]);
  }, [categories]);

  useEffect(() => {
    if (statsSaveSuccess) {
      loadComments();
      loadIAPStats();
    }
  }, [statsSaveSuccess]);

  useEffect(() => {
    //load next pages of iap stats
    statsCurrentPage &&
      selectedIAPCategory &&
      IAPApi(
        `/api/v1/sport-profiles/${sportsProfileId}/iap-stats?categoryId=${selectedIAPCategory}&page=${
          statsCurrentPage + 1
        }&size=${statsPageSize}`,
        '',
        PLAYER_IAP_STAT_UPDATE_SUCCESS,
        '',
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
  }, [statsCurrentPage]);

  const loadMoreComments = () => {
    //load next page of comments
    IAPApi(
      `/api/v1/sport-profiles/${sportsProfileId}/iap-stats/comments?categoryId=${selectedIAPCategory}&page=${
        commentsCurrentPage + 1
      }&size=${commentsPageSize}`,
      PLAYER_IAP_COMMENTS_UPDATE_REQUEST,
      PLAYER_IAP_COMMENTS_UPDATE_SUCCESS,
      PLAYER_IAP_COMMENTS_UPDATE_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    //load iap comments
    selectedIAPCategory && loadComments();
    //load iap stats
    selectedIAPCategory && loadIAPStats();
  }, [selectedIAPCategory, sportsProfileId]);

  useEffect(() => {
const categoryObject = categories.find(
  item => item._id === selectedIAPCategory
);
setSelectedCategoryName(categoryObject?.name)
  }, [selectedIAPCategory,categories]);


  const loadComments = () => {
    GetCommentsApi(
      `/api/v1/sport-profiles/${sportsProfileId}/iap-stats/comments?categoryId=${selectedIAPCategory}&page=1&size=${commentsPageSize}`,
      PLAYER_IAP_COMMENTS_REQUEST,
      PLAYER_IAP_COMMENTS_SUCCESS,
      PLAYER_IAP_COMMENTS_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  const loadIAPStats = () => {
    IAPApi(
      `/api/v1/sport-profiles/${sportsProfileId}/iap-stats?categoryId=${selectedIAPCategory}&page=1&size=${statsPageSize}`,
      PLAYER_IAP_STAT_REQUEST,
      PLAYER_IAP_STAT_SUCCESS,
      PLAYER_IAP_STAT_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    if (modalResponse) {
      setModalResponse(false);
      setModalVisible(false);
      selectCategory(selectedCategoryTemp);
    } else {
      setModalVisible(false);
    }
  }, [modalResponse]);

  useEffect(() => {
    setUpdatedStats({});
  }, [stats]);

  useEffect(() => {
    if (criteria) {
      const { criteriaData } = stats[0] || {};
      let items = {};
      criteriaData &&
        criteriaData.forEach(stat => {
          items[stat.criteriaId] = {
            current: stat.current,
            target: stat.target,
            criteriaId: stat.criteriaId,
          };
        });
      const newCriteria = criteria.map(stat => {
        if (items[stat._id]) {
          const item = items[stat._id];
          return {
            ...stat,
            criteriaId: stat._id,
            current: item.current,
            target: item.target,
          };
        }
        return { ...stat, criteriaId: stat._id };
      });

      setMergedStats(newCriteria);
      setUpdatedStats(newCriteria);
    }
  }, [criteria, stats]);

  const onCategoryItemPress = item => {
    setSelectedCategoryTemp(item);
    checkStatsUpdated() ? setModalVisible(true) : selectCategory(item);
  };

  const selectCategory = item => {
    setComment('');
    dispatch({
      type: PLAYER_IAP_CATEGORY_SET,
      payload: item._id,
    });
    IAPApi(
      `/api/v1/iap-categories/${item._id}/criterion`,
      PLAYER_IAP_CRITERIA_REQUEST,
      PLAYER_IAP_CRITERIA_SUCCESS,
      PLAYER_IAP_CRITERIA_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  const [apiStats, setApiStats] = useState({});

  useEffect(() => {
    let statData = {};
    mergedStats.forEach(originalStat => {
      statData[originalStat._id] = updatedStats[originalStat._id] || {
        current: originalStat.current || 0,
        target: originalStat.target || 0,
        criteriaId: originalStat._id,
      };
    });
    setApiStats(statData);
  }, [JSON.stringify(mergedStats)]);

  const updateStats = stat => {
    if (apiStats[stat.criteriaId]) {
      apiStats[stat.criteriaId] = stat;
    }

    setApiStats(apiStats);
    setUpdatedStats(apiStats);
  };

  const saveStats = () => {
    if (updatedStats) {
      const postData = {
        comment: comment || 'Updated IAP stats',
        criteriaData: Object.values(updatedStats),
      };
      IAPSaveApi(
        `/api/v1/sport-profiles/${sportsProfileId}/iap-stats?categoryId=${selectedIAPCategory}`,
        PLAYER_IAP_STAT_SAVE_REQUEST,
        PLAYER_IAP_STAT_SAVE_SUCCESS,
        PLAYER_IAP_STAT_SAVE_FAILED,
        postData,
        null,
        'POST',
        false,
        FOOTBALL_SERVICE
      );
      setComment('');
    }
  };

  const checkStatsUpdated = () => {
    let unsavedChanges = false;
    if (stats?.length && Object.keys(updatedStats)) {
      const { criteriaData } = stats[0]; //get latest stats
      for (let index = 0; index < criteriaData?.length; index++) {
        const stat = criteriaData[index];
        if (updatedStats[stat.criteriaId]) {
          const updated = updatedStats[stat.criteriaId];
          if (
            stat.current != updated.current ||
            stat.target != updated.target
          ) {
            unsavedChanges = true;
            break;
          }
        }
      }
    }

    // If IAP stats are not initialized yet and stats are edited
    if (!stats?.length && Object.keys(updatedStats)) {
      let intialUpdated = Object.values(updatedStats);
      if (
        intialUpdated.some(
          ({ current, target }) =>
            (current !== undefined && current !== 0) ||
            (target !== undefined && target !== 0)
        )
      ) {
        unsavedChanges = true;
      } else {
        unsavedChanges = false;
      }
    }

    return unsavedChanges || comment;
  };

  const onCriteriaGraphSelect = criteriaId => {
    dispatch({
      type: IS_USER_IN_IAP_CHART,
      payload: true,
    });

    setSelectedCriteria(criteriaId);

    const selectedCategory = categories.find(
      item => item._id === selectedIAPCategory
    );
    const selectedCriteria = criteria.find(item => item._id === criteriaId);

    dispatch({
      type: IAP_STAT_SET_GRAPH_TITLE,
      payload: {
        category: selectedCategory?.name,
        criteria: selectedCriteria?.activity,
        criteriaId: selectedCriteria?._id,
      },
    });

    setShowStatGraph(true);
  };

  if (categoryLoading || statsSaveLoading) {
    return (
      <View style={IAPStyles.iapWrapper}>
        <View style={IAPStyles.spinnerWrapper}>
          <ActivitySpinner />
        </View>
      </View>
    );
  }

  if (showStatGraph) {
    return (
      <IAPStatGraph
        sportProfileId={sportsProfileId}
        iapCategoryId={selectedIAPCategory}
        criteriaId={selectedCriteria}
      />
    );
  }

  // if inside IAP charts and u click back => setShowStatGraph False

  TODO: if (!categories || !mergedStats) {
    <NoContentMessage message={'No data available'} />;
  }
  return (
    <View style={IAPStyles.iapWrapper}>
      <KeyboardAwareScrollView
        contentContainerStyle={IAPStyles.iapInnerScroll}
        scrollEnabled={!isTabDevice()}
        extraScrollHeight={isTabDevice() ? 200 : 100}
      >
        <ScrollView
          style={{height: isTabDevice() ? hp('70%') : hp('45%')}}
        >
          <View style={IAPStyles.iapContent}>
            <View style={IAPStyles.iapData}>
              <View style={IAPStyles.categories}>
                <IAPCategories
                  title={'Select IAP Category'}
                  categories={categories}
                  onCategoryItemPress={onCategoryItemPress}
                  selectedIAPCategory={selectedIAPCategory}
                  selectedCategoryTemp={selectedCategoryTemp}
                />
                 {!isTabDevice() &&  
                  <TouchableOpacity
                    onPress={() => setIsCommentsModalOpen(true)}
                    style={IAPStyles.btnComments} 
                  > 
                    <Text style={IAPStyles.btnCommentTxt}>
                      Comments
                    </Text>
                  </TouchableOpacity>
                  }
              </View>
              {statsLoading ? (
                <View style={IAPStyles.spinnerWrapper}>
                  <ActivitySpinner />
                </View>
              ) : (
                <View style={IAPStyles.stats}>
                  <IAPStats
                    statsDefinition={mergedStats}
                    statsData={stats}
                    setUpdatedStats={updateStats}
                    isEditMode={isEditMode}
                    onCriteriaGraphSelect={onCriteriaGraphSelect}
                  />
                </View>
              )}
            </View>

            {isEditMode && criteria && criteria?.length && (
              <View style={IAPStyles.comment}>
                <TextInput
                  style={IAPStyles.commentText}
                  onChangeText={text => setComment(text)}
                  value={comment}
                  placeholder="Type your comment here"
                  placeholderTextColor="#595959"
                  disableFullscreenUI={true}
                  returnKeyType={'go'}
                  blurOnSubmit={false}
                  theme={{ colors: { text: 'white', primary: '#1DC4D2' } }}
                />
                {isTabDevice() ? (
                  <TouchableOpacity
                    style={IAPStyles.saveButton}
                    onPress={() => saveStats()}
                  >
                    <View>
                      <Text style={IAPStyles.saveText}>Save</Text>
                    </View>
                  </TouchableOpacity>
                ) : (
                  <View style={IAPStyles.mobileComment}>
                    <TouchableOpacity
                      style={IAPStyles.mobileCommentsaveButton}
                      onPress={() => saveStats()}
                    >
                      <View>
                        <Text style={IAPStyles.mobileCommentsaveText}>Save</Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                )}
              </View> 
            )}
          </View>
        </ScrollView>
      </KeyboardAwareScrollView>

      <UnsavedChangesModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        setModalResponse={setModalResponse}
      />
      {
        isCommentsModalOpen && <IapCommentsModal setIsIapCommentsModalOpen={setIsCommentsModalOpen} comments={comments}
        loadMoreComments={loadMoreComments} mergedStats={mergedStats} selectedCategoryName={selectedCategoryName}/>
      }
    </View>
  );
};
export default IAP;
