import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import RNEChartsPro from 'react-native-echarts-pro';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { months } from '../../../constants/constants';
import { addLeadingZeros } from '../../../helpers';
import useStyles from '../../../hooks/useStyles';
import customIAPStatGraphStyles from './IAPStatGraphStyles.js';

export default function Chart({
  statGraphCategoryName,
  statGraphCriteriaName,
  summarySelectedDetails,
  currentValues,
  targetValues,
}: any) {
  const IAPStatGraphStyles = useStyles(customIAPStatGraphStyles);

  const graphData: any = {
    series: [
      {
        name: 'Current',
        type: 'line',
        symbol: 'circle',
        data: [...currentValues],
        symbolSize: isTabDevice() ? '10' : '4',
        itemStyle: { color: '#fff' },
        lineStyle: {
          type: 'solid',
          color: '#3a5c6b',
          width: isTabDevice() ? '3' : '1',
        },

        areaStyle: {
          color: '#1DC4D2',
          opacity: 0.02,
        },
      },

      {
        name: 'Target',
        type: 'line',
        symbol: 'circle',
        data: [...targetValues],
        symbolSize: isTabDevice() ? '10' : '4',
        itemStyle: { color: '#36d982' },
        lineStyle: {
          type: 'solid',
          color: '#1d423f',
          width: isTabDevice() ? '3' : '1',
        },
        areaStyle: {
          color: '#36d982',
          opacity: 0.02,
        },
      },
    ],
    legendNames: ['Current', 'Target'],
    name: 'Total Touches',
  };
  const selectedType: any = 'totalTouchesCount';

  const getFormattedDate = (dateString: string) => {
    const dateObject = new Date(dateString);
    const month = months[dateObject.getMonth()].slice(0, 3);
    const date = addLeadingZeros(dateObject.getDate());
    const year = dateObject.getFullYear();

    return { month, date, year };
  };

  const fromDate =
    summarySelectedDetails?.summaryFromDate &&
    getFormattedDate(summarySelectedDetails.summaryFromDate);

  const toDate =
    summarySelectedDetails?.summaryToDate &&
    getFormattedDate(summarySelectedDetails.summaryToDate);

  const setDateRange = (fromDate: any, toDate: any) => {
    if (fromDate?.year === toDate?.year) {
      return `${fromDate?.month} ${fromDate?.date}  -  ${toDate?.month} ${toDate?.date} ${toDate?.year} `;
    } else {
      return `${fromDate?.month} ${fromDate?.date} ${fromDate?.year}  -  ${toDate?.month} ${toDate?.date} ${toDate?.year} `;
    }
  };

  const option = useMemo(() => {
    return {
      dataZoom: [
        {
          id: 'dataZoomX',
          type: 'inside',
          xAxisIndex: [0],
          filterMode: 'none',
        },
      ],
      tooltip: {
        axisPointer: {
          type: 'cross',
          label: {
            show: false,
          },
        },
        show: true,
        trigger: 'axis',
        position: (pos: any, params: any, el: any, elRect: any, size: any) => {
          const obj: any = { top: 10 };
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
          return obj;
        },
        formatter: function (params: any) {
          const selectedData = params[0];
          const chartdate = echarts.format.formatTime(
            `dd/MM/yyyy`,
            selectedData.value[0]
          );

          const hours = Number(new Date(selectedData.value[0]).getHours());
          const hourString = String(hours >= 12 ? 24 - hours : hours);
          const min = Number(new Date(selectedData.value[0]).getMinutes());
          const chartTime = `${
            hourString.length == 1 ? '0' : ''
          }${hourString}:${String(min).length == 1 ? '0' : ''}${min}`;

          let val =
            '<li style="list-style:none">' +
            params[0].marker +
            params[0].seriesName +
            '&nbsp;&nbsp;' +
            params[0].value[1] +
            '</li>';

          if (params.length > 1) {
            val =
              val +
              '<li style="list-style:none">' +
              params[1].marker +
              params[1].seriesName +
              '&nbsp;&nbsp;' +
              params[1].value[1] +
              '</li>';
          }

          return chartdate + ` ${chartTime} ${hours >= 12 ? 'PM' : 'AM'}` + val;
        },
      },
      legend: {
        data: ['current', 'target'],
        align: 'left',
        right: 0,
        textStyle: {
          color: 'white',
          fontSize: isTabDevice() ? 16 : 11,
        },
        itemGap: 30,
      },
      xAxis: {
        type: 'time',
        show: true,
        name: setDateRange(fromDate, toDate),
        nameGap: 30,
        nameLocation: 'center',
        nameTextStyle: {
          fontSize: isTabDevice() ? 14 : 9,
          color: 'white',
          fontWeight: 'bold',
          padding: [7, 0, 0, 0],
        },
        boundaryGap: false,

        axisLabel: {
          fontSize: isTabDevice() ? 12 : 9,
          interval: 'auto',
          rotate: 45,
          textStyle: {
            color: 'white',
          },
        },
      },
      yAxis: {
        type: 'value',
        show: true,
        name: `${statGraphCategoryName} - ${statGraphCriteriaName}`,
        nameLocation: 'end',
        nameGap: 20,
        nameTextStyle: {
          fontSize: isTabDevice() ? 16 : 9,
          color: 'white',
          align: 'left',
          fontWeight: 'bold',
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: true,
        },
        axisLabel: {
          textStyle: {
            fontSize: isTabDevice() ? 12 : 9,
            color: 'white',
            align: 'right',
          },
        },
      },
      animation: true,
      series: graphData.series,
    };
  }, [graphData, selectedType]);

  return (
    <View
      style={{
        height: isTabDevice() ? hp('70%') : hp('25%'),
        width: isTabDevice() ? wp('70%') : wp('100%'),
      }}
    >
      <View style={IAPStatGraphStyles.legendIconContainer}>
        <View style={IAPStatGraphStyles.legendIconWrapper}>
          <View
            style={[IAPStatGraphStyles.legendIcon, { backgroundColor: '#FFF' }]}
          ></View>
          <Text style={IAPStatGraphStyles.legendIconText}>Current</Text>
        </View>
        <View style={IAPStatGraphStyles.legendIconWrapper}>
          <View
            style={[
              IAPStatGraphStyles.legendIcon,
              { backgroundColor: '#36d982' },
            ]}
          ></View>
          <Text style={IAPStatGraphStyles.legendIconText}>Target</Text>
        </View>
      </View>
      <RNEChartsPro
        height={isTabDevice() ? hp('65%') : hp('25%')}
        width={isTabDevice() ? wp('70%') : wp('100%')}
        option={option}
      />
    </View>
  );
}
