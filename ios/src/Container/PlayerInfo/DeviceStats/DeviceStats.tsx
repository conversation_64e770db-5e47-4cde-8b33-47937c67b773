import React, { FC, useEffect, useState, useMemo } from 'react';
import { Text, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';
import CollectedData from '../../../components/DeviceStats/CollectedData/CollectedData';
import DeviceStatsCategory from '../../../components/DeviceStats/DeviceStatsCategory/DeviceStatsCategory';
import EventDetail from '../../../components/DeviceStats/EventDetail/Events';
import DateRangeModal from '../../../components/modal/DaterangeModal/DateRangeModal';
import UnsavedChangesModal from '../../../components/modal/UnsavedChangesModal/UnsavedChangesModal';
import { isTabDevice } from '../../../config/appConfig';
import {
  SET_SELECTED_EVENT_TYPE,
  CLEAR_SESSION_DATA,
  SET_SESSION_PAGE_COUNT,
  SET_SELECTED_FILTER_TYPE,
  CLEAR_PLAYER_MAKER_DATA,
} from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import { useSelector, useDispatch } from 'react-redux';
import useGetSessionData from '../../../hooks/useGetSessionData';
import { deviceStatfilterList, userRoleType } from '../../../constants/constants';
import {
  DeviceStatsSessionType,
  DEVICE_STATS_CATEGORY_LIST,
} from '../../../constants/data';
import { PLAYER_MAKER_SERVICE } from '../../../constants/services';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';
import {
  DELETE_MAPPED_USER_FAIL,
  DELETE_MAPPED_USER_REQUEST,
  DELETE_MAPPED_USER_SUCCESS,
  IS_USER_CONNECTED_WITH_PLAYER_MAKER_FAIL,
  IS_USER_CONNECTED_WITH_PLAYER_MAKER_REQUEST,
  IS_USER_CONNECTED_WITH_PLAYER_MAKER_SUCCESS,
} from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import { RootStore } from '../../../store/store';
import DeviceConfigureContainer from './DeviceConfigureContainer';
import customDeviceStatsStyle from './DeviceStatsStyle';
import SelectPlayerDeviceContainer from './SelectPlayerDeviceContainer';
import SummaryDateRangeModal from '../../../components/DeviceStats/SummaryDateRangeModal/SummaryDateRangeModal';

interface IDeviceStatsType {
  PlayerInfoData: {
    userId: string;
    firstName: string;
    lastName: string;
    profileImageUrl: string;
    emailId: string;
    type: string;
    sportsProfileId: string;
    isAvailable: boolean;
  };
}

const DeviceStats: FC<IDeviceStatsType> = ({ PlayerInfoData }) => {
  const {
    sessionData,
    currentSessionId,
    selectedEventType,
    sessionDataLoading,
    sessionDataTotalRecords,
    sessionDataCurrentPageCount,
    sessionPaginationCount,
    selectedFilterType,
    selectedDeviceStateType,
    PlayerMakerSyncedData,
    isPlayerMakerSyncedLoading,
    isSummaryOpenModal,
    summarySelectedDetails,
    hasTrainingDataForSession,
    hasMatchDataForSession,
    hasTrainingDataForSummary,
    hasMatchDataForSummary,
  } = useSelector((state: RootStore) => state?.deviceStats);

  const { userData } = useSelector((state : any) => state?.auth);
  const isParent = userRoleType.PARENT === userData?.type;

  const isDataEmptyForSession =
    !hasTrainingDataForSession && !hasMatchDataForSession;
  const isDataEmptyForSummary =
    !hasTrainingDataForSummary && !hasMatchDataForSummary;

  const isDataEmpty =
    selectedDeviceStateType == DeviceStatsSessionType
      ? isDataEmptyForSession
      : isDataEmptyForSummary;

  const deviceStatsStyle = useStyles(customDeviceStatsStyle);
  const [removeMappedUser] = useApi();
  const dispatch = useDispatch();
  const [isUnsyncModalOpen, setIsUnsyncModalOpen] = useState(false);

  const {
    getInitialSessionData,
    getSessionData,
    getSessionStat,
    getSummaryStat,
  } = useGetSessionData();

  const [isUserConnectedToPlayerMaker] = useApi();

  const isDeviceSynced = !!Object.keys(PlayerMakerSyncedData || {})?.length;

  const isUserConnectedWithPlayerMaker = () => {
    PlayerInfoData?.userId &&
      isUserConnectedToPlayerMaker(
        `/api/v1/playermaker-mapped-users?userId=${PlayerInfoData.userId}`,
        IS_USER_CONNECTED_WITH_PLAYER_MAKER_REQUEST,
        IS_USER_CONNECTED_WITH_PLAYER_MAKER_SUCCESS,
        IS_USER_CONNECTED_WITH_PLAYER_MAKER_FAIL,
        null,
        '',
        'GET',
        false,
        PLAYER_MAKER_SERVICE
      );
  };
  useEffect(() => {
    isUserConnectedWithPlayerMaker();
  }, []);

  const setSelectedEventType = (data: string) => {
    dispatch({
      type: SET_SELECTED_EVENT_TYPE,
      payload: { data },
    });
  };

  const setSessionPaginationCount = (data: number) => {
    dispatch({
      type: SET_SESSION_PAGE_COUNT,
      payload: { data },
    });
  };

  const currentSessionData = useMemo(
    () => sessionData?.[sessionPaginationCount],
    [sessionPaginationCount, sessionData]
  );

  useEffect(() => {
    PlayerMakerSyncedData?.pmUserId &&
      getInitialSessionData(PlayerMakerSyncedData?.pmUserId);
  }, [PlayerMakerSyncedData]);

  const handlePrev = () => {
    if (sessionData?.length - 1 === sessionPaginationCount) {
      onReachEndHandler();
    }
    if (sessionDataTotalRecords - 1 > sessionPaginationCount) {
      setSessionPaginationCount(sessionPaginationCount + 1);
      dispatch({
        type: SET_SELECTED_FILTER_TYPE,
        payload: {
          data: deviceStatfilterList[0].id,
        },
      });
      if (PlayerMakerSyncedData?.pmUserId && currentSessionData?.phaseId) {
        getSessionStat(
          PlayerMakerSyncedData?.pmUserId,
          sessionData?.[sessionPaginationCount + 1]?.sessionId,
          deviceStatfilterList[0].id,
          sessionData?.[sessionPaginationCount + 1]?.phaseId
        );
      }
    }
  };

  const handleNext = () => {
    if (sessionPaginationCount > 0) {
      setSessionPaginationCount(sessionPaginationCount - 1);
      dispatch({
        type: SET_SELECTED_FILTER_TYPE,
        payload: {
          data: deviceStatfilterList[0].id,
        },
      });
      if (PlayerMakerSyncedData && currentSessionData?.phaseId) {
        getSessionStat(
          PlayerMakerSyncedData?.pmUserId,
          sessionData?.[sessionPaginationCount - 1].sessionId,
          deviceStatfilterList[0].id,
          sessionData?.[sessionPaginationCount - 1].phaseId
        );
      }
    } else return;
  };

  const onReachEndHandler = () => {
    if (sessionDataTotalRecords > (sessionData?.length || 0)) {
      PlayerMakerSyncedData &&
        getSessionData(
          PlayerMakerSyncedData?.pmUserId,
          selectedEventType || '',
          sessionDataCurrentPageCount + 1
        );
    }
  };

  const cleanPrevSessionData = () => {
    dispatch({
      type: CLEAR_SESSION_DATA,
    });
    setSessionPaginationCount(0);
  };

  useEffect(() => {
    const pmUserId = PlayerMakerSyncedData?.pmUserId;
    cleanPrevSessionData();
    if (selectedDeviceStateType === DEVICE_STATS_CATEGORY_LIST[0].key) {
      selectedEventType &&
        pmUserId &&
        getSessionData(pmUserId, selectedEventType);
    } else {
      pmUserId &&
        summarySelectedDetails &&
        summarySelectedDetails.summarySelectedTeam?.length &&
        summarySelectedDetails.summarySelectedPosition?.length &&
        summarySelectedDetails.summaryFromDate &&
        summarySelectedDetails.summaryToDate &&
        selectedEventType &&
        getSummaryStat(
          pmUserId,
          summarySelectedDetails.summaryFromDate?.toJSON(),
          summarySelectedDetails.summaryToDate?.toJSON(),
          selectedFilterType,
          selectedEventType,
          summarySelectedDetails.summarySelectedTeam[0].value,
          summarySelectedDetails.summarySelectedPosition[0].value
        );
    }
  }, [
    selectedEventType,
    selectedEventType,
    PlayerMakerSyncedData,
    selectedDeviceStateType,
  ]);

  const cleanupPlayerMakerData = () => {
    dispatch({
      type: CLEAR_SESSION_DATA,
    });
    dispatch({
      type: CLEAR_PLAYER_MAKER_DATA,
    });
  };

  useEffect(() => {
    return () => {
      cleanupPlayerMakerData();
    };
  }, []);

  const renderContent = () => {
    if (isDataEmpty) {
      return (
        <View style={deviceStatsStyle.statsWrapper}>
          <Text style={deviceStatsStyle.txtWhite}>
            No data avalable for this player ID yet!
          </Text>
        </View>
      );
    }

    return (
      <>
        <View style={deviceStatsStyle.contentWrapper}>
          <EventDetail
            setSelectedEventType={setSelectedEventType}
            data={currentSessionData}
            handlePrev={handlePrev}
            handleNext={handleNext}
            loading={sessionDataLoading}
            selectedEventType={selectedEventType}
          />
        </View>
        <View style={deviceStatsStyle.contentWrapper}>
          <CollectedData selectedType={selectedDeviceStateType} />
        </View>
      </>
    );
  };

  if (isPlayerMakerSyncedLoading) {
    return (
      <View style={deviceStatsStyle.wrapper}>
        <View style={deviceStatsStyle.content}>
          <View style={deviceStatsStyle.statsWrapper}>
            <ActivitySpinner />
          </View>
        </View>
      </View>
    );
  }

  if (!isDeviceSynced) {
    return (
      <View style={deviceStatsStyle.wrapper}>
        <View style={deviceStatsStyle.spinnerWrapper}>
          {!isTabDevice() && <SelectPlayerDeviceContainer />}
          <Text style={deviceStatsStyle.headerText}>Device Stats</Text>
          <DeviceConfigureContainer
            onSuccessSync={isUserConnectedWithPlayerMaker}
          />
        </View>
      </View>
    );
  }

  const deleteMappedUser = () => {
    PlayerMakerSyncedData?.pmUserId &&
      removeMappedUser(
        `/api/v1/playermaker-mapped-users/${PlayerMakerSyncedData?.pmUserId}`,
        DELETE_MAPPED_USER_REQUEST,
        DELETE_MAPPED_USER_SUCCESS,
        DELETE_MAPPED_USER_FAIL,
        null,
        '',
        'DELETE',
        false,
        PLAYER_MAKER_SERVICE,
        ''
      );
  };

  return (
    <View style={deviceStatsStyle.wrapper}>
      <ScrollView 
      contentContainerStyle = {
        isParent && !isTabDevice() && {paddingBottom : 100 }
      }
      >
        {!isTabDevice() && <SelectPlayerDeviceContainer />}
        <View
          style={deviceStatsStyle.content}
          onStartShouldSetResponder={() => true}
        >
          <DeviceStatsCategory
            isUnsyncModalOpen={isUnsyncModalOpen}
            setIsUnsyncModalOpen={setIsUnsyncModalOpen}
            pmUserId={PlayerMakerSyncedData?.pmUserId}
            sessionData={sessionData}
          />
          {renderContent()}
          {isUnsyncModalOpen && (
            <UnsavedChangesModal
              modalVisible={isUnsyncModalOpen}
              setModalVisible={() => setIsUnsyncModalOpen(false)}
              setModalResponse={(res: boolean) => res && deleteMappedUser()}
              message={'Are you sure you want to unsync the device'}
            />
          )}
          {isSummaryOpenModal && (
            <SummaryDateRangeModal modalVisible={isSummaryOpenModal} />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default DeviceStats;
