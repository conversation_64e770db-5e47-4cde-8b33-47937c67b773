import React, { FC, useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ApplicationUserSearchModal from '../../../components/modal/DeviceStatsModal/ApplicationUserSearchModal';
import SyncApplicationPermissionModal from '../../../components/modal/DeviceStatsModal/SyncApplicationPermissionModal';
import SyncApplicationSuccessModal from '../../../components/modal/DeviceStatsModal/SyncApplicationSuccessModal';
import { playerMakerTexts, userRoleType } from '../../../constants/constants';
import { PLAYER_MAKER_SERVICE } from '../../../constants/services';
import useApi from '../../../hooks/useApi';
import useApiPromise from '../../../hooks/useApiPromise';
import useStyles from '../../../hooks/useStyles';
import {
  CLEAR_SEARCH_PM_DATA,
  GET_USER_DETAILS_FROM_PM_FAIL,
  GET_USER_DETAILS_FROM_PM_REQUEST,
  GET_USER_DETAILS_FROM_PM_SUCCESS,
  POST_USER_DETAILS_PM_FAIL,
  POST_USER_DETAILS_PM_REQUEST,
  POST_USER_DETAILS_PM_SUCCESS,
} from '../../../store/actionTypes/DeviceStats/DeviceStatsActions';
import { RootStore } from '../../../store/store';
import customDeviceStatsStyle from './DeviceStatsStyle';

type DeviceConfigureContainerType = {
  onSuccessSync: Function;
};

const DeviceConfigureContainer: FC<DeviceConfigureContainerType> = ({
  onSuccessSync,
}) => {
  const deviceStatsStyle = useStyles(customDeviceStatsStyle);
  const [getUserPMDetails] = useApi();
  const [postPMUserSync] = useApiPromise();
  const dispatch = useDispatch();
  const { userDetailsFromPM, userDetailsFromPMLoading, postPMuserDataLoading } =
    useSelector((state: RootStore) => state?.deviceStats);
  const { PlayerInfoData } = useSelector((state: any) => state?.playerInfo);
  const { userData } = useSelector((state: RootStore) => state?.auth);
  const [isErrorMessage, setErrorMessage] = useState({
    isError: false,
    message: '',
  });

  const [isUserSearchModalOpen, setIsUserSearchModalOpen] =
    useState<boolean>(false);
  const [isConfirmSyncModalOpen, setIsConfirmModalOpen] =
    useState<boolean>(false);
  const [isSyncSuccessModalOpen, setIsSyncSuccessModalOpen] =
    useState<boolean>(false);
  const [isPlayerDetailsScreen, setIsPlayerDetailsScreen] =
    useState<boolean>(false);
  const [tempPMuserId, setTempPMUserId] = useState<string>('');

  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;

  const onClickSyncProvider = () => {
    dispatch({ type: CLEAR_SEARCH_PM_DATA });
    setTempPMUserId('');
    setIsPlayerDetailsScreen(false);
    setIsUserSearchModalOpen(true);
    setErrorMessage({
      isError: false,
      message: '',
    });
  };

  const postUserForPmSync = async () => {
    if (PlayerInfoData?.userId && tempPMuserId) {
      try {
        const results = await postPMUserSync(
          `/api/v1/playermaker-mapped-users`,
          POST_USER_DETAILS_PM_REQUEST,
          POST_USER_DETAILS_PM_SUCCESS,
          POST_USER_DETAILS_PM_FAIL,
          {
            userId: PlayerInfoData?.userId,
            pmUserId: tempPMuserId,
          },
          '',
          'POST',
          false,
          PLAYER_MAKER_SERVICE
        );

        if (results?.status === 201) {
          setIsConfirmModalOpen(false);
          setTimeout(() => {
            setIsSyncSuccessModalOpen(true);
          }, 10);
        } else {
          if (
            results?.data?.message ===
            playerMakerTexts.PLAYER_MAKER_USER_IS_ALREADY_MAPPED
          ) {
            setIsConfirmModalOpen(false);
            setErrorMessage({
              isError: true,
              message: playerMakerTexts.SYNC_FAIL_PLAYER_MAKER_ALREADY_ID_TAKEN,
            });
            setTimeout(() => {
              setIsSyncSuccessModalOpen(true);
            }, 10);
          }
        }
      } catch (error) {}
    }
  };

  const onSyncDataClick = (pmUserId: string) => {
    setTempPMUserId(pmUserId);
    setTimeout(() => {
      setIsConfirmModalOpen(true);
    }, 10);
    setIsUserSearchModalOpen(false);
  };
  const onUserSearchFetch = (pmUserId: string) => {
    getUserPMDetails(
      `/api/v1/playermaker-users/${pmUserId}`,
      GET_USER_DETAILS_FROM_PM_REQUEST,
      GET_USER_DETAILS_FROM_PM_SUCCESS,
      GET_USER_DETAILS_FROM_PM_FAIL,
      null,
      '',
      'GET',
      false,
      PLAYER_MAKER_SERVICE
    );
  };
  useEffect(() => {
    userDetailsFromPM && setIsPlayerDetailsScreen(true);
  }, [userDetailsFromPM]);

  return (
    <View style={deviceStatsStyle.statsWrapper}>
      <Text style={deviceStatsStyle.txtWhite}>No device configured</Text>
      {!isParent && !isPlayer ? (
        <TouchableOpacity
          onPress={onClickSyncProvider}
          style={deviceStatsStyle.syncButton}
        >
          <Text style={deviceStatsStyle.syncText}>Sync Data</Text>
        </TouchableOpacity>
      ) : null}

      {isUserSearchModalOpen && (
        <ApplicationUserSearchModal
          setIsApplicationUserSearchModalOpen={setIsUserSearchModalOpen}
          isPlayerDetailsScreen={isPlayerDetailsScreen}
          onUserSearch={onUserSearchFetch}
          userDetailsFromPM={userDetailsFromPM}
          userDetailsFromPMLoading={userDetailsFromPMLoading}
          onSyncDataClick={onSyncDataClick}
        />
      )}
      {isConfirmSyncModalOpen && (
        <SyncApplicationPermissionModal
          setIsPermissionModalOpen={setIsConfirmModalOpen}
          onClickSyncPM={postUserForPmSync}
          playerName={userDetailsFromPM?.pmUserName || ''}
          postPMuserDataLoading={postPMuserDataLoading}
        />
      )}
      {isSyncSuccessModalOpen && (
        <SyncApplicationSuccessModal
          isErrorMessage={isErrorMessage}
          setIsSyncSuccessModalOpen={(status: boolean, isError: boolean) => {
            setIsSyncSuccessModalOpen(status);
            !isError && onSuccessSync();
          }}
        />
      )}
    </View>
  );
};

export default DeviceConfigureContainer;
