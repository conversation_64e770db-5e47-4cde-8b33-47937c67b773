import React, { useEffect } from 'react';
import {
  FlatList,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { isTabDevice } from '../../config/appConfig';
import { PlayerInfoLabel } from '../../constants/constants';
import useStyles from '../../hooks/useStyles';
import { PLAYER_IAP_SET_SHOW_STATS_GRAPH } from '../../store/actionTypes/PlayerIAP/PlayerIapAction';
import { SET_PLAYER_LABEL_ID } from '../../store/actionTypes/TeamID/TeamID';
import customProfileLabelStyle from './ProfileLabelStyle';

export default function ProfileLabel({ playerLabelIdData }) {
  const ProfileLabelStyle = useStyles(customProfileLabelStyle);
  const dispatch = useDispatch();

  useEffect(() => {
    //clear selected tab on unmount
    return () => {
      dispatch({ type: SET_PLAYER_LABEL_ID, PlayerLabelId: null });
    };
  }, []);

  const Item = ({ title, id }) => (
    <TouchableOpacity
      style={{ ...ProfileLabelStyle.lableView }}
      onPress={() => {
        setPlayerLabelId(id);
        id !== 1 &&
          dispatch({
            type: PLAYER_IAP_SET_SHOW_STATS_GRAPH,
            payload: false,
          });
      }}
    >
      <Text
        style={[
          ProfileLabelStyle.text,
          id == playerLabelIdData || (id == 1 && !playerLabelIdData)
            ? ProfileLabelStyle.textSelected
            : {},
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderItem = ({ item }) => <Item title={item.title} id={item.id} />;
  const setPlayerLabelId = Id => {
    dispatch({ type: SET_PLAYER_LABEL_ID, PlayerLabelId: Id });
  };

  return (
    // <FlatList
    //     horizontal={isTabDevice() ? false : true}
    //     data={PlayerInfoLabel}
    //     renderItem={renderItem}
    //     keyExtractor={item => item.id}
    //     numColumns={isTabDevice() ? 6 : null}
    //     contentContainerStyle={ProfileLabelStyle.list}
    //   />
    <ScrollView style={ProfileLabelStyle.container}>
      <FlatList
        horizontal={true}
        data={PlayerInfoLabel}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        numColumns={null}
      />
    </ScrollView>
  );
}
