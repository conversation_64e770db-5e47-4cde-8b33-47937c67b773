import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const ProfileImageStyle = colors => ({
  container: isTabDevice()
    ? {
        height: hp('46%'),
        justifyContent: 'flex-start',
      }
    : {
        paddingLeft: wp('2%'),
      },
  playerContainer: isTabDevice()
    ? {
        height: wp('30%'),
        justifyContent: 'flex-start',
      }
    : {
        height: wp('30%'),
        justifyContent: 'flex-start',
      },
  colTwo: isTabDevice()
    ? {}
    : {
        width: wp('35%'),
        flexDirection: 'column',
        justifyContent: 'center',
      },
  colThree: isTabDevice()
    ? {}
    : {
        width: wp('23%'),
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'flex-start',
        paddingRight: wp('3%'),
      },
  userImg: isTabDevice()
    ? {
        height: hp('27%'),
        width: wp('18%'),
        borderRadius: wp('2%'),
        marginBottom: hp('2%'),
        marginTop: wp('1%'),
      }
    : {
        height: wp('40%'),
        width: wp('40%'),
        marginRight: wp('2%'),
        borderRadius: wp('3%'),
      },
  userImgWrapper: isTabDevice()
    ? {
        flexDirection: 'column',
        alignItems: 'flex-start',
      }
    : {
        width: wp('20%'),
        flexDirection: 'row',
        marginTop: wp('3%'),
      },
  exclamationIcon: isTabDevice()
    ? {
        zIndex: 1,
        position: 'absolute',
        top: 0,
        right: wp('3.3'),
        width: wp('4%'),
        height: wp('4%'),
      }
    : {
        zIndex: 1,
        position: 'absolute',
        top: -7,
        right: wp('-1'),
        width: wp('8%'),
        height: wp('8%'),
      },
  topRow: {
    flexDirection: 'column',
    justifyContent: 'center',
    width: '70%',
  },
  userNameWrapper: {
    paddingRight: wp('3%'),
  },
  firstName: isTabDevice()
    ? {
        fontSize: wp('1.9%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      }
    : {
        fontSize: wp('4.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
      },
  playerId: isTabDevice()
    ? {
        fontSize: hp('2%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
      },
  lastName: isTabDevice()
    ? {
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginBottom: hp('4%'),
        lineHeight: hp('6%'),
      }
    : {
        fontSize: wp('5.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        marginBottom: hp('1%'),
        // marginTop: wp('-1%'),
      },
  editModeViewSave: {
    height: '100%',
    width: wp('20%'),
  },
  editModeView: {
    marginTop: wp('2%'),
  },
  exclamation: {
    backgroundColor: colors.red,
    borderRadius: 1000,
    width: 40,
    height: 40,
  },
  userNameContainer: isTabDevice()
    ? {
        flexDirection: 'column',
        width: wp('30%'),
      }
    : {
        flexDirection: 'column',
        width: wp('50%'),
      },
  btn: {
    backgroundColor: colors.green,
    width: wp('15%'),
    height: wp('5%'),
    borderRadius: wp('50%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp('2%'),
  },
  btn2: {
    width: wp('8%'),
    height: wp('5%'),
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp('2%'),
  },
  btnSave: {
    backgroundColor: colors.aquaBlue,
    width: wp('16%'),
    height: wp('6%'),
    borderRadius: wp('50%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp('5%'),
    marginRight: hp('2%'),
    marginBottom: hp('2%'),
  },
  btnDisabled: {
    backgroundColor: colors.grey,
    width: wp('16%'),
    height: wp('6%'),
    borderRadius: wp('50%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp('5%'),
    marginRight: hp('2%'),
    marginBottom: hp('2%'),

    opacity: 0.7,
  },
  btnCancel: {
    backgroundColor: colors.borderBlue,
    width: wp('16%'),
    height: wp('6%'),
    borderRadius: wp('50%'),
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: hp('2%'),
  },
  btnTxt: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        fontWeight: 'bold',
        color: colors.white,
      },
  btnTxtSave: {
    fontSize: wp('3%'),
    fontWeight: 'bold',
    color: colors.white,
  },
  btnTxtCancel: {
    fontSize: wp('3%'),
    fontWeight: 'bold',
    color: colors.white,
  },
  shareIcon: {
    width: wp('4%'),
    resizeMode: 'contain',
  },
  statGraphCategoryName: {
    color: colors.green,
    fontSize: wp('2.5%'),
    fontWeight: 'bold',
  },
  statGraphCriteriaName: {
    color: colors.white,
    fontSize: wp('2%'),
    marginBottom: hp('5%'),
  },
  switchView: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    // marginBottom: wp('3%'),
    height: hp('4%'),
  },
  switchText: {
    fontSize: wp('3.5%'),
    color: colors.red,
    fontFamily: 'Poppins-Bold',
  },
  switch: {
    flexDirection: 'row',
    width: wp('15 %'),
    // justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: wp('0.5%'),
  },
  switchText2: {
    fontSize: wp('4%'),
    color: colors.white,
    left: wp('-2%'),
  },
  unavailableSwitch: {
    // width: wp('10%'),
    // height: hp('10%'),
    left: wp('-2%'),
    transform: [{ scaleX: 0.7 }, { scaleY: 0.7 }],
  },
  bottomRow: {},
  updatePicButton: isTabDevice()
    ? {
        alignItems: 'center',
        backgroundColor: colors.green,
        borderRadius: 100,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        left: wp('2%'),
        padding: wp('0.3%'),
        position: 'absolute',
        top: hp('24%'),
        width: '60%',
      }
    : {
        alignItems: 'center',
        backgroundColor: colors.green,
        borderRadius: 100,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        left: wp('3.5%'),
        padding: wp('1%'),
        position: 'absolute',
        top: hp('14%'),
        width: '75%',
      },
  updatePicIcon: isTabDevice()
    ? {
        marginRight: wp('1%'),
        resizeMode: 'contain',
        width: wp('1.2%'),
        height: wp('1.2%'),
      }
    : {
        marginRight: wp('1%'),
        resizeMode: 'contain',
        width: wp('3%'),
        height: wp('3%'),
      },
  updatePicText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  profileUploadError: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1%'),
        position: 'relative',
        top: wp('-1%'),
        textAlign: 'center',
        width: wp('18%'),
      }
    : {
        color: colors.red,
        fontSize: wp('3.5%'),
        marginTop: wp('2%'),
        textAlign: 'center',
        width: wp('40%'),
      },
});
export default ProfileImageStyle;
