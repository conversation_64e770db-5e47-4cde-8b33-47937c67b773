import { StyleSheet, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const PlayerCommonStyle = colors => ({
  navHeaderView: isTabDevice()
    ? {
        flexDirection: 'row',
      }
    : {
        flexDirection: 'row',
        width: '97%',
      },
  topNavSelected: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('20%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('32%'),
      },
  topNavNotSelected: isTabDevice()
    ? {
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('20%'),
      }
    : {
        borderRadius: wp('100%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: hp('1%'),
        marginTop: hp('2%'),
        marginBottom: hp('2%'),
        width: wp('32%'),
      },
  navHeadertext: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
        textAlign: 'center',
      },
  headerStyle: {
    flexDirection: 'row',
    paddingBottom: 10,
  },
  headerText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontWeight: 'bold',
        marginTop: hp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontWeight: 'bold',
        marginTop: hp('2%'),
        marginLeft: wp('2%'),
      },
  dateView: {
    backgroundColor: '#36D982',
    height: 35,
    width: 80,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  dateText: {
    color: '#fff',
  },
  msg: {
    fontSize: 15,
    color: '#fff',
  },
  optionView: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  btnText: {
    color: '#fff',
    marginHorizontal: 5,
  },
  highlight: {
    backgroundColor: '#3f4e63',
    borderRadius: 10,
    marginVertical: 5,
  },
  contain: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingLeft: 10,
    justifyContent: 'space-between',
  },
  primary: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
export default PlayerCommonStyle;
