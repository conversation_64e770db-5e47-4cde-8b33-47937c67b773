import { AntDesign } from '@expo/vector-icons';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  FlatList,
  Image,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import AddEditDocumentRepositoryModal from '../../components/modal/AddEditDocumentRepositoryModal/AddEditDocumentRepositoryModal';
import SelectionModal from '../../components/modal/SelectionModal/SelectionModal';
import PlayerDocument from '../../components/PlayerDocument/PlayerDocument';
import { isTabDevice } from '../../config/appConfig';
import {
  jerseyErrorList,
  teamProfilePropNames,
  userRoleType,
} from '../../constants/constants';
import {
  FOOTBALL_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import {
  convertDateObjectToJSDate,
  dateTimeConversion,
  generateNewDateWithTimeReset,
  getDateObjectFromISOString,
  renderDefaultValue,
  stringLength,
} from '../../helpers';
import {
  dateFormatConvert,
  dateFormatConvertByString,
  getAge,
} from '../../helpers/DateHelper';
import useApi from '../../hooks/useApi';
import useApiPromise from '../../hooks/useApiPromise';
import useDebounce from '../../hooks/useDebounce';
import useDeviceInfo from '../../hooks/useDeviceInfo';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import useStyles from '../../hooks/useStyles';
import {
  SHOW_DOCUMENT_REPOSITORY_MODAL_FOR_EDIT,
  USER_DOCUMENT_TYPE_FAIL,
  USER_DOCUMENT_TYPE_REQUEST,
  USER_DOCUMENT_TYPE_SUCCESS,
} from '../../store/actionTypes/DocumentRepository/DocumentRepositoryAction';
import {
  PLAYER_DOCUMENT_FAIL,
  PLAYER_DOCUMENT_INITIAL_SUCCESS,
  PLAYER_DOCUMENT_REQUEST,
  PLAYER_DOCUMENT_SUCCESS,
  PLAYER_INFO_DOCUMENT_DELETE_FAIL,
  PLAYER_INFO_DOCUMENT_DELETE_REQUEST,
  PLAYER_INFO_DOCUMENT_DELETE_SUCCESS,
  PLAYER_INFO_SHOW_UPLOAD_MODAL,
  PLAYER_INFO_UPDATE_REQUEST,
  PLAYER_INFO_UPDATE_SUCCESS,
  PLAYER_INFO__UPDATE_FAIL,
  PLAYER_POSITION_FAIL,
  PLAYER_POSITION_REQUEST,
  PLAYER_POSITION_SUCCESS,
  PLAYER_TEAM_LABEL_FAILED,
  PLAYER_TEAM_LABEL_REQUEST,
  PLAYER_TEAM_LABEL_SUCCESS,
} from '../../store/actionTypes/player/playerAction';
import customPlayersInfoStyle from './playersInfoStyle';
import { genders } from '../../constants/constants';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Ionicons } from '@expo/vector-icons';

const WrapTab = ({ wrap, children }) => wrap(children);

export default function ProfileInfo({
  PlayerData,
  globalEdit,
  TeamID,
  savePlayerStatus,
  handlePlayerStatus,
  ToggleEnabled,
  profileID,
  hideUploadModal,
  hideEditModal,
}) {
  const { uploadPofileImageData } = useSelector(state => state?.addUser);
  const { preferences } = useSelector(state => state?.auth);
  const playersInfoStyle = useStyles(customPlayersInfoStyle);
  const dispatch = useDispatch();
  const [editData, setEditData] = useState({
    firstName: '',
    lastName: '',
    nickName: '',
    preferredPosition: null,
    givenPosition: null,
    gender: '',
    height: '',
    weight: '',
    jerseyNo: '',
    dateOfBirth: null,
    foot: '',
    playerContact: '',
    parentContact: '',
    emergencyContact: '',
    joinedDate: null,
  });
  const hundredYearsAgo = new Date().setFullYear(
    new Date().getFullYear() - 100
  );
  const [validation, setValidation] = useState(false);
  const [activeUserId, setActiveUserId] = useState();
  const [positionArray, setPositionArray] = useState([]);
  const [showDatePicker, setShowDatePicker] = useState({
    isVisible: false,
    key: 'dateOfBirth',
  });
  let givenPositionLabel = PlayerData?.givenPosition;
  let preferedPositionLabel = PlayerData?.preferedPosition;
  let footLabel = PlayerData?.foot;
  const [selectedPropName, setSelectedPropName] = useState('');
  const footData = [
    { label: 'Left', value: 'Left' },
    { label: 'Right', value: 'Right' },
    { label: 'Both', value: 'Both' },
  ];
  const [
    setIsGenderModalOpen,
    isGenderModalOpen,
    setSelected_gender,
    selectedGender,
  ] = useInputSelectModal();
  const [mode, setMode] = useState('date');
  const { userData } = useSelector(state => state?.auth);
  const [deviceInfo] = useDeviceInfo();
  const isPlayer = userRoleType.PLAYER === userData?.type;
  const isParent = userRoleType.PARENT === userData?.type;
  const isCoach =
    userRoleType.COACH === userData?.type ||
    userRoleType.HEAD_COACH === userData?.type;

  const editMode = isCoach ? globalEdit : false

  const onChange = (pikerDate, key) => {
    setShowDatePicker(data => ({ ...data, isVisible: false }));

    if (pikerDate && key === 'joinedDate') {
      const { year, month, date } = dateTimeConversion(pikerDate);

      pikerDate = { year, month, date };
    } else if (pikerDate && key === 'dateOfBirth') {
      const dateOfBirthAdjustedToLocalTimeObj =
        getDateObjectFromISOString(pikerDate);

      pikerDate = dateOfBirthAdjustedToLocalTimeObj;
    }
    setEditData({ ...editData, [key]: pikerDate || null });
  };

  const showMode = (currentMode, key) => {
    setShowDatePicker(data => ({ key, isVisible: true }));
    setMode(currentMode);
  };

  const [fetchPositionsData] = useApi();
  const [updatePosition] = useApi();
  const [fetchTeamData] = useApi();
  const [fetchDocuments] = useApi();
  const [fetchData] = useApi();
  const [deleteDocument] = useApi();
  const [fetchUserDocTypes] = useApi();

  const {
    playerPositionData,
    playerInfoUpdateLoading,
    documentList,
    documentListLoading,
    documentListPage,
    documentListPageSize,
    deleteDocumentLoading,
    deleteDocumentSuccess,
    deleteDocumentFailed,
    teamLabels,
    isUploadDocumentEditMode,
    isUploadBtnClicked,
    playerInfoUpdateData_error,
  } = useSelector(state => state?.player);
  const [jerseyNumberError, setJerSeyNumberError] = useState({
    show: false,
    message: '',
  });

  const {
    isEditDocumentRepository,
    isShowDocumentRepository,
    documentRepositoryCreatedOrUpdated,
    isUserDocumentTypeLoaded,
    userDocumentTypes,
  } = useSelector(state => state?.documentRepository);

  useEffect(() => {
    setEditData({
      ...editData,
      gender: selectedGender[0]?.value,
    });
  }, [JSON.stringify(selectedGender)]);

  useEffect(() => {
    if (PlayerData?.userId) {
      setActiveUserId(PlayerData.userId);
    }

    if (PlayerData && (editMode || globalEdit) && playerPositionData) {
      const {
        firstName,
        lastName,
        nickName,
        parentContact,
        preferedPosition,
        givenPosition,
        height,
        weight,
        gender,
        jersyNo,
        foot,
        contact,
        dateOfBirth,
        emergencyContact,
        joinedDate,
      } = PlayerData;

      let preferredPositionId = null;
      let givenPositionId = null;

      const tempPositionArray = [];

      playerPositionData.forEach(position => {
        if (position.name === preferedPosition) {
          preferredPositionId = position._id;
        }

        if (position.name === givenPosition) {
          givenPositionId = position._id;
        }

        tempPositionArray.push({
          label: position.name,
          value: position._id,
        });
      });

      setPositionArray(tempPositionArray);

      setEditData({
        ...editData,
        firstName,
        lastName,
        nickName,
        parentContact,
        preferredPosition: preferredPositionId,
        givenPosition: givenPositionId,
        gender: gender,
        height: height ? height.toString() : '',
        weight: weight ? weight.toString() : '',
        jerseyNo: jersyNo,
        foot,
        playerContact: contact,
        emergencyContact,
        dateOfBirth: dateOfBirth || null,
        joinedDate: joinedDate?.year ? joinedDate : null,
      });
      setJerSeyNumberError({ show: false, message: '' });
    }
  }, [PlayerData, editMode, playerPositionData, globalEdit]);

  useEffect(() => {
    if (editMode) {
      setEditData({
        ...editData,
        isAvailable: ToggleEnabled,
        profileImage: uploadPofileImageData || null,
      });
    }
  }, [JSON.stringify(uploadPofileImageData), ToggleEnabled]);

  useEffect(() => {
    setEditData({
      ...editData,
      gender: selectedGender[0]?.value,
    });
  }, [JSON.stringify(selectedGender)]);

  useEffect(() => {
    if (playerInfoUpdateData_error) {
      setJerSeyNumberError({
        show: true,
        message:
          playerInfoUpdateData_error === jerseyErrorList.INVALID_JERSEY_NO
            ? jerseyErrorList.INVALID_JERSEY_NO_MESSAGE
            : null,
      });
    }
  }, [playerInfoUpdateData_error]);
  useEffect(() => {
    if (profileID) {
      fetchTeamData(
        `/api/v1/sport-profiles/${profileID}/teams?page=1&size=20`,
        PLAYER_TEAM_LABEL_REQUEST,
        PLAYER_TEAM_LABEL_SUCCESS,
        PLAYER_TEAM_LABEL_FAILED,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [profileID]);

  const getDocument = () => {
    fetchDocuments(
      `/api/v1/user-documents?userIds=${activeUserId}&page=${1}&size=${documentListPageSize}`,
      PLAYER_DOCUMENT_REQUEST,
      PLAYER_DOCUMENT_INITIAL_SUCCESS,
      PLAYER_DOCUMENT_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  const loadMoreUpdate = () => {
    fetchData(
      `/api/v1/user-documents?userIds=${activeUserId}&page=${documentListPage + 1
      }&size=${documentListPageSize}`,
      PLAYER_DOCUMENT_REQUEST,
      PLAYER_DOCUMENT_SUCCESS,
      PLAYER_DOCUMENT_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  useEffect(() => {
    if (activeUserId) {
      getDocument();
    }
  }, [documentRepositoryCreatedOrUpdated, activeUserId]);

  useEffect(() => {
    if (activeUserId && userDocumentTypes.length > 0) {
      getDocument();
    }
  }, [activeUserId, userDocumentTypes]);

  useEffect(() => {
    if (activeUserId && userDocumentTypes.length > 0) {
      getDocument();
    }
  }, [deleteDocumentSuccess, deleteDocumentFailed]);

  useEffect(() => {
    fetchPositionsData(
      '/api/v1/positions',
      PLAYER_POSITION_REQUEST,
      PLAYER_POSITION_SUCCESS,
      PLAYER_POSITION_FAIL,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );

    fetchUserDocTypes(
      `/api/v1/user-document-types`,
      USER_DOCUMENT_TYPE_REQUEST,
      USER_DOCUMENT_TYPE_SUCCESS,
      USER_DOCUMENT_TYPE_FAIL,
      null,
      null,
      'GET',
      false,
      USER_MANAGEMENT_SERVICE
    );
  }, []);

  useEffect(() => {
    if (!editMode) {
      setValidation(false);
    }
  }, [editMode]);

  const required = ['firstName'];
  useEffect(() => {
    if (savePlayerStatus) {
      const { sportsProfileId } = PlayerData;
      if (isCoach) {
        let isHaveEmptyValue = false;

        for (let key in editData) {
          if (!required.includes(key) && editData[key] === '') {
            editData[key] = null;
          }
          if (required.includes(key) && editData[key] === '') {
            isHaveEmptyValue = true;
          }
        }

        if (!isHaveEmptyValue) {
          setValidation(false);
          onUpdatePosition(sportsProfileId, editData);
        } else if (isHaveEmptyValue) {
          setValidation(true);
          handlePlayerStatus(false);
        }
      } else {
        const { sportsProfileId } = PlayerData;

        onUpdatePosition(sportsProfileId, {
          ...PlayerData,
          ...editData,
          ...(uploadPofileImageData ? { profileImage: uploadPofileImageData } : {}),
          givenPosition:
            playerPositionData?.find(
              playerPosition =>
                playerPosition?.name == PlayerData?.givenPosition
            )?._id || undefined,
        });
      }
    }
  }, [savePlayerStatus]);

  const onUpdatePosition = (sportsProfileId, data) => {
    updatePosition(
      `/api/v1/teams/${TeamID}/sport-profiles/${sportsProfileId}`,
      PLAYER_INFO_UPDATE_REQUEST,
      PLAYER_INFO_UPDATE_SUCCESS,
      PLAYER_INFO__UPDATE_FAIL,
      data,
      '',
      'PUT',
      null,
      FOOTBALL_SERVICE,
      null
    );
  };

  const onDocumentDelete = id => {
    deleteDocument(
      `/api/v1/user-documents/${id}`,
      PLAYER_INFO_DOCUMENT_DELETE_REQUEST,
      PLAYER_INFO_DOCUMENT_DELETE_SUCCESS,
      PLAYER_INFO_DOCUMENT_DELETE_FAIL,
      null,
      '',
      'DELETE',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  const getKeyboardType = (type) => {
    const validTypes = [
      'default',
      'number-pad',
      'decimal-pad',
      'numeric',
      'email-address',
      'phone-pad',
    ];
    return validTypes.includes(type) ? type : 'default';
  };


  const renderPlayerInfoTextRow = ({
    label,
    value,
    placeHolder,
    propName,
    maxLength,
    keyboardType,
  }) => {

    const isEditMode = isCoach
      ? (editMode && propName !== 'age' && propName !== 'teamName')
      : (globalEdit && (propName === 'height' || propName === 'weight'));

    return (
      <View style={playersInfoStyle.dataRow}>
        <View style={playersInfoStyle.dataRowLeft}>
          <Text style={playersInfoStyle.nameLabel}>{label}</Text>
          {editMode &&
            propName === 'firstName' &&
            mandatoryField(editData[propName])}
        </View>
        <View
          style={[
            playersInfoStyle.dataRowRight,
            propName === 'teamName' && playersInfoStyle.teamDataRowRight,
          ]}
        >

          {isEditMode ? (
            <>
              <TextInput
                style={playersInfoStyle.nameValueInput}
                placeholder={placeHolder}
                placeholderTextColor="#595959"
                onChangeText={text => {
                  if (keyboardType) {
                    /^[0-9]*$/.test(text) &&
                      setEditData({ ...editData, [propName]: text });
                  } else {
                    setEditData({ ...editData, [propName]: text });
                  }
                }}
                value={editData[propName]}
                maxLength={maxLength || null}
                keyboardType={getKeyboardType(keyboardType)}
              />
              {keyboardType && jerseyNumberError.show && (
                <Text style={playersInfoStyle.errorText}>
                  {jerseyNumberError.message}
                </Text>
              )}
            </>
          ) : propName === 'teamName' ? (
            <View style={playersInfoStyle.teamScrollWrapper}>
              {isTabDevice() && !editMode && (
                <Text
                  style={{
                    ...playersInfoStyle.teamValue,
                    marginLeft: hp('1%'),
                  }}
                >
                  :
                </Text>
              )}
              <ScrollView nestedScrollEnabled>
                {value?.split(',').map((teamName, index) => (
                  <Text
                    key={index}
                    numberOfLines={1}
                    style={[
                      playersInfoStyle.nameValue,
                      playersInfoStyle.teamValue,
                      !index && { marginLeft: -hp('0.5%') },
                      index && { marginLeft: -hp('1%') },
                    ]}
                  >
                    {teamName}
                  </Text>
                ))}
              </ScrollView>
            </View>
          ) : (
            <Text style={playersInfoStyle.nameValue}>
              {isTabDevice() && !editMode && (
                <Text style={{ paddingLeft: hp('15%') }}>: </Text>
              )}
              {stringLength(value, 45)}
            </Text>
          )}
        </View>
      </View>
    );
  };

  const renderPlayerContactInfoRow = ({ label, value, propName }) => {
    return (
      <View style={playersInfoStyle.contactRow}>
        <View style={playersInfoStyle.contactTextWrapper}>
          <Text style={playersInfoStyle.contactText}>{label}</Text>
        </View>
        <View
          style={{
            ...playersInfoStyle.dataRowRight,
            ...playersInfoStyle.dataRowRightPhone,
          }}
        >
          {!globalEdit ? (
            <View style={playersInfoStyle.editView2}>
              <Text style={playersInfoStyle.contactNumber}>{value}</Text>
            </View>
          ) : (
            <View style={playersInfoStyle.editView2}>
              <TextInput
                keyboardType="numeric"
                style={playersInfoStyle.contactNumberEdit}
                placeholder={'Type Here'}
                placeholderTextColor="#1DC4D2"
                onChangeText={text =>
                  setEditData({ ...editData, [propName]: text })
                }
                value={editData[propName]}
              />
            </View>
          )}
        </View>
      </View>
    );
  };

  const appendContactDetailsWrapper = () => {
    return (
      <View style={playersInfoStyle.contactsAreaWrapper}>
        <Text style={playersInfoStyle.contactsText}>Contacts</Text>
        <View style={playersInfoStyle.contactView}>
          {renderPlayerContactInfoRow({
            label: 'Player Contact',
            value: PlayerData?.contact,
            propName: 'playerContact',
          })}
          {renderPlayerContactInfoRow({
            label: 'Parent Contact',
            value: PlayerData?.parentContact,
            propName: 'parentContact',
          })}
          {renderPlayerContactInfoRow({
            label: 'Emergency Contact',
            value: PlayerData?.emergencyContact,
            propName: 'emergencyContact',
          })}
        </View>
      </View>
    );
  };
  const [selectedUpdate, setSelectedUpdate] = useState(null);
  const [
    setIsGivenPositionModalOpen,
    isGivenPositionModalOpen,
    setSelected_givenPosition,
    givenPosition,
  ] = useInputSelectModal();

  const [
    setIsPreferedPositionModalOpen,
    isPreferedPositionModalOpen,
    setSelected_preferredPosition,
    preferredPosition,
  ] = useInputSelectModal();

  const [setFootModalOpen, isFootModalOpen, setSelected_foot, foot] =
    useInputSelectModal();

  const [selectedDataArray, setSelectedDataArray] = useState([]);

  useEffect(() => {
    setEditData({
      ...editData,
      [selectedPropName]:
        selectedPropName === teamProfilePropNames.GIVEN_POSITION
          ? givenPosition?.[0]?.value
          : selectedPropName === teamProfilePropNames.PREFERRED_POSITION
            ? preferredPosition?.[0]?.value
            : foot?.[0]?.value,
    });
  }, [givenPosition, preferredPosition, foot]);

  const onDocumentRepoSelect = item => {
    setSelectedUpdate(item);
    dispatch({
      type: SHOW_DOCUMENT_REPOSITORY_MODAL_FOR_EDIT,
    });
  };

  const mappedUserDocRepo = useCallback(
    item => {
      return {
        ...item,
        documentTypeLabel: userDocumentTypes.filter(
          userDocumentType => userDocumentType._id === item.documentTypeId
        )[0]?.name,
      };
    },
    [userDocumentTypes]
  );

  const renderItem = ({ item }) => {
    return (
      <PlayerDocument
        key={item._id}
        item={mappedUserDocRepo(item)}
        setSelectedUpdate={setSelectedUpdate}
        selectedUpdate={selectedUpdate}
        isVisible
        iconType={'document'}
        isEditMode={editMode}
        onDeleteHandler={onDocumentDelete}
      />
    );
  };

  const mandatoryField = fieldName => {
    return (
      <View>
        {!fieldName && (
          <AntDesign
            name="star"
            size={10}
            color="black"
            style={playersInfoStyle.requiredAstric}
          />
        )}
      </View>
    );
  };

  const contactMandatoryField = fieldName => {
    return (
      <View>
        {!fieldName && (
          <AntDesign
            name="star"
            size={10}
            color="black"
            style={playersInfoStyle.requiredAstric2}
          />
        )}
      </View>
    );
  };

  let givenPositionData = useMemo(
    () => PlayerData?.givenPosition,
    [PlayerData]
  );

  let preferredPositionData = useMemo(
    () => PlayerData?.preferedPosition,
    [PlayerData]
  );
  let footPositionData = useMemo(() => PlayerData?.foot, [PlayerData]);

  let genderData = useMemo(() => {
    return PlayerData?.gender ? PlayerData.gender.charAt(0).toUpperCase() + PlayerData.gender.slice(1).toLowerCase() : '';
  }, [PlayerData]);


  const appendDocumentRepo = () => {
    return (
      <>
        <View style={{ height: isTabDevice() ? isCoach ? "50%" : '100%' : isCoach ? "35%" : '100%'}} >
          <View style={{ flexDirection: "row", justifyContent: "space-between", width: '95%' }}>
            <Text style={playersInfoStyle.contactsText}>Documents</Text>
            <TouchableOpacity
              onPress={() => {
                hideUploadModal()
                hideEditModal()
              }}
            >
              <Ionicons
                name="close"
                style={{
                  fontSize: 25,
                  color: 'white',
                }}
              />
            </TouchableOpacity>
          </View>
          <View style={playersInfoStyle.contactView2}>
            {documentListLoading || deleteDocumentLoading ? (
              <ActivitySpinner />
            ) : documentList?.length ? (
              <FlatList
                data={documentList}
                renderItem={renderItem}
                keyExtractor={item => item._id}
                onEndReached={() => loadMoreUpdate()}
                numberOfLines={5}
                style={{ maxHeight: isTabDevice() ? isCoach ? "90%": '85%'  : isCoach ? "85%" :  '90%'}}
              />
            ) : (
              <Text style={{...playersInfoStyle.contactText, color: 'white'}}>No documents added.</Text>
            )}
          </View>
        </View>
      </>
    );
  };
  return (
    <View style={playersInfoStyle.container}>
      {playerInfoUpdateLoading && isTabDevice() ? (
        <ActivitySpinner />
      ) : (
        <View style={playersInfoStyle.wrapper}>
          <View style={playersInfoStyle.playerInfoScreen}>
            {/* <ScrollView
              contentContainerStyle={playersInfoStyle.scrollViewWrapper}
              style={playersInfoStyle.scrollView}
              nestedScrollEnabled
              keyboardShouldPersistTaps="handled"
              > */}
            <WrapTab
              wrap={children => (
                <KeyboardAwareScrollView
                  contentContainerStyle={playersInfoStyle.playerInfoScreenInner}
                  extraScrollHeight={!isTabDevice() ? isCoach ? 300 : 430 : isCoach ? 150 : 0}
                  keyboardShouldPersistTaps="handled"
                >
                  {children}
                </KeyboardAwareScrollView>
              )}
            >
              <ScrollView
                contentContainerStyle={playersInfoStyle.scrollViewWrapper}
              >
                <View style={playersInfoStyle.dataWrapper}>
                  <View style={playersInfoStyle.dataColumn}>
                    {renderPlayerInfoTextRow({
                      label: 'First Name',
                      value: PlayerData?.firstName,
                      propName: 'firstName',
                      placeHolder: 'First Name',
                    })}
                    {renderPlayerInfoTextRow({
                      label: 'Last Name',
                      value: PlayerData?.lastName,
                      propName: 'lastName',
                      placeHolder: 'Last Name',
                    })}
                    {renderPlayerInfoTextRow({
                      label: 'Nick Name',
                      value: PlayerData?.nickName,
                      propName: 'nickName',
                      placeHolder: 'Nick Name',
                    })}
                    <View style={{ ...playersInfoStyle.dataRow, zIndex: 10 }}>
                      <View style={playersInfoStyle.dataRowLeft}>
                        <Text style={playersInfoStyle.nameLabel}>Gender</Text>
                      </View>
                      <View style={playersInfoStyle.dataRowRight}>
                        <SelectionModal
                          title={`Select Gender`}
                          items={genders}
                          onCloseHook={value => {
                            setIsGenderModalOpen(value);
                          }}
                          onSelectItemHook={item => {
                            setSelectedPropName(teamProfilePropNames.GENDER);
                            setSelected_gender(item);
                          }}
                          selectedItemLabel={
                            globalEdit
                              ? selectedGender?.[0]?.label || genderData
                              : deviceInfo?.deviceType !== 'phone'
                                ? `: ${genderData || ''}`
                                : genderData || ''
                          }
                          isModalOpen={isGenderModalOpen}
                          disableOnPress={!globalEdit}
                        />
                      </View>
                    </View>
                    <View style={playersInfoStyle.dataRow}>
                      <View style={playersInfoStyle.dataRowLeft}>
                        <Text style={playersInfoStyle.nameLabel}>
                          Date of Birth
                        </Text>
                      </View>
                      <View style={playersInfoStyle.dataRowRight}>
                        {editMode ? (
                          <View
                            style={
                              editData.dateOfBirth
                                ? playersInfoStyle.nameValueDateInputWithValue
                                : playersInfoStyle.nameValueDateInput
                            }
                          >
                            <TouchableOpacity
                              onPress={() => showMode('date', 'dateOfBirth')}
                            >
                              <Text style={playersInfoStyle.nameValue}>
                                {editData.dateOfBirth
                                  ? dateFormatConvert(
                                    convertDateObjectToJSDate(
                                      editData?.dateOfBirth
                                    )
                                  )
                                  : null}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <Text style={playersInfoStyle.nameValue}>
                            {isTabDevice() && <Text>: </Text>}
                            {PlayerData?.dateOfBirth &&
                              dateFormatConvert(
                                convertDateObjectToJSDate(PlayerData?.dateOfBirth)
                              )}
                          </Text>
                        )}
                      </View>
                    </View>
                    {renderPlayerInfoTextRow({
                      label: 'Age',
                      value: PlayerData?.dateOfBirth
                        ? getAge(
                          convertDateObjectToJSDate(PlayerData?.dateOfBirth)
                        ) + ' years'
                        : '',
                      propName: 'age',
                    })}
                    {renderPlayerInfoTextRow({
                      label: 'Jersey Number',
                      value: PlayerData?.jersyNo,
                      propName: 'jerseyNo',
                      placeHolder: 'Jersey No',
                      maxLength: 2,
                      keyboardType: 'numeric',
                    })}
                  </View>
                  <View style={playersInfoStyle.dataColumn}>
                    {renderPlayerInfoTextRow({
                      label: 'Height (cm)',
                      value: PlayerData?.height,
                      propName: 'height',
                      placeHolder: 'Height in cm',
                      keyboardType: 'numeric'
                    })}
                    {renderPlayerInfoTextRow({
                      label: 'Weight (kg)',
                      value: PlayerData?.weight,
                      propName: 'weight',
                      placeHolder: 'Weight in kg',
                      keyboardType: 'numeric'

                    })}
                    <View style={{ ...playersInfoStyle.dataRow, zIndex: 1000 }}>
                      <View style={playersInfoStyle.dataRowLeft}>
                        <Text style={playersInfoStyle.nameLabel}>
                          Given Position
                        </Text>
                      </View>
                      <View style={playersInfoStyle.dataRowRight}>
                        <SelectionModal
                          title={`Select ${teamProfilePropNames.GIVEN_POSITION}`}
                          items={positionArray || []}
                          onCloseHook={value => {
                            setIsGivenPositionModalOpen(value);
                          }}
                          onSelectItemHook={item => {
                            setSelectedPropName(
                              teamProfilePropNames.GIVEN_POSITION
                            );
                            setSelected_givenPosition(item);
                          }}
                          isEnableAutoComplete
                          selectedItemLabel={
                            editMode
                              ? givenPosition?.[0]?.label || givenPositionData
                              : deviceInfo?.deviceType !== 'phone'
                                ? `: ${givenPositionData || ''}`
                                : givenPositionData || ''
                          }
                          isModalOpen={isGivenPositionModalOpen}
                          disableOnPress={!editMode}
                          defaultValues={
                            givenPosition?.length
                              ? [givenPosition?.[0]?.value]
                              : renderDefaultValue(
                                positionArray,
                                PlayerData?.givenPosition
                              )
                          }
                        />
                      </View>
                    </View>
                    <View style={{ ...playersInfoStyle.dataRow, zIndex: 9 }}>
                      <View style={playersInfoStyle.dataRowLeft}>
                        <Text style={playersInfoStyle.nameLabel}>
                          Preferred Position
                        </Text>
                      </View>
                      <View style={playersInfoStyle.dataRowRight}>
                        <SelectionModal
                          title={`Select ${teamProfilePropNames.PREFERRED_POSITION}`}
                          items={positionArray || []}
                          onCloseHook={setIsPreferedPositionModalOpen}
                          onSelectItemHook={item => {
                            setSelectedPropName(
                              teamProfilePropNames.PREFERRED_POSITION
                            );
                            setSelected_preferredPosition(item);
                          }}
                          isEnableAutoComplete
                          selectedItemLabel={
                            globalEdit
                              ? preferredPosition?.[0]?.label ||
                              preferredPositionData
                              : deviceInfo?.deviceType !== 'phone'
                                ? `: ${preferredPositionData || ''}`
                                : preferredPositionData || ''
                          }
                          isModalOpen={isPreferedPositionModalOpen}
                          disableOnPress={!globalEdit}
                          defaultValues={
                            preferredPosition?.length
                              ? [preferredPosition?.[0]?.value]
                              : renderDefaultValue(
                                positionArray,
                                PlayerData?.preferedPosition
                              )
                          }
                        />
                      </View>
                    </View>
                    <View style={{ ...playersInfoStyle.dataRow, zIndex: 8 }}>
                      <View style={playersInfoStyle.dataRowLeft}>
                        <Text style={playersInfoStyle.nameLabel}>Foot</Text>
                      </View>
                      <View style={playersInfoStyle.dataRowRight}>
                        <SelectionModal
                          title={`Select ${teamProfilePropNames.FOOT}`}
                          items={footData}
                          onCloseHook={setFootModalOpen}
                          onSelectItemHook={item => {
                            setSelectedPropName(teamProfilePropNames.FOOT);
                            setSelected_foot(item);
                          }}
                          isEnableAutoComplete
                          selectedItemLabel={
                            globalEdit
                              ? foot?.[0]?.label || footPositionData
                              : deviceInfo?.deviceType !== 'phone'
                                ? `: ${footPositionData || ''}`
                                : footPositionData || ''
                          }
                          isModalOpen={isFootModalOpen}
                          disableOnPress={!globalEdit}
                          defaultValues={
                            foot?.length
                              ? [foot?.[0]?.value]
                              : renderDefaultValue(footData, PlayerData?.foot)
                          }
                        />
                      </View>
                    </View>
                    <View style={playersInfoStyle.dataRow}>
                      <View style={playersInfoStyle.dataRowLeft}>
                        <Text style={playersInfoStyle.nameLabel}>
                          Club Joined Date
                        </Text>
                      </View>
                      <View style={playersInfoStyle.dataRowRight}>
                        {editMode ? (
                          <View
                            style={
                              editData.joinedDate
                                ? playersInfoStyle.nameValueDateInputWithValue
                                : playersInfoStyle.nameValueDateInput
                            }
                          >
                            <TouchableOpacity
                              onPress={() => showMode('date', 'joinedDate')}
                            >
                              <Text style={playersInfoStyle.nameValue}>
                                {editData?.joinedDate?.year
                                  ? dateFormatConvertByString(
                                    editData.joinedDate.year,
                                    editData.joinedDate.month,
                                    editData.joinedDate.date,
                                    preferences?.dateFormat || 'DD/MM/YYYY'
                                  )
                                  : null}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <Text style={playersInfoStyle.nameValue}>
                            {isTabDevice() && <Text>: </Text>}
                            {PlayerData?.joinedDate?.year
                              ? dateFormatConvertByString(
                                PlayerData.joinedDate.year,
                                PlayerData.joinedDate.month,
                                PlayerData.joinedDate.date,
                                preferences?.dateFormat || 'DD/MM/YYYY'
                              )
                              : null}
                          </Text>
                        )}
                      </View>
                    </View>
                    {renderPlayerInfoTextRow({
                      label: 'Team Assigned',
                      value: teamLabels?.join(', '),
                      propName: 'teamName',
                    })}
                  </View>
                </View>
                {validation && (
                  <Text style={playersInfoStyle.validationTxt}>
                    Please fill the indicated mandatory fields.
                  </Text>
                )}
                {appendContactDetailsWrapper()}
                {!isTabDevice() && (
                  <View style={playersInfoStyle.uploadButtonWrapper}>
                    <TouchableOpacity
                      onPress={() =>
                        dispatch({
                          type: PLAYER_INFO_SHOW_UPLOAD_MODAL,
                          payload: true,
                        })
                      }
                      style={playersInfoStyle.btnUpload}
                    >
                      <Text style={playersInfoStyle.btnUploadTxt}>
                        Documents
                      </Text>
                      <Image
                        style={playersInfoStyle.btnUploadIcon}
                        source={require('../../../assets/icons/document-white.png')}
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </ScrollView>
            </WrapTab>
            {/* </ScrollView> */}
          </View>
          {showDatePicker.isVisible && (
            <View style={playersInfoStyle.datePickerWrapper}>
              <DateTimePickerModal
                isVisible
                mode="date"
                style={playersInfoStyle.datePicker}
                onConfirm={date => onChange(date, showDatePicker.key)}
                // display="spinner"
                onCancel={() => {
                  setShowDatePicker(false);
                  onChange(
                    showDatePicker.key === 'dateOfBirth'
                      ? editData?.dateOfBirth
                        ? convertDateObjectToJSDate(editData?.dateOfBirth)
                        : null
                      : editData.joinedDate
                        ? convertDateObjectToJSDate(editData?.joinedDate)
                        : null,
                    showDatePicker.key
                  );
                }}
                date={
                  showDatePicker.key === 'dateOfBirth'
                    ? convertDateObjectToJSDate(editData?.dateOfBirth)
                    : editData?.joinedDate
                      ? convertDateObjectToJSDate(editData?.joinedDate)
                      : generateNewDateWithTimeReset()
                }
                modalStyleIOS={playersInfoStyle.datePickerSelector}
                pickerContainerStyleIOS={playersInfoStyle.datePickerWrapper}
                maximumDate={new Date()}
                isDarkModeEnabled={false}
                minimumDate={new Date(hundredYearsAgo)}
                timeZoneOffsetInMinutes={0}
              />
            </View>
          )}
          {(isUploadBtnClicked || isUploadDocumentEditMode) && (
            <AddEditDocumentRepositoryModal
              showModal={isUploadBtnClicked || isUploadDocumentEditMode}
              isEditMode={isUploadDocumentEditMode}
              onClose={() => {
                hideUploadModal();
                hideEditModal();
              }}
              profileID={profileID}
              setSelectedItem={setSelectedUpdate}
              selectedItem={selectedUpdate}
              userId={activeUserId}
              appendDocumentRepo={appendDocumentRepo}
              documentCount={documentList?.length || 0}
            />
          )}
        </View>
      )}
    </View>
  );
}
