import { View, Text } from 'react-native';
import React, { FC, useEffect, useState, useMemo } from 'react';
import ForwardMsgModal from '../../components/modal/ForwardMsgModal/ForwardMsgModal';
import useApi from '../../hooks/useApi';
import { useSelector, useDispatch } from 'react-redux';
import { RootStore } from '../../store/store';
import {
  USER_MANAGEMENT_SERVICE,
  MESSAGING_SERVICE,
} from '../../constants/services';
import {
  FETCH_FORWARD_MSG_PLAYERS_REQUEST,
  FETCH_FORWARD_MSG_PLAYERS_SUCCESS,
  FETCH_FORWARD_MSG_PLAYERS_FAIL,
  FETCH_FORWARD_MSG_TEAM_REQUEST,
  FETCH_FORWARD_MSG_TEAM_SUCCESS,
  FETCH_FORWARD_MSG_TEAM_FAIL,
  IMPORT_LAST_MESSAGE,
  IMPORT_MESSAGES,
  MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
  SET_PAST_MESSAGE_CHAT_INFO,
  SET_PAST_MESSAGE_CHAT_MEMBER_INFO,
} from '../../store/actionTypes/Message/MessageAction';
import useDebounce from '../../hooks/useDebounce';
import { WebSocketParams, messageTypes } from '../../constants/constants';
import uuid from 'react-native-uuid';
import useApiRequestInLoop from '../../hooks/useApiRequestInLoops';
import { messageDataMapping } from '../../helpers/common';

interface IMessageOptionsModal {
  setShowForwardModal: Function;
  setSelectedMsgType: Function;
  selectedMsgType: String;
  selectedMessageId: Object | null;
  setIsForwardMessageError: Function;
  selectedMsgContent: any;
  setSelectedMsgContent: Function;
  getUserSelectedMessage: Function;
}

const ForwardMsgContainer: FC<IMessageOptionsModal> = props => {
  const { v4: uuidv4 } = uuid;
  const {
    selectedMsgType,
    selectedMessageId,
    setShowForwardModal,
    selectedMsgContent,
    getUserSelectedMessage,
    setSelectedMsgContent,
  } = props;
  const [getCreatedUserData] = useApi();
  const [getCreatedTeamData] = useApi();
  const { userData } = useSelector((state: RootStore) => state.auth);
  const { wsClient } = useSelector((state: RootStore) => state.common);
  const messageWsClient = wsClient?.[WebSocketParams.MESSAGE_WS];
  const dispatch = useDispatch();

  const {
    selectedMessageType: MainSelectedMessageType,
    forwardMsgUsers,
    forwardMsgTeams,
    forwardMsgUsersLoading,
    forwardMsgTeamsLoading,
    forwardMsgUsersPage,
    forwardMsgTeamsPage,
    forwardMsgTeamsTotalRecords,
    forwardMsgUsersTotalRecords,
  } = useSelector((state: RootStore) => state?.message);
  const [searchKey, setSearchKey] = useState<string>('');
  const [isSubmitError, setSubmitError] = useState<boolean>(false);
  const debouncedSearchText = useDebounce(searchKey, 500);
  const [
    selectedPersonOrTeamToForwardMessage,
    setSelectedPersonOrTeamToForwardMesage,
  ] = useState([]);
  const [availableChatIds, setAvailableChatIds] = useState<string[]>([]);
  const [createChatIdForSelectedUserId, setCreateChatIdForSelectedUserId] =
    useState<string[]>([]);
  const [forwardMsgWebsocketLoading, setForwardMsgWebsocketLoading] =
    useState(false);

  const [checkChatInitiated, chatInitiatedRequestStatus] =
    useApiRequestInLoop();
  const [createChatId, createChatIdRequestStatus] = useApiRequestInLoop();
  const { selectedMessageChatId, selectedUsersForMessages } = useSelector(
    (state: RootStore) => state?.message
  );

  const isChatInitiatedForForwardMessage = () => {
    const getUrl = () =>
      selectedPersonOrTeamToForwardMessage.map(
        selectedId =>
          `/api/v1/chats?type=PERSONAL&membersUserIds=${userData?.id},${selectedId}`
      );
    checkChatInitiated(getUrl(), MESSAGING_SERVICE, 'GET', []);
  };

  const createChatIdForForwardMessage = () => {
    const getChatIdCreationObject = () =>
      createChatIdForSelectedUserId.map(id => ({
        type: messageTypes.PERSONAL,
        memberUserIds: [userData.id, id],
        creatorUserId: userData.id,
      }));

    createChatId(
      [`/api/v1/chats`],
      MESSAGING_SERVICE,
      'POST',
      getChatIdCreationObject()
    );
  };

  // Add selected users to the top of left container message list -> PastChatMemberInfo
  const addDataToChatMemberInfo = (index: number) => {
    const data = forwardMsgUsers?.find(
      user => user?.id === selectedPersonOrTeamToForwardMessage[index]
    );
    dispatch({ type: SET_PAST_MESSAGE_CHAT_MEMBER_INFO, payload: data });
  };

  // Add selected users to the top of left container message list -> PastChatInfo
  useEffect(() => {
    if (createChatIdForSelectedUserId.length) {
      createChatIdRequestStatus?.status?.forEach(item => {
        if (item?.data) {
          setAvailableChatIds(prevState => {
            return [...prevState, item?.data?._id];
          });
          dispatch({
            type: SET_PAST_MESSAGE_CHAT_INFO,
            payload: {
              data: {
                ...item?.data,
                opponentUserIds: [item?.data?.memberUserIds?.[1]],
              },
            },
          });
        }
      });
    }
  }, [createChatIdRequestStatus]);

  useEffect(() => {
    chatInitiatedRequestStatus?.status?.forEach(item => {
      const { data: hasChatId } = item || null;
      const { data: { data: dataArray = {} } = {} } = item || {};
      if (hasChatId) {
        setAvailableChatIds(prevState => {
          return Array.from(new Set([...prevState, dataArray?.[0]?._id]));
        });
      } else {
        const userWithoutChatId = selectedPersonOrTeamToForwardMessage?.filter(
          id => id !== dataArray?.[0]?.memberUserIds[1]
        );
        setCreateChatIdForSelectedUserId(prevState => {
          return Array.from(new Set([...prevState, ...userWithoutChatId]));
        });
      }
    });
  }, [JSON.stringify(chatInitiatedRequestStatus)]);

  //if a user does not have a chat Id => create one
  useEffect(() => {
    if (createChatIdForSelectedUserId?.length) {
      if (userData?.id) {
        createChatIdForForwardMessage();
      }
    }
  }, [createChatIdForSelectedUserId]);

  const fetchForwardMsgPlayers = (page: number) => {
    userData?.id &&
      getCreatedUserData(
        `/api/v1/users/${userData?.id}/personal-chat-candidates?generateImageUrl=false&page=${page}&size=15&searchKey=${debouncedSearchText}`,
        FETCH_FORWARD_MSG_PLAYERS_REQUEST,
        FETCH_FORWARD_MSG_PLAYERS_SUCCESS,
        FETCH_FORWARD_MSG_PLAYERS_FAIL,
        null,
        '',
        'GET',
        false,
        USER_MANAGEMENT_SERVICE
      );
  };

  const fetchForwardMsgTeams = (page: number) => {
    userData?.id &&
      getCreatedTeamData(
        `/api/v1/chats?type=TEAM&membersUserIds=${userData?.id}&size=5&page=${page}&searchNameLike=${debouncedSearchText}`,
        FETCH_FORWARD_MSG_TEAM_REQUEST,
        FETCH_FORWARD_MSG_TEAM_SUCCESS,
        FETCH_FORWARD_MSG_TEAM_FAIL,
        null,
        '',
        'GET',
        false,
        MESSAGING_SERVICE
      );
  };

  const populateChatDetails = () => {
    if (MainSelectedMessageType === messageTypes.PERSONAL) {
      const selectedUserId = selectedUsersForMessages?.[0]?.id;
      if (
        selectedUserId &&
        Object?.keys(selectedMessageChatId?.[selectedUserId] || {})?.length
      ) {
        const chatId = selectedMessageChatId?.[selectedUserId]?._id;
        chatId && getUserSelectedMessage(chatId);
      }
    } else {
      const chatId = Object?.keys(selectedMessageChatId || {})?.[0];
      getUserSelectedMessage(chatId);
    }
  };

  const sendWebsocketMessage = () => {
    if (messageWsClient?.readyState === messageWsClient?.OPEN) {
      availableChatIds?.forEach((availableChatId, i) => {
        const msg = {
          _id: uuidv4().toString(),
          chatId: availableChatId,
          senderUserId: userData?.id,
          type: 'TEXT',
          sentDate: new Date(),
          content: selectedMsgContent?.content,
          isDeleted: false,
          isBroadcast: false,
          isForwarded: false,
          forwardedMessageId: selectedMessageId,
        };
        dispatch({
          type: IMPORT_MESSAGES,
          payload: {
            data: messageDataMapping(userData, [msg]),
            customInput: selectedPersonOrTeamToForwardMessage[i],
          },
        });

        if (selectedMsgType === messageTypes.TEAMS) {
          dispatch({
            type: MOVE_TOP_LATEST_MESSAGE_FOR_TEAMS_MESSAGE,
            payload: {
              data: availableChatId || '',
            },
          });
        } else {
          dispatch({
            type: IMPORT_LAST_MESSAGE,
            payload: {
              data: msg,
            },
          });

          addDataToChatMemberInfo(i);
        }

        messageWsClient?.send(
          `{"_id":${msg?._id},"MessageGroupId":${uuidv4()},"chatId":${
            msg?.chatId
          },"senderUserId":${
            msg?.senderUserId
          },"forwardedMessageId":${selectedMessageId},"action":"send" }`
        );
      });
      populateChatDetails();
      setForwardMsgWebsocketLoading(false);
      setShowForwardModal(false);
    }
  };

  //Initiate websocket send,once chat id's are available for selected users
  useEffect(() => {
    if (
      selectedPersonOrTeamToForwardMessage.length > 0 &&
      selectedPersonOrTeamToForwardMessage?.length === availableChatIds?.length
    ) {
      sendWebsocketMessage();
    }
  }, [selectedPersonOrTeamToForwardMessage, availableChatIds]);

  //make api calls based on message type selection
  useEffect(() => {
    selectedMsgType === messageTypes.PERSONAL
      ? fetchForwardMsgPlayers(1)
      : fetchForwardMsgTeams(1);
  }, [debouncedSearchText, selectedMsgType]);

  //reset state when switching between message types
  useEffect(() => {
    setSearchKey('');
    setSelectedPersonOrTeamToForwardMesage([]);
    setSubmitError(false);
  }, [selectedMsgType]);

  //remove submit error if user selects item from list
  useEffect(() => {
    selectedPersonOrTeamToForwardMessage.length && setSubmitError(false);
  }, [selectedPersonOrTeamToForwardMessage]);

  const addTeamIdsToAvailableChatIds = () => {
    selectedPersonOrTeamToForwardMessage.forEach(id => {
      setAvailableChatIds(prevState => [...prevState, id]);
    });
  };

  //Initiate forward message on submit
  const onSubmitHandler = () => {
    if (!selectedPersonOrTeamToForwardMessage.length) {
      setSubmitError(true);
    } else if (selectedMsgType === messageTypes.PERSONAL) {
      isChatInitiatedForForwardMessage();
      setForwardMsgWebsocketLoading(true);
    } else if (selectedMsgType === messageTypes.TEAMS) {
      addTeamIdsToAvailableChatIds();
      setForwardMsgWebsocketLoading(true);
    }
  };

  const onModleCloseHandler = () => {};

  //load more data on scroll
  const onReachEndHandler = () => {
    if (selectedMsgType === messageTypes.PERSONAL) {
      if (forwardMsgUsersTotalRecords > (forwardMsgUsers?.length || 0)) {
        fetchForwardMsgPlayers(forwardMsgUsersPage + 1);
      }
    } else {
      if (forwardMsgTeamsTotalRecords > (forwardMsgTeams?.length || 0)) {
        fetchForwardMsgTeams(forwardMsgTeamsPage + 1);
      }
    }
  };

  useEffect(() => {
    return () => {
      setSelectedMsgContent({});
    };
  }, []);

  return (
    <View>
      <ForwardMsgModal
        {...props}
        players={forwardMsgUsers}
        teams={forwardMsgTeams}
        searchKey={searchKey}
        setSearchKey={setSearchKey}
        loading={forwardMsgUsersLoading || forwardMsgTeamsLoading}
        selectedChat={selectedPersonOrTeamToForwardMessage}
        setSelectedChat={setSelectedPersonOrTeamToForwardMesage}
        onSubmitHandler={onSubmitHandler}
        isSubmitError={isSubmitError}
        onReachEndHandler={onReachEndHandler}
        forwardMsgWebsocketLoading={forwardMsgWebsocketLoading}
      />
    </View>
  );
};

export default ForwardMsgContainer;
