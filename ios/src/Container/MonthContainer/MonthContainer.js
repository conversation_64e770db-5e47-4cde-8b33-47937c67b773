import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';
import WeekSlider from '../../components/Matches/WeekSlider';
import { EVENT_SERVICE } from '../../constants/services';
import { calendarDays, dateTimeUTCConversion} from '../../helpers/index';
import useApi from '../../hooks/useApi';
import {
  INITIAL_MATCHES_FAIL,
  INITIAL_MATCHES_REQUEST,
  INITIAL_MATCHES_SUCCESS,
  MORE_MATCHES_FAIL,
  MORE_MATCHES_REQUEST,
  MORE_MATCHES_SUCCESS,
} from '../../store/actionTypes/MatchesInfo/MatchesAction';

const MonthContainer = ({ selectedMonth }) => {
  const [fetchData] = useApi();
  const { selectedTeam } = useSelector(state => state?.team);
  const { matchesPage } = useSelector(state => state?.matches);
  const [selectedMonthCalendar, setSelectedMonthCalendar] = useState({});
  let { monthName, month, year } = selectedMonth;

  const [selectedWeek, setSelectedWeek] = useState({});
  const [selectedWeekIndex, setSelectedWeekIndex] = useState(0);
  const [currentWeekIndex, setCurrentWeekIndex] = useState(0);

  const addLeadingZeros = value => {
    return value < 10 ? '0' + value : value;
  };
  const weekStartEndDate = week => {
    const nonNullDates = week.dates.filter(date => date.date !== null);
    let start = dateTimeUTCConversion(nonNullDates[0].date);
    let end = dateTimeUTCConversion(nonNullDates[nonNullDates.length - 1].date);
    setSelectedWeekIndex(
      Number(`${year}${addLeadingZeros(month)}${week.week}`)
    );

    setSelectedWeek({
      start: `${start.year}-${addLeadingZeros(start.month)}-${addLeadingZeros(
        start.date
      )}`,
      end: `${end.year}-${addLeadingZeros(end.month)}-${addLeadingZeros(
        end.date
      )}`,
    });
  };

  useEffect(() => {
    if (year && month) {
      const calendarData = calendarDays(`${year}-${addLeadingZeros(month)}-01`);
      setCurrentWeekIndex(
        Number(`${year}${addLeadingZeros(month)}${calendarData.currentWeek}`)
      );
    }
  }, []);

  useEffect(() => {
    if (year && month) {
      const calendarData = calendarDays(`${year}-${addLeadingZeros(month)}-01`);
      setSelectedMonthCalendar(calendarData);
      weekStartEndDate(calendarData.calendar[calendarData.currentWeek]);
      // setCurrentWeekIndex(Number(`${year}${month}${calendarData.currentWeek}`));
      setSelectedWeekIndex(
        Number(`${year}${addLeadingZeros(month)}${calendarData.currentWeek}`)
      );
    }
  }, [selectedMonth]);

  const initialMatchLoad = (selectedWeek, selectedTeam) => {
    const { _id } = selectedTeam;
    const { start, end } = selectedWeek;

    if (_id && start && end) {
      const startDate = new Date(start + 'T01:00:00').toISOString();
      const endDate = new Date(end + 'T23:00:00').toISOString();

      fetchData(
        `/api/v1/events?teamId=${_id}&type=match&startDate=${startDate}&endDate=${endDate}&page=1&size=20`,
        INITIAL_MATCHES_REQUEST,
        INITIAL_MATCHES_SUCCESS,
        INITIAL_MATCHES_FAIL,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  };

  const loadMoreMatches = () => {
    const { _id } = selectedTeam;
    const { start, end } = selectedWeek;

    if (_id && start && end) {
      const startDate = new Date(start + 'T01:00:00').toISOString();
      const endDate = new Date(end + 'T23:00:00').toISOString();
      const nextPage = Number(matchesPage) + 1;
      fetchData(
        `/api/v1/events?teamId=${_id}&type=match&startDate=${startDate}&endDate=${endDate}&page=${nextPage}&size=10`,
        MORE_MATCHES_REQUEST,
        MORE_MATCHES_SUCCESS,
        MORE_MATCHES_FAIL,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      initialMatchLoad(selectedWeek, selectedTeam);
    }, [selectedWeek, selectedTeam])
  );

  return (
    <View>
      <WeekSlider
        selectedMonthCalendar={selectedMonthCalendar}
        setSelectedWeek={weekStartEndDate}
        selectedWeekIndex={selectedWeekIndex}
        loadMoreMatches={loadMoreMatches}
        currentWeekIndex={currentWeekIndex}
        year={year}
        month={month}
      />
    </View>
  );
};

export default MonthContainer;
