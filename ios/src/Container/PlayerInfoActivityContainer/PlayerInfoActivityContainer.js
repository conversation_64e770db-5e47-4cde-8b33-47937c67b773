import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import { View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import Events from '../../components/PlayerInfoActivity/Events';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import useStyles from '../../hooks/useStyles';
import {
  PLAYER_EVENTS_TEAM_DATA_FAILED,
  PLAYER_EVENTS_TEAM_DATA_REQUEST,
  PLAYER_EVENTS_TEAM_DATA_SUCCESS,
  PLAYER_PAST_EVENTS_ATTENDANCE_FAILED,
  PLAYER_PAST_EVENTS_ATTENDANCE_REQUEST,
  PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS,
  PLAYER_PAST_EVENTS_FAILED,
  PLAYER_PAST_EVENTS_REQUEST,
  PLAYER_PAST_EVENTS_SUCCESS,
  PLAYER_PAST_EVENTS_UPDATE_FAILED,
  PLAYER_PAST_EVENTS_UPDATE_REQUEST,
  PLAYER_PAST_EVENTS_UPDATE_SUCCESS,
  PLAYER_UPCOMING_EVENTS_FAILED,
  PLAYER_UPCOMING_EVENTS_REQUEST,
  PLAYER_UPCOMING_EVENTS_SUCCESS,
  PLAYER_UPCOMING_EVENTS_UPDATE_FAILED,
  PLAYER_UPCOMING_EVENTS_UPDATE_REQUEST,
  PLAYER_UPCOMING_EVENTS_UPDATE_SUCCESS,
} from '../../store/actionTypes/player/playerAction';
import customPlayerInfoActivityStyles from './PlayerInfoActivityStyles';
import SessionsPlanContainer from '../SessionsPlan/SessionsPlanContainer';

const PlayerInfoActivityContainer = ({ selectedTab }) => {
  const PlayerInfoActivityStyles = useStyles(customPlayerInfoActivityStyles);
  const [fetchData] = useApi();
  const [fetchTeamData] = useApi();
  const dispatch = useDispatch();
  const { PlayerInfoData } = useSelector(state => state.playerInfo);
  const {
    playerPastEvents,
    playerPastEventTotalRecords,
    playerPastEventsCurrentPage,
    playerPastEventsError,
    playerPastEventsLoading,
    playerPastEventsPageSize,
    playerUpcomingEvents,
    playerUpcomingEventsCurrentPage,
    playerUpcomingEventsError,
    playerUpcomingEventsLoading,
    playerUpcomingEventsPageSize,
    playerAttendance,
    playerEventTeams,
  } = useSelector(state => state.player);
  const [eventIdsWithoutAttendance, setEventIdsWithoutAttendance] = useState(
    []
  );
  const [pastEventIds, setPastEventIds] = useState([]);
  const [formattedPastEvents, setFormattedPastEvents] = useState([]);

  useFocusEffect(
    useCallback(() => {
      //cleanup when navigated out. otherwise previous data will be loaded from the redux state
      return () => {
        const payload = { data: [] };
        dispatch({
          type: PLAYER_PAST_EVENTS_SUCCESS,
          payload,
        });
        dispatch({
          type: PLAYER_UPCOMING_EVENTS_SUCCESS,
          payload,
        });
        dispatch({
          type: PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS,
          payload,
        });
      };
    }, [])
  );

  useEffect(() => {
    if (playerUpcomingEvents) {
      const currentTeamIds = playerEventTeams.map(e => e._id);
      const newTeamIds = playerUpcomingEvents
        .map(item => item.teamId)
        .filter(teamId => !currentTeamIds.includes(teamId));
      if (newTeamIds) {
        loadTeamData(newTeamIds);
      }
    }
  }, [playerUpcomingEvents]);

  useEffect(() => {
    const loadPastEvents = () => {
      fetchData(
        `/api/v1/events?participantIds=${PlayerInfoData.userId}&page=1&size=${playerPastEventsPageSize}&pastEvent=true&sort=DESC`,
        PLAYER_PAST_EVENTS_REQUEST,
        PLAYER_PAST_EVENTS_SUCCESS,
        PLAYER_PAST_EVENTS_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    };
    const loadUpcomingEvents = () => {
      fetchData(
        `/api/v1/events?participantIds=${PlayerInfoData.userId}&page=1&size=${playerUpcomingEventsPageSize}&pastEvent=false&sort=ASC`,
        PLAYER_UPCOMING_EVENTS_REQUEST,
        PLAYER_UPCOMING_EVENTS_SUCCESS,
        PLAYER_UPCOMING_EVENTS_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    };

    if (selectedTab === 2) {
      loadPastEvents();
    } else {
      loadUpcomingEvents();
    }
  }, [selectedTab]);

  useEffect(() => {
    //combine attendance data with existing event data
    if (playerAttendance && playerAttendance.length && playerPastEvents) {
      const processedKeys =
        pastEventIds?.length === eventIdsWithoutAttendance?.length
          ? []
          : formattedPastEvents.map(d => d._id);

      const nonProcessedEvents = playerPastEvents.filter(
        item => !processedKeys.includes(item._id)
      );
      const newProcessedData = nonProcessedEvents.map(item => {
        if (eventIdsWithoutAttendance.includes(item._id)) {
          const attendanceData = playerAttendance.find(
            attendance => attendance.eventId === item._id
          );
          if (attendanceData) {
            if (!attendanceData?.hasOwnProperty('isAttended')) {
              return item;
            } else {
              return {
                ...item,
                attended: attendanceData.isAttended,
              };
            }
          }
        }
        return item;
      });

      const formattedData =
        pastEventIds?.length === eventIdsWithoutAttendance?.length
          ? [...newProcessedData]
          : [...formattedPastEvents, ...newProcessedData];

      setFormattedPastEvents(formattedData);
    }
  }, [playerAttendance]);

  useEffect(() => {
    //load attendance data for new events
    if (playerPastEvents && playerPastEvents.length) {
      const eventIds = playerPastEvents.map(item => item._id);
      const newIds = eventIds.filter(item => !pastEventIds.includes(item));
      setPastEventIds(eventIds);
      setEventIdsWithoutAttendance(newIds);
      if (newIds && newIds.length && PlayerInfoData.userId) {
        !formattedPastEvents?.length &&
          setFormattedPastEvents(playerPastEvents);
        loadAttendanceData(newIds);
      }
      //load team data
      const currentTeamIds = playerEventTeams.map(e => e._id);
      const newTeamIds = playerPastEvents
        .map(item => item.teamId)
        .filter(teamId => !currentTeamIds.includes(teamId));
      if (newTeamIds) {
        loadTeamData(newTeamIds);
      }
    }
  }, [playerPastEvents, PlayerInfoData]);

  const loadAttendanceData = eventIds => {
    fetchData(
      `/api/v1/invitees?eventIds=${eventIds}&participantId=${PlayerInfoData.userId}&page=1&size=${eventIds.length}`,
      PLAYER_PAST_EVENTS_ATTENDANCE_REQUEST,
      PLAYER_PAST_EVENTS_ATTENDANCE_SUCCESS,
      PLAYER_PAST_EVENTS_ATTENDANCE_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };
  const loadTeamData = teamIds => {
    fetchTeamData(
      `/api/v1/teams?teamIds=${teamIds}&page=1&size=${teamIds.length}`,
      PLAYER_EVENTS_TEAM_DATA_REQUEST,
      PLAYER_EVENTS_TEAM_DATA_SUCCESS,
      PLAYER_EVENTS_TEAM_DATA_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  const loadUpcomingEventsNextPage = () => {
    fetchData(
      `/api/v1/events?participantIds=${PlayerInfoData.userId}&page=${
        playerUpcomingEventsCurrentPage + 1
      }&size=${playerUpcomingEventsPageSize}&pastEvent=false&sort=DESC`,
      PLAYER_UPCOMING_EVENTS_UPDATE_REQUEST,
      PLAYER_UPCOMING_EVENTS_UPDATE_SUCCESS,
      PLAYER_UPCOMING_EVENTS_UPDATE_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  const loadPastEventsNextPage = () => {
    if (playerPastEventTotalRecords !== playerPastEvents.length) {
      fetchData(
        `/api/v1/events?participantIds=${PlayerInfoData.userId}&page=${
          playerPastEventsCurrentPage + 1
        }&size=${playerPastEventsPageSize}&pastEvent=true&sort=DESC`,
        PLAYER_PAST_EVENTS_UPDATE_REQUEST,
        PLAYER_PAST_EVENTS_UPDATE_SUCCESS,
        PLAYER_PAST_EVENTS_UPDATE_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    }
  };

  const addPlayerAvailability = data => {
    if (!data) return [];
    return data.map(item => {
      return {
        ...item,
        isAvailable: PlayerInfoData.isAvailable,
      };
    });
  };

  return (
    <View style={PlayerInfoActivityStyles.PlayerInfoWrapper}>
      <View style={PlayerInfoActivityStyles.container}>
        {playerPastEventsLoading || playerUpcomingEventsLoading ? (
          <ActivitySpinner />
        ) : selectedTab === 1 ? (
          <Events
            data={addPlayerAvailability(playerUpcomingEvents)}
            loadMoreEvents={loadUpcomingEventsNextPage}
            selectedTab={selectedTab}
            teams={playerEventTeams}
          />
        ) : selectedTab === 2 ? (
          <Events
            data={formattedPastEvents}
            loadMoreEvents={loadPastEventsNextPage}
            selectedTab={selectedTab}
            teams={playerEventTeams}
          />
        ) : (
          <SessionsPlanContainer playerInfoData={PlayerInfoData} />
        )}
      </View>
    </View>
  );
};

export default PlayerInfoActivityContainer;
