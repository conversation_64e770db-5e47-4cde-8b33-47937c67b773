import { AntDesign, Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, Image, Text, TextInput, View } from 'react-native';
import Autocomplete from 'react-native-autocomplete-input';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import SelectionModal from '../../components/modal/SelectionModal/SelectionModal';
import SuperAdminChangePasswordModal from '../../components/modal/SuperAdminChangePasswordModal';
import SuccessModal from '../../components/modal/SuperAdminChangePasswordModal/SuccessModal';
import { isTabDevice } from '../../config/appConfig';
import {
  S3_BUCKET_LOCATION,
  emailRegex,
  imageError,
  messageFileTypes,
  userCreateTypes,
  userRoleType,
} from '../../constants/constants';
import {
  FOOTBALL_SERVICE,
  USER_MANAGEMENT_SERVICE,
} from '../../constants/services';
import {
  convertDateObjectToJSDate,
  generateNewDateWithTimeReset,
  getDateObjectFromISOString,
  get_url_extension,
} from '../../helpers';
import fileDownload from '../../helpers/FileDownload';
import removeSpace from '../../helpers/removeSpace';
import useApi from '../../hooks/useApi';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import useStyles from '../../hooks/useStyles';
import {
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../store/actionTypes/Team/TeamAction';
import {
  UPLOAD_IMAGE_FAIL,
  UPLOAD_IMAGE_REQUEST,
  UPLOAD_IMAGE_RESET,
  UPLOAD_IMAGE_SUCCESS,
} from '../../store/actionTypes/UploadImage/uploadImageAction';
import {
  ADD_USER_FAIL,
  ADD_USER_REQUEST,
  ADD_USER_SUCCESS,
  CHANGE_USER_SEARCH_PARAMS_TYPE,
  GET_ALL_SELECTED_CHILDREN_FAIL,
  GET_ALL_SELECTED_CHILDREN_REQUEST,
  GET_ALL_SELECTED_CHILDREN_RESET,
  GET_ALL_SELECTED_CHILDREN_SUCCESS,
  GET_ALL_USERS_FAIL,
  GET_ALL_USERS_REQUEST,
  GET_ALL_USERS_RESET,
  GET_ALL_USERS_SUCCESS,
  GET_KOACH_USERS_BY_EMAIL_FAIL,
  GET_KOACH_USERS_BY_EMAIL_REQUEST,
  GET_KOACH_USERS_BY_EMAIL_SUCCESS,
  GET_PASSWORD_RESET_FAIL,
  GET_PASSWORD_RESET_REQUEST,
  GET_PASSWORD_RESET_SUCCESS,
  GET_USER_BY_EMAIL_FAIL,
  GET_USER_BY_EMAIL_REQUEST,
  GET_USER_BY_EMAIL_SUCCESS,
  RESET_PASSWORD_INITIAL,
  GET_CITIES_FAIL,
  GET_CITIES_REQUEST,
  GET_CITIES_SUCCESS,
  GET_CITIES_RESET,
  GET_CERTIFICATIONS_RESET,
} from '../../store/actionTypes/User/User';
import customAddUserStyle from './addUserStyle';
import Checkbox from 'expo-checkbox';

import useS3bucketLocation from '../../hooks/useS3bucketLocation';
import useFileUpload from '../../hooks/useFileUpload';

import useGenerateImageUrls from '../../hooks/useGenerateImageUrls';
import { genders } from '../../constants/constants';
import { useAddUser } from '../../hooks/useAddUser';
import { countryList } from '../../constants/countries';

const initialUserState = {
  firstName: '',
  lastName: '',
  emailId: '',
  contact: '',
  secondaryContactNumber: '',
  dateOfBirth: null,
  country: '',
  city: '',
  comment: '',
};

const AddUser = ({
  userCreationMode,
  userCreationTypeMode,
  isEditMode,
  userDetails,
  renderUserCreationType,
}) => {
  const [getFileObject, preSignedUrl] = useGenerateImageUrls();
  const [getBucketLocation, bucketLocation] = useS3bucketLocation();
  const [upload, uploadedContent, uploadProgress] = useFileUpload();
  const addUserStyle = useStyles(customAddUserStyle);
  const [isSubmit, setIsSubmit] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [mode, setMode] = useState('date');
  const [profileImageInfo, setProfileImageInfo] = useState(null);
  const [image, setImage] = useState(null);
  const [userInput, setUserInput] = useState({ ...initialUserState });
  const [teamArray, setTeamArray] = useState([]);
  const [childrenArray, setChildrenArray] = useState([]);
  const [selectedChildrenArray, setSelectedChildrenArray] = useState([]);
  const [assignedTeam, setAssignedTeam] = useState([]);
  const [showValidationError, setShowValidationError] = useState(null);
  const [isImageUplading, setImageUploading] = useState(false);

  const [errorMessage, setErrorMessage] = useState('');
  const [koachEmail, setKoachEmail] = useState('');
  const [koachProfileImageUrl, setKoachProfileImageUrl] = useState('');
  const [setIsTeamModalOpen, isTeamModalOpen] = useInputSelectModal();
  const [setIsChildModalOpen, isChildModalOpen] = useInputSelectModal();
  const [
    onEndReachedCalledDuringMomentum,
    setOnEndReachedCalledDuringMomentum,
  ] = useState(true);
  const [isAutoCompleteDropdownOpen, setIsAutoCompleteDropdownOpen] =
    useState(false);
  const [searchKey, setSearchKey] = useState('');
  const [isAutoCompleteResultShow, setIsAutoCompleteResultShow] =
    useState(false);
  const [isChecked, setChecked] = useState(false);
  const [isResetPlayerPassword, setIsResetPlayerPassword] = useState(false);
  const [isGenderModalOpen, setGenderModalOpen] = useState(false);
  const [isCertificateModalOpen, setCertificateModalOpen] = useState(false);
  const [selectedGender, setSelectedGender] = useState(null);
  const [selectedCertificates, setSelectedCertificates] = useState([]);
  const [isCountryModalOpen, setCountryModalOpen] = useState(false);
  const [isCityModalOpen, setIsCityModalOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState();
  const [selectedCity, setSelectedCity] = useState();
  const [searchCityKey, setSearchCityKey] = useState('');
  const [cityList, setCityList] = useState([]);

  const { userData } = useSelector(state => state?.auth);
  const { teamData } = useSelector(state => state?.team);
  const {
    uploadPofileImageData,
    uploadPofileImageLoading,
    creatUserSuccess,
    createUserFail,
    createUserLoading,
    userExists,
    koachUsersData,
    koachUsersFetching,
    koachUsersFetchSuccess,
    koachUsersError,
    children,
    childrenTotalRecords,
    childrenPage,
    selectedChildren: tmpSelectedChildren,
    passwordResetLoading,
    passwordResetSuccess,
    isPasswordResetError,
    cerficatesLoading,
    certificates,
    cerficatesError,
    createCertificateLoading,
    createCertificationSuccess,
    createCertificationFail,
    cities,
  } = useSelector(state => state?.addUser);

  const hundredYearsAgo = new Date().setFullYear(new Date().getFullYear() - 100);
  const [apiCall] = useApi();
  const [fetchChildren] = useApi();
  const [getKoachUsers] = useApi();
  const [passwordReset] = useApi();
  const { getCertifications, createCertifications } = useAddUser();

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [isFileError, setIsFileError] = useState({
    status: false,
    error: undefined,
  });
  const [isPasswordChangeModalOpen, setIsPasswordChangeModalOpen] =
    useState(false);
  const [isSuccessPasswordOpen, setIsSuccessPasswordOpen] = useState(false);
  const [password, setPassword] = useState('');
  const {
    firstName,
    lastName,
    emailId,
    contact,
    dateOfBirth,
    secondaryContactNumber,
    guardianName,
    city,
    country,
    comment,
  } = userInput;

  useEffect(() => {
    getBucketLocation({
      path: S3_BUCKET_LOCATION.profileImages,
      service: USER_MANAGEMENT_SERVICE,
    });
  }, []);

  const startUploading = useCallback(
    async (filePath, fileName) => {
      if (bucketLocation.filePath) {
        const picture = await fetch(filePath);

        const pictureBlob = await picture.blob();
        const file = new File([pictureBlob], fileName);

        upload(file, bucketLocation.filePath, bucketLocation?.bucketName || '');
      }
    },
    [bucketLocation]
  );

  useEffect(() => {
    userCreationMode === userRoleType.COACH && getCertifications();
    setSelectedGender(
      userDetails?.gender ? getGenderString(userDetails?.gender) : null
    );
    const tempCountry = countryList
      ?.filter(country => country.name_en == userDetails?.country)
      .map(data => {
        return {
          value: data.alpha_2,
          label: data.name_en,
        };
      });
    setSelectedCity(
      userDetails?.city
        ? [{ value: userDetails?.city, label: userDetails?.city }]
        : null
    );
    setSelectedCountry(userDetails?.country ? tempCountry : null);
  }, [isEditMode, userDetails, userCreationMode]);

  const getGenderString = value => {
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  };

  useEffect(() => {
    if (uploadedContent) {
      dispatch({
        type: UPLOAD_IMAGE_SUCCESS,
        payload: uploadedContent,
      });

      getFileObject({
        fileKey: uploadedContent.fileKey,
        bucketName: uploadedContent.bucketName,
        id: 'user-image_profile',
      });
      setImageUploading(false);
    }
  }, [uploadedContent]);

  const resetState = () => {
    setSelectedCertificates([]);
    setSelectedGender(null);
    setAssignedTeam([]);
    setUserInput({ ...userInput, ...initialUserState });
    setProfileImageInfo(null);
    setImage('');
    setIsFileError(false);
  };

  useEffect(() => {
    if (userExists) {
      setErrorMessage('Email already exists');
    } else {
      if (errorMessage) {
        setErrorMessage('');
      }
    }
  }, [userExists, userInput]);

  useEffect(() => {
    if (passwordResetSuccess) {
      setIsPasswordChangeModalOpen(false);
      setIsSuccessPasswordOpen(true);
      setPassword('');
    }
  }, [passwordResetSuccess]);

  useEffect(() => {
    userInput && showValidationError && setShowValidationError(false);
  }, [userInput]);

  useEffect(() => {
    if (isEditMode && userDetails) {
      setUserInput({
        ...userDetails,
        dateOfBirth: userDetails.dateOfBirth || null,
      });
      setImage(userDetails?.profileImageUrl);

      if (userCreationMode === userRoleType.PARENT) {
        const ownChldren = userDetails?.childrenIds || [];
        if (ownChldren) {
          fetchChildren(
            `/api/v1/users?userIds=${String(ownChldren)}`,
            GET_ALL_SELECTED_CHILDREN_REQUEST,
            GET_ALL_SELECTED_CHILDREN_SUCCESS,
            GET_ALL_SELECTED_CHILDREN_FAIL,
            null,
            '',
            'GET',
            null,
            USER_MANAGEMENT_SERVICE
          );
        }
      }
    }
  }, [isEditMode, userDetails, userCreationMode]);

  useEffect(() => {
    createCertificationSuccess && getCertifications();
  }, [createCertificateLoading]);

  useEffect(() => {
    selectedCountry &&
      searchCityKey &&
      handleGetCities(searchCityKey, selectedCountry?.[0]?.value);
  }, [searchCityKey]);

  useEffect(() => {
    const mapCityList = cities?.map(city => {
      return {
        value: city,
        label: city,
      };
    });
    setCityList(mapCityList);
  }, [cities]);

  useEffect(() => {
    dispatch({
      type: GET_CITIES_RESET,
    });
  }, [selectedCountry]);

  useEffect(() => {
    if (
      teamArray?.length &&
      isEditMode &&
      userDetails &&
      userDetails?.type === userRoleType.COACH &&
      Array.isArray(userDetails?.teamLabels) &&
      userDetails?.teamLabels?.length
    ) {
      const teams = teamArray.map(t => t.value);
      userDetails.teamLabels.forEach(team => {
        teams.includes(team?._id) &&
          !assignedTeam.includes(team?._id) &&
          pushValue(team?._id);
      });
    }
  }, [teamArray]);

  useEffect(() => {
    if (
      certificates?.length &&
      isEditMode &&
      userDetails &&
      userDetails?.type === userRoleType.COACH &&
      Array.isArray(userDetails?.certificationIds) &&
      userDetails?.certificationIds?.length
    ) {
      const newCertificates = customCertificateList().map(t => t.value);
      userDetails?.certificationIds?.forEach(certificate => {
        newCertificates.includes(certificate) &&
          !selectedCertificates.includes(certificate) &&
          pushCertificates(certificate);
        console.log(certificate);
      });
    }
  }, [certificates]);

  useEffect(() => {
    if (isSubmit && creatUserSuccess) {
      navigation.pop();
      dispatch({
        type: UPLOAD_IMAGE_RESET,
      });
    }
  }, [creatUserSuccess]);

  useEffect(() => {
    !isEditMode && resetState();
  }, [userCreationMode, userCreationTypeMode]);

  useEffect(() => {
    if (!uploadPofileImageLoading && isImageUplading) {
      setImageUploading(false);
    }
  }, [uploadPofileImageLoading]);

  useEffect(() => {
    return () => {
      dispatch({
        type: CHANGE_USER_SEARCH_PARAMS_TYPE,
      });
      dispatch({
        type: GET_ALL_USERS_RESET,
      });

      dispatch({
        type: GET_ALL_SELECTED_CHILDREN_RESET,
      });
      dispatch({
        type: GET_CITIES_RESET,
      });
    };
  }, []);

  //get the Teams
  useEffect(() => {
    if (userCreationMode === userRoleType.COACH) {
      apiCall(
        `/api/v1/teams?coachId=${userData?.id}&page=1&size=100`,
        TEAM_REQUEST,
        TEAM_SUCCESS,
        TEAM_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }
  }, [userCreationMode]);

  const onPasswordSubmit = (password, userDetails) => {
    userDetails &&
      password &&
      passwordReset(
        `/api/v1/users/${userDetails.id}/password?password=${encodeURIComponent(
          password
        )}`,
        GET_PASSWORD_RESET_REQUEST,
        GET_PASSWORD_RESET_SUCCESS,
        GET_PASSWORD_RESET_FAIL,
        null,
        '',
        'PUT',
        null,
        USER_MANAGEMENT_SERVICE
      );
  };

  useEffect(() => {
    if (userCreationMode === userRoleType.PARENT) {
      handleChlidSearchData();
    }
  }, [userCreationMode]);

  const handleChlidSearchData = (page = 1, searchText = '') => {
    apiCall(
      `/api/v1/users?page=${page}&size=20&search=${searchText}&type=PLAYER`,
      GET_ALL_USERS_REQUEST,
      GET_ALL_USERS_SUCCESS,
      GET_ALL_USERS_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  useEffect(() => {
    const tempTeamArray = [];
    teamData?.data?.map(TeamData => {
      tempTeamArray.push({
        label: TeamData.teamName,
        value: TeamData._id,
      });
    });
    setTeamArray(tempTeamArray);
  }, [teamData]);

  useEffect(() => {
    if (children?.length > 0) {
      setChildrenArray(
        children.map(child => {
          return {
            label:
              child.firstName && child.lastName
                ? child.firstName + ' ' + child.lastName
                : child.firstName,
            value: child.id,
          };
        })
      );
    }
  }, [children]);

  useEffect(() => {
    if (tmpSelectedChildren?.length) {
      setSelectedChildrenArray(
        tmpSelectedChildren.map(child => {
          return {
            label:
              child.firstName && child.lastName
                ? child.firstName + ' ' + child.lastName
                : child.firstName,
            value: child.id,
          };
        })
      );
    }
  }, [tmpSelectedChildren]);

  useEffect(() => {
    if (uploadPofileImageData) {
      setProfileImageInfo(uploadPofileImageData);
    }
  }, [uploadPofileImageData]);

  const onChange = date => {
    setShowDatePicker(false);

    const dateOfBirthAdjustedToLocalTimeObj = getDateObjectFromISOString(date);

    setUserInput({
      ...userInput,
      dateOfBirth: date ? dateOfBirthAdjustedToLocalTimeObj : null,
    });
  };

  const pushValue = value => {
    setAssignedTeam(teams => [...teams, value]);
  };

  const pushCertificates = value => {
    setSelectedCertificates(certificates => [...certificates, value]);
  };

  const deleteChild = index => {
    const tempSelectedChildrenArray = [...selectedChildrenArray];
    tempSelectedChildrenArray.splice(index, 1);
    setSelectedChildrenArray(tempSelectedChildrenArray);
  };

  const customCertificateList = () => {
    return certificates?.map(item => ({
      value: item?._id,
      label: item?.title,
    }));
  };

  const initCountry = countryList.map(country => {
    return {
      value: country.alpha_2,
      label: country.name_en,
    };
  });

  const handleGetCities = (searchText, countryKey) => {
    apiCall(
      `/api/v1/locations/cities?searchCityNameLike=${searchText}&type=(cities)&country=${countryKey}`,
      '',
      GET_CITIES_SUCCESS,
      GET_CITIES_FAIL,
      null,
      '',
      'GET',
      null,
      USER_MANAGEMENT_SERVICE
    );
  };

  const deleteTeam = index => {
    const tempAssigned = [...assignedTeam];
    tempAssigned.splice(index, 1);
    setAssignedTeam(tempAssigned);
  };

  const deleteCertificate = index => {
    const tempCertficates = [...selectedCertificates];
    tempCertficates.splice(index, 1);
    setSelectedCertificates(tempCertficates);
  };

  useEffect(() => {
    if (koachProfileImageUrl) {
      fileDownload(koachProfileImageUrl, 'tempProfileImage', 'jpg')
        .then(uri => {
          setImageUploading(true);
          fileUpload(uri);
          setImage(uri);
        })
        .catch(err => {
          setImageUploading(false);
          console.error(
            'Error while downloading and uploading profile image from Koach',
            err
          );
        });
    }
  }, [koachProfileImageUrl]);

  const getFileInfo = async fileURI => {
    const fileInfo = await FileSystem.getInfoAsync(fileURI);
    return fileInfo;
  };

  const pickImage = async () => {
    try {
      setImageUploading(true);
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });
      //Limit File size to 3mb when uploading image
      if (!result.canceled) {
        const fileExtension = get_url_extension(result.assets[0].uri);
        if (messageFileTypes.includes(fileExtension.toLowerCase())) {
          const fileInfo = await getFileInfo(result.assets[0].uri);
          if (3 > Number(fileInfo?.size / 1048576)) {
            setImageUploading(true);
            startUploading(result.assets[0].uri, `photo.${fileExtension}`);
            setImage(result.assets[0].uri);
            setIsFileError({
              status: false,
              error: undefined,
            });
          } else {
            setIsFileError({
              status: true,
              error: imageError.MAX_FILE_SIZE_ERROR,
            });
            setImageUploading(false);
            setImage('');
          }
        }
      } else {
        setImageUploading(false);
      }
    } catch {
      setImageUploading(false);
    }
  };

  const addUser = () => {
    setIsSubmit(true);
    if (userExists || errorMessage) return;

    let hasChild = selectedChildrenArray?.length;
    let isChildValid =
      userCreationMode === userRoleType.PARENT ? hasChild : true;

    if (
      firstName &&
      emailId &&
      isChildValid &&
      (userCreationMode !== userRoleType.PLAYER || guardianName)
    ) {
      setShowValidationError(false);
      const createUserObject = {
        firstName,
        lastName,
        emailId,
        contact,
        dateOfBirth,
        type: userCreationMode?.toUpperCase(),
        profileImage: profileImageInfo,
        isUserTermAccepted: isChecked,
      };

      if (selectedGender) {
        createUserObject.gender = selectedGender?.toUpperCase();
      }

      if (isResetPlayerPassword) {
        createUserObject.isResetPlayerPassword = isResetPlayerPassword;
      }

      if (userCreationMode === userRoleType.COACH) {
        createUserObject.teamIds = assignedTeam;
        createUserObject.certificationIds = selectedCertificates;
        createUserObject.country = selectedCountry?.[0]?.label;
        createUserObject.city = selectedCity?.[0]?.label;
        createUserObject.comment = comment;
      }
      if (userCreationMode === userRoleType.PARENT) {
        createUserObject.secondaryContactNumber = secondaryContactNumber;
        createUserObject.childrenIds = selectedChildrenArray.map(
          child => child.value
        );
      }
      if (userCreationTypeMode === userCreateTypes.KOACH && !isEditMode) {
        createUserObject.platformType = 'KOACH';
      }

      if (userCreationMode === userRoleType.PLAYER) {
        createUserObject.guardianName = guardianName;
      }

      apiCall(
        '/api/v1/users',
        ADD_USER_REQUEST,
        ADD_USER_SUCCESS,
        ADD_USER_FAIL,
        createUserObject,
        '',
        'POST',
        null,
        USER_MANAGEMENT_SERVICE
      );
    } else {
      setShowValidationError(true);
    }
  };

  const updateUser = () => {
    setIsSubmit(true);
    if (!isEditMode || (isEditMode && !userDetails)) return;

    let hasChild = selectedChildrenArray?.length;
    let isChildValid =
      userCreationMode === userRoleType.PARENT ? hasChild : true;

    if (firstName && emailId && isChildValid) {
      setShowValidationError(false);

      let updatedUserDetails = {
        id: userDetails?.id,
        firstName,
        lastName,
        dateOfBirth: dateOfBirth || null,
        type: userDetails.type,
        contact,
        emailId,
        profileImage: profileImageInfo ?? userDetails?.profileImage,
      };

      if (userCreationMode === userRoleType.PARENT) {
        updatedUserDetails.secondaryContactNumber = secondaryContactNumber;
        updatedUserDetails.childrenIds = selectedChildrenArray.map(
          child => child.value
        );
      }

      if (userCreationMode === userRoleType.COACH) {
        updatedUserDetails.teamIds = assignedTeam;
        updatedUserDetails.certificationIds = selectedCertificates;
        updatedUserDetails.country = selectedCountry?.[0]?.label;
        updatedUserDetails.city = selectedCity?.[0]?.label;
        updatedUserDetails.comment = comment;
      }

      if (selectedGender) {
        updatedUserDetails.gender = selectedGender?.toUpperCase() || null;
      }

      console.log({ ...userInput, ...updatedUserDetails });

      apiCall(
        '/api/v1/users',
        ADD_USER_REQUEST,
        ADD_USER_SUCCESS,
        ADD_USER_FAIL,
        { ...userInput, ...updatedUserDetails },
        '',
        'PUT',
        null,
        USER_MANAGEMENT_SERVICE
      );
    } else {
      setShowValidationError(true);
    }
  };

  const showMode = currentMode => {
    setShowDatePicker(true);
    setMode(currentMode);
  };

  const getUserDetailsByEmail = email => {
    email &&
      apiCall(
        `/api/v1/users?email=${email}`,
        GET_USER_BY_EMAIL_REQUEST,
        GET_USER_BY_EMAIL_SUCCESS,
        GET_USER_BY_EMAIL_FAIL,
        null,
        '',
        'GET',
        null,
        USER_MANAGEMENT_SERVICE
      );
  };

  const updateEmail = text => {
    setUserInput({ ...userInput, emailId: removeSpace(text) });
    if (!text.match(emailRegex)) {
      setErrorMessage('Enter a valid email address');
      return;
    } else {
      setErrorMessage('');
    }
    if (isEditMode && userDetails?.emailId === emailId) return;
    getUserDetailsByEmail(text);
  };

  const mandatoryField = fieldName => {
    return (
      <View>
        {!fieldName && <Text style={addUserStyle.mandatoryField}>*</Text>}
      </View>
    );
  };

  const fetchKoachUser = email => {
    if (
      !koachUsersData ||
      koachUsersData?.data.length < koachUsersData.totalRecords
    )
      getKoachUsers(
        `/api/v1/koach/users?emailLike=${email}&page=${(koachUsersData?.page || 0) + 1
        }&size=5&enabled=true`,
        GET_KOACH_USERS_BY_EMAIL_REQUEST,
        GET_KOACH_USERS_BY_EMAIL_SUCCESS,
        GET_KOACH_USERS_BY_EMAIL_FAIL,
        null,
        '',
        'GET',
        null,
        USER_MANAGEMENT_SERVICE,
        {
          searchParams: email,
        }
      );
  };

  useEffect(() => {
    if (!onEndReachedCalledDuringMomentum && !!koachUsersData?.data?.length) {
      fetchKoachUser(koachEmail);
      setOnEndReachedCalledDuringMomentum(true);
    }
  }, [onEndReachedCalledDuringMomentum]);

  const toggleCheckbox = ()=> {
    setChecked(!isChecked)
  }

  const renderLoading = () => (
    <TouchableOpacity style={addUserStyle.autoCompleteListItem}>
      <ActivityIndicator
        size="small"
        color="white"
        style={addUserStyle.loader}
      />
    </TouchableOpacity>
  );

  const calMarginTopForCreateBtn = () => {
    if (
      userCreationTypeMode === userCreateTypes.KOACH &&
      userCreationMode === userRoleType.COACH &&
      !isEditMode
    ) {
      return -wp('9%');
    } else {
      if (userCreationTypeMode !== userCreateTypes.KOACH) {
        if (userCreationMode === userRoleType.PARENT) {
          return wp('6%');
        } else {
          return -wp('3.5%');
        }
      } else {
        return wp('2%');
      }
    }
  };

  const renderDateOfBirth = () => (
    <View style={{ ...addUserStyle.inputFieldWrapper }}>
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Date of Birth</Text>
      </View>
      <TouchableOpacity onPress={() => showMode('date')}>
        <View View TouchableOpacity style={addUserStyle.inputView}>
          <View style={[addUserStyle.textInput, addUserStyle.dateInput]}>
            <Text style={addUserStyle.dateTxt}>
              {dateOfBirth
                ? convertDateObjectToJSDate(dateOfBirth).toDateString()
                : null}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderCreateButton = () => {
    return (
      <View style={addUserStyle.buttonWrapper}>
        {isSubmit && (
          <View style={addUserStyle.errorTextWrapper}>
            <Text style={addUserStyle.errorText}>
              {showValidationError
                ? `Please fill required fields`
                : errorMessage
                  ? errorMessage
                  : isSubmit && createUserFail
                    ? createUserFail
                    : ''}
            </Text>
          </View>
        )}
        <View>
          <TouchableOpacity
            onPress={() => {
              !isImageUplading && (isEditMode ? updateUser() : addUser());
            }}
            style={
              (isImageUplading
                ? {
                  ...addUserStyle.createView,
                  ...addUserStyle.disableButton,
                }
                : addUserStyle.createView,
                isEditMode && userCreationMode === userRoleType.PLAYER
                  ? { ...addUserStyle.createView, marginTop: wp('2%') }
                  : { ...addUserStyle.createView })
            }
          >
            <Text style={addUserStyle.createTxt}>
              {isEditMode ? 'Save' : 'Create'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderTeamsSelection = () =>
    userCreationMode === userRoleType.COACH && (
      <View style={{ ...addUserStyle.rowSelectionView, zIndex: -1 }}>
        <ScrollView
          style={{
            ...addUserStyle.selectedTeams,
            maxHeight: isTabDevice() ? hp('9.5%') : hp('10%'),
          }}
        >
          {assignedTeam?.map((data, index) => (
            <View key={index} style={addUserStyle.selectedLabel}>
              <Text key={index} style={addUserStyle.labelTxt}>
                {teamArray?.find(({ value }) => value === data)?.label}
              </Text>
              <AntDesign
                onPress={() => deleteTeam(index)}
                name="close"
                style={addUserStyle.icon}
                color="black"
              />
            </View>
          ))}
        </ScrollView>
      </View>
    );

  const certificateSelection = () =>
    userCreationMode === userRoleType.COACH && (
      <View style={{ ...addUserStyle.rowSelectionView }}>
        <ScrollView
          style={{
            ...addUserStyle.selectedTeams,
            maxHeight: isTabDevice() ? hp('9.5%') : hp('10%'),
          }}
        >
          {selectedCertificates?.map((data, index) => (
            <View key={index} style={addUserStyle.selectedLabel}>
              <Text key={index} style={addUserStyle.labelTxt}>
                {
                  customCertificateList()?.find(({ value }) => value === data)
                    ?.label
                }
              </Text>
              <AntDesign
                onPress={() => deleteCertificate(index)}
                name="close"
                style={addUserStyle.icon}
                color="black"
              />
            </View>
          ))}
        </ScrollView>
      </View>
    );

  const renderSelectedChildren = () => (
    <View style={{ ...addUserStyle.rowSelectionView, zIndex: 1 }}>
      <ScrollView
        style={{
          ...addUserStyle.selectedTeams,
          maxHeight: isTabDevice() ? hp('9.5%') : hp('10%'),
        }}
      >
        {selectedChildrenArray?.map((data, index) => (
          <View key={index} style={addUserStyle.selectedLabel}>
            <Text key={index} style={addUserStyle.labelTxt}>
              {data.label}
            </Text>
            <AntDesign
              onPress={() => deleteChild(index)}
              name="close"
              style={addUserStyle.icon}
              color="black"
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
  const formatedHeaderLabel = () => {
    if (userCreationTypeMode === userCreateTypes.NEW) {
      switch (userCreationMode) {
        case userRoleType.COACH:
          return 'Create New Coach';
        case userRoleType.PLAYER:
          return 'Create New Player';
        case userRoleType.PARENT:
          return 'Create New Parent';
        default:
          break;
      }
    } else {
      return 'Add Existing Profile';
    }
  };

  const renderImageUploadErrorMsg = () => {
    return (
      <View style={addUserStyle.errorMessage2Wrapper}>
        <Text style={addUserStyle.errorText2}>
          {isFileError.status ? isFileError.error : ''}
        </Text>
      </View>
    );
  };

  const renderLabel = () => {
    if (isEditMode) {
      return (
        <>
          <Text style={addUserStyle.avatarText}>Edit Profile</Text>
          <TouchableOpacity
            onPress={() => {
              dispatch({ type: RESET_PASSWORD_INITIAL });
              setIsPasswordChangeModalOpen(!isPasswordChangeModalOpen);
            }}
            style={addUserStyle.changePasswordButton}
          >
            <Ionicons
              name="lock-closed"
              color="#fff"
              size={isTabDevice() ? 23 : 20}
            />
            <Text style={addUserStyle.changePasswordText}>Change Password</Text>
          </TouchableOpacity>
          {renderImageUploadErrorMsg()}
        </>
      );
    } else {
      return (
        <>
          <Text style={addUserStyle.avatarText}>{formatedHeaderLabel()}</Text>
          {renderImageUploadErrorMsg()}
        </>
      );
    }
  };

  const renderImgUpload = () => {
    return (
      <TouchableOpacity
        style={addUserStyle.TouchablePressed}
        onPress={() => pickImage()}
      >
        {isImageUplading ? (
          <ActivitySpinner color="white" />
        ) : (
          <>
            <Image
              style={addUserStyle.addUserImg}
              source={require('../../../assets/icons/addUser.png')}
            />

            <Image
              style={addUserStyle.userImg}
              source={{
                uri: image,
              }}
            />
          </>
        )}
      </TouchableOpacity>
    );
  };

  const renderSecondaryContactElement = () => (
    <View style={addUserStyle.inputFieldWrapper}>
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>
          Secondary Contact Number
        </Text>
      </View>
      <View style={addUserStyle.inputView}>
        <TextInput
          style={addUserStyle.textInput}
          keyboardType={'numeric'}
          onChangeText={text =>
            setUserInput({ ...userInput, secondaryContactNumber: text })
          }
          placeholder="+1-xxx-xxx-xxxx"
          placeholderTextColor="#595959"
          value={secondaryContactNumber}
        />
      </View>
    </View>
  );

  const renderTeamsElement = () => (
    <View
      style={{
        ...addUserStyle.inputFieldWrapper,
      }}
    >
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Team</Text>
      </View>
      <SelectionModal
        title={'Assign to'}
        items={teamArray.filter(team => !assignedTeam?.includes(team?.value))}
        onCloseHook={setIsTeamModalOpen}
        onSelectItemHook={selectedItem => {
          selectedItem?.map(item => pushValue(item.value));
        }}
        defaultValues={null}
        isEnableAutoComplete
        isModalOpen={isTeamModalOpen}
        enableDefaultLabel
        multiple
      />

      {renderTeamsSelection()}
    </View>
  );

  const renderSearchEmailElement = () => (
    <View
      style={{
        ...addUserStyle.rowView,
        height: isTabDevice() ? hp('10%') : 60,
      }}
    >
      <View>
        <View style={addUserStyle.inputFieldLabelWrapper}>
          <Text style={addUserStyle.inputFieldLabel}>Search Email</Text>
        </View>
        <Autocomplete
          flatListProps={{
            onEndReachedThreshold: 5,
            onMomentumScrollBegin: () =>
              setOnEndReachedCalledDuringMomentum(true),
            onMomentumScrollEnd: () =>
              setOnEndReachedCalledDuringMomentum(false),
          }}
          placeholder="Search Email"
          placeholderTextColor="#595959"
          data={
            !!koachUsersData?.data?.length
              ? koachUsersData.data
              : koachUsersFetching
                ? ['loading']
                : (koachUsersFetchSuccess || koachUsersError) && koachEmail
                  ? ['Not Found']
                  : []
          }
          value={koachEmail}
          onChangeText={email => {
            setKoachEmail(removeSpace(email));
            dispatch({
              type: CHANGE_USER_SEARCH_PARAMS_TYPE,
              payload: removeSpace(email),
            });
            fetchKoachUser(removeSpace(email));
          }}
          style={addUserStyle.autoCompleteInput}
          inputContainerStyle={addUserStyle.autoCompleteInputContainer}
          listContainerStyle={addUserStyle.autoCompletelistContainerStyle}
          listStyle={addUserStyle.autoCompleteList}
          renderItem={({ item }) => {
            return (
              <>
                {item === 'loading' ? (
                  renderLoading()
                ) : item === 'Not Found' ? (
                  <TouchableOpacity
                    key={item}
                    style={addUserStyle.autoCompleteListItem}
                  >
                    <Text
                      style={{
                        ...addUserStyle.autoCompleteText,
                        textAlign: 'center',
                      }}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item}
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <TouchableOpacity
                    key={item.email}
                    style={addUserStyle.autoCompleteListItem}
                    onPress={() => {
                      setKoachEmail(item.email);
                      setUserInput({
                        ...userInput,
                        emailId: item.email,
                        firstName: item.firstName,
                      });
                      setKoachProfileImageUrl(item.profilePicUrl);
                    }}
                  >
                    <Text
                      style={addUserStyle.autoCompleteText}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item.email}
                    </Text>
                  </TouchableOpacity>
                )}
                {item !== 'loading' &&
                  koachUsersData?.data[koachUsersData?.data?.length - 1]
                    .email === item.email &&
                  koachUsersFetching &&
                  renderLoading()}
              </>
            );
          }}
          hideResults={
            !koachUsersData?.data?.length ||
            (koachUsersData?.data?.length &&
              koachUsersData.data.some(({ email }) => email === koachEmail))
          }
          onFocus={() => {
            setIsAutoCompleteDropdownOpen(true);
          }}
          onBlur={() => {
            setIsAutoCompleteDropdownOpen(false);
          }}
        />
      </View>
    </View>
  );

  const onReachEndHandler = () => {
    if (childrenTotalRecords > children?.length || 0) {
      const nextPage = childrenPage + 1;
      handleChlidSearchData(nextPage,searchKey)
    }
  };

  const renderChildListElement = () => (
    <View style={{ ...addUserStyle.inputFieldWrapper }}>
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Add Child</Text>
        <AntDesign
          name="star"
          size={10}
          color="black"
          style={addUserStyle.requiredAstric}
        />
      </View>
      <View style={{ zIndex: 10 }}>
        <SelectionModal
          title={'Select Child'}
          items={childrenArray?.filter(
            child =>
              !selectedChildrenArray
                ?.map(selectedChild => selectedChild?.value)
                .includes(child?.value)
          )}
          onCloseHook={setIsChildModalOpen}
          onSelectItemHook={selectedItem => {
            setSearchKey('');
            setIsAutoCompleteResultShow(false);
            setSelectedChildrenArray(oldChildren => [
              ...oldChildren,
              ...selectedItem,
            ]);
          }}
          defaultValues={null}
          isEnableAutoComplete
          isModalOpen={isChildModalOpen}
          onAutoCompleteChange={searchText => {
            setSearchKey(searchText);
            handleChlidSearchData(1, searchText);
          }}
          enableDefaultLabel
          multiple
          onReachEndHandler={onReachEndHandler}
        />
      </View>
      {renderSelectedChildren()}
    </View>
  );

  const renderGuardianElement = () => (
    <View style={addUserStyle.inputFieldWrapper}>
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Guardian's Name</Text>
        <AntDesign
          name="star"
          size={10}
          color="black"
          style={addUserStyle.requiredAstric}
        />
      </View>
      <View
        style={addUserStyle.inputView}
        pointerEvents={isAutoCompleteDropdownOpen ? 'none' : 'auto'}
      >
        <TextInput
          style={addUserStyle.textInput}
          onChangeText={text => {
            if (!/^[a-zA-Z\s]*$/.test(text)) {
              return;
            }
            setUserInput({ ...userInput, guardianName: text });
          }}
          placeholder="Enter Guardian's Name"
          placeholderTextColor="#595959"
          value={guardianName}
          editable={!isEditMode}
        />
      </View>
    </View>
  );

  const renderGenderElement = () => (
    <View
      style={{
        ...addUserStyle.inputFieldWrapper,
      }}
    >
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Gender</Text>
      </View>
      <SelectionModal
        title={'Select Gender'}
        items={genders}
        onCloseHook={setGenderModalOpen}
        onSelectItemHook={selectedItem => {
          selectedItem?.map(item => setSelectedGender(item?.value));
        }}
        defaultValues={null}
        isModalOpen={isGenderModalOpen}
        selectedItemLabel={selectedGender}
        enableDefaultLabel
      />
    </View>
  );

  const renderCertificatesElement = () => (
    <View
      style={{
        ...addUserStyle.inputFieldWrapper,
      }}
    >
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Certifications</Text>
      </View>
      <SelectionModal
        title={`Select Certificate`}
        items={
          customCertificateList()?.filter(
            certificate => !selectedCertificates?.includes(certificate?.value)
          ) || []
        }
        onCloseHook={setCertificateModalOpen}
        onSelectItemHook={selectedItem => {
          selectedItem?.map(item => pushCertificates(item?.value));
        }}
        isEnableAddNew
        isEnableAutoComplete
        isModalOpen={isCertificateModalOpen}
        defaultValues={null}
        isNewDataAdding={createCertificateLoading}
        addNewAction={newContent => {
          createCertifications({ title: newContent, userType: 'COACH' });
        }}
        multiple
        enableDefaultLabel
      />
      {certificateSelection()}
    </View>
  );

  const renderContryAndCities = () => (
    <View style={{ ...addUserStyle.rowView }}>
      <View style={addUserStyle.inputFieldWrapper}>
        <View style={addUserStyle.inputFieldLabelWrapper}>
          <Text style={addUserStyle.inputFieldLabel}>Country</Text>
        </View>
        <SelectionModal
          title={`Select Country`}
          items={initCountry.length ? initCountry : []}
          onCloseHook={setCountryModalOpen}
          onSelectItemHook={item => {
            if (item) {
              setSelectedCountry(item);
              setSelectedCity([]);
            }
          }}
          selectedItemLabel={selectedCountry?.[0]?.label || ''}
          isModalOpen={isCountryModalOpen}
          defaultValues={null}
          isEnableAutoComplete
          enableDefaultLabel
        />
      </View>
      <View style={addUserStyle.inputFieldWrapper}>
        <View style={addUserStyle.inputFieldLabelWrapper}>
          <Text style={addUserStyle.inputFieldLabel}>Province/City</Text>
        </View>
        <SelectionModal
          title={`Select City`}
          items={cityList?.length ? cityList : []}
          onCloseHook={setIsCityModalOpen}
          onSelectItemHook={item => {
            item && setSelectedCity(item);
          }}
          selectedItemLabel={selectedCity?.[0]?.label || ''}
          isModalOpen={isCityModalOpen}
          defaultValues={null}
          isEnableAutoComplete
          enableDefaultLabel
          onAutoCompleteChange={text => setSearchCityKey(text)}
        />
      </View>
    </View>
  );

  const renderCommentElement = () => (
    <View style={addUserStyle.commentFieldWrapper}>
      <View style={addUserStyle.inputFieldLabelWrapper}>
        <Text style={addUserStyle.inputFieldLabel}>Comment</Text>
      </View>
      <View style={addUserStyle.commentField}>
        <TextInput
          style={addUserStyle.commentInput}
          onChangeText={text => setUserInput({ ...userInput, comment: text })}
          placeholder="Add comment"
          placeholderTextColor="#595959"
          value={comment}
          multiline
        />
      </View>
    </View>
  );

  return (
    <View style={addUserStyle.container}>
      {createUserLoading ? (
        <View style={addUserStyle.loaderWrapper}>
          <ActivitySpinner />
        </View>
      ) : (
        <>
          <KeyboardAwareScrollView
            contentContainerStyle={{
              paddingBottom: isTabDevice()
                ? userCreationMode === userRoleType.COACH 
                  ? 600
                  : 350
                : userCreationMode === userRoleType.COACH
                  ? 800
                  : 150,
            }}
            // style={addUserStyle.scrollContainer}
            style={[
              userCreationMode === userRoleType.PARENT && !isTabDevice()
                ? addUserStyle.scrollContainerParent
                : addUserStyle.scrollContainer,
            ]}
          >
            <View style={addUserStyle.topRow}>
              <View style={addUserStyle.avatarContainer}>
                <View style={addUserStyle.topRowLeft}>
                  <View style={addUserStyle.avatarWrapper}>
                    {renderImgUpload()}
                  </View>
                </View>
                <View style={addUserStyle.topRowRight}>
                  {renderLabel()}
                  {!isTabDevice() && renderUserCreationType()}
                </View>
              </View>
            </View>

            <View style={{ ...addUserStyle.bottomRow }}>
              {userCreationTypeMode === userCreateTypes.KOACH &&
                !isEditMode &&
                renderSearchEmailElement()}
              <View style={addUserStyle.rowView}>
                <View style={addUserStyle.inputFieldWrapper}>
                  <View style={addUserStyle.inputFieldLabelWrapper}>
                    <Text style={addUserStyle.inputFieldLabel}>First Name</Text>
                    <AntDesign
                      name="star"
                      size={10}
                      color="black"
                      style={addUserStyle.requiredAstric}
                    />
                  </View>
                  <View
                    style={addUserStyle.inputView}
                    pointerEvents={isAutoCompleteDropdownOpen ? 'none' : 'auto'}
                  >
                    <TextInput
                      style={addUserStyle.textInput}
                      onChangeText={text =>
                        setUserInput({ ...userInput, firstName: text })
                      }
                      placeholder="Enter First Name"
                      placeholderTextColor="#595959"
                      value={firstName}
                    />
                  </View>
                </View>

                <View style={addUserStyle.inputFieldWrapper}>
                  <View style={addUserStyle.inputFieldLabelWrapper}>
                    <Text style={addUserStyle.inputFieldLabel}>Last Name</Text>
                  </View>
                  <View
                    style={addUserStyle.inputView}
                    pointerEvents={isAutoCompleteDropdownOpen ? 'none' : 'auto'}
                  >
                    <TextInput
                      style={addUserStyle.textInput}
                      onChangeText={text =>
                        setUserInput({ ...userInput, lastName: text })
                      }
                      placeholder="Enter Last Name"
                      placeholderTextColor="#595959"
                      value={lastName}
                    />
                  </View>
                </View>
              </View>

              <View style={addUserStyle.rowView}>
                {(userCreationTypeMode === userCreateTypes.NEW ||
                  isEditMode) && (
                    <View style={addUserStyle.inputFieldWrapper}>
                      <View style={addUserStyle.inputFieldLabelWrapper}>
                        <Text style={addUserStyle.inputFieldLabel}>
                          Email Address
                        </Text>
                        <AntDesign
                          name="star"
                          size={10}
                          color="black"
                          style={addUserStyle.requiredAstric}
                        />
                      </View>
                      <View style={addUserStyle.inputView}>
                        <TextInput
                          style={addUserStyle.textInput}
                          onChangeText={updateEmail}
                          placeholder="<EMAIL>"
                          placeholderTextColor="#595959"
                          value={emailId}
                          editable={!isEditMode}
                        />
                      </View>
                    </View>
                  )}
                {userCreationTypeMode === userCreateTypes.KOACH &&
                  !isEditMode &&
                  userCreationMode !== userRoleType.PARENT &&
                  renderDateOfBirth()}

                <View style={addUserStyle.inputFieldWrapper}>
                  <View style={addUserStyle.inputFieldLabelWrapper}>
                    <Text style={addUserStyle.inputFieldLabel}>
                      Contact Number
                    </Text>
                  </View>
                  <View style={addUserStyle.inputView}>
                    <TextInput
                      style={addUserStyle.textInput}
                      keyboardType={'numeric'}
                      onChangeText={text =>
                        setUserInput({ ...userInput, contact: text })
                      }
                      placeholder="+1-xxx-xxx-xxxx"
                      placeholderTextColor="#595959"
                      value={contact}
                    />
                  </View>
                </View>
                {userCreationMode === userRoleType.PARENT &&
                  userCreationTypeMode === userCreateTypes.KOACH &&
                  !isEditMode &&
                  renderSecondaryContactElement()}
              </View>
              {userCreationMode === userRoleType.COACH &&
                renderContryAndCities()}

              <View style={{ ...addUserStyle.rowView }}>
                {(userCreationTypeMode === userCreateTypes.NEW || isEditMode) &&
                  userCreationMode === userRoleType.COACH &&
                  renderDateOfBirth()}
                {userCreationMode === userRoleType.COACH &&
                  renderGenderElement()}
              </View>

              <View style={addUserStyle.rowView}>
                {userCreationMode === userRoleType.PARENT &&
                  isTabDevice() &&
                  renderChildListElement()}
                {userCreationMode === userRoleType.PARENT &&
                  userCreationTypeMode === userCreateTypes.NEW &&
                  !isEditMode &&
                  renderSecondaryContactElement()}
                {userCreationMode === userRoleType.PARENT &&
                  isEditMode &&
                  renderSecondaryContactElement()}
                {userCreationMode === userRoleType.PARENT &&
                  !isTabDevice() &&
                  renderChildListElement()}
                {userCreationMode === userRoleType.COACH &&
                  renderTeamsElement()}
                {userCreationMode === userRoleType.COACH &&
                  renderCertificatesElement()}
                {(userCreationTypeMode === userCreateTypes.NEW || isEditMode) &&
                  userCreationMode === userRoleType.PLAYER &&
                  renderDateOfBirth()}
                {userCreationMode === userRoleType.PLAYER &&
                  renderGuardianElement()}
              </View>
              <View style={{ ...addUserStyle.rowView }}>
                {(
                  userCreationMode === userRoleType.PLAYER) &&
                  renderGenderElement()}
              </View>
              {userCreationMode === userRoleType.COACH &&
                renderCommentElement()}
              {userCreationMode === userRoleType.PARENT && !isEditMode && (
                <View style={addUserStyle.checkBoxSection}>
                  <Checkbox
                    style={addUserStyle.checkBox}
                    value={isResetPlayerPassword}
                    onValueChange={setIsResetPlayerPassword}
                  />
                  <Text style={addUserStyle.checkBoxText}>
                    Reset Player Password & Nofity Parent
                  </Text>
                </View>
              )}
              {userCreationMode !== userRoleType.COACH && !isEditMode && (
                  <View style={addUserStyle.checkBoxSection}>
                    <Checkbox
                      style={addUserStyle.checkBox}
                      value={isChecked}
                      onValueChange={setChecked}
                    />
                    <TouchableOpacity
                      onPress={toggleCheckbox}
                      activeOpacity={1}
                    >
                      <Text style={addUserStyle.checkBoxText}>
                        I agree with the Term and conditions
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}

              <View style={addUserStyle.createButtonWrapper}>
                {renderCreateButton()}
              </View>
            </View>
          </KeyboardAwareScrollView>
          <SuperAdminChangePasswordModal
            modalVisible={isPasswordChangeModalOpen}
            setModalVisible={setIsPasswordChangeModalOpen}
            userDetails={userDetails}
            password={password}
            passwordResetLoading={passwordResetLoading}
            isPasswordResetError={isPasswordResetError}
            setPassword={value => {
              dispatch({ type: RESET_PASSWORD_INITIAL });
              setPassword(value);
            }}
            onPasswordSubmit={onPasswordSubmit}
          />
          <SuccessModal
            modalVisible={isSuccessPasswordOpen}
            setModalVisible={value => {
              dispatch({ type: RESET_PASSWORD_INITIAL });
              setIsSuccessPasswordOpen(value);
            }}
          />
        </>
      )}
      {showDatePicker && (
        <View style={addUserStyle.datePickerWrapper}>
          <DateTimePickerModal
            isVisible
            mode="date"
            onConfirm={date => onChange(date)}
            display="spinner"
            onCancel={() => {
              setShowDatePicker(false);
              onChange(null);
            }}
            modalStyleIOS={addUserStyle.datePickerSelector}
            pickerContainerStyleIOS={addUserStyle.datePickerWrapper}
            date={
              convertDateObjectToJSDate(dateOfBirth) ||
              generateNewDateWithTimeReset()
            }
            maximumDate={new Date()}
            isDarkModeEnabled={false}
            cancelTextIOS={'Reset'}
            minimumDate={new Date(hundredYearsAgo)}
            timeZoneOffsetInMinutes={0}
          />
        </View>
      )}
    </View>
  );
};

export default AddUser;
