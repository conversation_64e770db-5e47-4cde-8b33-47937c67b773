import { StyleSheet, Text, View, Dimensions } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';
import { colorPalette } from '../../constants/constants';

const addUserStyle = colors => ({
  scrollContainer: isTabDevice()
    ? {}
    : {
        height: wp('10%'),
      },
  scrollContainerParent: isTabDevice()
    ? {}
    : {
        height: hp('35%'),
      },
  container: isTabDevice()
    ? {
        width: wp('58%'),
        height: hp('85%'),
        flexDirection: 'column',
        // marginBottom: wp('30%'),
      }
    : {
        width: wp('96%'),
        height: hp('110%'),
        flexDirection: 'column',
      },
  loaderWrapper: {
    position: 'absolute',
    top: wp('30%'),
    left: wp('30%'),
  },
  topRow: {
    width: wp('100%'),
    paddingBottom: wp('2%'),
  },
  topRowLeft: {},
  topRowRight: {},
  bottomRow: isTabDevice()
    ? {
        width: wp('100%'),
        height: wp('30%'),
      }
    : {
        width: wp('100%'),
        height: hp('90%'),
        marginBottom: wp('20%'),
      },
  scrollWrapper: {},
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarText: isTabDevice()
    ? {
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        width: wp('16%'),
        marginLeft: wp('2%'),
      }
    : {
        fontSize: wp('4.5%'),
        fontFamily: 'Poppins-Bold',
        color: colors.white,
        width: wp('30%'),
        marginLeft: wp('2%'),
      },
  TouchablePressed: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('15%'),
        width: wp('15%'),
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        margin: hp('1%'),
      }
    : {
        backgroundColor: colors.green,
        height: wp('35%'),
        width: wp('35%'),
        borderRadius: wp('2%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        marginTop: wp('1%'),
      },
  addUserImg: isTabDevice()
    ? {
        height: wp('7%'),
        width: wp('7%'),
      }
    : {
        height: wp('15%'),
        width: wp('15%'),
      },
  userImg: isTabDevice()
    ? {
        height: wp('15%'),
        width: wp('15%'),
        borderRadius: wp('1%'),
        position: 'absolute',
      }
    : {
        height: wp('35%'),
        width: wp('35%'),
        borderRadius: wp('2%'),
        position: 'absolute',
      },
  datePicker: {},
  datePickerContainer: {},
  datePickerSelector: isTabDevice()
    ? {
        width: wp('40%'),
        position: 'absolute',
        left: wp('27%'),
        top: wp('25%'),
      }
    : {
        width: wp('95%'),
        position: 'absolute',
        left: 0,
        height: wp('110%'),
        top: wp('50%'),
      },
  datePickerWrapper: isTabDevice()
    ? {
        zIndex: 3,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      }
    : {
        position: 'absolute',
        borderRadius: wp('3%'),
        top: 0,
        left: 0,
        zIndex: 3,
        width: '100%',
        borderWidth: 10,
        borderColor: colors.semiDarkBlue,
        backgroundColor: colors.white,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 12,
        },
        shadowOpacity: 0.58,
        shadowRadius: 16.0,
        elevation: 24,
      },

  textInput: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        width: wp('24%'),
        padding: wp('1.5%'),
      }
    : {
        height: wp('8%'),
        padding: wp('1.5%'),
        color: colors.white,
        fontSize: wp('3.5%'),
        width: '100%',
      },
  placeholderStyle: {
    color: colors.lightGrey,
    // fontSize: wp('1.5%')
    fontSize: 20,
  },
  dropdownList: {
    backgroundColor: colors.borderBlue,
    borderColor: colors.borderBlue,
    marginTop: wp('-1%'),
    borderRadius: wp('100%'),
    position: 'absolute',
    zIndex: 100,
    borderBottomLeftRadius: wp('1.5%'),
    borderBottomRightRadius: wp('1.5%'),
  },
  rowView: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('56%'),
        justifyContent: 'space-between',
        marginBottom: wp('2%'),
        zIndex: 10,
      }
    : {
        flexDirection: 'column',
        width: wp('96%'),
        justifyContent: 'space-between',
        // marginBottom: hp('2%'),
        zIndex: 10,
      },
  rowSelectionView: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('20%'),
        marginTop: wp('1%'),
        // height: wp('5%'),
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        width: wp('96%'),
        marginTop: wp('1%'),
      },
  rowLastView: isTabDevice()
    ? {
        width: wp('60%'),
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginTop: wp('2%'),
      }
    : {
        width: wp('97%'),
        flexDirection: 'row',
        justifyContent: 'center',
      },
  teamList: isTabDevice()
    ? {
        maxHeight: wp('22%'),
      }
    : {
        maxHeight: wp('40%'),
      },
  inputView: {
    backgroundColor: colors.tileBackground,
    borderRadius: wp('0.7%'),
    // padding: wp('1.5%'),
    paddingRight: wp('1.5%'),
    // width: wp('25%'),
    flexDirection: 'row',
    position: 'relative',
    alignItems: 'center',
  },
  selectedLabel: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: 10,
        padding: wp('1%'),
        paddingRight: wp('3%'),
        alignItems: 'center',
        flexDirection: 'row',
        marginRight: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        padding: wp('2%'),
        // paddingRight: wp('3%'),
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        marginRight: wp('1.5%'),
        marginBottom: wp('1.5%'),
      },
  labelTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  dateTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  dateInput: isTabDevice()
    ? {
        padding: wp('1.5%'),
        width: '100%',
        height: wp('4.8%'),
      }
    : {
        paddingTop: wp('2%'),
        paddingBottom: wp('2%'),
        width: '100%',
      },
  // createView: {
  //   flexDirection: 'column',
  //   alignItems: 'flex-end',
  //   paddingRight: 135,
  // },
  buttonWrapper: isTabDevice()
    ? {
        justifyContent: 'center',
        alignItems: 'flex-end',
      }
    : {
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
      },
  createView: isTabDevice()
    ? {
        backgroundColor: colors.green,
        height: wp('4%'),
        width: wp('10%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        backgroundColor: colors.green,
        height: wp('10%'),
        width: wp('45%'),
        borderRadius: wp('1%'),
        alignItems: 'center',
        justifyContent: 'center',
      },
  disableButton: {
    backgroundColor: colors.grey,
  },
  changePasswordButton: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: colors.semiDarkBlue,
        marginLeft: wp('2%'),
        borderRadius: wp('1%'),
        padding: wp('1%'),
        width: wp('16%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: colors.semiDarkBlue,
        marginLeft: wp('2%'),
        borderRadius: wp('2%'),
        padding: wp('2%'),
        width: wp('38%'),
      },
  changePasswordText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  createTxt: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  icon: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        width: wp('3%'),
        position: 'absolute',
        right: wp('-0.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        // width: wp('6%'),
        marginLeft: wp('3%'),
      },
  dropdownView: isTabDevice()
    ? {
        width: wp('25.3%'),
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.5%'),
      }
    : {
        width: wp('96%'),
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.5%'),
      },
  dropdown: {
    justifyContent: 'flex-start',
  },
  dropdownSelected: {
    backgroundColor: colors.white,
    borderColor: colors.red,
  },
  dropdownSelectedPlaceholder: {
    color: colors.lightGrey,
    fontSize: wp('1.5%'),
  },
  dropdownSelectedContainer: isTabDevice()
    ? {
        height: wp('4.8%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        height: wp('8%'),
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      },
  dropdownList: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderColor: colors.tileBackground,
        marginTop: wp('1%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 11,
        // width: wp('80%'),
        marginLeft: wp('4%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        borderColor: colors.tileBackground,
        marginTop: wp('-3%'),
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 11,
        // width: wp('80%'),
        marginLeft: wp('4%'),
      },
  dropDownLabel: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
      },
  commentInput: isTabDevice()
    ? {
      color: colors.white,
      fontSize: hp('2%'),
      width: '100%',
      padding: hp('1.5%'),
      height: hp('13%'),
    }
    : {
      height: hp('10%'),
      paddingLeft: hp('1%'),
      color: colors.white,
      fontSize: hp('1.5%'),
      width: '100%',
    },
  placeholderStyle: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
      },
  dropdownTopArea: {
    backgroundColor: colors.aqua,
    borderColor: colors.transparent,
    position: 'absolute',
    zIndex: 5,
  },
  mandatoryField: {
    color: 'red',
    fontSize: 25,
  },
  requiredAstric: isTabDevice()
    ? {
        color: colors.red,
        fontWeight: 'bold',
        fontSize: wp('0.7%'),
      }
    : {
        color: colors.red,
        fontWeight: 'bold',
        fontSize: wp('1.5%'),
      },
  inputFieldWrapper: isTabDevice()
    ? {
        width: '45%',
      }
    : {
        width: '100%',
        marginBottom: wp('2%'),
      },
  inputFieldLabelWrapper: {
    flexDirection: 'row',
  },
  inputFieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Regular',
        marginBottom: wp('0.5%'),
      },
  inputField: {
    width: '100%',
    height: hp('5%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  commentFieldWrapper: isTabDevice()
    ? {
      width: wp('70%'),
    }
    : {
      width: '100%',
      marginBottom: wp('2%'),
    },
  commentField: isTabDevice() ? {
    width: wp('56%'),
    height: hp('12%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('1%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  } : {
    width: wp('95%'),
    height: hp('12%'),
    backgroundColor: colors.borderBlue,
    borderRadius: wp('3%'),
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  scroll: isTabDevice()
    ? {
        height: wp('40%'),
      }
    : {
        height: '100%',
        // backgroundColor: colors.red,
        marginBottom: wp('80%'),
      },
  keyboardScroll: isTabDevice()
    ? {}
    : {
        height: hp('120%'),
      },
  autoCompleteInput: isTabDevice()
    ? {
        color: colors.white,
        height: wp('5%'),
        width: wp('25.5%'),
        borderWidth: 0,
        padding: 10,
        paddingLeft: 10,
        position: 'relative',
        zIndex: 100,
        fontSize: wp('1.5%'),
        // marginTop: 5,
      }
    : {
        color: colors.white,
        height: wp('10%'),
        width: wp('100%'),
        borderWidth: 0,
        padding: 10,
        paddingLeft: 10,
        position: 'relative',
        fontSize: wp('4%'),
      },
  autoCompleteInputContainer: isTabDevice()
    ? {
        borderWidth: 0,
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.7%'),
        width: wp('25%'),
        height: wp('5%'),
      }
    : {
        zIndex: 10,
        borderWidth: 0,
        backgroundColor: colors.tileBackground,
        borderRadius: wp('0.7%'),
        width: wp('95%'),
        height: wp('10%'),
      },
  autoCompletelistContainerStyle: isTabDevice()
    ? {
        borderRadius: wp('1.5%'),
        height: wp('15%'),
        width: wp('25%'),
        top: 0,
        position: 'relative',
        left: 0,
        zIndex: 2,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 10,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }
    : {
        height: wp('10%'),
        width: '100%',
        position: 'absolute',
        left: 0,
        zIndex: 2,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        fontSize: wp('3.5%'),
      },
  autoCompleteList: isTabDevice()
    ? {
        borderWidth: 0,
        // padding: wp('0.5%'),
        backgroundColor: colors.borderBlue,
        borderBottomLeftRadius: wp('1.5%'),
        borderBottomRightRadius: wp('1.5%'),
        position: 'absolute',
        zIndex: 2,
        left: 0,
        top: 0,
        maxHeight: wp('20%'),
        minHeight: wp('5%'),
        width: '100%',
      }
    : {
        borderWidth: 0,
        padding: wp('0.5%'),
        backgroundColor: colors.borderBlue,
        borderBottomLeftRadius: wp('2.5%'),
        borderBottomRightRadius: wp('2.5%'),
        position: 'absolute',
        zIndex: 2,
        left: 0,
        top: 0,
        maxHeight: wp('40%'),
        minHeight: wp('10%'),
        width: '100%',
      },
  autoCompleteListItem: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        height: 20,
        marginBottom: 10,
        marginTop: 10,
        paddingBottom: 20,
      }
    : {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
        height: 15,
        paddingBottom: 3,
      },
  autoCompleteText: isTabDevice()
    ? {
        height: wp('4%'),
        color: colors.white,
        padding: wp('1.5%'),
        fontSize: wp('1.5%'),
      }
    : {
        height: wp('8%'),
        color: colors.white,
        padding: wp('1%'),
        fontSize: wp('3.5%'),
      },
  inputField: isTabDevice()
    ? {
        width: '50%',
        height: hp('6%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'absolute',
      }
    : {
        width: '100%',
        height: hp('5%'),
        backgroundColor: colors.borderBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
      },
  loader: isTabDevice()
    ? {
        paddingLeft: 10,
        paddingRight: 10,
      }
    : {
        paddingLeft: 5,
        paddingRight: 5,
      },
  errorText: isTabDevice()
    ? {
        color: colors.red,
        marginBottom: wp('1%'),
        // width: wp('30%'),
        fontSize: wp('1.1%'),
        textAlign: 'right',
      }
    : {
        color: colors.red,
        marginBottom: wp('1%'),
        // width: wp('100%'),
        fontSize: wp('3%'),
        textAlign: 'center',
      },
  errorTextWrapper: isTabDevice()
    ? {
        width: wp('27%'),
      }
    : {
        width: '80%',
        flexDirection: 'row',
        flexWrap: 'wrap',
      },
  errorMessage2Wrapper: isTabDevice()
    ? {
        marginLeft: wp('2%'),
      }
    : {
        marginLeft: wp('2%'),
      },
  errorText2: isTabDevice()
    ? {
        color: colors.red,
        marginTop: wp('1%'),
        width: wp('30%'),
        fontSize: wp('1.2%'),
        textAlign: 'left',
      }
    : {
        color: colors.red,
        marginTop: wp('1%'),
        width: wp('100%'),
        fontSize: wp('2%'),
      },
  createButton: {
    display: 'flex',
    alignItems: !isTabDevice() ? 'center' : 'flex-end',
  },
  createButtonWrapper: isTabDevice()
    ? {
        zIndex: 20,
        width: wp('20'),
        position: 'relative',
        left: wp('36.5'),
        marginBottom:wp('3%'),
        marginTop:wp('3%'),
      }
    : {
        width: '100%',
        height: hp('20%'),
      },
  selectedTeams: isTabDevice()
    ? {
        // height: wp('100%'),
        maxHeight: wp('16%'),
      }
    : {
        // height: wp('100%'),
        maxHeight: wp('30%'),
      },
  checkBoxSection: isTabDevice()
    ? {
        flexDirection: 'row',
        marginBottom: wp('2%'),
      }
    : {
        flexDirection: 'row',
        marginLeft: wp('2%'),
        marginBottom: wp('4%'),
        marginTop: wp('2%'),
      },
  checkBox: isTabDevice()
    ? {
        borderRadius: 5,
        width: wp('2%'),
        height: wp('2%'),
      }
    : {
        borderRadius: 3,
        width: wp('3%'),
        height: wp('3%'),
      },
  checkBoxText: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginLeft: wp('1%'),
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
        color: colors.white,
        marginLeft: wp('2%'),
        marginTop: wp('-0.5%'),
      },
});
export default addUserStyle;
