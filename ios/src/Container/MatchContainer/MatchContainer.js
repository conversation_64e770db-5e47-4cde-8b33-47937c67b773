import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View, Image } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import { userRoleType } from '../../constants/constants';
import { dateTimeConversion } from '../../helpers/index';
import useStyles from '../../hooks/useStyles';
import { SET_SELECTED_MATCH_DETAILS } from '../../store/actionTypes/MatchPlan/MatchPlanActions';
import customMatchContainerStyles from './MatchContainerStyles';
import useMatchPlanService from '../../hooks/ServiceHook/MatchPlanServicehook/useMatchPlanServicehook';

const MatchContainer = ({ item, pastEvent }) => {
  const matchContainerStyles = useStyles(customMatchContainerStyles);
  const navigator = useNavigation();
  const { fetchMatchPlanDataByMatchId } = useMatchPlanService()
  const dispatch = useDispatch();
  const [isDisableBtn, setIsDisableBtn] = useState(false)
  const {
    _id,
    tournament,
    name,
    location,
    startTime,
    isReady,
    opponentId,
    concluded,
    teamId,
  } = item;

  const { userData } = useSelector(state => state?.auth);
  const isPlayer =
    userRoleType.PLAYER === userData?.type ||
    userRoleType.PARENT === userData?.type;

  const isParentOrPlayer =
    userRoleType.PLAYER === userData?.type ||
    userRoleType.PARENT === userData?.type;

  const {
    monthString,
    dateReadable,
    dateString,
    hours12,
    minutesString,
    amPm,
  } = dateTimeConversion(startTime);

  useEffect(() => {
    const onFetchMatchPlan = async (_id) => {
      try {
        if (isParentOrPlayer) {
          const result = await fetchMatchPlanDataByMatchId(_id);
          setIsDisableBtn(!result?.visible || false);
        } else {
          setIsDisableBtn(false);
        }
      } catch (error) {
        console.error('Error fetching match plan:', error);
        setIsDisableBtn(false);
      }
    };

    if (_id) {
      onFetchMatchPlan(_id);
    }
  }, [item?._id, isParentOrPlayer])

  const EventInfo = () => (
    <View style={matchContainerStyles.wrapper}>
      <View style={matchContainerStyles.leftCol}>
        <Text style={matchContainerStyles.text}>{name}</Text>
        <Text style={matchContainerStyles.text2}>{tournament?.name}</Text>
        <Text style={matchContainerStyles.text3}>
          Date: {monthString} {dateReadable}, {dateString}
        </Text>
        <Text style={matchContainerStyles.text3}>
          Time: {hours12}:{minutesString} {amPm}
        </Text>
        <Text style={matchContainerStyles.text3}>Venue: {location?.name}</Text>
      </View>
      {!isReady ? (
        <View style={matchContainerStyles.loaderWrapper}>
          <ActivitySpinner />
        </View>
      ) : concluded ? (
        <View style={[
          matchContainerStyles.rightColBlue,
          isDisableBtn && { opacity: 0.5 }
        ]}>
          <Image
            style={matchContainerStyles.greenTick}
            source={require('../../../assets/icons/green_round_tick.png')}
          />
          <Text style={matchContainerStyles.rightColText}>Match Completed</Text>
        </View>
      ) : isReady === 'yes' ? (
        <View style={[
          matchContainerStyles.rightColGreen,
          isDisableBtn && { opacity: 0.5 }
        ]}>
          <Text style={matchContainerStyles.rightColText}>Team Ready</Text>
        </View>
      ) : (
        <View style={[
          matchContainerStyles.rightColBlue,
          isDisableBtn && { opacity: 0.5 }
        ]}>
          <Text style={matchContainerStyles.rightColTextMatchNotReady}>
            Team not ready
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={matchContainerStyles.contianerWrapper}>
      {pastEvent ? (
        <EventInfo />
      ) : (
        <TouchableOpacity
          onPress={() => {
            if (!isDisableBtn) {
              (isReady === 'yes' || !isPlayer) &&
                navigator.navigate('MatchPlan', {
                  matchId: _id,
                  opponent: opponentId,
                  teamId: teamId,
                });
              dispatch({ type: SET_SELECTED_MATCH_DETAILS, payload: item });
            }

          }}
          disabled={isDisableBtn}
        >
          <EventInfo />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default MatchContainer;
