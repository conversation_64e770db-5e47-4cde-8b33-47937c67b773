import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MessageStyles = colors => ({
  leftViewContainer: isTabDevice()
    ? {
        paddingRight: wp('2.5%'),
        height: hp('75%'),
      }
    : {
        marginTop: wp('7%'),
        paddingRight: wp('2.5%'),
        height: hp('80%'),
      },
  createSelectButton: isTabDevice()
    ? {
        backgroundColor: colors.green,
        textAlign: 'center',
        alignItems: 'center',
        padding: wp('1%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        textAlign: 'center',
        alignItems: 'center',
        padding: wp('3%'),
        borderRadius: 10,
      },
  createSelectButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
      },
  messageList: isTabDevice()
    ? {
        height: hp('64%'),
        marginBottom: wp('4%'),
      }
    : {
        height: hp('60%'),
      },
  newMessageList: {
    marginTop: wp('1%'),
    height: hp('58%'),
  },
  messageListWrapper: isTabDevice()
    ? {
        height: hp('70%'),
      }
    : {
        height: hp('67%'),
      },
});

export default MessageStyles;
