import React, { FC, useMemo } from 'react';
import { FlatList, ScrollView } from 'react-native';
import { useSelector } from 'react-redux';
import SingleUserMessageBox from '../../../components/Message/MeessageLeftComponents/SingleUserMessageBox';
import { messageTypes, singleMessageTypes } from '../../../constants/constants';
import useStyles from '../../../hooks/useStyles';
import { RootStore } from '../../../store/store';
import customMessageStyles from '../MessageLeftContainer/MessageStyles';

type MessageListProps = { messageClicked: Function; onEndReached: () => void };

const MessageList: FC<MessageListProps> = ({
  messageClicked,
  onEndReached,
}) => {
  const MessageStyles = useStyles(customMessageStyles);

  const {
    selectedUsersForMessages,
    pastMessageChatMemberInfo,
    pastMessageChatInfo,
    pastChatLatestMessage,
    selectedMessageType,
    selectedMessageChatId,
    unreadMessageCount,
  } = useSelector((state: RootStore) => state?.message);

  const pastMsgChatInfoPersonal = useMemo(() => {
    let chatInfoList: any[] = [];
  if (
      pastMessageChatInfo &&
      pastMessageChatMemberInfo &&
      pastChatLatestMessage
    ) {
      chatInfoList = pastChatLatestMessage?.map(latestMessage => {
        const chatInfo = pastMessageChatInfo?.find(
          data => data?._id === latestMessage?.chatId
        );
        let chatDetails: any = {};

        const currentUser = pastMessageChatMemberInfo?.find(
          data => data?.id === chatInfo?.opponentUserIds?.[0]
        );

        chatDetails = {
          id: currentUser?.id,
          firstName: currentUser?.firstName,
          img: currentUser?.profileImageUrl,
          type: singleMessageTypes.USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE,
          lastMessage: latestMessage?.content,
          lastName: currentUser?.lastName,
          unReadMessage: 2,
          deleted : latestMessage?.deleted
        };
        return {
          ...chatInfo,
          ...chatDetails,
          unreadMessageCount: chatInfo?._id
            ? unreadMessageCount?.[chatInfo?._id]
            : undefined,
        };
      });
    }
    return chatInfoList;
  }, [
    JSON.stringify(pastMessageChatMemberInfo),
    JSON.stringify(pastMessageChatInfo),
    JSON.stringify(pastChatLatestMessage),
  ]);
  const pastMsgChatInfoTeams = useMemo(() => {
    let chatInfoList: any[] = [];

    if (pastMessageChatInfo) {
      chatInfoList = pastMessageChatInfo?.map(chatInfoDetails => {
        const latetMessage = pastChatLatestMessage?.find(
          data => data?.chatId === chatInfoDetails?._id
        );
        let chatDetails: any = {};

        chatDetails = {
          id: chatInfoDetails?._id,
          firstName: chatInfoDetails?.name,
          type: singleMessageTypes.USER_PAST_MESSAGE_VIEW_WITH_UNREAD_MESSAGE,
          lastMessage: latetMessage?.content || '',
          lastName: '',
          unReadMessage: 2,
        };

        return { ...chatInfoDetails, ...chatDetails };
      });
    }
    return chatInfoList;
  }, [
    JSON.stringify(pastMessageChatInfo),
    JSON.stringify(pastChatLatestMessage),
    JSON.stringify(unreadMessageCount),
  ]);

  const renderItem = ({ item, index }: any) => (
    <SingleUserMessageBox
      item={item}
      messageClicked={messageClicked}
      isSelectedPersonal={selectedMessageType === messageTypes.PERSONAL}
      selectedMessage={
        selectedMessageType === messageTypes.PERSONAL
          ? selectedUsersForMessages?.[0]?.id || null
          : Object.keys(selectedMessageChatId || {})?.[0] || null
      }
    />
  );

  return (
    <ScrollView>
      <FlatList
        data={
          selectedMessageType === messageTypes.PERSONAL
            ? pastMsgChatInfoPersonal
            : pastMsgChatInfoTeams
        }
        style={MessageStyles.messageList}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        onEndReached={onEndReached}
      />
    </ScrollView>
  );
};

export default MessageList;
