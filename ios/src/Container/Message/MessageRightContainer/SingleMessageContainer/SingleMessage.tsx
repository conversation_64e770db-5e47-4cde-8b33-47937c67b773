import React, { FC, useEffect, useState } from 'react';
import { FlatList, View } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import SingleMessageComponent from '../../../../components/Message/MeessageRightComponents/SingleMessageComponent/SingleMessageComponent';
import MediaPreview from '../../../../components/modal/MessageImageVideoPreview/MessageMediaPreview';
import MessageOptionsModal from '../../../../components/modal/MessageOptions/MessageOptionsModal';
import {
  messageOptions,
  messageTypes,
  userRoleType,
} from '../../../../constants/constants';
import useStyles from '../../../../hooks/useStyles';
import {
  createNewMessageUsersType,
  singleMessageContentTypes,
} from '../../../../store/reducers/Message/MessageReducer';
import { RootStore } from '../../../../store/store';
import ForwardMsgContainer from '../../../ForwardMsgContainer/ForwardMsgContainer';
import customMessageStyles from '../MessageRightStyles';
import * as Clipboard from 'expo-clipboard';
import ConfirmModal from '../../../../components/modal/ConfirmModal/ConfirmModal';
import useApi from '../../../../hooks/useApiPromise';
import { MESSAGING_SERVICE } from '../../../../constants/services';

type SingleMessageProps = {
  setSelectedMsgType: Function;
  selectedMsgType: String;
  getUserSelectedMessage: Function;
  isReplyMode: boolean;
  setIsReplyMode: Function;
  selectedMsgContent: Object;
  setSelectedMsgContent: Function;
  onEndReached: () => void;
  isAttached: boolean;
  renderGetUserSelectedMessage: () => void;
};
const SingleMessageContainer: FC<SingleMessageProps> = ({
  setSelectedMsgType,
  selectedMsgType,
  getUserSelectedMessage,
  isReplyMode,
  setIsReplyMode,
  setSelectedMsgContent,
  selectedMsgContent,
  onEndReached,
  isAttached,
  renderGetUserSelectedMessage,
}) => {
  const {
    selectedUsersMessageContent,
    selectedUsersForMessages,
    selectedUsersMessageContentLoading,
    selectedMessageType,
    selectedMessageChatId,
    pastMessageChatMemberInfo,
    repliedContent,
    isChatValid,
  } = useSelector((state: RootStore) => state.message);
  const { userData } = useSelector((state: RootStore) => state.auth);
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const [showMsgOptionsModal, setShowMsgOptionsModal] =
    useState<boolean>(false);
  const [showForwardModal, setShowForwardModal] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [selectedMessageId, setSelectedMessageId] = useState(null);
  const [isPreviewMedia, setIsPreviewMedia] = useState(false);
  const [selectedMediaType, setSelectedMediaType] = useState<string | null>(
    null
  );
  const [isForwardMessageError, setIsForwardMessageError] =
    useState<boolean>(false);
  const [isDisableSendMessage, setIsDisableSendMessage] =
    useState<boolean>(false);
  const [messageCopied, setMessageCopied] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [selectedChatId,setSelectedChatId] = useState(null)

  const [deleteMessage] = useApi();

  const onLongPressHandler = (selectedItem: any) => {
    setIsReplyMode(false);
    setSelectedMessageId(selectedItem?._id);
    setSelectedMsgContent(selectedItem);
    setShowMsgOptionsModal(true);
  };

  const [chatMessages, setChatMessages] = useState<
    singleMessageContentTypes[] | null
  >(null);
  const [fileContentMedia, setFileContentMedia] = useState<any>(null);
  const [mediaUserData, setMediaUserData] =
    useState<createNewMessageUsersType | null>(null);
  const [msgTimeDate, setMsgTimeDate] = useState<any>(null);

  useEffect(() => {
    if (selectedUsersForMessages?.length) {
      if (
        userData?.type === userRoleType.PLAYER &&
        selectedUsersForMessages[0]?.type === userRoleType.PLAYER
      ) {
        setIsDisableSendMessage(true);
      } else {
        setIsDisableSendMessage(false);
      }
    }
  }, [JSON.stringify(userData), JSON.stringify(selectedUsersForMessages)]);

/**
 * This Effect to handle image preview modal, of message got deleted
 */
  useEffect(() => {
    const firstSelectedUser = selectedUsersForMessages?.[0];
    const userId = firstSelectedUser?.id;
    let  messageContent;

    if(selectedMessageType === messageTypes.PERSONAL && userId){
      messageContent = selectedUsersMessageContent?.[userId] 
    } 
    
    if(selectedMessageType === messageTypes.TEAMS && selectedChatId){
      messageContent = selectedUsersMessageContent?.[selectedChatId] 
    }

    if (isPreviewMedia && selectedMessageId && messageContent) {
      const targetIndex = messageContent.findIndex((singleMessage) => {
        return singleMessage._id == selectedMessageId
      })
      if(targetIndex > -1 && messageContent[targetIndex]?.deleted) {
        setIsPreviewMedia(false);
        setFileContentMedia(null);
        setMediaUserData(null);
        setMsgTimeDate(null);
        setSelectedMediaType(null);
      }
    }
  }, [isPreviewMedia, selectedMessageId, selectedUsersForMessages, selectedUsersMessageContent , selectedChatId])

  const renderItem = ({ item, index }: any) => (
    <SingleMessageComponent
      data={item}
      onLongPressHandler={(selectedItem: any) => {
        !isDisableSendMessage &&
          (selectedMessageType === messageTypes.PERSONAL
            ? isChatValid
            : true) &&
          onLongPressHandler(selectedItem);
      }}
      chatMembersInfo={pastMessageChatMemberInfo}
      repliedContent={repliedContent}
      onMediaPreview={(
        fileContent: any,
        userData: createNewMessageUsersType | null,
        timeDate: any,
        type: string
      ) => {
        setFileContentMedia(fileContent);
        setMediaUserData(userData);
        setMsgTimeDate(timeDate);
        setSelectedMediaType(type);
        setIsPreviewMedia(true);
        /**
         * Adding Message Id, cause if message got deleted by admin, then it's helps to reset the image priview modal again 
         */
        setSelectedMessageId(item._id)
        setSelectedChatId(item.chatId)
      }}
    />
  );

  const MessageRightStyles = useStyles(customMessageStyles);

  useEffect(() => {
    if (selectedMessageType === messageTypes.PERSONAL) {
      setChatMessages(
        selectedUsersMessageContent?.[selectedUsersForMessages?.[0]?.id!] || []
      );
    } else {
      setChatMessages(
        selectedUsersMessageContent?.[
          Object?.keys(selectedMessageChatId || {})?.[0]
        ] || []
      );
    }
  }, [JSON.stringify(selectedUsersMessageContent)]);

  useEffect(() => {
    if (selectedOption === messageOptions.FORWARD) {
      setShowMsgOptionsModal(false);
      setSelectedOption(null);
      setTimeout(() => {
        setShowForwardModal(true);
      }, 200);
    }
    if (selectedOption === messageOptions.REPLY) {
      setShowMsgOptionsModal(false);
      setIsReplyMode(true);
      setSelectedOption(null);
    }
    if (selectedOption === messageOptions.COPY) {
      handleCopyMessage();
    }
    if (selectedOption === messageOptions.DELETE) {
      setShowMsgOptionsModal(false);
      setSelectedOption(null);
      setTimeout(() => {
        setIsConfirmModalOpen(true);
      }, 400);
    }
  }, [selectedOption, showMsgOptionsModal]);

  const handleCopyMessage = async () => {
    // @ts-ignore
    await Clipboard.setStringAsync(selectedMsgContent?.content);
    setMessageCopied(true);
    setShowMsgOptionsModal(false);
    setSelectedOption(null);

    setTimeout(() => {
      setMessageCopied(false);
    }, 1500);
  };

  const handleDeleteMessage = async () => {
    setIsDeleting(true);
    deleteMessage(
      // @ts-ignore
      `/api/v1/messages/${selectedMsgContent?._id}`,
      '-',
      '-',
      '-',
      null,
      null,
      'DELETE',
      false,
      MESSAGING_SERVICE,
      null,
      true
    )
      .then(() => {
        renderGetUserSelectedMessage();
        setIsConfirmModalOpen(false);
        setIsDeleting(false);
      })
      .catch(err => {
        setIsConfirmModalOpen(false);
        setIsDeleting(false);
        console.log(err);
      });
  };

  return (
    // <KeyboardAwareScrollView>
    <View
      style={
        isReplyMode || isAttached
          ? MessageRightStyles.chatBoxShort
          : MessageRightStyles.chatBox
      }
    >
      {isPreviewMedia && (
        <MediaPreview
          closeModal={() => {
            setIsPreviewMedia(false);
            setFileContentMedia(null);
            setMediaUserData(null);
            setMsgTimeDate(null);
            setSelectedMediaType(null);
          }}
          type={selectedMediaType}
          fileContent={fileContentMedia}
          userData={mediaUserData}
          msgTimeDate={msgTimeDate}
        />
      )}
      {showForwardModal && (
        <ForwardMsgContainer
          setShowForwardModal={setShowForwardModal}
          selectedMsgType={selectedMsgType}
          setSelectedMsgType={setSelectedMsgType}
          selectedMessageId={selectedMessageId}
          selectedMsgContent={selectedMsgContent}
          setSelectedMsgContent={setSelectedMsgContent}
          setIsForwardMessageError={setIsForwardMessageError}
          getUserSelectedMessage={getUserSelectedMessage}
        />
      )}
      {showMsgOptionsModal && (
        <MessageOptionsModal
          setShowMsgOptionsModal={setShowMsgOptionsModal}
          setSelectedOption={setSelectedOption}
          selectedMsgContent={selectedMsgContent}
          hasDeleteOption={
            userData?.type === userRoleType.HEAD_COACH ||
            userData?.type === userRoleType.COACH ||
            userData?.type === userRoleType.SUPERADMIN
          }
        />
      )}

      {isConfirmModalOpen && (
        <ConfirmModal
          isConfirmModalOpen={isConfirmModalOpen}
          setIsConfirmModalOpen={setIsConfirmModalOpen}
          isDeleting={isDeleting}
          handleDeleteMessage={handleDeleteMessage}
        />
      )}

      {!selectedUsersMessageContent && selectedUsersMessageContentLoading ? (
        <ActivityIndicator style={{ margin: 150 }} color="#36d982" />
      ) : (
        <>
          <FlatList
            data={chatMessages}
            renderItem={renderItem}
            keyExtractor={(item, index) => item?._id?.toString()}
            onEndReached={onEndReached}
            contentContainerStyle={MessageRightStyles.chatList}
            inverted
          />
          {messageCopied && (
            <View style={MessageRightStyles.notificationPopUpWrapper}>
              <View style={MessageRightStyles.notificationPopUp}>
                <Text style={MessageRightStyles.notificationPopUpText}>
                  Message copied!
                </Text>
              </View>
            </View>
          )}
        </>
      )}
    </View>
    // </KeyboardAwareScrollView>
  );
};

export default SingleMessageContainer;
