import { StyleSheet } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const MessageRightStyles = colors => ({
  rightMainContainer: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: hp('85%'),
        marginRight: wp('3%'),
        borderRadius: 40,
        overflow: 'hidden',
      }
    : {
        backgroundColor: colors.tileBackground,
        height: hp('72%'),
        marginTop: wp('3%'),
        marginRight: wp('3%'),
        borderRadius: 20,
        overflow: 'hidden',
      },
  chatBox: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('65%'),
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('53%'),
      },
  chatBoxShort: isTabDevice()
    ? {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('58%'),
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        justifyContent: 'space-between',
        height: hp('55%'),
      },
  chatList: {
    width: '100%',
  },
  typeArea: {
    margin: wp('1%'),
  },
  notificationPopUp: isTabDevice()
    ? {
        marginTop: 10,
        backgroundColor: colors.green,
        padding: 5,
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('10%'),
        borderRadius: wp('50%'),
        marginBottom: 10,
      }
    : {
        marginTop: 10,
        backgroundColor: colors.green,
        padding: 5,
        alignItems: 'center',
        justifyContent: 'center',
        width: wp('33%'),
        borderRadius: wp('50%'),
      },
  notificationPopUpText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('0.9%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
      },
  notificationPopUpWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MessageRightStyles;
