import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  FlatList,
  Image,
  // ActivityIndicator,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Switch
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Field from '../../components/MatchPlan/Field';
import SelectCandidatePlayerModal from '../../components/modal/SelectCandidatePlayerModal/SelectCandidatePlayerModal';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import {
  MATCH_CANDIDATE_PLAYERS_FAILED,
  MATCH_CANDIDATE_PLAYERS_REQUEST,
  MATCH_CANDIDATE_PLAYERS_SUCCESS,
  MATCH_CANDIDATE_PLAYERS_SEARCH_REQUEST,
  MATCH_CANDIDATE_PLAYERS_SEARCH_SUCCESS,
  MATCH_CANDIDATE_PLAYERS_SEARCH_FAILED,
  MATCH_PLAN_CLEAR_SAVE_STATUS,
  MATCH_PLAN_FAILED,
  MATCH_PLAN_REQUEST,
  MATCH_PLAN_RESET,
  MATCH_PLAN_SAVE_FAILED,
  MATCH_PLAN_SAVE_REQUEST,
  MATCH_PLAN_SAVE_SUCCESS,
  MATCH_PLAN_SUCCESS,
  MATCH_TYPES_FAILED,
  MATCH_TYPES_REQUEST,
  MATCH_TYPES_SUCCESS,
  MATCH_TYPE_FORMATIONS_FAILED,
  MATCH_TYPE_FORMATIONS_REQUEST,
  MATCH_TYPE_FORMATIONS_SUCCESS,
  SET_SELECTED_MATCH_DETAILS,
} from '../../store/actionTypes/MatchPlan/MatchPlanActions';
import customMatchPlanContainerStyles from './MatchPlanContainerStyles';

import deleteIcon from '../../../assets/buttons/deleteIcon.png';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import Substitute from '../../components/MatchPlan/Substitute';
import UnsavedChangesModal from '../../components/modal/UnsavedChangesModal/UnsavedChangesModal';
import { ROUTE_PATH, userRoleType } from '../../constants/constants';
import {
  EVENT_OPPONENT_DETAILS_FAILED,
  EVENT_OPPONENT_DETAILS_REQUEST,
  EVENT_OPPONENT_DETAILS_SUCCESS,
} from '../../store/actionTypes/Planner/PlannerAction';

import { TextInput } from 'react-native-gesture-handler';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { ActivityIndicator } from 'react-native-paper';
import ExclamationIcon from '../../../assets/buttons/exclamation.png';
import DeleteModal from '../../components/modal/DeleteModal/DeleteModal';
import SelectionModal from '../../components/modal/SelectionModal/SelectionModal';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import { isTabDevice } from '../../config/appConfig';
import useApiPromise from '../../hooks/useApiPromise';
import useColors from '../../hooks/useColors';
import useInputSelectModal from '../../hooks/useInputSelectModal';
import useStyles from '../../hooks/useStyles';
import { useJerseyNumberHook } from '../../hooks/PlannerAPIHook/useJerseyNumberHook';

const MatchPlanContainer = () => {
  const MatchPlanContainerStyles = useStyles(customMatchPlanContainerStyles);
  const colors = useColors();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const dropdowns = {
    matchType: 'matchType',
    matchFormations: 'matchFormations',
  };

  const [fetchData] = useApi();
  const [fetchPlanData] = useApi();
  const [fetchCandidatePlayers] = useApi();
  const [fetchFilterCandidatePlayers] = useApi();
  const [fetchOpponentData] = useApi();
  const [fetchFormationData] = useApi();
  const [fetchOngoingMatchData] = useApiPromise();

  const [setIsMatchTypeModalOpen, isMatchTypeModalOpen] = useInputSelectModal();

  const [setIsFormationTypeModalOpen, isFormationTypeModalOpen] =
    useInputSelectModal();

  const { userData } = useSelector(state => state?.auth);

  const [disabledOrIsPlayer, setDisabledOrIsPlayer] = useState(
    userRoleType.PLAYER === userData?.type ||
    userRoleType.PARENT === userData?.type
  );

  const isCoach =
    userRoleType.COACH === userData?.type ||
    userRoleType.HEAD_COACH === userData?.type;

  const {
    matchTypes,
    matchFormations,
    matchPlan,
    matchCandidatePlayers,
    matchCandidatePlayersTotalRecord,
    matchCandidatePlayersPage,
    matchCandidatePlayersLoading,
    matchPlanSaveSuccess,
    matchPlanSaveLoading,
    matchPlanLoading,
    selectedMatchDetails,
    duplicateSelectedMatchDetails,
  } = useSelector(state => state.matchPlan);
  const { opponentDetails } = useSelector(state => state.planner);

  const { currentRoute } = useSelector(state => state?.common);

  const [formattedMatchTypes, setFormattedMatchTypes] = useState([]);
  const [formattedMatchFormations, setFormattedMatchFormations] = useState([]);
  const [selectedMatchType, setSelectedMatchType] = useState(null);
  const [selectedPlayerViewMatchType, setSelectedPlayerViewMatchType] =
    useState('');
  const [selectedMatchFormation, setSelectedMatchFormation] = useState(null);
  const [currentCoordinates, setCurrentCoordinates] = useState([]);
  const [dropdownVisibility, setDropdownVisibility] = useState({
    [dropdowns.matchType]: false,
    [dropdowns.matchFormations]: false,
  });
  const [selectedFieldPlayers, setSelectedFieldPlayers] = useState([]);
  const [selectedCoordinate, setSelectedCoordinate] = useState(null);
  const [formattedMatchPlan, setFormattedMatchPlan] = useState({});

  const [substitutes, setSubstitutes] = useState([]);
  const [substitutesPlayers, setSubstitutesPlayers] = useState([]);
  const [allPlayers, setAllPlayers] = useState([]);
  const [opponentName, setOpponentName] = useState('');
  const [selectedDeletePlayerID, setSelectedDeletePlayerID] = useState(null);
  const [showPlayerDeleteModal, setShowPlayerDeleteModal] = useState(false);

  const [playerSearchKey, setPlayerSearchKey] = useState('');

  const [showPlayerSelectModal, setShowPlayerSelectModal] = useState(false);
  let matchTypeController;
  let matchFormationController;

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [unsavedPopup, setUnsavedPopup] = useState(false);
  const [navigationEvent, setNavigationEvent] = useState(null);
  const [matchTypesChanged, setMatchTypesChanged] = useState(false);
  const [matchDuration, setMatchDuration] = useState(90);
  const [validationError, setValidationError] = useState('');
  const substituteListRef = useRef(null);
  const [isComponentInit, setIsComponentInit] = useState(true);
  const [fetchJerseyNumbers] = useJerseyNumberHook();
  const [jerseyNumbers, setJerseyNumbers] = useState(null);
  const [visible, setVisible] = useState(true); // Add this near other state declarations

  const navigateToClickedRoute = () => {
    navigationEvent && navigation.dispatch(navigationEvent.data.action);
  };

  useEffect(() => {
    if (!showPlayerSelectModal) {
      handleSearch();
    }
  }, [showPlayerSelectModal]);

  useEffect(() => {
    if (currentRoute == ROUTE_PATH.MATCH_PLAN) {
      duplicateSelectedMatchDetails &&
        dispatch({
          type: SET_SELECTED_MATCH_DETAILS,
          payload: duplicateSelectedMatchDetails,
        });
    }
  }, [currentRoute]);

  useEffect(() => {
    userData?.id &&
      matchPlan?.eventId &&
      fetchOngoingMatchData(
        `/api/v1/matches/${matchPlan?.eventId}/match-activities?page=1&size=1`,
        '-',
        '-',
        '-',
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      ).then(res => {
        res?.data?.data?.[0]?._id && setDisabledOrIsPlayer(true);
      });
  }, [userData, matchPlan]);

  useEffect(() => {
    if (matchPlan?.formationId && disabledOrIsPlayer) {
      fetchFormationData(
        `/api/v1/formations/${matchPlan?.formationId}`,
        MATCH_TYPE_FORMATIONS_REQUEST,
        MATCH_TYPE_FORMATIONS_SUCCESS,
        MATCH_TYPE_FORMATIONS_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
    }
  }, [matchPlan, disabledOrIsPlayer]);

  useEffect(() => {
    if (selectedFieldPlayers && substitutes && matchPlan?.substitutePlayers) {
      const substitutePlayers = substitutes.map(item => item.playerId);
      const currentSubstitutes = matchPlan?.substitutePlayers.map(
        player => player.sportsProfileId
      );

      if (substitutePlayers && currentSubstitutes) {
        if (substitutePlayers.length != currentSubstitutes.length) {
          setHasUnsavedChanges(true);
          return;
        }
        const extraSubs = currentSubstitutes.filter(
          sub => !substitutePlayers.includes(sub)
        );
        if (extraSubs?.length) {
          setHasUnsavedChanges(true);
          return;
        }
        const reducedSubs = substitutePlayers.filter(
          sub => !currentSubstitutes.includes(sub)
        );
        if (reducedSubs?.length) {
          setHasUnsavedChanges(true);
          return;
        }
      }

      const coords = matchPlan.playerCoordinates;
      for (let i = 0; i < coords.length; i++) {
        const coordinate = coords[i];
        const id = coordinate.coordinateId;

        const newCoordinate = selectedFieldPlayers.find(
          item => item.coordinateId === id
        );

        if (coordinate.playerData && !newCoordinate) {
          setHasUnsavedChanges(true);
          return;
        }

        if (!coordinate.playerData && newCoordinate?.sportsProfileId) {
          setHasUnsavedChanges(true);
          return;
        }

        if (
          newCoordinate?.sportsProfileId !=
          coordinate?.playerData?.sportsProfileId
        ) {
          setHasUnsavedChanges(true);
          return;
        }
      }
    }

    setHasUnsavedChanges(false);
  }, [selectedFieldPlayers, substitutes]);

  useEffect(
    () =>
      navigation.addListener('beforeRemove', e => {
        if (!hasUnsavedChanges) {
          // If we don't have unsaved changes, then we don't need to do anything
          return;
        }

        setNavigationEvent(e);
        // Prevent default behavior of leaving the screen
        e.preventDefault();

        setUnsavedPopup(true);
      }),
    [navigation, hasUnsavedChanges]
  );

  useEffect(() => {
    if (matchCandidatePlayers) {
      const assignedFieldPlayers = matchPlan?.playerCoordinates
        ? matchPlan.playerCoordinates
            .filter(coordinateData => coordinateData.playerData)
            .map(coordinateData => coordinateData.playerData)
        : [];
      const assignedSubstitutePlayers = matchPlan?.substitutePlayers || [];

      const allPlayersData = [
        ...matchCandidatePlayers,
        ...assignedFieldPlayers,
        ...assignedSubstitutePlayers,
      ];
      setAllPlayers(allPlayersData);
    }
    const matchCandidatePlayersSportsProfileId =
      matchCandidatePlayers?.map(data => data?.sportsProfileId) || [];
    const matchPlanPlayersSportsProfileId =
      matchPlan?.playerCoordinates
        ?.map(data => data?.playerData?.sportsProfileId)
        .filter(Boolean) || [];
    const matchPlanSubstitutesSportsProfileId =
      matchPlan?.substitutePlayers?.map(data => data?.sportsProfileId) || [];
    const allSportsProfileIds = [
      ...matchCandidatePlayersSportsProfileId,
      ...matchPlanPlayersSportsProfileId,
      ...matchPlanSubstitutesSportsProfileId,
    ];

    if (
      !matchPlanLoading &&
      !matchCandidatePlayersLoading &&
      allSportsProfileIds?.length
    ) {
      setAllJerseyNumbers(allSportsProfileIds);
    }
  }, [matchCandidatePlayers, matchPlan]);

  const setAllJerseyNumbers = async allSportsProfileIds => {
    const allJerseyNumbers = await fetchJerseyNumbers(
      allSportsProfileIds,
      route?.params?.teamId
    );
    setJerseyNumbers(allJerseyNumbers);
  };

  useEffect(() => {
    fetchData(
      `/api/v1/match-types`,
      MATCH_TYPES_REQUEST,
      MATCH_TYPES_SUCCESS,
      MATCH_TYPES_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );

    return () => {
      dispatch({ type: MATCH_PLAN_RESET });
    };
  }, []);

  useEffect(() => {
    if (matchTypes && !disabledOrIsPlayer) {
      const formattedData = matchTypes.map(item => {
        return {
          label: item.name,
          value: item._id,
        };
      });
      setFormattedMatchTypes(formattedData);
    }
  }, [matchTypes, disabledOrIsPlayer]);

  useEffect(() => {
    if (matchFormations && matchTypes && disabledOrIsPlayer) {
      const matchType = matchTypes.find(
        ({ _id }) => _id === matchFormations.matchTypeId
      );

      setSelectedPlayerViewMatchType(matchType?.name);
    }
  }, [matchTypes, matchFormations, disabledOrIsPlayer]);

  useEffect(() => {
    if (matchTypeController && formattedMatchTypes?.length) {
      matchPlan?.matchTypeId &&
        formattedMatchTypes.some(mType => mType.value === matchPlan.matchTypeId)
        ? matchTypeController.selectItem(matchPlan.matchTypeId)
        : matchTypeController.selectItem(formattedMatchTypes[0].value);
    }
  }, [matchTypeController, formattedMatchTypes, matchPlan]);

  useEffect(() => {
    if (matchFormationController && formattedMatchFormations?.length) {
      matchPlan?.formationId &&
      formattedMatchFormations.some(
        mFormation => mFormation.value === matchPlan.formationId
      )
        ? matchFormationController.selectItem(matchPlan.formationId)
        : matchFormationController.selectItem(
            formattedMatchFormations[0].value
          );
    }
  }, [matchFormationController, formattedMatchFormations, matchPlan]);

  useEffect(() => {
    if (selectedMatchFormation && matchFormations) {
      const formation = matchFormations.find(
        item => item._id === selectedMatchFormation.value
      );
      if (formation) {
        setCurrentCoordinates(formation.formationCoordinates);
      } else {
        setCurrentCoordinates(null);
      }
      resetPlayerPositions(selectedMatchFormation);
    }
  }, [selectedMatchFormation]);

  useEffect(() => {
    if (selectedMatchType?.length && !disabledOrIsPlayer) {
      matchFormationController &&
        matchFormationController.select({ label: null, value: null });
      fetchData(
        `/api/v1/match-types/${selectedMatchType?.[0]?.value}/formations`,
        MATCH_TYPE_FORMATIONS_REQUEST,
        MATCH_TYPE_FORMATIONS_SUCCESS,
        MATCH_TYPE_FORMATIONS_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
    }
  }, [selectedMatchType, disabledOrIsPlayer]);

  useEffect(() => {
    if (matchFormations?.length && !disabledOrIsPlayer) {
      const formattedData = matchFormations?.map(item => {
        return {
          label: item.name,
          value: item._id,
        };
      });

      formattedData && setFormattedMatchFormations(formattedData);
      formattedData && !matchTypesChanged && setMatchTypesChanged(true);
    }
  }, [matchFormations, disabledOrIsPlayer]);

  const getCandidatePlayers = (matchId, page = 1, searchKey) => {
    fetchCandidatePlayers(
      `/api/v1/matches/${matchId}/candidate-players?page=${page}&size=20&search=${searchKey}`,
      MATCH_CANDIDATE_PLAYERS_REQUEST,
      MATCH_CANDIDATE_PLAYERS_SUCCESS,
      MATCH_CANDIDATE_PLAYERS_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  const filterCandidatePlayers = (matchId, page = 1, searchKey) => {
    fetchFilterCandidatePlayers(
      `/api/v1/matches/${matchId}/candidate-players?page=${page}&size=20&search=${searchKey}`,
      MATCH_CANDIDATE_PLAYERS_SEARCH_REQUEST,
      MATCH_CANDIDATE_PLAYERS_SEARCH_SUCCESS,
      MATCH_CANDIDATE_PLAYERS_SEARCH_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  useEffect(() => {
    if (route?.params?.matchId) {
      const { matchId, opponent } = route.params;

      fetchPlanData(
        `/api/v1/matches/${matchId}/match-plan`,
        MATCH_PLAN_REQUEST,
        MATCH_PLAN_SUCCESS,
        MATCH_PLAN_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );

      getCandidatePlayers(matchId, 1, '');

      fetchOpponentData(
        `/api/v1/opponents?opponentIds=${opponent}&page=1&size=1`,
        EVENT_OPPONENT_DETAILS_REQUEST,
        EVENT_OPPONENT_DETAILS_SUCCESS,
        EVENT_OPPONENT_DETAILS_FAILED,
        null,
        null,
        'GET',
        false,
        EVENT_SERVICE
      );
    }
  }, [route]);

  useEffect(() => {
    opponentDetails?.name && setOpponentName(opponentDetails?.name);
  }, [opponentDetails]);

  const getSelectedFieldPlayers = () => {
    const { playerCoordinates } = matchPlan;

    let playerGeneratedCoordination = [];

    playerCoordinates.forEach(coord => {
      if (coord?.playerData?.sportsProfileId) {
        playerGeneratedCoordination.push({
          coordinateId: coord?.coordinateId,
          sportsProfileId: coord?.playerData?.sportsProfileId,
        });
      }
    });

    return playerGeneratedCoordination;
  };

  useEffect(() => {
    if (matchPlan?.playerCoordinates) {
      setSelectedFieldPlayers(getSelectedFieldPlayers());
      setFormattedMatchPlan(JSON.parse(JSON.stringify(matchPlan)));

      selectedMatchFormation && resetPlayerPositions(selectedMatchFormation);
    }
    if (matchPlan?.duration && matchDuration !== matchPlan.duration) {
      setMatchDuration(matchPlan.duration);
    }
    if (matchPlan) {
      setVisible(matchPlan.visible ?? true); // Default to true if not defined
    }
  }, [matchPlan]);

  const showDropdown = dropdown => {
    let visibility = {};
    Object.values(dropdowns).forEach(item => {
      visibility[item] = false;
    });
    visibility[dropdown] = true;
    setDropdownVisibility(visibility);
  };
  const hideDropdown = dropdown => {
    let visibility = { ...dropdownVisibility };
    visibility[dropdown] = false;
    setDropdownVisibility(visibility);
  };

  const getSubstitutePlayers = () => {
    const { substitutePlayers } = matchPlan;

    const formattedData = substitutePlayers.map(item => {
      const key = `sub_${substitutes.length + 1}${Math.random()}`;
      return {
        _id: key,
        profileImageUrl: item.profileImageUrl,
        playerId: item.sportsProfileId,
        dropdown: { name: key, [key]: false },
        isAvailable: item?.isAvailable,
      };
    });

    return formattedData;
  };

  useEffect(() => {
    if (matchPlan && matchPlan.substitutePlayers) {
      setSubstitutes(getSubstitutePlayers());
      selectedMatchFormation && resetPlayerPositions(selectedMatchFormation);
    }
  }, [matchPlan]);

  const addSubstitute = () => {
    const key = `sub_${substitutes.length + 1}${Math.random()}`;

    setSubstitutes([
      ...substitutes,
      { _id: key, playerId: null, dropdown: { name: key, [key]: false } },
    ]);
  };

  const hideDropdownOnclick = data => {
    handleSearch();
    setSubstitutes(substitutes =>
      substitutes.map(item => {
        if (item._id !== data) {
          return { ...item, dropdown: { name: data, [data]: false } };
        } else {
          return { ...item, dropdown: { name: data, [data]: true } };
        }
      })
    );
  };

  const removeSubstitute = item => {
    const { _id } = item;

    setSubstitutes(substitutes =>
      substitutes.filter(substitute => substitute._id !== _id)
    );
  };

  const setSelectedSubstitute = (item, index, arrayId) => {
    substitutes[index] = {
      ...substitutes[index],
      playerId: item?.value,
      dropdown: { name: arrayId, [arrayId]: false },
    };

    setSubstitutes([...substitutes]);
  };

  const openCandidatePlayerModal = position => {
    if (!disabledOrIsPlayer) {
      setSelectedCoordinate(position);
      setShowPlayerSelectModal(true);
    }
  };

  const closeDeleteModal = () => {
    setShowPlayerDeleteModal(false);
  };

  const deletePlayer = () => {
    onCandidatePlayerDelete(selectedDeletePlayerID);
    setShowPlayerDeleteModal(false);
  };

  const openCandidatePlayerDeleteModal = sportsProfileId => {
    if (!disabledOrIsPlayer) {
      setSelectedDeletePlayerID(sportsProfileId);
      setShowPlayerDeleteModal(true);
    }
  };

  const resetPlayerPositions = selectedMatchFormation => {
    const { value } = selectedMatchFormation;

    const { playerCoordinates: playerCoordinatesInitial, formationId } =
      matchPlan || {};

    const { playerCoordinates } = formattedMatchPlan;

    let matchPlayerCoordination;

    if (formationId === value) {
      matchPlayerCoordination = JSON.parse(
        JSON.stringify(playerCoordinatesInitial)
      );
      setSelectedFieldPlayers(getSelectedFieldPlayers());
      setSubstitutes(getSubstitutePlayers());
    } else if (matchTypesChanged) {
      matchPlayerCoordination = playerCoordinates?.map(item => {
        item.playerData = {};

        return item;
      });

      setSelectedFieldPlayers([]);
      setSubstitutes([]);
    } else {
      const { codes, formationObjects, selectedFormation } =
        getFormationDataFromId(selectedMatchFormation?.value);
      let players = {};
      let fieldPlayers = [];
      playerCoordinates?.forEach(item => {
        if (item?.playerData) {
          players[item?.code] = item?.playerData;
        }
      });

      matchPlayerCoordination = selectedFormation?.formationCoordinates?.map(
        item => {
          if (item._id) {
            item.coordinateId = item._id;
          }
          if (players[item?.code]) {
            item.playerData = players[item.code];
            fieldPlayers.push({
              coordinateId: item?._id,
              sportsProfileId: players[item?.code]?.sportsProfileId,
            });
          } else {
            item.playerData = {};
          }

          return item;
        }
      );
      setSelectedFieldPlayers(fieldPlayers);
    }

    setFormattedMatchPlan({
      ...formattedMatchPlan,
      playerCoordinates: matchPlayerCoordination,
    });
    matchTypesChanged && setMatchTypesChanged(false);
  };

  const getFormationDataFromId = formationId => {
    const selectedFormation = matchFormations.find(
      ({ _id }) => _id === formationId
    );
    let codes = {};
    let formationObjects = {};
    if (selectedFormation) {
      selectedFormation?.formationCoordinates.forEach(coord => {
        codes[coord._id] = coord.code;
        formationObjects[coord.code] = coord;
      });
    }
    return { codes, formationObjects, selectedFormation };
  };

  const onCandidatePlayerDelete = sportsProfileId => {
    if (sportsProfileId) {
      const [position] = selectedFieldPlayers.filter(
        item => item.sportsProfileId === sportsProfileId
      );

      // delete
      const playerList = selectedFieldPlayers.filter(
        item => item.sportsProfileId !== sportsProfileId
      );

      const { playerCoordinates } = formattedMatchPlan;
      //delete
      const matchPlayerCoordination = playerCoordinates.map(item => {
        if (item.coordinateId === position.coordinateId) {
          delete item.playerData;
          return item;
        }

        return item;
      });

      setSelectedFieldPlayers([...playerList]);
      setSelectedDeletePlayerID(null);
      setFormattedMatchPlan({
        ...formattedMatchPlan,
        playerCoordinates: matchPlayerCoordination,
      });
    }
  };

  const onCandidatePlayerSelect = profileId => {
    if (selectedCoordinate && profileId) {
      const positionIndex = selectedFieldPlayers.findIndex(
        item => item.coordinateId === selectedCoordinate
      );
      const newPositionObject = {
        coordinateId: selectedCoordinate,
        sportsProfileId: profileId,
      };
      let newPlayerPositions = [...selectedFieldPlayers];
      if (positionIndex > -1) {
        newPlayerPositions[positionIndex] = newPositionObject;
      } else {
        newPlayerPositions.push(newPositionObject);
      }
      setSelectedFieldPlayers(newPlayerPositions);
      setSelectedCoordinate(null);
      mergeNewlySelectedPlayerWithExistingData(newPositionObject);
    }
    setShowPlayerSelectModal(false);
  };

  const mergeNewlySelectedPlayerWithExistingData = ({
    coordinateId,
    sportsProfileId,
  }) => {
    let { playerCoordinates, formationId } = formattedMatchPlan;
    if (!playerCoordinates) playerCoordinates = [];
    const playerData = allPlayers.find(
      player => player.sportsProfileId === sportsProfileId
    );

    const coordinateIndex = currentCoordinates.findIndex(
      coord => coord._id === coordinateId
    );

    let newPlayerCoordinate;

    let newCoordinates =
      formationId === selectedMatchFormation?.value
        ? [...playerCoordinates]
        : [
            ...currentCoordinates.map(({ _id, ...c }) => {
              return { ...c, coordinateId: _id };
            }),
          ];

    if (coordinateIndex < 0) {
      const coordinate = currentCoordinates.find(
        coordinate => coordinate._id === coordinateId
      );

      const { _id, x, y, code, name } = coordinate || {};

      newPlayerCoordinate = {
        coordinateId: _id,
        x,
        y,
        code,
        name,
        playerData: {
          sportsProfileId: playerData?.sportsProfileId,
          profileImageUrl: playerData?.profileImageUrl,
          firstName: playerData?.firstName,
          lastName: playerData?.lastName,
          isAvailable: playerData?.isAvailable,
        },
      };
      newCoordinates.push(newPlayerCoordinate);
    } else {
      newPlayerCoordinate = {
        ...newCoordinates[coordinateIndex],
        playerData: {
          sportsProfileId: playerData?.sportsProfileId,
          profileImageUrl: playerData?.profileImageUrl,
          firstName: playerData?.firstName,
          lastName: playerData?.lastName,
          isAvailable: playerData?.isAvailable,
        },
      };
      newCoordinates[coordinateIndex] = newPlayerCoordinate;
    }

    setFormattedMatchPlan({
      ...formattedMatchPlan,
      formationId: selectedMatchFormation?.value,
      playerCoordinates: newCoordinates,
    });
  };

  const filteredCandidatePlayers = useMemo(() => {
    const selectedFieldPlayerIds = selectedFieldPlayers.map(
      player => player.sportsProfileId
    );
    const filteredList = allPlayers.filter(
      player => !selectedFieldPlayerIds.includes(player.sportsProfileId)
    );
    return filteredList;
  }, [selectedFieldPlayers, allPlayers]);

  const filteredPlayers = useMemo(() => {
    const subs = substitutes.map(sub => sub.playerId);
    return filteredCandidatePlayers.filter(
      player => !subs.includes(player.sportsProfileId)
    );
  }, [substitutes, filteredCandidatePlayers]);

  useEffect(() => {
    if (filteredCandidatePlayers.length) {
      const formattedData = filteredCandidatePlayers.map(item => {
        return {
          profileImageUrl: item?.profileImageUrl,
          value: item.sportsProfileId,
          label: `${item.firstName}  ${item.lastName ?? ''}`,
          isAvailable: item?.isAvailable,
        };
      });

      setSubstitutesPlayers(formattedData);
    } else {
      setSubstitutesPlayers([]);
    }
  }, [filteredCandidatePlayers]);

  useEffect(() => {
    if (matchPlanSaveSuccess) {
      navigation.goBack();
    }
    return () => {
      dispatch({
        type: MATCH_PLAN_CLEAR_SAVE_STATUS,
      });
    };
  }, [matchPlanSaveSuccess]);

  const validateDuration = () => {
    if (
      !matchDuration ||
      isNaN(matchDuration) ||
      matchDuration <= 0 ||
      matchDuration > 90
    )
      return false;

    return true;
  };

  const saveMatchPlan = () => {
    if (!validateDuration()) {
      setValidationError('Enter a valid match duration');
      return;
    }

    const formationId =
      selectedMatchFormation?.value || matchPlan?.formationId || null;
    const { matchId } = route?.params;
    const substitutePlayers = substitutes
      .filter(item => item.playerId)
      .map(item => item.playerId);

    const requestBody = {
      eventId: matchId,
      formationId,
      playerCoordinates: selectedFieldPlayers,
      substitutePlayers,
      duration: matchDuration,
      visible: visible  // Use the state variable here
    };

    fetchData(
      `/api/v1/matches/${matchId}/match-plan`,
      MATCH_PLAN_SAVE_REQUEST,
      MATCH_PLAN_SAVE_SUCCESS,
      MATCH_PLAN_SAVE_FAILED,
      requestBody,
      null,
      'PUT',
      false,
      EVENT_SERVICE
    );

    setHasUnsavedChanges(false);
  };

  const handleUnsavedChangesPopupResponse = response => {
    if (response) {
      saveMatchPlan();
    } else {
      navigateToClickedRoute();
    }
  };

  const renderSubs = ({ item }) => (
    <View style={MatchPlanContainerStyles.selectedSubs}>
      <View>
        <ProfileImage
          imageStyles={MatchPlanContainerStyles.subsProfileImage}
          profileImageUrl={item?.profileImageUrl}
        />
        {!item?.isAvailable && (
          <Image
            style={MatchPlanContainerStyles.playerExclamationIcon}
            source={ExclamationIcon}
          />
        )}
      </View>
      <View style={MatchPlanContainerStyles.subsNameWrapper}>
        <Text
          style={MatchPlanContainerStyles.subsName}
          key={item?.sportsProfileId}
        >
          {item?.firstName}
        </Text>
      </View>
    </View>
  );

  const onReachEndHandler = searchKey => {
    const { matchId } = route.params;

    if (matchCandidatePlayersTotalRecord > 4) {
      if (searchKey) {
        getCandidatePlayers(matchId, matchCandidatePlayersPage + 1, searchKey);
      } else {
        getCandidatePlayers(matchId, matchCandidatePlayersPage + 1, '');
      }
    }
  };

  const handleSearch = searchKey => {
    const { matchId } = route.params;
    filterCandidatePlayers(matchId, 1, searchKey || '');
  };

  const renderSubstituteItem = (item, index) => (
    <View
      key={index}
      style={{
        ...MatchPlanContainerStyles.substituteSelectionWrapper,
        zIndex: 100 - index,
      }}
    >
      <View style={MatchPlanContainerStyles.dropDownWrapper}>
        <Substitute
          substitutesPlayers={substitutesPlayers}
          selectedPlayer={item.playerId}
          dropDownSelectedValues={substitutes}
          instant={item}
          index={index}
          arrayId={item._id}
          setSelectedSubstitute={setSelectedSubstitute}
          substitutes={substitutes}
          allPlayers={allPlayers}
          hideDropdownOnclick={hideDropdownOnclick}
          onReachEndHandler={onReachEndHandler}
          searchByText={filterCandidatePlayers}
          handleSearch={handleSearch}
        />
      </View>
      <TouchableOpacity
        style={MatchPlanContainerStyles.DeleteBtn}
        onPress={() => removeSubstitute(item)}
      >
        <Image
          source={deleteIcon}
          style={MatchPlanContainerStyles.deleteIcon}
        />
      </TouchableOpacity>
    </View>
  );

  const getPrevSelectedLabel = (id, dataArr) => {
    return dataArr?.find(item => item?.value === id)?.label;
  };

  //Autoselect match type
  useEffect(() => {
    if (matchPlan?._id) {
      setSelectedMatchType([
        {
          label: getPrevSelectedLabel(
            matchPlan?.matchTypeId || '',
            formattedMatchTypes || []
          ),
          value: matchPlan?.matchTypeId,
        },
      ]);
    } else if (!matchPlan?._id && formattedMatchTypes?.[0]?.label) {
      setSelectedMatchType([formattedMatchTypes?.[0]]);
    }
  }, [matchPlan, formattedMatchTypes]);

  //autoselect match formation
  useEffect(() => {
    if (matchPlan?._id) {
      const prevSelectedMatchFormation = formattedMatchFormations?.find(
        item => item?.value === matchPlan?.formationId
      );
      setSelectedMatchFormation(prevSelectedMatchFormation);
    } else if (!matchPlan?._id && formattedMatchFormations?.[0]?.label) {
      setSelectedMatchFormation(formattedMatchFormations?.[0]);
    }
  }, [matchPlan, formattedMatchFormations, selectedMatchType]);

  //reset formations when match type is changed by user
  useEffect(() => {
    if (selectedMatchType && !isComponentInit) {
      formattedMatchFormations?.length &&
        setSelectedMatchFormation(formattedMatchFormations?.[0] || {});
    }
  }, [selectedMatchType, formattedMatchFormations]);

  return (
    <KeyboardAwareScrollView
      behavior={'position'}
      contentContainerStyle={MatchPlanContainerStyles.container}
    >
      <View style={MatchPlanContainerStyles.top}>
        {matchPlanLoading ? (
          <ActivitySpinner />
        ) : (
          <View style={MatchPlanContainerStyles.Wrapper}>
            <Text style={MatchPlanContainerStyles.opponentName}>
              {opponentName ? `vs ${opponentName}` : ''}
            </Text>

            <ScrollView
              contentContainerStyle={MatchPlanContainerStyles.scroll}
              scrollEnabled={isTabDevice() ? false : true}
            >
              <View style={MatchPlanContainerStyles.innerWrapper}>
                <View
                  style={{ ...MatchPlanContainerStyles.leftCol, zIndex: 10 }}
                >
                  <View style={MatchPlanContainerStyles.leftColInner}>
                    <View
                      style={{
                        ...MatchPlanContainerStyles.dropdown,
                        zIndex: 10,
                      }}
                    >
                      <Text style={MatchPlanContainerStyles.dropdownLabel}>
                        Match Type
                      </Text>
                      {disabledOrIsPlayer ? (
                        <View
                          style={MatchPlanContainerStyles.selectedMatchWrapper}
                        >
                          <Text style={MatchPlanContainerStyles.selectedMatch}>
                            {selectedPlayerViewMatchType}
                          </Text>
                        </View>
                      ) : (
                        <SelectionModal
                          title={'Select Match Type'}
                          items={formattedMatchTypes}
                          onCloseHook={setIsMatchTypeModalOpen}
                          onSelectItemHook={selectedItem => {
                            setSelectedMatchType(selectedItem);
                            setIsComponentInit(false);
                          }}
                          defaultValues={
                            selectedMatchType?.length
                              ? [selectedMatchType?.[0]?.value]
                              : matchPlan?._id
                              ? [matchPlan?.matchTypeId]
                              : ''
                          }
                          isEnableAutoComplete
                          selectedItemLabel={
                            selectedMatchType?.length
                              ? selectedMatchType?.[0]?.label
                              : getPrevSelectedLabel(
                                  matchPlan?.matchTypeId || '',
                                  formattedMatchTypes || []
                                )
                          }
                          isModalOpen={isMatchTypeModalOpen}
                        />
                      )}
                    </View>
                    <View
                      style={{
                        ...MatchPlanContainerStyles.dropdown,
                        zIndex: 9,
                      }}
                    >
                      <Text style={MatchPlanContainerStyles.dropdownLabel}>
                        {disabledOrIsPlayer ? 'Match ' : ''} Formation
                      </Text>
                      {disabledOrIsPlayer ? (
                        <View
                          style={
                            MatchPlanContainerStyles.selectedFormationWrapper
                          }
                        >
                          <Text
                            style={MatchPlanContainerStyles.selectedFormation}
                          >
                            {matchFormations?.name}
                          </Text>
                        </View>
                      ) : (
                        <SelectionModal
                          title={'Select a Formation'}
                          items={formattedMatchFormations}
                          onCloseHook={setIsFormationTypeModalOpen}
                          onSelectItemHook={selectedOption =>
                            setSelectedMatchFormation(selectedOption?.[0])
                          }
                          defaultValues={
                            selectedMatchFormation?.value?.length
                              ? [selectedMatchFormation?.value]
                              : matchPlan?._id
                              ? [matchPlan?.formationId]
                              : ''
                          }
                          isEnableAutoComplete
                          selectedItemLabel={
                            selectedMatchFormation?.label?.length
                              ? selectedMatchFormation?.label
                              : getPrevSelectedLabel(
                                  matchPlan?.formationId,
                                  formattedMatchFormations
                                )
                          }
                          isModalOpen={isFormationTypeModalOpen}
                          disableOnPress={
                            !matchPlan?.matchTypeId &&
                            !selectedMatchType?.length
                          }
                        />
                      )}
                    </View>
                    <View
                      style={{
                        ...MatchPlanContainerStyles.dropdown,
                        zIndex: 8,
                      }}
                    >
                      <Text style={MatchPlanContainerStyles.dropdownLabel}>
                        Match Duration
                      </Text>

                      <View style={MatchPlanContainerStyles.textContainer}>
                        <TextInput
                          style={MatchPlanContainerStyles.textInput}
                          value={`${matchDuration}`}
                          onChangeText={text => {
                            validationError && setValidationError('');
                            setMatchDuration(text.replace(/\D/g, ''));
                          }}
                          placeholder={'90'}
                          placeholderTextColor="#595959"
                          disableFullscreenUI={true}
                          returnKeyType={'done'}
                          keyboardType={'numeric'}
                          contextMenuHidden
                          maxLength={2}
                          editable={!disabledOrIsPlayer}
                        />
                        {!isTabDevice() && <Text style={MatchPlanContainerStyles.textDivider}>|</Text>}
                        <Text style={MatchPlanContainerStyles.minsText}>
                          min
                        </Text>
                      </View>
                      <Text style={MatchPlanContainerStyles.errorMessage}>
                        {validationError}
                      </Text>
                      {isCoach && <View>
                        <Text style={{ ...MatchPlanContainerStyles.dropdownLabel, marginBottom: 10 }}>
                          Match Formation
                        </Text>
                        <View style={MatchPlanContainerStyles.notificationToggle}>
                          <Switch
                            trackColor={{ false: '#c3c5c8', true: '#c3c5c8' }}
                            thumbColor={visible ? '#36d982' : '#071324'}
                            ios_backgroundColor="#3e3e3e"
                            onValueChange={(newValue) => setVisible(newValue)}
                            value={visible}
                          />
                          <Text style={MatchPlanContainerStyles.notificationText2}>
                            {visible ? 'Show' : 'Hide'}
                          </Text>
                        </View>
                      </View>}
                    </View>
                  </View>
                </View>
                <View style={{ ...MatchPlanContainerStyles.field, zIndex: 9 }}>
                  {disabledOrIsPlayer ? (
                    <Field
                      jerseyNumbers={jerseyNumbers}
                      matchPlan={formattedMatchPlan}
                      playerCoordinates={matchPlan?.playerCoordinates || []}
                      isPlayer={disabledOrIsPlayer}
                    />
                  ) : (
                    <Field
                      jerseyNumbers={jerseyNumbers}
                      playerCoordinates={currentCoordinates}
                      matchPlan={formattedMatchPlan}
                      openCandidatePlayerModal={openCandidatePlayerModal}
                      openCandidatePlayerDeleteModal={
                        openCandidatePlayerDeleteModal
                      }
                    />
                  )}
                </View>
                <View
                  style={{ ...MatchPlanContainerStyles.rightCol, zIndex: 8 }}
                >
                  <View style={MatchPlanContainerStyles.rightColInner}>
                    <View
                      style={{
                        ...MatchPlanContainerStyles.substituteWrapper,
                        zIndex: 10,
                      }}
                    >
                      <View
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          marginBottom: 10,
                          alignItems: 'center',
                        }}
                      >
                        <Text style={MatchPlanContainerStyles.dropdownLabel}>
                          Substitutes
                        </Text>
                        {!disabledOrIsPlayer && (
                          <TouchableOpacity
                            style={{
                              ...MatchPlanContainerStyles.addMoreButton,
                              opacity: filteredPlayers.length
                                ? !substitutes.length
                                  ? 1
                                  : !substitutes[substitutes.length - 1]
                                      .playerId
                                  ? 0.5
                                  : 1
                                : 0.5,
                            }}
                            onPress={() => addSubstitute()}
                            disabled={
                              !filteredPlayers.length ||
                              (substitutes.length &&
                                !substitutes[substitutes.length - 1].playerId)
                            }
                          >
                            <Text style={MatchPlanContainerStyles.addMoreText}>
                              +
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>

                      {disabledOrIsPlayer ? (
                        <FlatList
                          data={matchPlan?.substitutePlayers || []}
                          renderItem={renderSubs}
                          keyExtractor={item => item.sportsProfileId}
                        />
                      ) : (
                        <ScrollView
                          style={MatchPlanContainerStyles.subsituteListScroller}
                          contentContainerStyle={{ flexGrow: 1 }}
                        >
                          <View
                            style={
                              MatchPlanContainerStyles.subsituteListWrapper
                            }
                          >
                            {!!substitutes?.length &&
                              substitutes.map((substitute, index) =>
                                renderSubstituteItem(substitute, index)
                              )}
                          </View>
                        </ScrollView>
                      )}
                    </View>
                    {!disabledOrIsPlayer && (
                      <View
                        style={{
                          ...MatchPlanContainerStyles.buttons,
                          zIndex: 9,
                        }}
                      >
                        <TouchableOpacity
                          style={{
                            ...MatchPlanContainerStyles.submitButton,
                            ...MatchPlanContainerStyles.buttonSave,
                          }}
                          onPress={() => saveMatchPlan()}
                        >
                          <Text
                            style={MatchPlanContainerStyles.submitButtonText}
                          >
                            Save
                          </Text>
                          {matchPlanSaveLoading && (
                            <ActivityIndicator
                              size="small"
                              color={colors.white}
                            />
                          )}
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{
                            ...MatchPlanContainerStyles.submitButton,
                            ...MatchPlanContainerStyles.buttonCancel,
                          }}
                          onPress={() => navigation.goBack()}
                        >
                          <Text
                            style={MatchPlanContainerStyles.submitButtonText}
                          >
                            Cancel
                          </Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            </ScrollView>
          </View>
        )}
        <SelectCandidatePlayerModal
          visible={showPlayerSelectModal}
          showModal={setShowPlayerSelectModal}
          candidatePlayers={filteredPlayers}
          onCandidatePlayerSelect={onCandidatePlayerSelect}
          loading={matchCandidatePlayersLoading}
          onReachEndHandler={onReachEndHandler}
          handleSearch={handleSearch}
        />
        <UnsavedChangesModal
          modalVisible={unsavedPopup}
          setModalVisible={setUnsavedPopup}
          setModalResponse={handleUnsavedChangesPopupResponse}
          message={'Do you want to save the current formation?'}
        />

        {showPlayerDeleteModal && (
          <DeleteModal
            message={'Do you want to remove this player?'}
            cancelAction={closeDeleteModal}
            submitAction={deletePlayer}
          />
        )}
      </View>
    </KeyboardAwareScrollView>
  );
};

export default MatchPlanContainer;
