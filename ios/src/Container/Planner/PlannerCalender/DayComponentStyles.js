import { StyleSheet, Dimensions, Platform } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';
import { colorPalette } from '../../../constants/constants';

const DayComponentStyles = colors => ({
  todayWrapper: isTabDevice()
    ? {
        height: wp('7%'),
        width: wp('8%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: hp('2%'),
      }
    : {
        height: wp('6%'),
        width: wp('8%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('2%'),
      },
  todayWrapperParent: isTabDevice()
    ? {
        height: wp('4%'),
        width: wp('8%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: hp('2%'),
      }
    : {
        height: wp('6%'),
        width: wp('8%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: wp('2%'),
      },
  today: isTabDevice()
    ? {
        height: hp('5%'),
        width: hp('5%'),
        borderColor: colors.green,
        borderWidth: 2,
        borderRadius: wp('100%'),
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: hp('1%'),
      }
    : {
        height: Platform.OS === 'android' ? wp('6%') : wp('7%'),
        width: Platform.OS === 'android' ? wp('6%') : wp('7%'),
        borderColor: colors.green,
        borderWidth: wp('0.4%'),
        borderRadius: wp('100%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  selectedDay: isTabDevice()
    ? {
        height: hp('5%'),
        width: hp('5%'),
        backgroundColor: colors.green,
        borderColor: colors.green,
        borderWidth: 2,
        borderRadius: hp('100%'),
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        height: Platform.OS === 'android' ? wp('6%') : wp('7%'),
        width: Platform.OS === 'android' ? wp('6%') : wp('7%'),
        backgroundColor: colors.green,
        borderColor: colors.green,
        borderWidth: 2,
        borderRadius: wp('100%'),
        justifyContent: 'center',
        alignItems: 'center',
      },
  otherDays: isTabDevice()
    ? {
        height: hp('5%'),
        width: hp('5%'),
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.transparent,
        borderColor: colors.transparent,
        borderWidth: 2,
      }
    : {
        height: wp('7%'),
        width: wp('7%'),
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.transparent,
        borderColor: colors.transparent,
        borderWidth: 2,
      },
  date: isTabDevice()
    ? {
        fontSize: hp('2.6%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        marginTop: hp('0.8%'),
      }
    : {
        fontSize: Platform.OS === 'android' ? wp('3%') : wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  dateToday: isTabDevice()
    ? {
        fontSize: hp('2.6%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      }
    : {
        fontSize: Platform.OS === 'android' ? wp('3%') : wp('3.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Medium',
      },
  pointersWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: hp('2.7%'),
        marginTop: hp('2.2%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: wp('3%'),
        marginTop: wp('3%'),
      },
  pointers: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
  pointer: isTabDevice()
    ? {
        width: hp('2.3%'),
        height: hp('2.3%'),
        borderRadius: hp('100%'),
        // marginRight: wp('0.4%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        marginRight: wp('0.5%'),
        margin: Platform.OS === 'android' ? wp('0.5%') : 0,
        marginTop: Platform.OS === 'android' ? 0 : 0,
        marginBottom: Platform.OS === 'android' ? 0 : 0,
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      },
  extraPointersWrapper: isTabDevice()
    ? {
        borderWidth: 1,
        borderColor: colors.white,
        // padding: hp('0.2%'),
        borderRadius: hp('100%'),
        width: hp('2.6%'),
        height: hp('2.6%'),
        marginLeft: hp('0.6%'),
      }
    : {
        borderWidth: 1,
        borderColor: colors.white,
        padding: wp('0.2%'),
        borderRadius: wp('100%'),
        width: wp('4%'),
        height: wp('4%'),
        marginTop: -wp('0.5%'),
        marginLeft: wp('1%'),
      },
  extraPointers: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('1.1%'),
        marginLeft: hp('0.5%'),
        marginTop: hp('0.3%'),
      }
    : {
        color: colors.white,
        fontSize: wp('2.3%'),
        fontWeight: 'bold',
      },
  extraPointersLong: isTabDevice()
    ? {
        color: colors.white,
        fontSize: hp('0.9%'),
        marginLeft: hp('0.4%'),
        marginTop: hp('0.5%'),
      }
    : {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontWeight: 'bold',
      },
  star: isTabDevice()
    ? {
        width: wp('1.5%'),
        height: hp('1.5%'),
        fontSize: hp('1.7%'),
        marginLeft: wp('0.4%'),
        marginBottom: wp('0.2%'),
      }
    : {
        width: wp('2%'),
        height: wp('2%'),
        fontSize: wp('2.2%'),
        marginTop: -wp('0.5%'),
        // marginLeft: wp('0.4%'),
      },
  pointerMatch: {
    width: wp('1.3%'),
    height: hp('1.3%'),
    // marginRight: wp('0.5%')
  },
});
export default DayComponentStyles;
