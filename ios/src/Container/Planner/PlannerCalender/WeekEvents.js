import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { eventType } from '../../../constants/constants';
import {
  dateTimeConversion,
  dateTimeUTCConversion,
  eventSearchForWeek,
  getNextWeekDate,
  stringLength,
  stringToTitleCase,
} from '../../../helpers/index';
import useStyles from '../../../hooks/useStyles';
import { WEEK_DATA_SUCCESS } from '../../../store/actionTypes/Planner/PlannerAction';
import customEventsComponentStyles from './EventsComponentStyles';

const WeekEvents = ({ events, onEventViewButtonClicked }) => {
  const EventsComponentStyles = useStyles(customEventsComponentStyles);
  const dispatch = useDispatch();
  const navigator = useNavigation();
  const {
    timestamp,
    isToday,
    schedule,
    tomorrowSchedule,
    userType,
    isPastDate,
  } = events;

  const { yearMonthDateString } = dateTimeUTCConversion(timestamp);
  const { teamData } = useSelector(state => state?.team);
  const { calendar = [] } = useSelector(state => state?.planner);

  const [selectedEvent, setSelectedEvent] = useState('');

  const [weekType, setWeekType] = useState('currentWeek');

  const [weekSchedule, setWeekSchedule] = useState({
    thisWeekSchedule: [],
    nextWeekSchedule: [],
  });

  useEffect(() => {
    dispatch({
      type: WEEK_DATA_SUCCESS,
      WeekType: weekType,
      WeekSchedule: weekSchedule,
    });
  }, [weekType, weekSchedule]);

  useEffect(() => {
    if (teamData?.data?.length && calendar?.length) {
      weekSchedule.thisWeekSchedule = [
        ...eventSearchForWeek(calendar, yearMonthDateString, teamData),
      ];

      const { year, dateNumberString, monthNumberString } = getNextWeekDate(
        yearMonthDateString.split('-')
      );

      weekSchedule.nextWeekSchedule = [
        ...eventSearchForWeek(
          calendar,
          `${year}-${monthNumberString}-${dateNumberString}`,
          teamData
        ),
      ];
    }
    setWeekSchedule({ ...weekSchedule });
  }, [teamData, calendar, events]);

  const eventItemWeek = ({ item }) => {
    const {
      hours12String,
      amPm,
      minutesString,
      dateString,
      date,
      month,
      year,
    } = dateTimeConversion(item?.startTime);

    return (
      <TouchableOpacity onPress={() => setSelectedEvent(item._id)}>
        <View
          style={
            selectedEvent == item._id
              ? EventsComponentStyles.eventContainerSelected
              : EventsComponentStyles.eventContainer
          }
        >
          <View style={EventsComponentStyles.eventWeekRow1}>
            <Text style={EventsComponentStyles.eventWeekName}>
              {stringLength(item.team.teamName, 15)}{' '}
              {stringToTitleCase(item.type)}
            </Text>
            <Text style={EventsComponentStyles.eventWeekTime}>
              {hours12String}:{minutesString} {amPm}
            </Text>
          </View>

          <View style={EventsComponentStyles.eventWeekRow2}>
            <Text style={EventsComponentStyles.eventWeekDate}>
              {dateString}, {date}/{month}/{year}
            </Text>
          </View>
          {selectedEvent == item._id && (
            <View style={EventsComponentStyles.eventWeekRow3}>
              <Text style={EventsComponentStyles.eventWeeklocation}>
                {stringLength(item.location.name, 10)}
              </Text>
              <View style={EventsComponentStyles.eventWeekButtons}>
                <TouchableOpacity
                  style={EventsComponentStyles.eventWeekButton}
                  onPress={() => onEventViewButtonClicked(item)}
                >
                  <Text style={EventsComponentStyles.eventButtonText}>
                    View
                  </Text>
                </TouchableOpacity>
                {item.type === eventType.MATCH && !isPastDate ? (
                  <TouchableOpacity
                    style={EventsComponentStyles.eventWeekButton}
                    onPress={() =>
                      navigator.navigate('MatchPlan', {
                        matchId: item._id,
                        opponent: item.opponentId,
                      })
                    }
                  >
                    <Text style={EventsComponentStyles.eventButtonText}>
                      Plan
                    </Text>
                  </TouchableOpacity>
                ) : null}
              </View>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const { nextWeekSchedule, thisWeekSchedule } = weekSchedule;
  return (
    <View>
      <View style={EventsComponentStyles.eventWeekSelector}>
        <TouchableOpacity
          style={
            weekType == 'currentWeek'
              ? EventsComponentStyles.eventWeekSelected
              : EventsComponentStyles.eventWeek
          }
          onPress={() => setWeekType('currentWeek')}
        >
          <Text style={EventsComponentStyles.eventWeekText}>This Week</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={
            weekType == 'nextWeek'
              ? EventsComponentStyles.eventWeekSelected
              : EventsComponentStyles.eventWeek
          }
          onPress={() => setWeekType('nextWeek')}
        >
          <Text style={EventsComponentStyles.eventWeekText}>Next Week</Text>
        </TouchableOpacity>
      </View>

      <View>
        {weekType == 'currentWeek' && (
          <View>
            {thisWeekSchedule && thisWeekSchedule.length ? (
              <FlatList
                data={thisWeekSchedule}
                renderItem={eventItemWeek}
                keyExtractor={(item, index) => index.toString()}
                style={EventsComponentStyles.eventsFlatList}
              />
            ) : (
              <Text style={EventsComponentStyles.eventsNoEvents}>
                No Events
              </Text>
            )}
          </View>
        )}

        {weekType == 'nextWeek' && (
          <View>
            {nextWeekSchedule && nextWeekSchedule.length ? (
              <FlatList
                data={nextWeekSchedule}
                renderItem={eventItemWeek}
                keyExtractor={(item, index) => index.toString()}
                style={EventsComponentStyles.eventsFlatList}
              />
            ) : (
              <Text style={EventsComponentStyles.eventsNoEvents}>
                No Events
              </Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};
export default WeekEvents;
