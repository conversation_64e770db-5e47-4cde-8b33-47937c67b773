import { Entypo } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import { isTabDevice } from '../../../config/appConfig';
import { eventType, userRoleType } from '../../../constants/constants';
import {
  dateTimeUTCConversion,
  dateTimeConversion,
  eventSearchByDate,
  getNextDate,
} from '../../../helpers/index';
import useStyles from '../../../hooks/useStyles';
import customDayComponentStyles from './DayComponentStyles';

const DayComponent = ({ individualDate, onDayClick }) => {
  const DayComponentStyles = useStyles(customDayComponentStyles);
  const dateTime = dateTimeConversion(new Date());
  const { calendar = [], selectedDateEvents, calendarMap } = useSelector(
    state => state?.planner
  );

  const { userData } = useSelector(state => state?.auth);

  const {
    teamData,
    teamInitialPage,
    teamInitialSize,
    teamPage,
    teamSize,
    selectedTeam,
  } = useSelector(state => state?.team);
  const [dateInfo, setDateInfo] = useState({
    isToday: false,
    schedule: [],
    tomorrowSchedule: [],
    scheduleShow: [],
    scheduleCount: 0,
    nextDay: {},
  });
  const { yearMonthDateString } = dateTime;
  useEffect(() => {
    const { dateString } = individualDate;

    const nextDay = getNextDate(dateString.split('-'));
    const { year, dateNumberString, monthNumberString } = nextDay;
    dateInfo.nextDay = nextDay;
    if (teamData?.data?.length && calendar?.length) {
      const filteredSchedule = eventSearchByDate(
        calendarMap[dateString],
        dateString,
        teamData
      );

      dateInfo.schedule = filteredSchedule;

      if (filteredSchedule.length >= 2) {
        dateInfo.scheduleCount = filteredSchedule.length - 2;
        dateInfo.scheduleShow = [filteredSchedule[0], filteredSchedule[1]];
      } else {
        dateInfo.scheduleShow = filteredSchedule;
      }

      dateInfo.tomorrowSchedule = eventSearchByDate(
        calendarMap[dateString],
        `${year}-${monthNumberString}-${dateNumberString}`,
        teamData
      );
    }
    if (yearMonthDateString === dateString) {
      dateInfo.isToday = true;
    }
    setDateInfo({ ...dateInfo });
  }, [teamData, calendar, individualDate]);

  const { isToday, schedule, scheduleShow, scheduleCount } = dateInfo;
  const { dateString } = individualDate;

  const isParent = userRoleType.PARENT === userData?.type;

  return (
    <TouchableOpacity
      onPress={() => {
        onDayClick({
          ...dateInfo,
          ...individualDate,
          userType: userData?.type,
        });
      }}
    >
      <View
        style={
          isParent
            ? DayComponentStyles.todayWrapperParent
            : DayComponentStyles.todayWrapper
        }
      >
        <View
          style={
            isToday
              ? DayComponentStyles.today
              : selectedDateEvents.dateString == dateString
              ? DayComponentStyles.selectedDay
              : DayComponentStyles.otherDays
          }
        >
          <Text
            style={
              isToday ? DayComponentStyles.dateToday : DayComponentStyles.date
            }
          >
            {individualDate.day}
          </Text>

          <View style={DayComponentStyles.pointersWrapper}>
            {scheduleShow.map((item, index) => (
              <View key={index} style={DayComponentStyles.pointers}>
                {item.type === eventType.MATCH ? (
                  <View
                    style={[
                      DayComponentStyles.pointer,
                      { backgroundColor: item.team.colour },
                    ]}
                  >
                    {isTabDevice() ? (
                      <Entypo
                        name="star"
                        size={15}
                        color="#0E1B2E"
                        style={DayComponentStyles.star}
                      />
                    ) : (
                      <Entypo
                        name="star"
                        size={8}
                        color="#0E1B2E"
                        style={DayComponentStyles.star}
                      />
                    )}
                  </View>
                ) : (
                  <View
                    style={[
                      DayComponentStyles.pointer,
                      { backgroundColor: item.team.colour },
                    ]}
                  ></View>
                )}
              </View>
            ))}

            {scheduleCount > 0 && scheduleShow.length ? (
              <View style={DayComponentStyles.extraPointersWrapper}>
                <Text
                  style={
                    scheduleCount >= 10
                      ? DayComponentStyles.extraPointersLong
                      : DayComponentStyles.extraPointers
                  }
                >
                  +{scheduleCount}
                </Text>
              </View>
            ) : null}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default DayComponent;
