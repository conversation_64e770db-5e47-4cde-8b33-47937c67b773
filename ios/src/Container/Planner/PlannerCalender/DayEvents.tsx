import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { eventType, userRoleType } from '../../../constants/constants';
import {
  dateTimeUTCConversion,
  stringLength,
  stringToTitleCase,
} from '../../../helpers/index';
import useStyles from '../../../hooks/useStyles';
import { SET_SELECTED_MATCH_DETAILS } from '../../../store/actionTypes/MatchPlan/MatchPlanActions';
import customEventsComponentStyles from './EventsComponentStyles';
import EventItem from './EventItem'

type DayEventType = {
  events: any;
  onRSVPButtonClicked: Function;
  onEventViewButtonClicked: Function;
};

const DayEvent: React.FC<DayEventType> = ({
  events,
  onRSVPButtonClicked,
  onEventViewButtonClicked,
}) => {
  const EventsComponentStyles = useStyles(customEventsComponentStyles);
  const navigator = useNavigation();
  
  const dispatch = useDispatch();
  const {
    timestamp,
    isToday,
    isPastDate,
    schedule,
    tomorrowSchedule,
    userType,
    nextDay = {},
  } = events;

  const {
    dateString: nextDateString,
    monthString: nextMonthString,
    date: nextDate,
  } = nextDay;

  const { dateString, monthString, date, yearMonthDateString } =
    dateTimeUTCConversion(timestamp);
  const [selectedEvent, setSelectedEvent] = useState('');

  return (
    <View style={EventsComponentStyles.eventsContainer}>
      {isToday ? (
        <Text style={EventsComponentStyles.eventsToday}>Today</Text>
      ) : (
        <Text style={EventsComponentStyles.eventsDate}>
          {monthString} {date}, {dateString}
        </Text>
      )}

      {schedule && schedule.length ? (
        <FlatList
          data={schedule}
          renderItem={({ item }) => (
            <EventItem
              EventsComponentStyles={EventsComponentStyles}
              item={item}
              selectedEvent={selectedEvent}
              setSelectedEvent={setSelectedEvent}
              onRSVPButtonClicked={onRSVPButtonClicked}
              onEventViewButtonClicked={onEventViewButtonClicked}
              isPastDate={isPastDate}
            />
          )}
          keyExtractor={(item, index) => index.toString()}
        />
      ) : (
        <Text style={EventsComponentStyles.eventsNoEvents}>No Events</Text>
      )}
      {/* <View style={EventsComponentStyles.eventsTomorrowWrapper}>
        {isToday ? (
          <Text style={EventsComponentStyles.eventsTomorrow}>Tomorrow</Text>
        ) : (
          <Text style={EventsComponentStyles.eventsDate}>
            {nextMonthString} {nextDate}, {nextDateString}
          </Text>
        )}
      </View> */}
      {/* {tomorrowSchedule && tomorrowSchedule.length ? (
        <FlatList
          data={tomorrowSchedule}
          renderItem={eventItem}
          keyExtractor={(item, index) => index.toString()}
        />
      ) : (
        <Text style={EventsComponentStyles.eventsNoEvents}>No Events</Text>
      )} */}
    </View>
  );
};
export default DayEvent;
