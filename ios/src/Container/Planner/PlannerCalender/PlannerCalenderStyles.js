import { isTabDevice } from '../../../config/appConfig';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

const PlannerCalenderStyle = colors => ({
  container: isTabDevice()
    ? {
        height: hp('80%'),
        zIndex: 2,
      }
    : {
        height: wp('80%'),
        zIndex: 2,
      },
  containerParent: isTabDevice()
    ? {
        height: hp('65%'),
        zIndex: 2,
      }
    : {
        height: wp('80%'),
        zIndex: 2,
        marginTop: wp('-2%'),
        paddingBottom: wp('5%'),
      },
  week: {
    marginTop: wp('2%'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: wp('3.5%'),
    paddingRight: wp('3.5%'),
  },
});
export default PlannerCalenderStyle;
