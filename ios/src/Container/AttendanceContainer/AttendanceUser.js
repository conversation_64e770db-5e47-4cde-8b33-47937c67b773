import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { useDispatch } from 'react-redux';
import customAttendanceStyles from './AttendanceStyles';
import { SET_MATCH_INVITEES_ATTENDANT_STATE } from '../../store/actionTypes/MatchLog/MatchLogActions';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import unAvailableIcon from '../../../assets/icons/unavailable.png';
import useStyles from '../../hooks/useStyles';

const AttendanceUser = ({ item, onChangedInvitees }) => {
  const attendanceStyles = useStyles(customAttendanceStyles);
  const dispatch = useDispatch();

  const setAttendant = type => {
    dispatch({
      type: SET_MATCH_INVITEES_ATTENDANT_STATE,
      payload: { ...item, type },
    });
    onChangedInvitees(item, type);
  };

  let { firstName, lastName, profileImageUrl, isAttended, isAvailable } = item;
  const fullName = `${firstName || ''} \n${lastName || ''}`;

  return (
    <View style={attendanceStyles.player}>
      <View style={attendanceStyles.playerWrapper}>
        <ProfileImage
          imageStyles={attendanceStyles.proImage}
          profileImageUrl={profileImageUrl}
        />

        {!isAvailable && (
          <View style={attendanceStyles.removeIconWrapper}>
            <Image style={attendanceStyles.icon} source={unAvailableIcon} />
          </View>
        )}
      </View>
      <Text
        style={attendanceStyles.playerName}
        // numberOfLines={1}
        // ellipsizeMode="tail"
      >
        {fullName}
      </Text>

      <View style={attendanceStyles.buttons}>
        <TouchableOpacity
          onPress={() => setAttendant(true)}
          style={
            item.hasOwnProperty('isAttended') && isAttended
              ? attendanceStyles.buttonGreen
              : attendanceStyles.buttonBlue
          }
        >
          <Text style={attendanceStyles.buttonWord}>Present</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setAttendant(false)}
          style={
            item.hasOwnProperty('isAttended') && !isAttended
              ? attendanceStyles.buttonRed
              : attendanceStyles.buttonBlue
          }
        >
          <Text style={attendanceStyles.buttonWord}>Absent</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default AttendanceUser;
