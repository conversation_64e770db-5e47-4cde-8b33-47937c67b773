import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { colorPalette } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';

const ManageUserContainerStyle = colors => ({
  container: {
    backgroundColor: colors.darkBlue,
    height: hp('100%'),
    width: wp('100%'),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  wrapper: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        height: hp('90%'),
        width: wp('95%'),
        borderRadius: wp('2%'),
        padding: wp('2%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        height: hp('90%'),
        width: wp('98%'),
        borderRadius: wp('2%'),
        padding: wp('2%'),
      },
  flatList: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // flexWrap: 'wrap',
    // backgroundColor: colors.red,
  },
  player: isTabDevice()
    ? {
        marginRight: wp('3%'),
        marginBottom: wp('2.5%'),
        width: '17%',
      }
    : {
        marginRight: wp('3%'),
        marginBottom: wp('3%'),
        width: '30%',
      },
  titleRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('2%'),
      }
    : {
        marginBottom: wp('2%'),
      },
  titleRowRight: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: isTabDevice()
    ? {
        fontWeight: 'bold',
        fontSize: wp('2%'),
        color: colors.white,
      }
    : {
        fontWeight: 'bold',
        fontSize: wp('3.5%'),
        color: colors.white,
        marginBottom: wp('2%'),
      },
  buttons: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }
    : {
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'center',
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('1%'),
        paddingLeft: wp('3%'),
        paddingBottom: wp('1%'),
        paddingRight: wp('3%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('1%'),
        paddingLeft: wp('3%'),
        paddingBottom: wp('1%'),
        paddingRight: wp('3%'),
        borderRadius: wp('1%'),
        display: 'flex',
        flexDirection: 'row',
      },
  button2: {
    backgroundColor: colors.transparent,
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonTransparent: {
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
      },
  proImage: isTabDevice()
    ? {
        width: wp('15%'),
        height: wp('15%'),
        borderRadius: wp('2%'),
        marginBottom: wp('0.5%'),
      }
    : {
        width: wp('27%'),
        height: wp('27%'),
        borderRadius: wp('2%'),
        marginBottom: wp('0.5%'),
      },
  imagePerLoader: {
    width: wp('11%'),
    height: wp('11%'),
    borderRadius: wp('2%'),
    marginBottom: wp('0.5%'),
    backgroundColor: colors.white,
  },
  imagePerLoaderImage: {
    width: wp('3%'),
    height: wp('3%'),
    marginTop: wp('4%'),
    marginLeft: wp('4%'),
  },

  playerName: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.2%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
        width: '100%',
      }
    : {
        color: colors.green,
        fontSize: hp('1.4%'),
        textAlign: 'center',
        marginTop: wp('2%'),
        marginBottom: wp('2%'),
        width: wp('27%'),
      },
  buttonGreen: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: wp('0.5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        paddingTop: wp('0.5%'),
        paddingLeft: wp('1.1%'),
        paddingBottom: wp('0.5%'),
        paddingRight: wp('1.1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.green,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'center',
        paddingTop: hp('0.8%'),
        paddingLeft: wp('5%'),
        paddingBottom: hp('0.9%'),
        paddingRight: wp('5%'),
        marginBottom: wp('1%'),
      },
  buttonBlue: isTabDevice()
    ? {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('0.5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        paddingTop: wp('0.5%'),
        paddingLeft: wp('1.1%'),
        paddingBottom: wp('0.5%'),
        paddingRight: wp('1.1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'center',
        paddingTop: hp('0.8%'),
        paddingLeft: wp('5%'),
        paddingBottom: hp('0.9%'),
        paddingRight: wp('5%'),
        marginBottom: wp('1%'),
      },
  buttonRed: isTabDevice()
    ? {
        backgroundColor: colors.red,
        borderRadius: wp('0.5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        paddingTop: wp('0.5%'),
        paddingLeft: wp('1.1%'),
        paddingBottom: wp('0.5%'),
        paddingRight: wp('1.1%'),
        marginBottom: wp('1%'),
      }
    : {
        backgroundColor: colors.red,
        borderRadius: wp('1%'),
        flexDirection: 'row',
        justifyContent: 'center',
        paddingTop: hp('0.8%'),
        paddingLeft: wp('5%'),
        paddingBottom: hp('0.9%'),
        paddingRight: wp('5%'),
        marginBottom: wp('1%'),
      },
  buttonWord: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3%'),
        fontWeight: 'bold',
      },
  playerWrapper: {
    position: 'relative',
  },
  icon: isTabDevice()
    ? {
        width: wp('3%'),
        height: wp('3%'),
      }
    : {
        width: hp('2%'),
        height: hp('2%'),
      },
  removeIconWrapper: isTabDevice()
    ? {
        backgroundColor: colors.white,
        width: wp('3%'),
        height: wp('3%'),
        borderRadius: wp('100%'),
        position: 'absolute',
        top: -wp('1%'),
        right: -wp('0.8%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignSelf: 'center',
      }
    : {
        backgroundColor: colors.white,
        width: hp('2%'),
        height: hp('2%'),
        borderRadius: hp('100%'),
        position: 'absolute',
        top: -hp('0.8%'),
        right: -hp('0.5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignSelf: 'center',
      },
  searchBarContainer: isTabDevice()
    ? {
        marginRight: hp('5%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: wp('30%'),
        borderRadius: wp('1%'),
        backgroundColor: colors.semiDarkBlue,
        height: hp('6%'),
        paddingRight: hp('2%'),
        paddingLeft: hp('2%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('1%'),
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: wp('2%'),
        backgroundColor: colors.semiDarkBlue,
        height: hp('6%'),
        paddingRight: hp('1%'),
        paddingLeft: hp('2%'),
        paddingTop: hp('1%'),
        paddingBottom: hp('1%'),
        width: '60%',
      },
  searchBarInput: isTabDevice()
    ? {
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Medium',
        width: '68%',
        color: colors.white,
      }
    : {
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Medium',
        width: '85%',
        color: colors.white,
      },
  searchBarIcon: isTabDevice()
    ? {
        marginLeft: hp('8%'),
        flexDirection: 'row',
        alignItems: 'center',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
      },
});
export default ManageUserContainerStyle;
