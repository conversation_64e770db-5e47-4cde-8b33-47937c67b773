import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback } from 'react';
import { FlatList, Image, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';
import { EventDateType, userRoleType } from '../../../constants/constants';
import { EVENT_SERVICE } from '../../../constants/services';
import { calculateEventDate, dateTimeConversion } from '../../../helpers/index';
import useApi from '../../../hooks/useApi';
import useStyles from '../../../hooks/useStyles';
import {
  UPCOMING_TRAINING_FAIL,
  UPCOMING_TRAINING_REQUEST,
  UPCOMING_TRAINING_SUCCESS,
} from '../../../store/actionTypes/SeasonUpdate/SeasonUpdateAction';
import customSeasonUpdateStyle from '../SeasonUpdateStyle';

const Item = ({ title, location, time, SeasonUpdateStyle }) => {
  const dateConvert = dateTimeConversion(time);
  const { dateReadable, monthString } = dateTimeConversion(time);
  return (
    <View style={SeasonUpdateStyle.itemView}>
      <Text style={SeasonUpdateStyle.match}>{title}</Text>
      <View style={SeasonUpdateStyle.matchDetails}>
        <View style={SeasonUpdateStyle.leftCol}>
          <Image
            style={SeasonUpdateStyle.locationIcon}
            source={require('../../../../assets/icons/locationIconGray.png')}
          />
          <Text style={SeasonUpdateStyle.location}>{location?.name}</Text>
        </View>
        <View style={SeasonUpdateStyle.rightCol}>
          <Text style={SeasonUpdateStyle.date}>
            {dateReadable} {monthString}
          </Text>
          <Text style={SeasonUpdateStyle.seperator}> | </Text>
          <Text style={SeasonUpdateStyle.time}>
            {dateConvert?.hours12}:{dateConvert?.minutesString}
            {dateConvert?.amPm}
          </Text>
        </View>
      </View>
    </View>
  );
};
const UpcomingTraining = ({ selectedChild }) => {
  const SeasonUpdateStyle = useStyles(customSeasonUpdateStyle);
  const [fetchData] = useApi();
  const { UpcomingTrainingData, UpcomingTrainingLoading } = useSelector(
    state => state?.SeasonUpdate
  );
  const { userData } = useSelector(state => state?.auth);

  const renderItem = ({ item }) => (
    <Item
      title={item.name}
      location={item.location}
      time={item.startTime}
      SeasonUpdateStyle={SeasonUpdateStyle}
    />
  );

  const getParticipantDetails = userData => {
    switch (userData?.type) {
      case userRoleType.COACH:
      case userRoleType.PLAYER:
        return `&participantIds=${userData?.id}`;

      case userRoleType.PARENT:
        return `&participantIds=${selectedChild?.id}`;
      default:
        return '';
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData(
        `/api/v1/events?startDate=${new Date().toISOString()}&endDate=${calculateEventDate(
          2,
          EventDateType.END
        )}&page=1&size=100&type=TRAINING${getParticipantDetails(userData)}`,
        UPCOMING_TRAINING_REQUEST,
        UPCOMING_TRAINING_SUCCESS,
        UPCOMING_TRAINING_FAIL,
        null,
        '',
        'GET',
        null,
        EVENT_SERVICE
      );
    }, [userData, selectedChild])
  );

  return (
    <>
      {!UpcomingTrainingLoading ? (
        <>
          <Text style={SeasonUpdateStyle.title}>Upcoming training</Text>
          <FlatList
            data={UpcomingTrainingData?.data || []}
            renderItem={renderItem}
            keyExtractor={item => item._id}
          />
        </>
      ) : (
        <ActivitySpinner />
      )}
    </>
  );
};

export default UpcomingTraining;
