import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { useSelector } from 'react-redux';
import ActivitySpinner from '../../../components/ActivitySpinner/ActivitySpinner';

import InputSelectModal from '../../../components/modal/InputSelectModal/InputSelectModal';
import {
  EventDateType,
  MatchStatus,
  userRoleType,
} from '../../../constants/constants';
import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../../constants/services';
import { calculateEventDate, dateTimeConversion } from '../../../helpers';
import useApi from '../../../hooks/useApi';
import useInputSelectModal from '../../../hooks/useInputSelectModal';
import useColors from '../../../hooks/useColors';
import useStyles from '../../../hooks/useStyles';
import {
  GET_CHILD_INFORMATION_FAILED,
  GET_CHILD_INFORMATION_REQUEST,
  GET_CHILD_INFORMATION_SUCCESS,
} from '../../../store/actionTypes/common/commonActionTypes';
import {
  SEASON_UPDATE_FAIL,
  SEASON_UPDATE_REQUEST,
  SEASON_UPDATE_SUCCESS,
} from '../../../store/actionTypes/SeasonUpdate/SeasonUpdateAction';
import {
  TEAM_FAIL,
  TEAM_REQUEST,
  TEAM_SUCCESS,
} from '../../../store/actionTypes/Team/TeamAction';
import customSeasonUpdateStyle from '../SeasonUpdateStyle';
import SelectionModal from '../../../components/modal/SelectionModal/SelectionModal';

const UpdateItem = ({
  result,
  scorecard,
  opponentTeam,
  time,
  tournament,
  SeasonUpdateStyle,
  colors,
  isMatchPlayed,
}) => {
  const { dateReadable, monthString } = dateTimeConversion(time);

  const score = scorecard?.score || 0;
  const opponentScore = scorecard?.opponentScore || 0;

  return (
    <View
      style={{
        ...SeasonUpdateStyle.matchItemView,
        backgroundColor:
          result === MatchStatus.LOOSE
            ? colors.red
            : result === MatchStatus.WON
            ? colors.green
            : colors.lightGrey,
      }}
    >
      {!isMatchPlayed && (
        <View style={SeasonUpdateStyle.overlayMatchPlayed}>
          <Text style={SeasonUpdateStyle.overlayMatchPlayedText}>
            The match has not been played
          </Text>
        </View>
      )}

      <View style={SeasonUpdateStyle.matchItemLeft}>
        <Text style={SeasonUpdateStyle.matchItemtitle}>VS {opponentTeam}</Text>
        <Text style={SeasonUpdateStyle.matchItemtournament}>{tournament}</Text>
        <Text style={SeasonUpdateStyle.matchItemDate}>
          {dateReadable} {monthString}
        </Text>
      </View>
      <View style={SeasonUpdateStyle.matchItemRight}>
        <Text style={SeasonUpdateStyle.score}>{score}</Text>
        <Text style={SeasonUpdateStyle.matchItemseperator}></Text>
        <Text style={SeasonUpdateStyle.score}>{opponentScore}</Text>
      </View>
    </View>
  );
};

const findURL = (userType, userData, childInformation) => {
  switch (userType) {
    case userRoleType.PARENT:
      return `/api/v1/sport-profiles/${childInformation?.sportsProfileId}/teams?page=1&size=500`;
    case userRoleType.PLAYER:
      return `/api/v1/sport-profiles/${userData?.sportsProfileId}/teams?page=1&size=500`;
    case userRoleType.COACH:
      return `/api/v1/teams?coachId=${userData?.id}&page=1&size=500`;
    default:
      return `/api/v1/teams?page=1&size=500`;
  }
};

const SeasonUpdates = ({ selectedChild }) => {
  const SeasonUpdateStyle = useStyles(customSeasonUpdateStyle);
  const colors = useColors();
  const [fetchData] = useApi();
  const [fetchChildInformation] = useApi();
  const [setIsTeamModalOpen, isTeamModalOpen, setSelectedTeam, selectedTeam] =
    useInputSelectModal();
  const [fetchData2] = useApi();
  const { childInformation } = useSelector(state => state?.common);
  const { userRole, userData } = useSelector(state => state?.auth);
  const { teamDataLoading, teamData } = useSelector(state => state?.team);
  const { SeasonUpdatesLoading, SeasonUpdatesData } = useSelector(
    state => state?.SeasonUpdate
  );
  const [teamArray, setTeamArray] = useState([]);
  const [teamId, setTeamID] = useState(null);

  const renderUpdateItem = ({ item }) => {
    return (
      <UpdateItem
        result={item?.matchScorecard?.result}
        scorecard={item?.matchScorecard}
        opponentTeam={item.opponent?.name}
        time={item.startTime}
        tournament={item.tournament?.name}
        isMatchPlayed={item.isMatchPlayed}
        SeasonUpdateStyle={SeasonUpdateStyle}
        colors={colors}
      />
    );
  };

  useEffect(() => {
    !selectedTeam?.length &&
      setSelectedTeam([
        {
          label: teamData?.data?.[0]?.teamName,
          value: teamData?.data?.[0]?._id,
        },
      ]);
  }, [teamData]);

  useEffect(() => {
    setTeamID(selectedTeam?.[0]?.value || null);
  }, [selectedTeam]);

  useFocusEffect(
    useCallback(() => {
      if (teamId) {
        fetchData(
          `/api/v1/events?endDate=${calculateEventDate(
            0,
            EventDateType.END
          )}&page=1&size=100&type=MATCH&teamId=${teamId}&startDate=${calculateEventDate(
            5,
            EventDateType.START
          )}&sort=DESC&pastEvent=true`,
          SEASON_UPDATE_REQUEST,
          SEASON_UPDATE_SUCCESS,
          SEASON_UPDATE_FAIL,
          null,
          '',
          'GET',
          null,
          EVENT_SERVICE
        );
      }
    }, [teamId])
  );

  useFocusEffect(
    useCallback(() => {
      fetchData2(
        findURL(userData?.type, userData, childInformation),
        TEAM_REQUEST,
        TEAM_SUCCESS,
        TEAM_FAIL,
        null,
        '',
        'GET',
        null,
        FOOTBALL_SERVICE
      );
    }, [userData, childInformation])
  );

  useEffect(() => {
    if (teamData && teamData?.data?.length) {
      const tempTeamArray = [];
      teamData?.data.forEach(teamsData => {
          tempTeamArray.push({
            label: teamsData.teamName,
            value: teamsData._id,
          });        
      });
      setTeamArray(tempTeamArray);
    }
  }, [teamData]);

  const getChildInformation = playerId => {
    fetchChildInformation(
      `/api/v1/sport-profiles?userIds=${playerId}`,
      GET_CHILD_INFORMATION_REQUEST,
      GET_CHILD_INFORMATION_SUCCESS,
      GET_CHILD_INFORMATION_FAILED,
      null,
      '',
      'GET',
      null,
      FOOTBALL_SERVICE
    );
  };

  useEffect(() => {
    selectedChild?.id && getChildInformation(selectedChild.id);
  }, [selectedChild]);

  return (
    <>
      <View style={{ ...SeasonUpdateStyle.topRow, zIndex: 11 }}>
        <Text style={SeasonUpdateStyle.title}>Season Updates</Text>
        <View style={{ ...SeasonUpdateStyle.dropdownView, zIndex: 10 }}>
          <SelectionModal
            title={'Select Team'}
            items={teamArray}
            onCloseHook={setIsTeamModalOpen}
            onSelectItemHook={setSelectedTeam}
            defaultValues={[teamId]}
            isEnableAutoComplete
            selectedItemLabel={selectedTeam?.[0]?.label}
            isModalOpen={isTeamModalOpen}
            enableDefaultLabel
          />
        </View>
      </View>
      {!SeasonUpdatesLoading ? (
        <FlatList
          data={SeasonUpdatesData?.data || []}
          renderItem={renderUpdateItem}
          keyExtractor={item => item._id}
        />
      ) : (
        <ActivitySpinner />
      )}
    </>
  );
};

export default SeasonUpdates;
