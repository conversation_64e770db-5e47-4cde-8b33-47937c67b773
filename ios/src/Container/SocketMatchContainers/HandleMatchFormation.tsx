import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { userRoleType } from '../../constants/constants';
import {
  SET_CURRENT_MATCH_PLAN,
  SET_MATCH_FORMATION_COORDINATES,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import { RootStore } from '../../store/store';

interface IPlayerCoordinateWS {
  coordinateId: string;
  sportsProfileId: string;
}

interface IMappedFormationCoordination {
  [id: string]: IPlayerCoordinateWS;
}

interface IPlayerData {
  firstName: string;
  isAvailable: boolean;
  lastName: string;
  profileImageUrl: string;
  sportsProfileId: string;
}
interface IPlayerCoordinate {
  coordinateId: string;
  sportsProfileId: string;
  x: number;
  y: number;
  code: number;
  playerData?: IPlayerData;
}
interface IMsgObject {
  _id: string;
  timeStamp: string;
  socketMessageType: string;
  matchId: string;
  formationId: string;
  substitutePlayers: string[];
  playerCoordinates: IPlayerCoordinateWS[];
}

const HandleMatchFormation = () => {
  const dispatch = useDispatch();

  const { currentMatchPlan, eventInitialPlayers, matchFormationCoordinates } =
    useSelector((state: any) => state?.matchLog);

  const { matchPlan: playerMatchPlan } = useSelector(
    (state: RootStore) => state?.playerMatchLog
  );

  const { userData } = useSelector((state: RootStore) => state?.auth);

  const isCoach =
    userRoleType.HEAD_COACH === userData?.type ||
    userRoleType.COACH === userData?.type;

  useEffect(() => {
    matchFormationCoordinates?.playerCoordinates &&
      onMatchFormationChange(matchFormationCoordinates);
  }, [matchFormationCoordinates]);

  useEffect(() => {
    return () => {
      dispatch({
        type: SET_MATCH_FORMATION_COORDINATES,
        payload: {
          data: {},
        },
      });
    };
  }, []);

  const setCurrentMatchPlan = (payload: any) => {
    dispatch({
      type: SET_CURRENT_MATCH_PLAN,
      payload: payload,
    });
  };

  const mapFormationCoordination = (payload: IPlayerCoordinateWS[]) => {
    if (!payload) {
      return {};
    } else {
      const formationObject = payload?.reduce(
        (formation: any, item: IPlayerCoordinateWS) =>
          Object.assign(formation, { [item?.coordinateId]: item }),
        {}
      );
      return formationObject;
    }
  };

  const genaratePlayerCoordinates = (
    playerCoordinates: IPlayerCoordinate[],
    mappedFormationCoordination: IMappedFormationCoordination
  ) => {
    return [...playerCoordinates]?.map(playerCoordinate => {
      let coordinateId = playerCoordinate?.coordinateId;
      const tmpPlayerCoordinate = { ...playerCoordinate };
      tmpPlayerCoordinate?.playerData && delete tmpPlayerCoordinate?.playerData;

      if (mappedFormationCoordination?.[coordinateId]) {
        let sportsProfileId =
          mappedFormationCoordination?.[coordinateId]?.sportsProfileId;

        tmpPlayerCoordinate.playerData = eventInitialPlayers?.[sportsProfileId];
      }

      return {
        ...tmpPlayerCoordinate,
      };
    });
  };

  const genarateSubstitutePlayers = (substitutePlayers: string[]) => {
    return substitutePlayers?.map((substitutePlayerId: string) => {
      return {
        ...eventInitialPlayers?.[substitutePlayerId],
      };
    });
  };

  const onMatchFormationChange = (msgObject: IMsgObject) => {
    const mappedFormationCoordination = mapFormationCoordination(
      msgObject?.playerCoordinates
    );
    const substitutePlayers = genarateSubstitutePlayers(
      msgObject?.substitutePlayers
    );
    if (isCoach) {
      let playerCoordinates = genaratePlayerCoordinates(
        [...currentMatchPlan.playerCoordinates],
        mappedFormationCoordination
      );

      setCurrentMatchPlan({
        ...currentMatchPlan,
        playerCoordinates: playerCoordinates,
        substitutePlayers: substitutePlayers,
      });
    } else {
      let playerCoordinates = genaratePlayerCoordinates(
        [...playerMatchPlan?.['playerCoordinates']],
        mappedFormationCoordination
      );

      setCurrentMatchPlan({
        ...(playerMatchPlan as any),
        playerCoordinates: playerCoordinates,
        substitutePlayers: substitutePlayers,
      });
    }
  };

  return <></>;
};

export default HandleMatchFormation;
