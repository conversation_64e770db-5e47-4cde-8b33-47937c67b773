import React, { FC, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { matchSocketTypes, WebSocketParams } from '../../constants/constants';
import WS from '../../hooks/useWSMessages';
import { SET_CONNECTION_ID_MATCH_WS } from '../../store/actionTypes/common/commonActionTypes';
import {
  SET_MATCH_CONCLUDE,
  SET_MATCH_FORMATION_COORDINATES,
  UPDATE_MATCH_SOCKET_MESSAGE,
  UPDATE_MATCH_SOCKET_REVERSE_MESSAGE,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import { RootStore } from '../../store/store';
import { useNetInfo } from '@react-native-community/netinfo';

interface HandleMatchWsType {
  matchId: string;
}

const HandleMatchWs: FC<HandleMatchWsType> = ({ matchId }) => {
  const dispatch = useDispatch();
  const netInfo = useNetInfo();
  const isInternetReachable = netInfo?.isInternetReachable || false;
  const { wsClient, wsIsConnect } = useSelector(
    (state: RootStore) => state.common
  );
  const customWsIsConnect = wsIsConnect?.[WebSocketParams.MATCH_WS];
  const matchWsClient = wsClient?.[WebSocketParams.MATCH_WS];
  const { userData, configurationData } = useSelector(
    (state: RootStore) => state?.auth
  );

  const { Auth } = configurationData || {};
  const { matchHubWSS } = Auth || {};

  const [initiateWS, ws, , onCloseWs] = WS(
    matchHubWSS,
    WebSocketParams.MATCH_WS
  );

  useEffect(() => {
    isInternetReachable && !ws && userData?.id && initiateWS();
  }, [userData, isInternetReachable]);

  useEffect(() => {
    if (matchWsClient && customWsIsConnect) {
      matchWsClient.onmessage = ({ data }) => {
        const msgObject: any = JSON.parse(data.toString());
        //TO_DEBUG purpose
        console.log(msgObject, 'onmessage');

        if (msgObject?.socketMessageType === matchSocketTypes.MATCH_CONCLUDE) {
          dispatch({
            type: SET_MATCH_CONCLUDE,
          });
        }

        if (msgObject?.connectionId) {
          dispatch({
            type: SET_CONNECTION_ID_MATCH_WS,
            payload: {
              data: msgObject?.connectionId,
            },
          });
        }
        if (
          msgObject?.socketMessageType === matchSocketTypes.MATCH_PLAN_UPDATE
        ) {
          dispatch({
            type: SET_MATCH_FORMATION_COORDINATES,
            payload: {
              data: msgObject,
            },
          });
        }
        //update socket message
        if (
          matchSocketTypes.MATCH_ACTIVITY_CREATE ===
          msgObject?.socketMessageType
        ) {
          dispatch({
            type: UPDATE_MATCH_SOCKET_MESSAGE,
            payload: msgObject,
          });
        } else if (
          msgObject?.socketMessageType ===
          matchSocketTypes.MATCH_ACTIVITY_DELETE
        ) {
          dispatch({
            type: UPDATE_MATCH_SOCKET_REVERSE_MESSAGE,
            payload: msgObject,
          });
        }
      };

      //get connection Id call
      if (matchWsClient?.readyState === matchWsClient?.OPEN) {
        matchWsClient?.send(
          JSON.stringify({
            action: 'getConnectionId',
          })
        );
      }
    }
  }, [matchWsClient, customWsIsConnect, matchId]);

  useEffect(() => {
    return () => {
      dispatch({
        type: SET_CONNECTION_ID_MATCH_WS,
        payload: {
          data: null,
        },
      });
      onCloseWs();
    };
  }, []);

  return <></>;
};

export default HandleMatchWs;
