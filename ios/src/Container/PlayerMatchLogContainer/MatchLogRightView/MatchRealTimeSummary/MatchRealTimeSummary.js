import React, { useEffect, useMemo, useState } from 'react';
import { Text, View, FlatList } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import ActivitySpinner from '../../../../components/ActivitySpinner/ActivitySpinner';
import {
  MatchLogActions,
  MATCH_LOG_SYNC_TIME_INTERVAL,
} from '../../../../constants/constants';

import { EVENT_SERVICE } from '../../../../constants/services';
import useApi from '../../../../hooks/useApi';
import {
  PLAYER_MATCH_LOG_ACTIVITIES_FAILED,
  PLAYER_MATCH_LOG_ACTIVITIES_REQUEST,
  PLAYER_MATCH_LOG_ACTIVITIES_RESET,
  PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS,
} from '../../../../store/actionTypes/MatchLog/PlayerMatchLogActions';
import customMatchRealTimeSummaryStyle from './MatchRealTimeSummaryStyle';
import useStyles from '../../../../hooks/useStyles';
import { isTabDevice } from '../../../../config/appConfig';

const MatchRealTimeSummary = () => {
  const MatchRealTimeSummaryStyle = useStyles(customMatchRealTimeSummaryStyle);
  const [fetchData] = useApi();
  const dispatch = useDispatch();
  const [isMatchEnd, setIsMatchEnd] = useState(false);

  const { matchActivity, matchActivityLoading, currentMatch, matchPlan } =
    useSelector(state => state?.playerMatchLog);

  const secondHalfStartLogIndex = useMemo(() => {
    return matchActivity?.findIndex(
      singleMatchLog =>
        singleMatchLog?.activity?.activityCode ===
        MatchLogActions.SECOND_HALF_START
    );
  }, [JSON.stringify(matchActivity)]);

  const getCalculatedTimeDifference = (min, sec, index) => {
    const matchDuration = matchPlan?.duration;
    const elapsedTimeInSeconds = Number(min) * 60 + Number(sec);
    const matchDurationInSeconds = matchDuration * 60;
    const halfTimeInSeconds = matchDurationInSeconds / 2;

    let additionalTime = 0;

    const isActivityAfter2ndHalf =
      secondHalfStartLogIndex !== -1 && index < secondHalfStartLogIndex;

    if (elapsedTimeInSeconds > halfTimeInSeconds && !isActivityAfter2ndHalf) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - halfTimeInSeconds) / 60
      );
    }

    if (
      elapsedTimeInSeconds > matchDurationInSeconds &&
      isActivityAfter2ndHalf
    ) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - matchDurationInSeconds) / 60
      );
    }
    return additionalTime > 0 ? ` ( +${additionalTime} min )` : '';
  };

  // Commented out since implementing websocket
  // useEffect(() => {
  //   let interval;

  //   if (currentMatch?._id && !isMatchEnd) {
  //     interval = setInterval(() => {
  //       loadSummaries(1);
  //     }, 1000 * MATCH_LOG_SYNC_TIME_INTERVAL);
  //   }
  //   return () => clearInterval(interval);
  // }, [currentMatch]);

  useEffect(() => {
    if (matchActivity?.length && matchActivity[0].elapsedTime) {
      const activityCode = matchActivity[0].activity?.activityCode;
      // If match finished, then stop calling
      setIsMatchEnd(activityCode === 'END_GAME');
    }
  }, [matchActivity]);

  useEffect(() => {
    return () =>
      dispatch({
        type: PLAYER_MATCH_LOG_ACTIVITIES_RESET,
      });
  }, []);

  const loadSummaries = pageNo => {
    fetchData(
      `/api/v1/matches/${currentMatch._id}/match-activities?page=${pageNo}&size=200`,
      PLAYER_MATCH_LOG_ACTIVITIES_REQUEST,
      PLAYER_MATCH_LOG_ACTIVITIES_SUCCESS,
      PLAYER_MATCH_LOG_ACTIVITIES_FAILED,
      null,
      '',
      'GET',
      null,
      EVENT_SERVICE
    );
  };

  useEffect(() => {
    if (currentMatch?._id) {
      dispatch({
        type: PLAYER_MATCH_LOG_ACTIVITIES_RESET,
      });

      loadSummaries(1);
    }
  }, [currentMatch]);

  const renderRealTimeSummary = ({ item, index }) => {
    const minNdSec = item?.elapsedTime?.split(' ')[0].split(':');
    const computedElapsedTime = `${
      item.elapsedTime
    } ${getCalculatedTimeDifference(minNdSec[0], minNdSec[1], index)}`;
    return (
      <View style={MatchRealTimeSummaryStyle.matchActionCommentContainer}>
        <Text style={MatchRealTimeSummaryStyle.matchActionCommentTime}>
          {computedElapsedTime}
        </Text>
        <Text style={MatchRealTimeSummaryStyle.matchActionComment}>
          {item.comment}
        </Text>
      </View>
    );
  };

  return (
    <View style={MatchRealTimeSummaryStyle.matchCommentContainerWrapper}>
      {matchActivityLoading && !matchActivity ? (
        <ActivitySpinner />
      ) : (
        <FlatList
          horizontal={isTabDevice() ? false : true}
          data={matchActivity || []}
          renderItem={renderRealTimeSummary}
          keyExtractor={item => item._id}
        />
      )}
    </View>
  );
};

export default MatchRealTimeSummary;
