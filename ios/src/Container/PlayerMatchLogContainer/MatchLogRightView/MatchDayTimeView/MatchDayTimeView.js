import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  FlatList,
  TouchableHighlight,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { SET_CURRENT_PLAYER_LOG_MATCH } from '../../../../store/actionTypes/MatchLog/PlayerMatchLogActions';
import useStyles from '../../../../hooks/useStyles';

import customMatchDayTimeViewStyle from './MatchDayTimeViewStyle';
import { dateTimeUTCConversion } from '../../../../helpers';

const MatchDayTimeView = () => {
  const MatchDayTimeViewStyle = useStyles(customMatchDayTimeViewStyle);
  const [selectedDate, setSelectedDate] = useState(null);
  const [matchTimes, setMatchTimes] = useState([]);
  const [selectedMatch, setSelectedMatch] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const {
    matches,
    matchActivityLoading,
    matchPlanLoading,
    matchFormationLoading,
    matchScoreLoading,
  } = useSelector(state => state?.playerMatchLog);
  const { selectedMatchDetails } = useSelector(state => state.matchPlan);

  const dispatch = useDispatch();

  useEffect(() => {
    setIsLoading(
      matchActivityLoading ||
        matchPlanLoading ||
        matchFormationLoading ||
        matchScoreLoading
    );
  }, [
    matchActivityLoading,
    matchPlanLoading,
    matchFormationLoading,
    matchScoreLoading,
  ]);

  useEffect(() => {
    if (matches?.length) {
      if (selectedMatchDetails) {
        const { monthString, date, dateString } = dateTimeUTCConversion(
          selectedMatchDetails.startTime
        );
        const startDate = `${monthString.substring(0, 3)} ${date}, ${dateString}`;
        const getSelectedMatches = matches.find(
          date => date.startDate == startDate
        );
        setSelectedDate(getSelectedMatches);
        return;
      }
      setSelectedMatch(matches[0]?.matchData[0]);
      setMatchTimes(matches[0]?.matchData);
      setSelectedDate(matches[0]);
    }
  }, [matches]);

  useEffect(() => {
    if (selectedDate) {
      setMatchTimes(selectedDate.matchData);
      setSelectedMatch(selectedDate.matchData[0]);
    }
  }, [selectedDate]);

  useEffect(() => {
    if (matchTimes.length) {
      const selectedEvent =
        (selectedMatchDetails &&
          matchTimes.find(event => selectedMatchDetails._id == event._id)) ||
        matchTimes[0];
      setSelectedMatch(selectedEvent);
    }
  }, [matchTimes]);

  useEffect(() => {
    if (selectedMatch) {
      dispatch({
        type: SET_CURRENT_PLAYER_LOG_MATCH,
        payload: selectedMatch,
      });
    }
  }, [selectedMatch]);

  const renderTimeSlots = ({ item }) => {
    let { startTime, _id } = item;

    return (
      <View key={_id} style={MatchDayTimeViewStyle.timeSlotWrapper}>
        <TouchableHighlight onPress={() => setSelectedMatch(item)}>
          <View style={MatchDayTimeViewStyle.timeSlot}>
            <Text
              style={
                _id === selectedMatch?._id
                  ? MatchDayTimeViewStyle.timeSlotTextSelected
                  : MatchDayTimeViewStyle.timeSlotTimeText
              }
            >
              {startTime}
            </Text>
            <Text style={MatchDayTimeViewStyle.timeSlotTextDivider}> | </Text>
          </View>
        </TouchableHighlight>
      </View>
    );
  };

  const renderMatchDays = ({ item }) => {
    return (
      <TouchableOpacity
        key={item.startDate}
        onPress={() => !isLoading && setSelectedDate(item)}
      >
        <View
          style={
            item.startDate === selectedDate?.startDate
              ? MatchDayTimeViewStyle.selectedMatchDay
              : MatchDayTimeViewStyle.matchDay
          }
        >
          <Text style={MatchDayTimeViewStyle.matchDayText}>
            {item.startDate}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <View style={MatchDayTimeViewStyle.matchDaySet}>
        <FlatList
          horizontal
          data={matches || []}
          extraData={[selectedDate, isLoading]}
          renderItem={renderMatchDays}
          keyExtractor={item => item.startDate}
        />
      </View>
      <View style={MatchDayTimeViewStyle.timeSlotContainer}>
        <Text style={MatchDayTimeViewStyle.timeSlotText}>Time</Text>
        <FlatList
          horizontal
          data={matchTimes || []}
          extraData={selectedMatch}
          renderItem={renderTimeSlots}
          keyExtractor={item => item._id}
        />
      </View>
    </View>
  );
};

export default MatchDayTimeView;
