import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const PlayerMatchLogScreenStyles = colors => ({
  contentWrapper: isTabDevice()
    ? {}
    : {
        width: wp('100%'),
        height: hp('115%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  mobileScroll: isTabDevice()
    ? {}
    : {
        width: wp('100%'),
        height: hp('100%'),
        marginBottom: hp('10%'),
      },
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
      },
  leftView: isTabDevice()
    ? {
        width: wp('35%'),
        height: hp('77%'),
        paddingLeft: wp('2%'),
      }
    : {
        width: wp('100%'),
      },
  teamsContainer: isTabDevice()
    ? {}
    : {
        width: wp('100%'),
      },
  matchFormationContainer: isTabDevice()
    ? {}
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('63%'),
        height: hp('90%'),
        paddingLeft: wp('2%'),
      }
    : {
        width: wp('100%'),
      },
  matchDayTimContainer: {
    // margin: 15,
  },
  matchScoreContainer: isTabDevice()
    ? {
        marginTop: wp('4%'),
        marginBottom: wp('2%'),
      }
    : {
        marginTop: wp('2%'),
        marginBottom: wp('2%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  matchRealTimeSummaryContainer: isTabDevice()
    ? {
        // margin: 15,
      }
    : {
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  playerViewSummaryButton: isTabDevice()
    ? {
        margin: 15,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
      }
    : {
        margin: 15,
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: wp('1.5%'),
        borderRadius: wp('1.5%'),
        flexDirection: 'row',
        width: wp('18%'),
        marginRight: wp('1.5%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: wp('3.5%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('3%'),
        borderRadius: wp('3.5%'),
        flexDirection: 'row',
        width: wp('30%'),
        marginRight: wp('1.5%'),
      },
  buttonText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      }
    : {
        fontSize: wp('3%'),
        color: colors.white,
        fontFamily: 'Poppins-Regular',
      },
});
export default PlayerMatchLogScreenStyles;
