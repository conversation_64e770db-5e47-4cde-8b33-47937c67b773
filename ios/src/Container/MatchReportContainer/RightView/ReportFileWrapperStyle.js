import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../../config/appConfig';

const ReportFileWrapperStyle = colors => ({
  fileWrapper: isTabDevice()
    ? {
        flex: 1,
        marginTop: wp('2%'),
        width: '120%',
      }
    : {
        flex: 1,
        marginTop: wp('2%'),
      },
  list: isTabDevice()
    ? {}
    : {
        // height: wp('40%'),
        marginBottom: wp('100%'),
      },
});
export default ReportFileWrapperStyle;
