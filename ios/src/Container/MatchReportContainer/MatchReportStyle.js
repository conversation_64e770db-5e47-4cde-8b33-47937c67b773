import { StyleSheet, Text, View, Dimensions } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const MatchReportStyle = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
      },
  leftView: isTabDevice()
    ? {
        width: wp('26%'),
        marginTop: wp('1%'),
        paddingLeft: wp('2%'),
      }
    : {
        width: wp('100%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('70%'),
        height: hp('90%'),
        paddingLeft: wp('2%'),
        marginTop: wp('0.5%'),
        borderLeftWidth: 1,
        borderLeftColor: colors.tileBackground,
      }
    : {
        width: wp('100%'),
        height: '100%',
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        marginTop: wp('1%'),
        borderTopWidth: 1,
        borderTopColor: colors.tileBackground,
      },
});
export default MatchReportStyle;
