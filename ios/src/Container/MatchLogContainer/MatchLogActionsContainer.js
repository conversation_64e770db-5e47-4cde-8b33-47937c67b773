import React, {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
  useMemo,
} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { AntDesign, EvilIcons } from '@expo/vector-icons';
import { isTabDevice } from '../../config/appConfig';
import * as Localization from 'expo-localization';
import uuid from 'react-native-uuid';
import Field from '../../components/MatchPlan/Field';

import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import useApi from '../../hooks/useApi';
import customMatchLogContainerStyles from './MatchLogContainerStyles';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {
  CLEAR_MATCH_SOCKET_MESSAGES,
  FETCH_ONGOING_MATCH_SUCCESS,
  MATCH_LOG_ADD_NEW_ACTION,
  MATCH_LOG_BULK_DELETE_FAILED,
  MATCH_LOG_BULK_DELETE_REQUEST,
  MATCH_LOG_BULK_DELETE_SUCCESS,
  MATCH_LOG_MATCH_FORMATION_FAILED,
  MATCH_LOG_MATCH_FORMATION_REQUEST,
  MATCH_LOG_MATCH_FORMATION_SUCCESS,
  MATCH_LOG_MATCH_PLAN_FAILED,
  MATCH_LOG_MATCH_PLAN_REQUEST,
  MATCH_LOG_MATCH_PLAN_SUCCESS,
  MATCH_LOG_REMOVE_LOG_ITEM,
  MATCH_LOG_SAVE_ACTIVITY_FAILED,
  MATCH_LOG_SAVE_ACTIVITY_REQUEST,
  MATCH_LOG_SAVE_ACTIVITY_SUCCESS,
  MATCH_LOG_SAVE_GAME_FAILED,
  MATCH_LOG_SAVE_GAME_REQUEST,
  MATCH_LOG_SAVE_GAME_SUCCESS,
  MATCH_LOG_SET_PLAYER_LIST,
  MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES,
  MATCH_LOG_UPDATE_SCORE,
  CLOSE_INVALID_ACTION,
  SET_CURRENT_MATCH_PLAN,
} from '../../store/actionTypes/MatchLog/MatchLogActions';
import SocketMatchLogContainer, {
  matchActions,
} from '../SocketMatchContainers/SocketMatchLogContainer';
import {
  forcedModalType,
  invalidActionMessages,
  MatchLogActions,
  MatchLogStages,
  MatchLogText,
  matchLogValidErrorMessage,
  userRoleType,
} from '../../constants/constants';
import { addLeadingZeros } from '../../helpers';
import UnsavedChangesModal from '../../components/modal/UnsavedChangesModal/UnsavedChangesModal';
import ProfileImage from '../../components/ProfileImage/ProfileImage';
import ActivitySpinner from '../../components/ActivitySpinner/ActivitySpinner';
import { useNavigation } from '@react-navigation/native';
import useStyles from '../../hooks/useStyles';
import useColors from '../../hooks/useColors';
import ForceModal from '../../components/modal/ForceModal/ForceModal';
import useApiPromise from '../../hooks/useApiPromise';

const MatchLogActionsContainer = ({
  seconds,
  minutes,
  isRunning,
  startTimer,
  pauseTimer,
  resumeTimer,
  resetTimer,
  setTime,
  setGameStatus,
  setGameStage,
  saveLog,
  deleteActivity,
  halfTimeDurationMillis,
  syncAllData,
  saveBulkData,
  refreshLoading,
  setRefreshLoading,
  disableAction,
  hideLoading,
  jerseyNumbers,
}) => {
  const { v4: uuidv4 } = uuid;
  const MatchLogContainerStyles = useStyles(customMatchLogContainerStyles);
  const colors = useColors();

  const [fetchData] = useApi();
  const [fetchMatchPlan] = useApi();
  const [saveData] = useApi();
  const [reverseData] = useApi();
  const { connectionIdMatchWs } = useSelector(state => state?.common);
  const [fetchUserDetails] = useApiPromise();

  const dispatch = useDispatch();
  const navigation = useNavigation();
  const matchLogActivityFlatlist = useRef(null);
  const {
    selectedEvent,
    eventLogs = {},
    selectedEventIndex,
    activities = [],
    selectedTeamId,
    matchesLoading,
    isInvalidRequest,
    isLoadingMatchActivity,
    eventInitialPlayers,
    currentMatchPlan,
  } = useSelector(state => state.matchLog);
  const { selectedTeam } = useSelector(state => state?.team);
  const { userData } = useSelector(state => state?.auth);
  const isCoach = userRoleType.COACH === userData?.type;

  const {
    isGameOngoing,
    score,
    opponentDetails,
    matchPlan = {},
    matchLog = [],
    formation,
    formationNames,
    playerList,
    currentStage,
    saveSuccess,
    saveLoading,
    logsLoading,
    logsLoaded,
    matchPlanLoading,
    unsyncedLogs,
    unsyncedDeletedLogs,
    bulkDeleteLoading,
    bulkSaveLoading,
  } = eventLogs[selectedTeamId]?.[selectedEventIndex] || {};

  const [selectedPlayer, setSelectedPlayer] = useState(null);
  const [selectedCoordinate, setSelectedCoordinate] = useState(null);
  const [selectedSub, setSelectedSub] = useState(null);
  const [selectedAction, setSelectedAction] = useState(null);
  const [isActionButtonsDisabled, setIsActionButtonsDisabled] = useState(true);
  const [isOpponentDisabled, setIsOpponentDisabled] = useState(true);
  const [isOpponent, setIsOpponent] = useState(false);
  const [isSaveModalVisible, setIsSaveModalVisible] = useState(false);
  const [swapPlayer, setSwapPlayer] = useState(null);
  const [swapCoordinate, setSwapCoordinate] = useState(null);
  const [refreshList, setRefreshList] = useState(true);
  const [matchSaveTriggered, setMatchSaveTriggered] = useState(false);
  const [isSyncInProgress, setIsSyncInProgress] = useState(false);
  const [syncErrorMessage, setSyncErrorMessage] = useState('');
  const [substitutePlayerTooltip, setSubstitutePlayerToolTip] = useState(null);

  const setCurrentMatchPlan = payload => {
    dispatch({
      type: SET_CURRENT_MATCH_PLAN,
      payload: payload,
    });
  };

  const isUnsyncedDataAvailable = useMemo(() => {
    if (unsyncedLogs?.length || unsyncedDeletedLogs?.length) {
      return true;
    } else {
      return false;
    }
  }, [unsyncedLogs, unsyncedDeletedLogs]);

  useEffect(() => {
    if (isGameOngoing && isRunning && isActionButtonsDisabled) {
      setIsActionButtonsDisabled(false);
    } else if (!isActionButtonsDisabled) {
      setIsActionButtonsDisabled(true);
    }
  }, [isGameOngoing, isRunning]);

  useEffect(() => {
    refreshLoading && selectedEvent?._id && loadMatchPlan();
  }, [refreshLoading]);

  const getElapsedTime = () => {
    return `${addLeadingZeros(minutes)}:${addLeadingZeros(seconds)} min`;
  };

  useEffect(() => {
    if (selectedEvent?.concluded && !isActionButtonsDisabled) {
      setIsActionButtonsDisabled(true);
    }
    selectedEvent?._id && loadMatchPlan();
  }, [selectedEvent]);

  const loadMatchPlan = () => {
    fetchMatchPlan(
      `/api/v1/matches/${selectedEvent._id}/match-plan`,
      MATCH_LOG_MATCH_PLAN_REQUEST,
      MATCH_LOG_MATCH_PLAN_SUCCESS,
      MATCH_LOG_MATCH_PLAN_FAILED,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    );
  };

  useEffect(() => {
    if (matchPlan?.playerCoordinates) {
      let players = {};

      matchPlan.playerCoordinates.forEach(position => {
        if (position?.playerData) {
          players[position.playerData.sportsProfileId] = `${
            position.playerData.firstName?.trim() || ''
          } ${
            position?.playerData?.lastName
              ?.trim()
              ?.slice(0, 1)
              ?.toUpperCase() || ''
          }`;
        }
      });

      matchPlan.substitutePlayers?.forEach(player => {
        players[player.sportsProfileId] = `${player.firstName?.trim() || ''} ${
          player?.lastName?.trim()?.slice(0, 1).toUpperCase() || ''
        }`;
      });

      dispatch({
        type: MATCH_LOG_SET_PLAYER_LIST,
        payload: players,
      });
      setCurrentMatchPlan(JSON.parse(JSON.stringify(matchPlan)));
    } else if (
      (currentMatchPlan && Object.keys(currentMatchPlan).length) ||
      !currentMatchPlan
    ) {
      setCurrentMatchPlan({});
    }

    matchPlan?.formationId &&
      fetchData(
        `/api/v1/formations/${matchPlan.formationId}`,
        MATCH_LOG_MATCH_FORMATION_REQUEST,
        MATCH_LOG_MATCH_FORMATION_SUCCESS,
        MATCH_LOG_MATCH_FORMATION_FAILED,
        null,
        null,
        'GET',
        false,
        FOOTBALL_SERVICE
      );
  }, [matchPlan]);

  const getAction = action => {
    return activities.find(activity => activity.code === action);
  };
  const getActionById = actionId => {
    return activities.find(activity => activity._id === actionId);
  };

  const generateComment = (code, isSwap) => {
    let commentTemplate = MatchLogText[code];

    if (code === MatchLogActions.SUB_IN && selectedSub) {
      return commentTemplate.replace('{PLAYER}', playerList[selectedSub] || '');
    }

    if (code === MatchLogActions.MOVE_PLAYER) {
      if (swapPlayer && isSwap) {
        return commentTemplate
          .replace('{PLAYER}', playerList[swapPlayer] || '')
          .replace(
            '{POSITION1}',
            formationNames[swapCoordinate.coordinateId] || ''
          )
          .replace(
            '{POSITION2}',
            formationNames[selectedCoordinate.coordinateId] || ''
          );
      } else if (selectedPlayer) {
        return commentTemplate
          .replace('{PLAYER}', playerList[selectedPlayer] || '')
          .replace(
            '{POSITION1}',
            formationNames[selectedCoordinate.coordinateId] || ''
          )
          .replace(
            '{POSITION2}',
            formationNames[swapCoordinate.coordinateId] || ''
          );
      }
    }

    if (selectedPlayer) {
      if (isOpponent) {
        return commentTemplate.replace('{PLAYER}', 'Opponent Team');
      } else {
        return commentTemplate.replace(
          '{PLAYER}',
          playerList[selectedPlayer] || ''
        );
      }
    }
    return commentTemplate;
  };

  const getTimeDifference = () => {
    const matchDuration = matchPlan?.duration;

    const elapsedTimeInSeconds = minutes * 60 + seconds;
    const matchDurationInSeconds = matchDuration * 60;
    const halfTimeInSeconds = halfTimeDurationMillis / 1000;

    if (!matchDuration) return '';
    let additionalTime = 0;
    if (
      currentStage === MatchLogStages.GAME_STARTED &&
      elapsedTimeInSeconds > halfTimeInSeconds
    ) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - halfTimeInSeconds) / 60
      );
    } else if (
      currentStage === MatchLogStages.SECOND_HALF_STARTED &&
      elapsedTimeInSeconds > matchDurationInSeconds
    ) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - matchDurationInSeconds) / 60
      );
    }

    return additionalTime > 0 ? ` ( +${additionalTime} min )` : '';
  };

  const addToLog = (
    data,
    sendToServer = true,
    msgObject = null,
    clearState = false
  ) => {
    dispatch({
      type: MATCH_LOG_ADD_NEW_ACTION,
      payload: data,
    });
    sendToServer && !msgObject && saveLog(data);
    if (clearState) {
      dispatch({
        type: CLEAR_MATCH_SOCKET_MESSAGES,
      });
    }
  };

  const onSwapPlayers = toPlayer => {
    const fromCoordinateIndex = currentMatchPlan?.playerCoordinates?.findIndex(
      position => position?.coordinateId === toPlayer?.fromCoordinateId
    );
    const toCoordinateIdIndex = currentMatchPlan?.playerCoordinates?.findIndex(
      position => position?.coordinateId === toPlayer?.toCoordinateId
    );

    const fromPlayerData =
      currentMatchPlan?.playerCoordinates?.[fromCoordinateIndex]?.playerData;
    const toPlayerData =
      currentMatchPlan?.playerCoordinates?.[toCoordinateIdIndex]?.playerData;

    let updatedPlayerCoordinates = [...currentMatchPlan?.playerCoordinates];
    updatedPlayerCoordinates[fromCoordinateIndex].playerData = toPlayerData;
    updatedPlayerCoordinates[toCoordinateIdIndex].playerData = fromPlayerData;

    onUpdateFormationCoordinates(updatedPlayerCoordinates);
  };
  const handleSocketSubInSubOut = (subInSocketMsg, subOutSocketMsg) => {
    let bulkData = [];

    if (subOutSocketMsg) {
      const {
        _id,
        activityId,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId = null,
      } = subOutSocketMsg;

      bulkData.push({
        _id,
        activityId,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId,
      });
    }

    if (subInSocketMsg) {
      const {
        _id,
        activityId,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId = null,
        toCoordinateId,
      } = subInSocketMsg;

      bulkData.push({
        _id,
        activityId,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId,
      });
    }

    bulkData.forEach(dataItem => {
      addToLog(dataItem, false, null, true);
    });
  };

  const onRedCardReverse = async item => {
    const selectedPlayer = eventInitialPlayers?.[item.playerId];
    const playerData = {
      firstName: selectedPlayer?.firstName,
      isAvailable: true,
      lastName: selectedPlayer?.lastName,
      profileImageUrl: selectedPlayer?.profileImageUrl,
      sportsProfileId: item.playerId,
    };

    let updatedPlayerCoordinates = [...currentMatchPlan?.playerCoordinates].map(
      playerCoordinate => {
        let tmpPlayerCoordinate = playerCoordinate;
        if (
          tmpPlayerCoordinate.coordinateId == item.fromCoordinateId &&
          playerData
        ) {
          tmpPlayerCoordinate.playerData = playerData;
        }
        return {
          ...tmpPlayerCoordinate,
        };
      }
    );

    onUpdateFormationCoordinates(updatedPlayerCoordinates);
  };

  const onUpdateFormationCoordinates = updatedPlayerCoordinates => {
    setCurrentMatchPlan({
      ...currentMatchPlan,
      playerCoordinates: updatedPlayerCoordinates,
    });
    dispatch({
      type: MATCH_LOG_MATCH_PLAN_SUCCESS,
      payload: {
        ...matchPlan,
        playerCoordinates: updatedPlayerCoordinates,
      },
    });
  };

  const swapSubPlayers = (playerId, subId) => {
    if (!playerId || !subId || !currentMatchPlan) {
      return;
    }
    let selectedFieldPlayer = null;
    let playerIndex = -1;
    let subIndex = -1;
    let selectedSubPlayer = null;
    playerIndex = currentMatchPlan?.playerCoordinates?.findIndex(
      position => position?.playerData?.sportsProfileId === playerId
    );

    selectedFieldPlayer =
      currentMatchPlan?.playerCoordinates?.[playerIndex]?.playerData;

    subIndex = currentMatchPlan?.substitutePlayers?.findIndex(
      player => player.sportsProfileId === subId
    );

    selectedSubPlayer = currentMatchPlan?.substitutePlayers?.[subIndex];

    let newCoordinates = [...currentMatchPlan?.playerCoordinates];
    let newSubs = [...currentMatchPlan?.substitutePlayers];

    if (selectedFieldPlayer && selectedSubPlayer) {
      newCoordinates[playerIndex].playerData = selectedSubPlayer;
      newSubs[subIndex] = selectedFieldPlayer;
      setCurrentMatchPlan({
        ...currentMatchPlan,
        playerCoordinates: newCoordinates,
        substitutePlayers: newSubs,
      });
      dispatch({
        type: MATCH_LOG_MATCH_PLAN_SUCCESS,
        payload: {
          ...matchPlan,
          playerCoordinates: newCoordinates,
          substitutePlayers: newSubs,
        },
      });
    }
  };

  useEffect(() => {
    if (
      selectedSub &&
      selectedPlayer &&
      selectedAction &&
      selectedAction === MatchLogActions.SUB_OUT
    ) {
      const timeStamp = new Date().toISOString();
      const elapsedTime = `${getElapsedTime()} ${getTimeDifference()}`;
      const codeOut = MatchLogActions.SUB_OUT;
      const actionOut = getAction(codeOut);
      let bulkData = [];

      bulkData.push({
        activityId: actionOut?._id,
        timeStamp: timeStamp,
        elapsedTime: elapsedTime,
        playerId: selectedPlayer,
        type: MatchLogActions.PLAYER_MOVE_ACTIVITY,
        comment: generateComment(codeOut),
        performedByOpponent: false,
        fromCoordinateId: selectedCoordinate.coordinateId,
        toCoordinateId: null,
        uuid: uuidv4(),
        performedByConnectionId: connectionIdMatchWs,
      });

      const codeIn = MatchLogActions.SUB_IN;
      const actionIn = getAction(codeIn);

      bulkData.push({
        activityId: actionIn?._id,
        timeStamp: timeStamp,
        elapsedTime: elapsedTime,
        playerId: selectedSub,
        type: MatchLogActions.PLAYER_MOVE_ACTIVITY,
        comment: generateComment(codeIn),
        performedByOpponent: false,
        fromCoordinateId: null,
        toCoordinateId: selectedCoordinate.coordinateId,
        uuid: uuidv4(),
        performedByConnectionId: connectionIdMatchWs,
      });

      bulkData.forEach(dataItem => {
        addToLog(dataItem, false);
      });
      saveBulkData(bulkData);
      setSelectedPlayer(null);
      setSelectedSub(null);
      setSelectedAction(null);
      setSelectedCoordinate(null);
      swapSubPlayers(selectedPlayer, selectedSub);
    }
  }, [selectedSub]);

  useEffect(() => {
    if (matchLog?.length) {
      const codes = matchLog.map(i => getActionById(i.activityId)?.code);
      if (codes.includes(MatchLogActions.END_GAME)) {
        pauseTimer();
        setGameStatus(false);
        setGameStage(MatchLogStages.GAME_ENDED);
      } else if (codes.includes(MatchLogActions.SYSTEM_END_GAME)) {
        pauseTimer();
        setGameStatus(false);
        setGameStage(MatchLogStages.GAME_ENDED);
      } else if (codes.includes(MatchLogActions.SECOND_HALF_START)) {
        resumeTimer();
        setGameStage(MatchLogStages.SECOND_HALF_STARTED);
        setGameStatus(true);
      } else if (codes.includes(MatchLogActions.FIRST_HALF_END)) {
        pauseTimer();
        setGameStage(MatchLogStages.FIRST_HALF_ENDED);
        setGameStatus(true);
      } else if (codes.includes(MatchLogActions.START_GAME)) {
        startTimer();
        setGameStage(MatchLogStages.GAME_STARTED);
        setGameStatus(true);
      } else {
        pauseTimer();
      }
    } else {
      pauseTimer();
    }
  }, [matchLog, activities]);

  const updateScore = (isOurs, isAdd) => {
    dispatch({
      type: MATCH_LOG_UPDATE_SCORE,
      payload: {
        isOurs,
        isAdd,
      },
    });
  };

  const handleOpponentActionBtn = selectedActionCode => {
    if (!selectedActionCode) {
      setIsOpponentDisabled(true);
      return;
    }

    if (
      selectedActionCode !== MatchLogActions.SUB_OUT &&
      selectedActionCode !== MatchLogActions.SUB_IN &&
      selectedActionCode !== MatchLogActions.MOVE_PLAYER
    ) {
      setIsOpponentDisabled(false);
    } else {
      setIsOpponentDisabled(true);
    }
  };

  const onActionButtonClicked = code => {
    if (code === selectedAction) {
      setSelectedAction(null);
      handleOpponentActionBtn(null);
    } else {
      setSelectedAction(code);
      handleOpponentActionBtn(code);
    }
    setSelectedPlayer(null);
    setSelectedCoordinate(null);
    setSelectedSub(null);
  };

  useEffect(() => {
    selectedPlayer &&
      selectedAction &&
      !selectedSub &&
      !swapPlayer &&
      executeAction(selectedAction);
  }, [selectedPlayer]);

  useEffect(() => {
    selectedAction &&
      selectedAction === MatchLogActions.MOVE_PLAYER &&
      !selectedSub &&
      swapCoordinate &&
      selectedCoordinate &&
      swapPlayerPositions();
  }, [swapPlayer, swapCoordinate]);

  const swapPlayerPositionsSocket = (firstSocketMsg, secondSocketMsg) => {
    let bulkData = [];

    if (firstSocketMsg) {
      const {
        _id,
        activityId,
        code,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId,
      } = firstSocketMsg;

      bulkData.push({
        _id,
        activityId,
        code,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId,
      });
    }

    if (Object.keys(secondSocketMsg)?.length) {
      const {
        _id,
        activityId,
        code,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId,
      } = secondSocketMsg;

      bulkData.push({
        _id,
        activityId,
        code,
        timeStamp,
        elapsedTime,
        playerId,
        type,
        comment,
        performedByOpponent,
        fromCoordinateId,
        toCoordinateId,
      });
    }

    bulkData.forEach(dataItem => {
      addToLog(dataItem, false, null, true);
    });
  };

  const swapPlayerPositions = () => {
    if (selectedPlayer === swapPlayer) {
      setSwapPlayer(null);
      setSwapCoordinate(null);
      setSelectedPlayer(null);
      setSelectedCoordinate(null);
      return;
    }
    const action = getAction(selectedAction);
    const timeStamp = new Date().toISOString();
    const newCoordinates = currentMatchPlan.playerCoordinates.map(c => {
      if (c.coordinateId === selectedCoordinate.coordinateId) {
        return {
          ...c,
          playerData: swapCoordinate.playerData,
        };
      } else if (c.coordinateId === swapCoordinate.coordinateId) {
        return {
          ...c,
          playerData: selectedCoordinate.playerData,
        };
      }
      return c;
    });

    let bulkData = [];

    if (swapPlayer) {
      bulkData.push({
        activityId: action?._id,
        code: selectedAction,
        timeStamp,
        elapsedTime: `${getElapsedTime()} ${getTimeDifference()}`,
        playerId: swapPlayer,
        type: action?.type,
        comment: generateComment(selectedAction, true),
        performedByOpponent: false,
        fromCoordinateId: swapCoordinate?.coordinateId,
        toCoordinateId: selectedCoordinate?.coordinateId,
        uuid: uuidv4(),
        performedByConnectionId: connectionIdMatchWs,
      });
    }
    if (selectedPlayer) {
      bulkData.push({
        activityId: action?._id,
        code: selectedAction,
        timeStamp,
        elapsedTime: `${getElapsedTime()} ${getTimeDifference()}`,
        playerId: selectedPlayer,
        type: action?.type,
        comment: generateComment(selectedAction),
        performedByOpponent: false,
        fromCoordinateId: selectedCoordinate?.coordinateId,
        toCoordinateId: swapCoordinate?.coordinateId,
        uuid: uuidv4(),
        performedByConnectionId: connectionIdMatchWs,
      });
    }

    bulkData.forEach(dataItem => {
      addToLog(dataItem, false);
    });
    saveBulkData(bulkData);
    setCurrentMatchPlan({
      ...currentMatchPlan,
      playerCoordinates: newCoordinates,
    });
    setSwapPlayer(null);
    setSwapCoordinate(null);
    setSelectedPlayer(null);
    setSelectedCoordinate(null);
    setSelectedAction(null);
    dispatch({
      type: MATCH_LOG_MATCH_PLAN_SUCCESS,
      payload: {
        ...matchPlan,
        playerCoordinates: newCoordinates,
      },
    });
  };

  const disableRedCardPlayer = playerId => {
    const newPlayerCoordinates = currentMatchPlan?.playerCoordinates?.map(
      coordinate => {
        if (coordinate?.playerData?.sportsProfileId === playerId) {
          delete coordinate.playerData;
        }
        return coordinate;
      }
    );
    setCurrentMatchPlan({
      ...currentMatchPlan,
      playerCoordinates: newPlayerCoordinates,
    });
    dispatch({
      type: MATCH_LOG_MATCH_PLAN_SUCCESS,
      payload: {
        ...matchPlan,
        playerCoordinates: newPlayerCoordinates,
      },
    });
  };

  const executeActionSocket = (code, msgObject = null) => {
    const action = getAction(code);
    const timeStamp = new Date().toISOString();
    let redCardLog = undefined;

    if (code === MatchLogActions.GOAL_SCORED) {
      if (msgObject.performedByOpponent) {
        updateScore(false, true);
      } else {
        updateScore(true, true);
      }
    } else if (code === MatchLogActions.GOAL_CONCEDED) {
      if (msgObject.performedByOpponent) {
        updateScore(true, true);
      } else {
        updateScore(false, true);
      }
    }

    if (
      code === MatchLogActions.SUB_OUT ||
      code === MatchLogActions.MOVE_PLAYER
    ) {
      return;
    }

    if (code === MatchLogActions.RED_CARD) {
      redCardLog = {
        fromCoordinateId: msgObject?.fromCoordinateId,
        toCoordinateId: null,
        type: MatchLogActions.PLAYER_MOVE_ACTIVITY,
      };
      !msgObject?.performedByOpponent &&
        disableRedCardPlayer(msgObject?.playerId);
    }

    let tmpMatchLog = {
      activityId: action?._id,
      code: code,

      _id: msgObject?._id,
      timeStamp: msgObject?.timeStamp,
      elapsedTime: msgObject?.elapsedTime,
      playerId: msgObject?.performedByOpponent ? null : msgObject?.playerId,
      type: msgObject?.performedByOpponent ? 'ACTIVITY' : msgObject?.type,
      performedByOpponent: msgObject?.performedByOpponent,
      comment: msgObject?.comment,
    };

    if (redCardLog) {
      tmpMatchLog = { ...tmpMatchLog, ...redCardLog };
    }

    addToLog(tmpMatchLog, true, msgObject, true);
  };

  const executeAction = code => {
    const action = getAction(code);
    const timeStamp = new Date().toISOString();
    let redCardLog = undefined;

    if (code === MatchLogActions.GOAL_SCORED) {
      if (isOpponent) {
        updateScore(false, true);
      } else {
        updateScore(true, true);
      }
    } else if (code === MatchLogActions.GOAL_CONCEDED) {
      if (isOpponent) {
        updateScore(true, true);
      } else {
        updateScore(false, true);
      }
    }

    if (
      code === MatchLogActions.SUB_OUT ||
      code === MatchLogActions.MOVE_PLAYER
    ) {
      return;
    }

    if (code === MatchLogActions.RED_CARD) {
      redCardLog = {
        fromCoordinateId: selectedCoordinate?.coordinateId,
        toCoordinateId: null,
        type: MatchLogActions.PLAYER_MOVE_ACTIVITY,
      };
      !isOpponent && disableRedCardPlayer(selectedPlayer);
    }

    let tmpMatchLog = {
      activityId: action?._id,
      code: code,
      timeStamp,
      elapsedTime: `${getElapsedTime()} ${getTimeDifference()}`,
      playerId: isOpponent ? null : selectedPlayer,
      type: isOpponent ? 'ACTIVITY' : action?.type,
      comment: generateComment(code),
      performedByOpponent: isOpponent,
      uuid: uuidv4(),
      performedByConnectionId: connectionIdMatchWs,
    };

    if (redCardLog) {
      tmpMatchLog = { ...tmpMatchLog, ...redCardLog };
    }

    addToLog(tmpMatchLog);
    setSelectedPlayer(null);
    setSelectedCoordinate(null);
    setIsOpponent(false);
    setSelectedAction(null);
  };

  const saveActivity = data => {
    const matchId = selectedEvent?._id;
    saveData(
      `/api/v1/matches/${matchId}/match-activities`,
      MATCH_LOG_SAVE_ACTIVITY_REQUEST,
      MATCH_LOG_SAVE_ACTIVITY_SUCCESS,
      MATCH_LOG_SAVE_ACTIVITY_FAILED,
      data,
      null,
      'POST',
      false,
      EVENT_SERVICE,
      data
    );
  };

  const onSingleDeleteSwap = toActivtyId => {
    toActivtyId && deleteBulkLog([toActivtyId]);
  };

  const onDeleteSubInOutSwap = (toActivtyId, fromActivtyId) => {
    toActivtyId && fromActivtyId && deleteBulkLog([toActivtyId, fromActivtyId]);
  };

  const deleteBulkLog = matchActivityIds => {
    const matchId = selectedEvent?._id;
    matchId &&
      matchActivityIds?.length > 0 &&
      reverseData(
        `/api/v1/matches/${matchId}/match-activities/bulk`,
        MATCH_LOG_BULK_DELETE_REQUEST,
        MATCH_LOG_BULK_DELETE_SUCCESS,
        MATCH_LOG_BULK_DELETE_FAILED,
        matchActivityIds,
        null,
        'DELETE',
        false,
        EVENT_SERVICE,
        matchActivityIds
      );
  };

  const playerClicked = positionId => {
    if (!selectedAction) return;
    const position = currentMatchPlan.playerCoordinates.find(
      position => position.coordinateId === positionId
    );

    if (selectedCoordinate && selectedAction === MatchLogActions.MOVE_PLAYER) {
      setSwapPlayer(position?.playerData?.sportsProfileId);
      setSwapCoordinate(position);
      return;
    }

    setSelectedPlayer(position?.playerData?.sportsProfileId);
    setSelectedCoordinate(position);
    setSwapPlayer(null);
    setSwapCoordinate(null);
    setSelectedSub(null);
  };

  const substitutePlayerClicked = player => {
    if (selectedPlayer && selectedAction) {
      setSelectedSub(player?.sportsProfileId);
      setSubstitutePlayerToolTip(null);
    } else {
      setSubstitutePlayerToolTip(
        player?.sportsProfileId === substitutePlayerTooltip?.sportsProfileId
          ? null
          : player
      );
    }
  };

  const onItemRemoveFromSocket = (msgObject, index) => {
    const action = getActionById(msgObject?.activityId);

    if (action?.code) {
      const { code } = action;

      switch (code) {
        case MatchLogActions.GOAL_SCORED:
          if (msgObject?.performedByOpponent) {
            updateScore(false, false);
          } else {
            updateScore(true, false);
          }
          break;
        case MatchLogActions.GOAL_CONCEDED:
          if (msgObject?.performedByOpponent) {
            updateScore(true, false);
          } else {
            updateScore(false, false);
          }
          break;
        case MatchLogActions.START_GAME:
          resetTimer();
          setTime(0);
          setGameStatus(false);
          setGameStage(MatchLogStages.NOT_STARTED);
          break;
        case MatchLogActions.END_GAME:
          resumeTimer();
          setGameStatus(true);
          setGameStage(MatchLogStages.SECOND_HALF_STARTED);
          break;
        case MatchLogActions.FIRST_HALF_END:
          resumeTimer();
          setGameStage(MatchLogStages.GAME_STARTED);
          break;
        case MatchLogActions.SECOND_HALF_START:
          pauseTimer();
          setGameStage(MatchLogStages.FIRST_HALF_ENDED);
          break;
        default:
          break;
      }

      dispatch({
        type: MATCH_LOG_REMOVE_LOG_ITEM,
        payload: index,
      });
    }
  };

  const onItemRemoveClick = (item, index) => {
    const action = getActionById(item?.activityId);

    let correspondingLogItem = null;
    let correspondingLogItemIndex = null;

    const findOtherSubItem = code => {
      const subActionId = getAction(code)._id;
      correspondingLogItemIndex = matchLog.findIndex(
        l => l.timeStamp === item.timeStamp && l.activityId === subActionId
      );
      if (correspondingLogItemIndex !== -1) {
        correspondingLogItem = matchLog[correspondingLogItemIndex];
      }
    };

    const findOtherSwapItem = code => {
      const actionId = getAction(code)._id;
      correspondingLogItemIndex = matchLog.findIndex(
        (l, idx) =>
          l.timeStamp === item.timeStamp &&
          l.activityId === actionId &&
          index !== idx
      );
      if (correspondingLogItemIndex !== -1) {
        correspondingLogItem = matchLog[correspondingLogItemIndex];
      } else {
        /**
         * If swap player replace with an empty coordinates
         */
        onSingleDeleteSwap(item._id);
        onSwapPlayers(item);
      }
    };

    if (action?.code) {
      const { code } = action;

      switch (code) {
        case MatchLogActions.GOAL_SCORED:
          if (item.performedByOpponent) {
            updateScore(false, false);
          } else {
            updateScore(true, false);
          }
          break;
        case MatchLogActions.GOAL_CONCEDED:
          if (item.performedByOpponent) {
            updateScore(true, false);
          } else {
            updateScore(false, false);
          }
          break;
        case MatchLogActions.START_GAME:
          resetTimer();
          setTime(0);
          setGameStatus(false);
          setGameStage(MatchLogStages.NOT_STARTED);
          break;
        case MatchLogActions.END_GAME:
          resumeTimer();
          setGameStatus(true);
          setGameStage(MatchLogStages.SECOND_HALF_STARTED);
          break;
        case MatchLogActions.FIRST_HALF_END:
          resumeTimer();
          setGameStage(MatchLogStages.GAME_STARTED);
          break;
        case MatchLogActions.SECOND_HALF_START:
          pauseTimer();
          setGameStage(MatchLogStages.FIRST_HALF_ENDED);
          break;
        case MatchLogActions.RED_CARD:
          onRedCardReverse(item);
          break;
        case MatchLogActions.SUB_IN:
          findOtherSubItem(MatchLogActions.SUB_OUT);
          break;
        case MatchLogActions.SUB_OUT:
          findOtherSubItem(MatchLogActions.SUB_IN);
          break;
        case MatchLogActions.MOVE_PLAYER:
          findOtherSwapItem(code);
          break;
        default:
          break;
      }

      dispatch({
        type: MATCH_LOG_REMOVE_LOG_ITEM,
        payload: index,
      });
      item?._id &&
        dispatch({
          type: MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES,
          payload: item,
        });

      if (
        correspondingLogItemIndex &&
        correspondingLogItem &&
        correspondingLogItemIndex !== -1
      ) {
        dispatch({
          type: MATCH_LOG_REMOVE_LOG_ITEM,
          payload:
            correspondingLogItemIndex < index
              ? correspondingLogItemIndex
              : index,
        });
        correspondingLogItem?._id &&
          dispatch({
            type: MATCH_LOG_SET_UNSYNCED_DELETED_ACTIVITIES,
            payload: correspondingLogItem,
          });
        onDeleteSubInOutSwap(item?._id, correspondingLogItem?._id);
        if (code === MatchLogActions.SUB_IN) {
          swapSubPlayers(item.playerId, correspondingLogItem.playerId);
        } else {
          onSwapPlayers(item);
        }
        return;
      }

      if (
        action?.code !== MatchLogActions.SUB_IN &&
        action?.code !== MatchLogActions.MOVE_PLAYER
      ) {
        deleteActivity(item?._id);
      }
      // need the below code later if we want to reload match plan
      // after removing log item
      // if (
      //   [
      //     MatchLogActions.RED_CARD,
      //     MatchLogActions.SUB_IN,
      //     MatchLogActions.SUB_OUT,
      //     MatchLogActions.MOVE_PLAYER,
      //   ].includes(code)
      // ) {
      //   loadMatchPlan();
      // }
    }
  };

  const onOpponentPressed = () => {
    setIsOpponent(true);
    setSelectedPlayer('1');
  };

  const saveGame = () => {
    setSyncErrorMessage('');
    if (selectedEvent) {
      const saveObj = {
        ...selectedEvent,
        concludedTime: new Date().toISOString(),
      };
      saveData(
        `/api/v1/events?preferredTimeZone=${Localization.timezone}`,
        MATCH_LOG_SAVE_GAME_REQUEST,
        MATCH_LOG_SAVE_GAME_SUCCESS,
        MATCH_LOG_SAVE_GAME_FAILED,
        saveObj,
        null,
        'PUT',
        null,
        EVENT_SERVICE
      );
      dispatch({
        type: FETCH_ONGOING_MATCH_SUCCESS,
        payload: false,
      });
    }
  };

  useEffect(() => {
    if (matchSaveTriggered && (bulkDeleteLoading || bulkSaveLoading)) {
      setIsSyncInProgress(true);
    }
  }, [matchSaveTriggered, bulkDeleteLoading, bulkSaveLoading]);

  useEffect(() => {
    if (isSyncInProgress && !bulkDeleteLoading && !bulkSaveLoading) {
      setIsSyncInProgress(false);
    }
  }, [isSyncInProgress, bulkDeleteLoading, bulkSaveLoading]);

  // This function is redundant, delete it if not necessasary
  useEffect(() => {
    if (!isSyncInProgress && isUnsyncedDataAvailable && matchSaveTriggered) {
      //sync failed
      setSyncErrorMessage('Data sync failed. Please try again!');
    }
  }, [isSyncInProgress, isUnsyncedDataAvailable]);

  useEffect(() => {
    if (matchSaveTriggered && !isUnsyncedDataAvailable) {
      saveGame();
      setMatchSaveTriggered(false);
    }
  }, [isUnsyncedDataAvailable, matchSaveTriggered]);

  const onSaveModalResponse = res => {
    if (res) {
      setSyncErrorMessage('');

      syncAllData();
      setMatchSaveTriggered(true);

      saveGame();
    }
  };

  const navigateToReportsPage = () => {
    if (selectedEvent?.concluded || saveSuccess) {
      navigation.navigate('MatchReport');
    }
  };
  const getImage = buttonName => {
    switch (buttonName) {
      case 'Penalty Won':
        return require('../../../assets/icons/penalty_won.png');
        break;
      case 'Penalty Conceded':
        return require('../../../assets/icons/penalty_conceded.png');
        break;
      case 'Penalty Missed':
        return require('../../../assets/icons/penalty_missed.png');
        break;
      case 'Sub Out':
        return require('../../../assets/icons/sub_out.png');
        break;
      case 'Red Card':
        return require('../../../assets/icons/red_card.png');
        break;
      case 'Yellow Card':
        return require('../../../assets/icons/yellow_card.png');
        break;
      case 'Corner':
        return require('../../../assets/icons/corner.png');
        break;
      case 'Assist':
        return require('../../../assets/icons/assist.png');
        break;
      case 'Swap Players':
        return require('../../../assets/icons/swap_players.png');
        break;
      default:
        break;
    }
  };

  const ColoredButton = useCallback(
    ({
      children,
      color,
      onPress,
      disabled,
      width,
      code,
      goalbutton,
      ...rest
    }) => {
      const selected = code === selectedAction;
      return (
        <TouchableOpacity
          style={[
            MatchLogContainerStyles.coloredButton,
            selected
              ? { backgroundColor: colors.midDarkBlue, height: hp('12') }
              : color
              ? { backgroundColor: color, height: hp('9') }
              : {},
            width ? { width: width } : {},
            goalbutton ? { height: hp('7.5') } : {},
            { opacity: disabled ? 0.5 : 1 },
          ]}
          onPress={onPress}
          disabled={disabled}
          {...rest}
        >
          <Text
            style={[
              MatchLogContainerStyles.coloredButtonText,
              color ? { textAlign: 'center', width: '100%' } : {},
            ]}
          >
            {children}
          </Text>
          <Image
            style={MatchLogContainerStyles.coloredButtonImage}
            source={getImage(children)}
          />
        </TouchableOpacity>
      );
    },
    [selectedAction, disableAction, isLoadingMatchActivity]
  );

  const Substitute = ({ item }) => {
    return (
      <View style={{ marginRight: 10 }}>
        {substitutePlayerTooltip?.sportsProfileId === item.sportsProfileId ? (
          <View style={MatchLogContainerStyles.toolTipPlayers}>
            <Text style={MatchLogContainerStyles.toolTipPlayersText}>
              {substitutePlayerTooltip?.firstName}
            </Text>
          </View>
        ) : null}

        <TouchableOpacity
          onPress={() => substitutePlayerClicked(item)}
          disabled={disableAction || isLoadingMatchActivity}
        >
          <View style={MatchLogContainerStyles.jerseyCircle}>
            <Text style={MatchLogContainerStyles.jerseyCircleMatchText}>
              {jerseyNumbers?.find(
                data => data?.sportsProfileId === item?.sportsProfileId
              )?.jersyNo || ''}
            </Text>
          </View>
          <ProfileImage
            imageStyles={MatchLogContainerStyles.substituteProfileImage}
            profileImageUrl={item?.profileImageUrl}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const secondHalfStartLogIndex = useMemo(() => {
    return matchLog?.findIndex(
      singleMatchLog =>
        singleMatchLog?.code === MatchLogActions.SECOND_HALF_START ||
        singleMatchLog?.activity?.activityCode ===
          MatchLogActions.SECOND_HALF_START
    );
  }, [JSON.stringify(matchLog)]);

  const getCalculatedTimeDifference = (min, sec, index) => {
    const matchDuration = matchPlan?.duration;
    const elapsedTimeInSeconds = Number(min) * 60 + Number(sec);
    const matchDurationInSeconds = matchDuration * 60;
    const halfTimeInSeconds = matchDurationInSeconds / 2;

    let additionalTime = 0;

    const isActivityAfter2ndHalf =
      secondHalfStartLogIndex !== -1 && index > secondHalfStartLogIndex;

    if (elapsedTimeInSeconds > halfTimeInSeconds && !isActivityAfter2ndHalf) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - halfTimeInSeconds) / 60
      );
    }

    if (
      elapsedTimeInSeconds > matchDurationInSeconds &&
      isActivityAfter2ndHalf
    ) {
      additionalTime = Math.ceil(
        (elapsedTimeInSeconds - matchDurationInSeconds) / 60
      );
    }
    return additionalTime > 0 ? ` ( +${additionalTime} min )` : '';
  };

  const handleSocketMessage = (firstObject, secondObject = null, code) => {
    switch (true) {
      case MatchLogActions.MOVE_PLAYER === code:
        swapPlayerPositionsSocket(firstObject, secondObject);
        break;
      case MatchLogActions.SUB_IN === code:
        handleSocketSubInSubOut(firstObject, secondObject);
        break;
      case matchActions?.includes(code):
        executeActionSocket(code, firstObject);
        break;
      default:
        break;
    }
  };

  const LogItem = ({ item, index }) => {
    let showDelete;
    if (isTabDevice()) {
      showDelete = true;
      if (
        selectedEvent?.concluded ||
        saveSuccess ||
        index !== matchLog.length - 1
      ) {
        showDelete = false;
      }
    } else {
      showDelete = false;
    }

    const minNdSec = item?.elapsedTime?.split(' ')[0].split(':');
    const computedElapsedTime = item?.elapsedTime?.includes('(')
      ? item?.elapsedTime
      : `${item.elapsedTime} ${getCalculatedTimeDifference(
          minNdSec[0],
          minNdSec[1],
          index
        )}`;

    return (
      <View style={MatchLogContainerStyles.matchActionCommentContainer}>
        {showDelete && (
          <TouchableOpacity
            style={MatchLogContainerStyles.matchActionCommentClose}
            onPress={() => onItemRemoveClick(item, index)}
            disabled={disableAction || isLoadingMatchActivity}
          >
            {!isLoadingMatchActivity && (
              <AntDesign
                name="close"
                size={20}
                color={colors.white}
                style={MatchLogContainerStyles.close}
              />
            )}
          </TouchableOpacity>
        )}

        <Text style={MatchLogContainerStyles.matchActionCommentTime}>
          {computedElapsedTime}
        </Text>
        <Text style={MatchLogContainerStyles.matchActionComment}>
          {item.comment}
        </Text>
      </View>
    );
  };

  return (
    <>
      {isInvalidRequest && (
        <ForceModal
          type={forcedModalType.INVALID_ACTION}
          errorMessage={
            matchLogValidErrorMessage.INVALID_MATCH_ACTION === isInvalidRequest
              ? invalidActionMessages.message
              : invalidActionMessages.actionFailedMsg
          }
          onClose={() => {
            setRefreshLoading(true);
            dispatch({ type: CLOSE_INVALID_ACTION });
          }}
        />
      )}

      <View style={MatchLogContainerStyles.matchActionContainer}>
        <SocketMatchLogContainer
          onMatchActionClicked={handleSocketMessage}
          matchId={selectedEvent?._id}
          deleteLogsForMatch={onItemRemoveFromSocket}
        />
        {matchesLoading || matchPlanLoading ? (
          <ActivitySpinner color={colors.green} />
        ) : (
          <Fragment>
            <View
              style={{
                ...MatchLogContainerStyles.matchActionColumn,
                ...MatchLogContainerStyles.matchActionColumnMobile,
              }}
            >
              <View style={MatchLogContainerStyles.matchStats}>
                <View style={MatchLogContainerStyles.matchActionFieldRow1}>
                  <Text
                    style={MatchLogContainerStyles.matchActionFormationTitle}
                  >
                    Match Formation
                  </Text>
                  <Text style={MatchLogContainerStyles.matchActionFormation}>
                    {formation?.name ? `${formation?.name} Formation` : ''}
                  </Text>
                </View>
                <View style={MatchLogContainerStyles.matchActionFieldRow2}>
                  <Field
                    jerseyNumbers={jerseyNumbers}
                    isActionSelected={!!selectedAction}
                    matchPlan={currentMatchPlan || {}}
                    playerCoordinates={
                      currentMatchPlan?.playerCoordinates || []
                    }
                    openCandidatePlayerModal={playerClicked}
                    isMatchLogView
                    disabledNet={disableAction || isLoadingMatchActivity}
                  />
                </View>
                <View style={MatchLogContainerStyles.matchActionFieldRow3}>
                  <Text style={MatchLogContainerStyles.substituteTitle}>
                    Substitutes
                  </Text>
                  <FlatList
                    data={currentMatchPlan?.substitutePlayers}
                    renderItem={Substitute}
                    horizontal
                    keyExtractor={item => item.sportsProfileId}
                  />
                </View>
              </View>
              {isTabDevice() && (
                <TouchableOpacity
                  style={{
                    ...MatchLogContainerStyles.matchActionBelowButton,
                    backgroundColor: colors.tileBackground,
                    opacity:
                      isOpponentDisabled ||
                      disableAction ||
                      isLoadingMatchActivity
                        ? 0.5
                        : 1,
                  }}
                  onPress={() => onOpponentPressed()}
                  disabled={
                    isOpponentDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                >
                  <Text
                    style={MatchLogContainerStyles.matchActionBelowButtonText}
                  >
                    Opponent
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            <View
              style={{
                ...MatchLogContainerStyles.matchActionColumn,
                ...MatchLogContainerStyles.matchActionColumnMobile2,
              }}
            >
              <View style={MatchLogContainerStyles.matchActionScoreBoard}>
                <View style={MatchLogContainerStyles.matchActionScoreTeam}>
                  <Text
                    style={MatchLogContainerStyles.matchActionScoreTeamScore}
                  >
                    {score?.our}
                  </Text>
                  <Text
                    style={MatchLogContainerStyles.matchActionScoreTeamName}
                  >
                    {selectedTeam?.teamName || selectedTeam?.name}
                  </Text>
                </View>
                <View style={MatchLogContainerStyles.matchActionScoreTimerTile}>
                  <Text style={MatchLogContainerStyles.matchActionScoreVsText}>
                    VS
                  </Text>
                  <Text
                    style={MatchLogContainerStyles.matchActionScoreTimerText}
                  >
                    {addLeadingZeros(minutes)}:{addLeadingZeros(seconds)}
                  </Text>
                </View>
                <View style={MatchLogContainerStyles.matchActionScoreTeam}>
                  <Text
                    style={MatchLogContainerStyles.matchActionScoreTeamScore}
                  >
                    {score?.opponent}
                  </Text>
                  <Text
                    style={MatchLogContainerStyles.matchActionScoreTeamName}
                  >
                    {opponentDetails?.name}
                  </Text>
                </View>
              </View>
              <View style={MatchLogContainerStyles.matchTimeline}>
                {logsLoading && !hideLoading ? (
                  <ActivityIndicator size="large" color={colors.green} />
                ) : hideLoading || logsLoaded ? (
                  <FlatList
                    ref={matchLogActivityFlatlist}
                    data={matchLog}
                    renderItem={LogItem}
                    keyExtractor={item => item?._id}
                    extraData={{
                      saveSuccess,
                      selectedEvent,
                      matchLog,
                      refreshList,
                    }}
                    onContentSizeChange={() => {
                      matchLogActivityFlatlist?.current?.scrollToEnd();
                      setRefreshList(refresh => !refresh);
                    }}
                  />
                ) : null}
              </View>
              {(isTabDevice() && selectedEvent?.concluded) || saveSuccess ? (
                <TouchableOpacity
                  style={{
                    ...MatchLogContainerStyles.matchActionBelowButton,
                    backgroundColor: colors.aquaBlue,
                    opacity:
                      isGameOngoing ||
                      currentStage !== MatchLogStages.GAME_ENDED ||
                      disableAction ||
                      isLoadingMatchActivity
                        ? 0.5
                        : 1,
                  }}
                  disabled={
                    isGameOngoing ||
                    currentStage !== MatchLogStages.GAME_ENDED ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() => navigateToReportsPage()}
                >
                  <Text
                    style={MatchLogContainerStyles.matchActionBelowButtonText}
                  >
                    Reports
                  </Text>
                </TouchableOpacity>
              ) : (
                isTabDevice() && (
                  <View
                    style={
                      MatchLogContainerStyles.matchActionBelowButtonWrapper
                    }
                  >
                    <TouchableOpacity
                      style={{
                        ...MatchLogContainerStyles.matchActionBelowButton2,
                        backgroundColor: colors.tileBackground,
                        opacity:
                          disableAction || isLoadingMatchActivity ? 0.5 : 1,
                      }}
                      disabled={disableAction || isLoadingMatchActivity}
                      onPress={() => setRefreshLoading(true)}
                    >
                      {!refreshLoading || hideLoading ? (
                        <>
                          <EvilIcons name="refresh" size={33} color="white" />
                          <Text
                            style={
                              MatchLogContainerStyles.matchActionBelowButtonText2
                            }
                          >
                            Refresh Now
                          </Text>
                        </>
                      ) : (
                        <ActivityIndicator
                          size={'small'}
                          color={colors.white}
                        />
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        ...MatchLogContainerStyles.matchActionBelowButton2,
                        backgroundColor: colors.aquaBlue,
                        opacity:
                          isGameOngoing ||
                          currentStage !== MatchLogStages.GAME_ENDED ||
                          disableAction ||
                          isLoadingMatchActivity
                            ? 0.5
                            : 1,
                      }}
                      disabled={
                        isGameOngoing ||
                        currentStage !== MatchLogStages.GAME_ENDED ||
                        disableAction ||
                        isLoadingMatchActivity
                      }
                      onPress={() => {
                        matchSaveTriggered
                          ? saveGame()
                          : setIsSaveModalVisible(true);
                      }}
                    >
                      {!saveLoading ? (
                        <Text
                          style={
                            MatchLogContainerStyles.matchActionBelowButtonText
                          }
                        >
                          Save
                        </Text>
                      ) : (
                        <ActivityIndicator
                          size={'small'}
                          color={colors.white}
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                )
              )}
            </View>
            <View
              style={{
                ...MatchLogContainerStyles.matchActionColumn,
                ...MatchLogContainerStyles.matchActionColumnHide,
              }}
            >
              <View style={MatchLogContainerStyles.matchActionButtonColumn}>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.PENALTY_WON)
                  }
                  code={MatchLogActions.PENALTY_WON}
                >
                  Penalty Won
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.PENALTY_CONCEDED)
                  }
                  code={MatchLogActions.PENALTY_CONCEDED}
                >
                  Penalty Conceded
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.PENALTY_MISSED)
                  }
                  code={MatchLogActions.PENALTY_MISSED}
                >
                  Penalty Missed
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() => onActionButtonClicked(MatchLogActions.SUB_OUT)}
                  code={MatchLogActions.SUB_OUT}
                >
                  Sub Out
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.RED_CARD)
                  }
                  code={MatchLogActions.RED_CARD}
                >
                  Red Card
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.YELLOW_CARD)
                  }
                  code={MatchLogActions.YELLOW_CARD}
                >
                  Yellow Card
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() => onActionButtonClicked(MatchLogActions.CORNER)}
                  code={MatchLogActions.CORNER}
                >
                  Corner
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() => onActionButtonClicked(MatchLogActions.ASSIST)}
                  code={MatchLogActions.ASSIST}
                >
                  Assist
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.MOVE_PLAYER)
                  }
                  code={MatchLogActions.MOVE_PLAYER}
                  width={'100%'}
                >
                  Swap Players
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.GOAL_SCORED)
                  }
                  code={MatchLogActions.GOAL_SCORED}
                  color={colors.green}
                  width={'100%'}
                  goalbutton={true}
                >
                  Goal Scored
                </ColoredButton>
                <ColoredButton
                  disabled={
                    isActionButtonsDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                  onPress={() =>
                    onActionButtonClicked(MatchLogActions.GOAL_CONCEDED)
                  }
                  code={MatchLogActions.GOAL_CONCEDED}
                  color={colors.red}
                  width={'100%'}
                  goalbutton={true}
                >
                  Goal Conceded
                </ColoredButton>
              </View>
            </View>

            {!isTabDevice() && (
              <View style={MatchLogContainerStyles.matchActionColumnBottom}>
                <TouchableOpacity
                  style={{
                    ...MatchLogContainerStyles.matchActionBelowButton,
                    backgroundColor: colors.tileBackground,
                    opacity:
                      isOpponentDisabled ||
                      disableAction ||
                      isLoadingMatchActivity
                        ? 0.5
                        : 1,
                  }}
                  onPress={() => onOpponentPressed()}
                  disabled={
                    isOpponentDisabled ||
                    disableAction ||
                    isLoadingMatchActivity
                  }
                >
                  <Text
                    style={MatchLogContainerStyles.matchActionBelowButtonText}
                  >
                    Opponent
                  </Text>
                </TouchableOpacity>
                {selectedEvent?.concluded || saveSuccess ? (
                  <TouchableOpacity
                    style={{
                      ...MatchLogContainerStyles.matchActionBelowButton,
                      backgroundColor: colors.aquaBlue,
                      opacity:
                        isGameOngoing ||
                        currentStage !== MatchLogStages.GAME_ENDED ||
                        disableAction ||
                        isLoadingMatchActivity
                          ? 0.5
                          : 1,
                    }}
                    disabled={
                      isGameOngoing ||
                      currentStage !== MatchLogStages.GAME_ENDED ||
                      disableAction ||
                      isLoadingMatchActivity
                    }
                    onPress={() => navigateToReportsPage()}
                  >
                    <Text
                      style={MatchLogContainerStyles.matchActionBelowButtonText}
                    >
                      Reports
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <>
                    <TouchableOpacity
                      style={{
                        ...MatchLogContainerStyles.matchActionBelowButton2,
                        backgroundColor: colors.tileBackground,
                        opacity:
                          disableAction || isLoadingMatchActivity ? 0.5 : 1,
                      }}
                      disabled={disableAction || isLoadingMatchActivity}
                      onPress={() => setRefreshLoading(true)}
                    >
                      {!refreshLoading || hideLoading ? (
                        <>
                          <EvilIcons name="refresh" size={33} color="white" />
                          <Text
                            style={
                              MatchLogContainerStyles.matchActionBelowButtonText2
                            }
                          >
                            Refresh Now
                          </Text>
                        </>
                      ) : (
                        <ActivityIndicator
                          size={'small'}
                          color={colors.white}
                        />
                      )}
                    </TouchableOpacity>
                  </>
                )}
              </View>
            )}
            <View
              style={{
                ...MatchLogContainerStyles.matchActionColumnBottom,
                ...MatchLogContainerStyles.errorView,
              }}
            >
              <Text style={MatchLogContainerStyles.errorText}>
                {syncErrorMessage}
              </Text>
            </View>
          </Fragment>
        )}
        <UnsavedChangesModal
          message={'Are you sure you want to save the match log?'}
          modalVisible={isSaveModalVisible}
          setModalVisible={setIsSaveModalVisible}
          setModalResponse={onSaveModalResponse}
        />
      </View>
    </>
  );
};

export default MatchLogActionsContainer;
