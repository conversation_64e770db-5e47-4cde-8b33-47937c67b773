import { StyleSheet } from 'react-native';

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { colorPalette } from '../../constants/constants';
import { isTabDevice } from '../../config/appConfig';

const MatchLogContainerStyles = colors => ({
  container: isTabDevice()
    ? {
        width: wp('100%'),
        flexDirection: 'row',
      }
    : {
        width: wp('100%'),
        flexDirection: 'column',
      },
  leftView: isTabDevice()
    ? {
        width: wp('26%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      }
    : {
        width: wp('100%'),
        paddingLeft: wp('2%'),
        height: wp('32%'),
      },
  rightView: isTabDevice()
    ? {
        width: wp('74%'),
        height: hp('90%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        borderLeftWidth: 1,
        borderLeftColor: colors.borderBlue,
      }
    : {
        width: wp('100%'),
        // height: hp('90%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        borderTopWidth: 1,
        borderTopColor: colors.borderBlue,
      },
  toolTipPlayers: {
    backgroundColor: colors.borderBlue,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    padding: 5,
    borderRadius: 5,
    zIndex: 10,
  },

  toolTipPlayersText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('0.8%'),
        fontFamily: 'Poppins-Thin',
      }
    : {
        color: colors.white,
        fontSize: wp('2.0%'),
        fontFamily: 'Poppins-Thin',
      },
  jerseyCircle: isTabDevice()
    ? {
        width: wp('1.7%'),
        height: wp('1.7%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.white,
        borderColor: colors.white,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        right: 10,
        bottom: 0,
        zIndex: 10,
      }
    : {
        width: wp('4.0%'),
        height: wp('4.0%'),
        borderRadius: wp('100%'),
        backgroundColor: colors.white,
        borderColor: colors.white,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: wp('6%'),
        left: wp('5%'),
        zIndex: 10,
      },
  jerseyCircleMatchText: isTabDevice()
    ? {
        color: colors.black,
        fontSize: wp('0.9%'),
        fontFamily: 'Poppins-Thin',
      }
    : {
        color: colors.black,
        fontSize: wp('2.3%'),
        fontFamily: 'Poppins-Thin',
      },

  rightViewWrapper: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  matchCompMatchDateWrapper: isTabDevice()
    ? {
        width: '100%',
        height: wp('1%'),
        flexDirection: 'row',
      }
    : {
        width: '100%',
        height: wp('4%'),
        flexDirection: 'row',
        paddingTop: wp('1%'),
      },
  matchTimeDate: isTabDevice()
    ? {
        flexDirection: 'row',
        backgroundColor: colors.veryDarkBlue,
        padding: wp('1%'),
        marginTop: wp('1%'),
        marginBottom: wp('1%'),
        width: '100%',
      }
    : {
        flexDirection: 'row',
        backgroundColor: colors.veryDarkBlue,
        padding: wp('2%'),
        marginTop: wp('2%'),
        marginBottom: wp('1%'),
        width: '100%',
      },
  matchCompMatchDate: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: wp('2%'),
        padding: wp('0.5%'),
        paddingLeft: wp('1%'),
        paddingRight: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        borderRadius: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: wp('2%'),
        padding: wp('2%'),
      },
  matchCompMatchDateNextDay: isTabDevice()
    ? {
        borderRadius: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: wp('2%'),
        padding: wp('0.5%'),
      }
    : {
        borderRadius: wp('5%'),
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: wp('2%'),
        padding: wp('2%'),
      },
  matchCompMatchDateText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.6%'),
        fontFamily: 'Poppins-Bold',
      },
  matchCompContainer: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        flexDirection: 'row',
        borderRadius: wp('1%'),
        padding: wp('1.5%'),
        marginBottom: wp('2%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        flexDirection: 'row',
        borderRadius: wp('1%'),
        padding: wp('2.5%'),
        marginBottom: wp('3%'),
      },
  matchCompScores: isTabDevice()
    ? {
        backgroundColor: colors.midDarkBlue,
        borderRadius: wp('1%'),
        width: '70%',
        paddingTop: wp('3%'),
        paddingBottom: wp('3%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.midDarkBlue,
        borderRadius: wp('1%'),
        width: '100%',
        paddingTop: wp('3%'),
        paddingBottom: wp('3%'),
        // paddingLeft: wp('2%'),
        // paddingRight: wp('2%'),
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      },
  matchCompScoreRow: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
      }
    : {
        flexDirection: 'row',
        // justifyContent: 'space-around',
        width: '100%',
      },
  matchCompScore: isTabDevice()
    ? {
        flexDirection: 'column',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
      }
    : {
        flexDirection: 'column',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        width: wp('40.1%'),
      },
  coloredText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Bold',
        marginRight: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('2.8%'),
        fontFamily: 'Poppins-Bold',
        marginRight: wp('1%'),
      },
  matchCompTeamName: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('0.5%'),
        width: wp('13%'),
        // height: wp('4%'),
        flexDirection: 'row',
        flexWrap: 'wrap',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('0.5%'),
        width: '100%',
        // height: wp('4%'),
        flexDirection: 'row',
        flexWrap: 'wrap',
      },
  matchCompCounter: isTabDevice()
    ? {
        flexDirection: 'row',
        width: wp('17%'),
        justifyContent: 'space-between',
      }
    : {
        flexDirection: 'row',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
      },
  matchCompCounterArrows: {
    justifyContent: 'space-evenly',
  },
  arrow: {
    backgroundColor: colors.green,
    borderRadius: wp('100%'),
  },
  matchCompCounterScoreWrapper: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: wp('13%'),
        height: wp('13%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.tileBackground,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: wp('30%'),
        height: wp('30%'),
        borderRadius: wp('2%'),
      },
  matchCompCounterScore: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('7%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('15%'),
        fontFamily: 'Poppins-Bold',
      },
  matchCompScoreCenter: {
    justifyContent: 'center',
    height: '85%',
    alignSelf: 'flex-end',
  },
  matchCompScoreVS: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-Bold',
      },
  matchCompTimer: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1.5%'),
        margin: wp('2%'),
        borderRadius: wp('1%'),
        width: '77%',
      }
    : {
        backgroundColor: colors.aquaBlue,
        justifyContent: 'center',
        alignItems: 'center',
        padding: wp('1.5%'),
        margin: wp('2%'),
        borderRadius: wp('1%'),
        width: '88%',
      },
  matchCompTimerText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      }
    : {
        fontSize: wp('4%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
      },
  matchCompGameButtons: isTabDevice()
    ? {
        justifyContent: 'space-evenly',
        width: '30%',
        paddingLeft: wp('1.5%'),
      }
    : {
        display: 'none',
      },
  matchCompGameButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: wp('1.5%'),
    margin: 5,
    borderRadius: 15,
  },
  matchCompGameButtonText: {
    fontSize: wp('1.5%'),
    color: colors.white,
  },
  matchCompOptionsButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  matchCompOptionsButton: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        // margin: 5,
        borderRadius: wp('1%'),
      }
    : {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: wp('1.5%'),
        paddingBottom: wp('1.5%'),
        paddingLeft: wp('3%'),
        paddingRight: wp('3%'),
        // margin: 5,
        borderRadius: wp('1%'),
        width: '30%',
      },
  matchCompOptionsButtonText: isTabDevice()
    ? {
        fontSize: wp('1.5%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        marginRight: wp('1%'),
      }
    : {
        fontSize: wp('2.7%'),
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        marginRight: wp('1%'),
      },
  matchCompOptionsButtonIcon: isTabDevice()
    ? {}
    : {
        fontSize: wp('5%'),
      },
  matchEventItemTouchable: {
    flexDirection: 'row',
  },

  /* ------ Match Action Screen ------ */

  matchActionContainer: isTabDevice()
    ? {
        width: wp('100%'),
        height: hp('100%'),
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      }
    : {
        width: wp('100%'),
        height: hp('100%'),
        flexDirection: 'column',
        justifyContent: 'space-between',
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  matchActionColumn: isTabDevice()
    ? {
        width: '32%',
        height: '89%',
        borderRadius: wp('2%'),
        justifyContent: 'space-between',
      }
    : {
        width: '100%',
        borderRadius: wp('2%'),
      },
  matchActionColumnMobile: isTabDevice()
    ? {}
    : {
        flexDirection: 'column',
        alignItems: 'center',
        height: '50%',
        paddingTop: wp('3%'),
      },
  matchActionColumnMobile2: isTabDevice()
    ? {}
    : {
        height: '20%',
        marginTop: wp('5%'),
      },
  matchActionColumnHide: isTabDevice()
    ? {}
    : {
        display: 'none',
      },
  matchActionColumnBottom: isTabDevice()
    ? {
        width: '66%',
        flexDirection: 'row',
        justifyContent: 'space-between',
      }
    : {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
      },
  matchTimeline: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        flexDirection: 'column',
        alignItems: 'center',
        padding: wp('1.5%'),
        height: '68.5%',
      }
    : {
        backgroundColor: colors.tileBackground,
        borderRadius: wp('2%'),
        flexDirection: 'column',
        alignItems: 'center',
        padding: wp('1.5%'),
        height: '100%',
      },
  matchStats: isTabDevice()
    ? {
        backgroundColor: colors.veryDarkBlue,
        padding: wp('1.5%'),
        borderRadius: wp('2%'),
        height: '88%',
      }
    : {
        flexDirection: 'column',
        alignItems: 'center',
        height: '90%',
        width: '100%',
      },
  matchActionFormationTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
      },
  matchActionFormation: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.2%'),
        textAlign: 'center',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        textAlign: 'center',
      },
  matchActionFieldRow1: {
    marginBottom: wp('2%'),
  },
  matchActionFieldRow2: isTabDevice()
    ? {
        width: '100%',
        height: '63%',
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
      }
    : {
        width: '90%',
        height: '70%',
        marginTop: wp('3%'),
        marginBottom: wp('3%'),
      },
  substituteTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        textAlign: 'center',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        textAlign: 'center',
        marginBottom: wp('3%'),
      },
  substituteProfileImage: isTabDevice()
    ? {
        width: wp('4.5%'),
        height: wp('4.5%'),
        borderRadius: wp('100%'),
        marginRight: wp('1%'),
      }
    : {
        width: wp('11%'),
        height: wp('11%'),
        borderRadius: wp('100%'),
        marginRight: wp('4%'),
      },
  matchActionCommentContainer: isTabDevice()
    ? {
        width: wp('25%'),
        borderBottomWidth: 1,
        borderBottomColor: colors.lightBlue,
        paddingBottom: wp('1%'),
        marginBottom: wp('1%'),
      }
    : {
        width: wp('90%'),
        borderBottomWidth: 1,
        borderBottomColor: colors.lightBlue,
        paddingBottom: wp('1%'),
        marginBottom: wp('1%'),
      },
  matchActionCommentClose: {
    position: 'absolute',
    right: 0,
    zIndex: 1,
  },
  matchActionCommentTime: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('0.5%'),
      }
    : {
        color: colors.green,
        fontSize: wp('3.3%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('0.5%'),
      },
  matchActionComment: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.3%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('4.3%'),
        fontFamily: 'Poppins-Bold',
      },
  matchActionScoreBoard: isTabDevice()
    ? {
        backgroundColor: colors.tileBackground,
        padding: wp('1%'),
        borderRadius: wp('1.5%'),
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: wp('1%'),
      }
    : {
        display: 'none',
      },
  matchActionScoreTeam: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '30%',
  },
  matchActionScoreTeamName: isTabDevice() ? {
    color: colors.white,
    fontSize: wp('0.8%'),
    fontFamily: 'Poppins-Bold',
    textAlign: 'center',
  } : {
    color: colors.white,
    fontSize: wp('1%'),
    fontFamily: 'Poppins-Bold',
    textAlign: 'center',
  },
  matchActionScoreTeamScore: isTabDevice() ? {
    color: colors.white,
    fontSize: wp('3.5%'),
    fontFamily: 'Poppins-Bold',
  } : {
    color: colors.white,
    fontSize: wp('5%'),
    fontFamily: 'Poppins-Bold',
  },
  matchActionScoreTimerTile: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    width: '30%',
  },
  matchActionScoreVsText: {
    color: colors.white,
    fontSize: wp('2%'),
    fontFamily: 'Poppins-Bold',
  },
  matchActionScoreTimerText: {
    color: colors.white,
    fontSize: wp('1.7%'),
    fontFamily: 'Poppins-Bold',
  },
  matchActionButtonColumn: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  coloredButton: {
    width: '47%',
    height: hp('12'),
    backgroundColor: colors.tileBackground,
    marginBottom: wp('1.65%'),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: wp('1.5%'),
    padding: wp('1.5%'),
  },
  coloredButtonText: {
    color: colors.white,
    fontSize: wp('1.3%'),
    fontFamily: 'Poppins-Medium',
    width: '70%',
  },
  coloredButtonImage: {
    width: wp('4%'),
    height: wp('4%'),
    resizeMode: 'contain',
  },
  matchActionBelowButtonWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }
    : {},
  matchActionBelowButton: isTabDevice()
    ? {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1.5%'),
        padding: wp('1.5%'),
        marginTop: wp('1.6%'),
      }
    : {
        width: '31%',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1.5%'),
        padding: wp('1.5%'),
        marginTop: wp('1.6%'),
      },
  matchActionBelowButton2: isTabDevice()
    ? {
        width: '48%',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1.5%'),
        padding: wp('1.5%'),
        marginTop: wp('1.6%'),
      }
    : {
        width: '31%',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: wp('1.5%'),
        // padding: wp('0.5%'),
        marginTop: wp('1.6%'),
      },
  matchActionBelowButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontFamily: 'Poppins-Medium',
      },
  matchActionBelowButtonText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Medium',
      }
    : {
        color: colors.white,
        fontSize: wp('2.8%'),
        fontFamily: 'Poppins-Medium',
      },
  close: isTabDevice()
    ? {}
    : {
        fontSize: wp('6%'),
      },
  scrollOver: isTabDevice()
    ? {
        display: 'none',
      }
    : {
        display: 'none',
        width: '100%',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      },
  scrollOverText: isTabDevice()
    ? {
        display: 'none',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Medium',
      },
  scrollOverArrow: isTabDevice()
    ? {
        display: 'none',
      }
    : {
        color: colors.green,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  errorView: {
    justifyContent: 'flex-end',
    marginTop: wp('1%'),
  },
  errorText: {
    color: colors.red,
    marginRight: wp('7%'),
    fontSize: wp('1.2%'),
  },
  onlineBar: isTabDevice()
    ? {
        position: 'absolute',
        width: '100%',
        height: 50,
        backgroundColor: colors.green,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
      }
    : {
        position: 'absolute',
        width: '100%',
        height: 40,
        backgroundColor: colors.green,
        bottom: 93,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
      },
  onlineBarText: {
    color: colors.white,
  },
});

export default MatchLogContainerStyles;
