import { EVENT_SERVICE, FOOTBALL_SERVICE } from '../../constants/services';
import useApiPromise from '../../hooks/useApiPromise';
import {
  FETCH_INITIAL_FORMATIONS_FAILED,
  FETCH_INITIAL_FORMATIONS_REQUEST,
  FETCH_INITIAL_FORMATIONS_SUCCESS,
  FETCH_INITIAL_PLAYERS_FAIL,
  FETCH_INITIAL_PLAYERS_REQUEST,
  FETCH_INITIAL_PLAYERS_SUCCESS,
} from '../../store/actionTypes/MatchLog/MatchLogActions';

const MatchLogAPIHooks = () => {
  const [fetchAllMatchPlayers] = useApiPromise();
  const [fetchFormationData] = useApiPromise();

  const getAllMatchPlayers = async (eventId: string) => {
    await fetchAllMatchPlayers(
      `/api/v1/matches/${eventId}/match-plan?isCurrent=false`,
      FETCH_INITIAL_PLAYERS_REQUEST,
      FETCH_INITIAL_PLAYERS_SUCCESS,
      FETCH_INITIAL_PLAYERS_FAIL,
      null,
      null,
      'GET',
      false,
      EVENT_SERVICE
    ).then(async res => {
      if (!res.data) {
        /**
         * This will only happen if match is not start yet
         * Reason => Get player Details
         */
        await fetchAllMatchPlayers(
          `/api/v1/matches/${eventId}/match-plan`,
          FETCH_INITIAL_PLAYERS_REQUEST,
          FETCH_INITIAL_PLAYERS_SUCCESS,
          FETCH_INITIAL_PLAYERS_FAIL,
          null,
          null,
          'GET',
          false,
          EVENT_SERVICE
        );
      }
    });
  };

  const getFormationData = async (formationId: string) => {
    await fetchFormationData(
      `/api/v1/formations/${formationId}`,
      FETCH_INITIAL_FORMATIONS_REQUEST,
      FETCH_INITIAL_FORMATIONS_SUCCESS,
      FETCH_INITIAL_FORMATIONS_FAILED,
      null,
      null,
      'GET',
      false,
      FOOTBALL_SERVICE
    );
  };

  return { getAllMatchPlayers, getFormationData } as const;
};
export default MatchLogAPIHooks;
