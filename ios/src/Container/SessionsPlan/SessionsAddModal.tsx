import { AntDesign } from '@expo/vector-icons';
import React from 'react';
import {
  Appearance,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import ModalWrapper from '../../components/modal/ModalWrapper/ModalWrapper';
import { isTabDevice } from '../../config/appConfig';
import useStyles from '../../hooks/useStyles';
import sessionStyles from './playerSessionStyles';

const SessionAddModal = ({
  setIsEditModalOpen,
  setAddNumberOfSessions,
  addNumberOfSessions,
  mode,
  isDatePikerEnable,
  setIsDatePikerEnable,
  addExpireDate,
  setAddExpireDate,
  updateSessionUpdate,
  errorMassageOnSubmit,
}: {
  setIsEditModalOpen: (arg: boolean) => void;
  setAddNumberOfSessions: (arg: any) => void;
  addNumberOfSessions: any;
  isDatePikerEnable: boolean;
  setIsDatePikerEnable: (arg: boolean) => void;
  mode: any;
  addExpireDate: any;
  setAddExpireDate: (date: any) => void;
  updateSessionUpdate: () => void;
  errorMassageOnSubmit: string;
}) => {
  const sessionPlanStyles = useStyles(sessionStyles);
  return (
    <ModalWrapper visible transparent>
      <View style={sessionPlanStyles.overlay} />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <View style={sessionPlanStyles.centeredView}>
          <KeyboardAvoidingView behavior="padding">
            <View style={sessionPlanStyles.modalView}>
              <TouchableOpacity
                style={sessionPlanStyles.closeButton}
                onPress={() => setIsEditModalOpen(false)}
              >
                <AntDesign
                  name="close"
                  size={isTabDevice() ? 25 : 20}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
              <View style={sessionPlanStyles.modalContentWrapper}>
                <View style={sessionPlanStyles.modalHeader}>
                  <Text style={sessionPlanStyles.modalTitle}>Add Sessions</Text>
                </View>

                <View>
                  <Text style={sessionPlanStyles.message}>
                    Hey there! Just a heads up: Any field left blank defaults to
                    unlimited.
                  </Text>
                </View>
                <View style={sessionPlanStyles.modalRow}>
                  <Text style={sessionPlanStyles.fieldLabel}>
                    Number of sessions
                  </Text>
                  <TextInput
                    numberOfLines={1}
                    style={sessionPlanStyles.messageText}
                    onChangeText={text => setAddNumberOfSessions(text)}
                    value={addNumberOfSessions}
                    placeholder={'Type Here'}
                    placeholderTextColor="#595959"
                    disableFullscreenUI={true}
                    blurOnSubmit={false}
                    keyboardType="numeric"
                  />
                </View>
                <View style={sessionPlanStyles.modalRow}>
                  <Text style={sessionPlanStyles.fieldLabel}>Expiry date</Text>
                  <View style={sessionPlanStyles.btnCalendar}>
                    <TouchableOpacity
                      style={sessionPlanStyles.btnCalendarTextWrapper}
                      onPress={() => setIsDatePikerEnable(true)}
                    >
                      <Text
                        style={[
                          sessionPlanStyles.btnCalendarText,
                          addExpireDate ? { color: 'white' } : '',
                        ]}
                      >
                        {addExpireDate || 'Select a expiry date'}
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        addExpireDate
                          ? setAddExpireDate(null)
                          : setIsDatePikerEnable(true);
                      }}
                    >
                      {!addExpireDate ? (
                        <Image
                          style={sessionPlanStyles.calendarIcon}
                          source={require('../../../assets/icons/calendarIcon.png')}
                        />
                      ) : (
                        <AntDesign
                          style={sessionPlanStyles.calendarIcon}
                          name="close"
                          size={isTabDevice() ? 20 : 13}
                          color="#FFFFFF"
                        />
                      )}
                    </TouchableOpacity>
                  </View>
                  {isDatePikerEnable && (
                    <DateTimePickerModal
                      isVisible
                      mode={mode}
                      onConfirm={data => {
                        setAddExpireDate(data);
                        setIsDatePikerEnable(false);
                      }}
                      is24Hour={true}
                      onCancel={() => setIsDatePikerEnable(false)}
                      isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
                      minimumDate={new Date()}
                    />
                  )}
                </View>
                <View>
                  <Text style={sessionPlanStyles.errorMessage}>
                    {errorMassageOnSubmit}
                  </Text>
                </View>
                <View style={sessionPlanStyles.buttons}>
                  <View style={sessionPlanStyles.button2}>
                    <TouchableOpacity
                      onPress={() => {
                        setIsEditModalOpen(false);
                      }}
                    >
                      <Text style={sessionPlanStyles.buttonText}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                  <View style={sessionPlanStyles.button}>
                    <TouchableOpacity
                      onPress={() => {
                        updateSessionUpdate();
                      }}
                    >
                      <Text style={sessionPlanStyles.buttonText}>Save</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        </View>
      </TouchableWithoutFeedback>
    </ModalWrapper>
  );
};

export default SessionAddModal;
