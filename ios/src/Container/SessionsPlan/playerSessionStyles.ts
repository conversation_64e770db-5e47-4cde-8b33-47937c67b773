import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import { isTabDevice } from '../../config/appConfig';

const sessionStyles = (colors: any) => ({
  sessionPlannerWrapper: isTabDevice()
    ? {}
    : {
        width: wp('100%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
      },
  dataTileWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('4%'),
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: wp('4%'),
      },
  dataTile: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1.5%'),
        padding: wp('2%'),
        paddingTop: wp('3%'),
        paddingBottom: wp('3%'),
        width: '32%',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1.5%'),
        padding: wp('2%'),
        paddingTop: wp('3%'),
        paddingBottom: wp('3%'),
        width: '48%',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
      },
  editButton: {
    position: 'absolute',
    top: wp('1%'),
    right: wp('1%'),
  },
  editIcon: isTabDevice()
    ? {
        resizeMode: 'contain',
        width: wp('3%'),
        height: hp('3%'),
      }
    : {
        width: hp('3%'),
        height: hp('3%'),
      },
  dataTileText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('2%'),
      }
    : {
        color: colors.white,
        fontSize: wp('6.5%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('2%'),
      },
  dataTileText2: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3.2%'),
        fontFamily: 'Poppins-Bold',
      },
  message: isTabDevice()
    ? {
        color: colors.grey,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-regular',
        marginBottom: wp('2%'),
      }
    : {
        color: colors.grey,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-regular',
        marginBottom: wp('2%'),
      },
  errorMessage: isTabDevice()
    ? {
        color: colors.red,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-regular',
      }
    : {
        color: colors.red,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-regular',
      },
  dataTableWrapper: {},
  dataTableHeader: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        padding: wp('1%'),
        paddingLeft: wp('2%'),
        paddingRight: wp('2%'),
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: wp('1%'),
        padding: wp('2%'),
        paddingLeft: wp('4%'),
        paddingRight: wp('4%'),
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      },
  DataTableBody: isTabDevice()
    ? {
        height: hp('25%'),
      }
    : {
        height: hp('12%'),
      },
  DataTableBodyParent: isTabDevice()
    ? {
        height: hp('10%'),
      }
    : {
        height: hp('22%'),
      },
  dataTableRow: isTabDevice()
    ? {
        padding: wp('1%'),
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }
    : {
        padding: wp('2%'),
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      },
  dataTableColumn: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '23%',
  },
  dataTableTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('1%'),
      }
    : {
        color: colors.white,
        fontSize: wp('3.8%'),
        fontFamily: 'Poppins-Bold',
        marginBottom: wp('3%'),
      },
  dataTableHeaderText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Bold',
      },
  dataTableText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.white,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
      },

  dataTableTextActive: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.2%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        fontSize: wp('2.5%'),
        fontFamily: 'Poppins-Regular',
      },

  // Modal Styles

  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: hp('100%'),
    width: wp('100%'),
  },
  overlay: {
    width: wp('100%'),
    height: hp('100%'),
    backgroundColor: colors.darkBlue,
    position: 'absolute',
    left: 0,
    top: 0,
    opacity: 0.9,
  },
  modalView: isTabDevice()
    ? {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 15,
        padding: 20,
        paddingVertical: 20,
        paddingBottom: 0,
        // alignItems: 'center',
        shadowColor: '#000',
        width: 450,
        maxHeight: hp('60%'),
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        position: 'relative',
      }
    : {
        margin: 20,
        backgroundColor: colors.borderBlue,
        borderRadius: 15,
        padding: 15,
        paddingVertical: 20,
        paddingBottom: 0,
        // alignItems: 'center',
        shadowColor: '#000',
        width: 320,
        maxHeight: hp('60%'),
        // height: 250,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        position: 'relative',
      },
  modalText: isTabDevice()
    ? {
        marginBottom: 15,
        color: '#fff',
        fontSize: 28,
        fontWeight: 'bold',
      }
    : {
        marginBottom: 15,
        color: '#fff',
        fontSize: 20,
        fontWeight: 'bold',
      },
  closeButton: isTabDevice()
    ? {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        right: 15,
        top: 15,
        padding: 5,
        zIndex: 1000,
      }
    : {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        right: 15,
        top: 15,
        padding: 1,
        zIndex: 1000,
      },
  modalTitleContainer: isTabDevice()
    ? {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        backgroundColor: colors.red,
      }
    : {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        backgroundColor: colors.red,
      },
  modalTitle: isTabDevice()
    ? {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('1.8%'),
        fontFamily: 'Poppins-regular',
      }
    : {
        marginBottom: 15,
        textAlign: 'center',
        color: colors.white,
        fontSize: wp('5%'),
        fontFamily: 'Poppins-regular',
      },
  buttons: isTabDevice()
    ? {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        marginBottom: wp('2%'),
        width: '100%',
      }
    : {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        marginBottom: wp('4%'),
        width: '100%',
      },
  button: isTabDevice()
    ? {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('1%'),
        paddingLeft: wp('3%'),
        paddingBottom: wp('1%'),
        paddingRight: wp('3%'),
        borderRadius: wp('1%'),
      }
    : {
        backgroundColor: colors.aquaBlue,
        paddingTop: wp('2%'),
        paddingLeft: wp('4%'),
        paddingBottom: wp('2%'),
        paddingRight: wp('4%'),
        borderRadius: wp('2%'),
      },
  button2: {
    backgroundColor: colors.borderBlue,
    paddingTop: wp('1%'),
    paddingLeft: wp('3%'),
    paddingBottom: wp('1%'),
    paddingRight: wp('3%'),
    borderRadius: wp('1%'),
  },
  buttonText: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.white,
        fontSize: wp('3.5%'),
        fontWeight: 'bold',
      },
  modalHeader: isTabDevice()
    ? {
        marginBottom: wp('2%'),
      }
    : {
        marginBottom: wp('3%'),
      },
  modalTitle: isTabDevice()
    ? {
        color: colors.white,
        fontSize: wp('2%'),
        fontFamily: 'Poppins-Bold',
      }
    : {
        color: colors.white,
        fontSize: wp('4%'),
        fontFamily: 'Poppins-Bold',
      },
  modalRow: isTabDevice()
    ? {
        marginBottom: wp('2%'),
      }
    : {
        marginBottom: wp('3%'),
      },
  fieldLabel: isTabDevice()
    ? {
        color: colors.green,
        fontSize: wp('1.5%'),
        fontFamily: 'Poppins-Regular',
      }
    : {
        color: colors.green,
        fontSize: wp('3%'),
        fontFamily: 'Poppins-Regular',
      },
  messageText: isTabDevice()
    ? {
        color: colors.white,
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        padding: wp('1.7%'),
        fontSize: wp('1.5%'),
        marginTop: wp('1%'),
      }
    : {
        color: colors.white,
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        padding: wp('2.4%'),
        fontSize: wp('3%'),
        marginTop: wp('1%'),
      },
  btnCalendar: isTabDevice()
    ? {
        width: '100%',
        backgroundColor: colors.darkBlue,
        borderRadius: wp('1%'),
        padding: wp('1.7%'),
        marginTop: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'relative',
      }
    : {
        width: '100%',
        backgroundColor: colors.darkBlue,
        borderRadius: wp('2%'),
        padding: wp('2.4%'),
        marginTop: wp('1%'),
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        position: 'relative',
      },
  btnCalendarTextWrapper: {
    width: '85%',
  },
  btnCalendarText: isTabDevice()
    ? {
        color: colors.lightGrey,
        fontSize: wp('1.5%'),
      }
    : {
        color: colors.lightGrey,
        fontSize: wp('3%'),
      },
  calendarIcon: isTabDevice()
    ? {
        width: wp('1.5%'),
        height: wp('2%'),
        resizeMode: 'contain',
      }
    : {
        width: wp('3.5%'),
        height: wp('3%'),
        resizeMode: 'contain',
      },
  lastPaymentWrapper: isTabDevice()
    ? {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 20,
      }
    : {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 10,
        padding: 10,
        display: 'flex',
        flexDirection: 'column',
        marginBottom: 20,
      },
  lastPaymentInfo: isTabDevice()
    ? {
        backgroundColor: colors.semiDarkBlue,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 10,
        paddingRight: 20,
        paddingLeft: 20,
        width: '74%',
      }
    : {
        display: 'flex',
        flexDirection: 'column',
      },
  lastPaymentDataWrapper: isTabDevice()
    ? {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }
    : {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      },
  lastPaymentDataDivider: {
    width: 1,
    height: '100%',
    backgroundColor: colors.darkBlue,
  },
  lastPaymentDataTitle: isTabDevice()
    ? {
        color: colors.green,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('1.5%'),
        fontWeight: 'bold',
      }
    : {
        color: colors.green,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('5.5%'),
        fontWeight: 'bold',
      },
  lastPaymentDataPeriod: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.3%'),
        fontWeight: '400',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3.3%'),
        fontWeight: '400',
      },
  lastPaymentDataDate: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.3%'),
        fontWeight: '400',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3.3%'),
        fontWeight: '400',
      },
  lastPaymentDataAmount: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.3%'),
        fontWeight: '400',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Bold',
        fontSize: wp('5.3%'),
        fontWeight: '400',
      },
  lastPaymentDataButton: isTabDevice()
    ? {
        backgroundColor: colors.green,
        borderRadius: 10,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '25%',
      }
    : {
        backgroundColor: colors.green,
        borderRadius: 5,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 10,
        marginTop: 10,
      },
  lastPaymentDataButtonText: isTabDevice()
    ? {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('1.1%'),
        fontWeight: '400',
      }
    : {
        color: colors.white,
        fontFamily: 'Poppins-Medium',
        fontSize: wp('3.1%'),
        fontWeight: '400',
      },
});
export default sessionStyles;
