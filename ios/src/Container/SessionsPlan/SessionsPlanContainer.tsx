import * as WebBrowser from 'expo-web-browser';
import React, { useCallback, useEffect, useState } from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { useSelector } from 'react-redux';
import calendarIcon from '../../../assets/buttons/calendar-icon.png';
import editIcon from '../../../assets/buttons/editIcon.png';
import { isTabDevice } from '../../config/appConfig';
import colors from '../../config/colors';
import useColors from '../../hooks/useColors';
import useSession from '../../hooks/useSession';
import useStyles from '../../hooks/useStyles';
import { RootStore } from '../../store/store';
import SessionAddModal from './SessionsAddModal';
import sessionStyles from './playerSessionStyles';
import { userRoleType } from '../../constants/constants';

const SessionsContentBox = ({
  title,
  value,
  isEditEnable,
  isCalenderIcon,
  onClickEdit,
  customClass,
  isPlayerOrParent,
}: {
  title: string;
  value: string | number | null;
  isEditEnable?: boolean;
  isCalenderIcon?: boolean;
  onClickEdit?: (arg: boolean) => void;
  customClass: any;
  isPlayerOrParent?: boolean;
}) => {
  const sessionPlanStyles = useStyles(sessionStyles);

  return (
    <View style={customClass}>
      {isEditEnable && !isPlayerOrParent && (
        <TouchableOpacity
          style={sessionPlanStyles.editButton}
          onPress={() => onClickEdit?.(true)}
        >
          {isCalenderIcon ? (
            <Image source={calendarIcon} style={sessionPlanStyles.editIcon} />
          ) : (
            <Image source={editIcon} style={sessionPlanStyles.editIcon} />
          )}
        </TouchableOpacity>
      )}

      <Text style={sessionPlanStyles.dataTileText}>{value}</Text>

      <Text style={sessionPlanStyles.dataTileText2}>{title}</Text>
    </View>
  );
};

const singleSessionUpdate = (sessionPlanStyles: any, item: any) => {
  const { addedAt, count, expiryDate, usedCount, userSubscriptionId } =
    item.item;

  const getCurrentStyleForUI = (userSubscriptionId: string | null) => {
    return userSubscriptionId
      ? sessionPlanStyles.dataTableTextActive
      : sessionPlanStyles.dataTableText;
  };

  return (
    <View style={sessionPlanStyles.dataTableRow}>
      <View
        style={[
          sessionPlanStyles.dataTableColumn,
          {
            alignItems: 'flex-start',
            paddingLeft: isTabDevice() ? wp('1%') : wp('2%'),
          },
        ]}
      >
        <Text style={getCurrentStyleForUI(userSubscriptionId)}>{addedAt}</Text>
      </View>
      <View style={sessionPlanStyles.dataTableColumn}>
        <Text style={getCurrentStyleForUI(userSubscriptionId)}>{count}</Text>
      </View>
      <View style={sessionPlanStyles.dataTableColumn}>
        <Text style={getCurrentStyleForUI(userSubscriptionId)}>
          {usedCount}
        </Text>
      </View>
      <View style={sessionPlanStyles.dataTableColumn}>
        <Text style={getCurrentStyleForUI(userSubscriptionId)}>
          {expiryDate}
        </Text>
      </View>
    </View>
  );
};

const SubscriptionPlan = ({ userId }: { userId: string }) => {
  const theme = useColors();
  const { clubSettings } = useSelector((state: RootStore) => state?.common);
  const { paymentEnabled } = clubSettings || {};
  const { currentEnvironment, user, userRole } = useSelector(
    (state: RootStore) => state?.auth
  );
  const { getUserLastSessionInfo } = useSession();

  const [latesSession, setLatesSession] = useState<any>(null);
  const [visible, setVisible] = useState(true);

  const hideSpinner = () => {
    setVisible(false);
  };

  useEffect(() => {
    const getUserLatestSession = async () => {
      await getUserLastSessionInfo().then(data => {
        setLatesSession(data);
      });
    };

    getUserLatestSession();
  }, []);

  // If username and password not  in db we need to ask user to insert it
  const sessionPlanStyles = useStyles(sessionStyles);
  const [isUserCredentialAvailable, setIsUserCredentialAvailable] =
    useState(true);

  const openWebView = useCallback(() => {
    setVisible(true);
    setIsUserCredentialAvailable(false);
  }, []);

  const closeWebView = useCallback(() => {
    setIsUserCredentialAvailable(true);
  }, []);

  const { price, date, subscriptionName } = latesSession || {};

  const getProperUrlForUserIAPNavigation = useCallback(() => {
    const { iapWebUrl } = clubSettings || {};
    const { email } = user || {};

    return `${iapWebUrl}`;
    // ? `${iapWebUrl}?user_id=${userId}&user_from=app&has-subscription=true&user_email=${email}&club_id=${currentEnvironment}&endpoint=my-payment-history`
    // : `${iapWebUrl}?user_id=${userId}&user_from=app&has-subscription=false&user_email=${email}&club_id=${currentEnvironment}&endpoint=subscription-plan`;
  }, [clubSettings, currentEnvironment, user]);

  const [result, setResult] = useState<any>(null);

  const _handlePressButtonAsync = async () => {
    let result = await WebBrowser.openBrowserAsync(
      getProperUrlForUserIAPNavigation()
    );
    setResult(result);
  };

  return (
    <>
      {isTabDevice() ? (
        <View style={sessionPlanStyles.lastPaymentWrapper}>
          <View style={sessionPlanStyles.lastPaymentInfo}>
            <View style={sessionPlanStyles.lastPaymentDataWrapper}>
              <Text style={sessionPlanStyles.lastPaymentDataTitle}>
                Last Payment
              </Text>
            </View>
            <View style={sessionPlanStyles.lastPaymentDataDivider}></View>
            <View style={sessionPlanStyles.lastPaymentDataWrapper}>
              <Text style={sessionPlanStyles.lastPaymentDataPeriod}>
                {subscriptionName || '-'}
              </Text>
            </View>
            <View style={sessionPlanStyles.lastPaymentDataDivider}></View>
            <View style={sessionPlanStyles.lastPaymentDataWrapper}>
              <Text style={sessionPlanStyles.lastPaymentDataDate}>
                {date || '-'}
              </Text>
            </View>
            <View style={sessionPlanStyles.lastPaymentDataDivider}></View>
            <View>
              <Text style={sessionPlanStyles.lastPaymentDataAmount}>
                {price ? `${price.amount} ${price.currency}` : '-'}
              </Text>
            </View>
          </View>

          {userRole !== 'HEAD_COACH' &&
            userRole !== 'COACH' &&
            paymentEnabled && (
              <TouchableOpacity
                onPress={_handlePressButtonAsync}
                style={sessionPlanStyles.lastPaymentDataButton}
              >
                <View>
                  {/* Navigation TO WEB VIEW */}
                  <Text style={sessionPlanStyles.lastPaymentDataButtonText}>
                    Subscription Plan
                  </Text>
                </View>
              </TouchableOpacity>
            )}
        </View>
      ) : (
        <View style={sessionPlanStyles.lastPaymentWrapper}>
          <View style={sessionPlanStyles.lastPaymentInfo}>
            <View>
              <Text style={sessionPlanStyles.lastPaymentDataTitle}>
                Last Payment
              </Text>
            </View>
            <View style={sessionPlanStyles.lastPaymentDataWrapper}>
              <View>
                <Text style={sessionPlanStyles.lastPaymentDataPeriod}>
                  {subscriptionName || '-'}
                </Text>
                <Text style={sessionPlanStyles.lastPaymentDataDate}>
                  {date || '-'}
                </Text>
              </View>
              <View>
                <Text style={sessionPlanStyles.lastPaymentDataAmount}>
                  {' '}
                  {price ? `${price.amount} ${price.currency}` : '-'}
                </Text>
              </View>
            </View>
          </View>
          {userRole !== 'HEAD_COACH' &&
            userRole !== 'COACH' &&
            paymentEnabled && (
              <TouchableOpacity onPress={_handlePressButtonAsync}>
                <View style={sessionPlanStyles.lastPaymentDataButton}>
                  {/* Navigation TO WEB VIEW */}
                  <Text style={sessionPlanStyles.lastPaymentDataButtonText}>
                    Subscription Plan
                  </Text>
                </View>
              </TouchableOpacity>
            )}
        </View>
      )}

      {/* {!isUserCredentialAvailable && (
        <>
          <ModalWrapper visible transparent>
            <View
              style={{
                position: 'relative',
                height: hp('100%'),
                width: wp('100%'),
              }}
            >
              <TouchableOpacity
                onPress={closeWebView}
                style={{
                  // backgroundColor: theme.darkBlue,
                  flexDirection: 'column',
                  justifyContent: 'flex-end',
                  height: hp('10%'),
                  position: 'absolute',
                  zIndex: 100,

                  paddingLeft: 20,
                }}
              >
                <Ionicons
                  name="arrow-back"
                  size={28}
                  color="white"
                  style={{ marginBottom: 15 }}
                />
              </TouchableOpacity>

              {/* This indicate will run till web page load
              
              {visible && (
                <View
                  style={{
                    position: 'relative',
                    height: hp('100%'),
                    width: wp('100%'),
                    backgroundColor: theme.darkBlue,
                  }}
                >
                  <ActivityIndicator
                    size="large"
                    style={{
                      position: 'absolute',
                      zIndex: 1000,
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  />
                </View>
              )}
              <WebView
                originWhitelist={['*']}
                source={{
                  uri: getProperUrlForUserIAPNavigation(),
                  baseUrl: '',
                }}
                style={{ flex: 1 }}
                onLoad={hideSpinner}
              />
            </View>
          </ModalWrapper>
        </>
      )} */}
    </>
  );
};

const SessionsPlanContainer = ({ playerInfoData }: { playerInfoData: any }) => {
  const { userId } = playerInfoData;

  const sessionPlanStyles = useStyles(sessionStyles);
  const {
    count,
    expiryDate,
    remaining,
    isEditModalOpen,
    setIsEditModalOpen,
    addNumberOfSessions,
    setAddNumberOfSessions,
    isDatePikerEnable,
    setIsDatePikerEnable,
    mode,
    updateSessionUpdate,
    getSessionExpiryDate,
    expireDateFormatted,
    getSessionInfo,
    userSessionUpdates,
    errorMassageOnSubmit,
    openSubmitModal,
    isPlayerOrParent,
    pageIncrement,
  } = useSession();

  useEffect(() => {
    getSessionInfo();
  }, []);

  const mobileWidthRemaining = isPlayerOrParent ? { width: '48%' } : {};
  const mobileWidthExpiry = isPlayerOrParent

    ? { width: '48%' }
    : { width: '100%', marginTop: wp('4%') };

    const { userData } = useSelector((state : any) => state?.auth);
    const isParent = userRoleType.PARENT === userData?.type;

  return (
    <>
      <View style={sessionPlanStyles.sessionPlannerWrapper}>
        {isTabDevice() ? (
          <>
            <SubscriptionPlan userId={userId} />
            <View style={sessionPlanStyles.dataTileWrapper}>
              {!isPlayerOrParent && (
                <SessionsContentBox
                  title="Number of sessions"
                  value={count}
                  isEditEnable
                  onClickEdit={setIsEditModalOpen}
                  customClass={sessionPlanStyles.dataTile}
                  isPlayerOrParent={isPlayerOrParent}
                />
              )}
              <SessionsContentBox
                title="No. sessions remaining"
                value={remaining}
                customClass={[
                  sessionPlanStyles.dataTile,
                  { backgroundColor: colors.darkGrey, ...mobileWidthRemaining },
                ]}
              />
              <SessionsContentBox
                title="Expiry date"
                value={expiryDate}
                isCalenderIcon
                isEditEnable
                onClickEdit={setIsEditModalOpen}
                customClass={[
                  sessionPlanStyles.dataTile,
                  isTabDevice()
                    ? isPlayerOrParent
                      ? { width: '48%' }
                      : { width: '32%' }
                    : { ...mobileWidthExpiry },
                ]}
                isPlayerOrParent={isPlayerOrParent}
              />
            </View>

            <View style={sessionPlanStyles.dataTableWrapper}>
              <Text style={sessionPlanStyles.dataTableTitle}>
                Updated History
              </Text>
              <View style={sessionPlanStyles.dataTableHeader}>
                <View
                  style={[
                    sessionPlanStyles.dataTableColumn,
                    { alignItems: 'flex-start' },
                  ]}
                >
                  <Text style={sessionPlanStyles.dataTableHeaderText}>
                    Date
                  </Text>
                </View>
                <View style={sessionPlanStyles.dataTableColumn}>
                  <Text style={sessionPlanStyles.dataTableHeaderText}>
                    Sessions Added
                  </Text>
                </View>
                <View style={sessionPlanStyles.dataTableColumn}>
                  <Text style={sessionPlanStyles.dataTableHeaderText}>
                    Total Used
                  </Text>
                </View>
                <View style={sessionPlanStyles.dataTableColumn}>
                  <Text style={sessionPlanStyles.dataTableHeaderText}>
                    Expiration Date
                  </Text>
                </View>
              </View>
              <ScrollView
                style={
                  isPlayerOrParent
                    ? sessionPlanStyles.DataTableBodyParent
                    : sessionPlanStyles.DataTableBody
                }
              >
                <FlatList
                  data={userSessionUpdates || []}
                  renderItem={item =>
                    singleSessionUpdate(sessionPlanStyles, item)
                  }
                  keyExtractor={item => item?._id}
                  onEndReached={pageIncrement}
                />
              </ScrollView>
            </View>
          </>
        ) : (
          <>
            <ScrollView contentContainerStyle={{ paddingBottom: 250 }}>
              <SubscriptionPlan userId={userId} />
              <View style={sessionPlanStyles.dataTileWrapper}>
                {!isPlayerOrParent && (
                  <SessionsContentBox
                    title="Number of sessions"
                    value={count}
                    isEditEnable
                    onClickEdit={setIsEditModalOpen}
                    customClass={sessionPlanStyles.dataTile}
                    isPlayerOrParent={isPlayerOrParent}
                  />
                )}
                <SessionsContentBox
                  title="No. sessions remaining"
                  value={remaining}
                  customClass={[
                    sessionPlanStyles.dataTile,
                    {
                      backgroundColor: colors.darkGrey,
                      ...mobileWidthRemaining,
                    },
                  ]}
                />
                <SessionsContentBox
                  title="Expiry date"
                  value={expiryDate}
                  isCalenderIcon
                  isEditEnable
                  onClickEdit={setIsEditModalOpen}
                  customClass={[
                    sessionPlanStyles.dataTile,
                    isTabDevice()
                      ? isPlayerOrParent
                        ? { width: '48%' }
                        : { width: '32%' }
                      : { ...mobileWidthExpiry },
                  ]}
                  isPlayerOrParent={isPlayerOrParent}
                />
              </View>

              <View style={sessionPlanStyles.dataTableWrapper}>
                <Text style={sessionPlanStyles.dataTableTitle}>
                  Updated History
                </Text>
                <View style={sessionPlanStyles.dataTableHeader}>
                  <View
                    style={[
                      sessionPlanStyles.dataTableColumn,
                      { alignItems: 'flex-start' },
                    ]}
                  >
                    <Text style={sessionPlanStyles.dataTableHeaderText}>
                      Date
                    </Text>
                  </View>
                  <View style={sessionPlanStyles.dataTableColumn}>
                    <Text style={sessionPlanStyles.dataTableHeaderText}>
                      Sessions Added
                    </Text>
                  </View>
                  <View style={sessionPlanStyles.dataTableColumn}>
                    <Text style={sessionPlanStyles.dataTableHeaderText}>
                      Total Used
                    </Text>
                  </View>
                  <View style={sessionPlanStyles.dataTableColumn}>
                    <Text style={sessionPlanStyles.dataTableHeaderText}>
                      Expiration Date
                    </Text>
                  </View>
                </View>
                <ScrollView
                  style={
                    isPlayerOrParent
                      ? sessionPlanStyles.DataTableBodyParent
                      : sessionPlanStyles.DataTableBody
                  }
                >
                  <FlatList
                    data={userSessionUpdates || []}
                    renderItem={item =>
                      singleSessionUpdate(sessionPlanStyles, item)
                    }
                    keyExtractor={item => item?._id}
                    onEndReached={pageIncrement}
                    contentContainerStyle={
                      isParent && !isTabDevice() &&  {paddingBottom: 100 } 
                    }
                  />
                </ScrollView>
              </View>
            </ScrollView>
          </>
        )}
      </View>
      {isEditModalOpen && (
        <SessionAddModal
          setIsEditModalOpen={openSubmitModal}
          addNumberOfSessions={addNumberOfSessions}
          setAddNumberOfSessions={setAddNumberOfSessions}
          isDatePikerEnable={isDatePikerEnable}
          setIsDatePikerEnable={setIsDatePikerEnable}
          mode={mode}
          addExpireDate={expireDateFormatted}
          setAddExpireDate={getSessionExpiryDate}
          updateSessionUpdate={updateSessionUpdate}
          errorMassageOnSubmit={errorMassageOnSubmit}
        />
      )}
    </>
  );
};

export default SessionsPlanContainer;
